﻿using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Digiturk.Workflow.Digiflow.Validation
{
    public interface IValidation
    {
        bool CreateValidate(EntityBase entityBase);

        bool ApproveValidate(EntityBase entityBase);


        bool RejectValidate(EntityBase entityBase);
    }
}
