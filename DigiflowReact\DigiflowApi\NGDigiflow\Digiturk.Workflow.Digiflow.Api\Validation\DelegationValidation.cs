﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Api.Controllers;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace Digiturk.Workflow.Digiflow.Validation
{
    public class DelegationValidation:ValidationBase,IValidation
    {
        public override bool CreateValidate(EntityBase entityBase)
        {
            var entity = (DelegationRequest)entityBase;
            DateTime startDate = entity.StartTime;
            DateTime endDate = entity.EndTime;
            bool isDelegasyonRequestExist = false;

            if(!string.IsNullOrEmpty(entity.WorkFlowIds) && entity.WorkFlowIds.Split(',').Contains("1247"))
            {
                isDelegasyonRequestExist = true;
            }

            if (entity.DelegatedLoginId == 0)
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Kullanıcı Seçiniz");
            }

            if (string.IsNullOrEmpty(entity.WorkFlowIds))
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen İş Akışı Seçiniz");
            }

            if (!(ConvertionHelper.ConvertValue<DateTime>(endDate) > ConvertionHelper.ConvertValue<DateTime>(startDate)))
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Delegasyon Bitiş Tarihi Delegasyon Başlangıç Tarihinden büyük olmalıdır");
            }

            if (isDelegasyonRequestExist)
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Delegasyon Akışı Delege edilemez");
            }

            if (entity.DelegatedLoginId == entity.CreatedBy)
            {
                throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Kendinize delege edemezsiniz");
            }

            foreach (string item in entity.WorkFlowIds.Split(','))
            {
                // Bana yapılan delegasyon mu?
                if (!Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.CheckForDelegation(entity.CreatedBy, ConvertionHelper.ConvertValue<long>(item), startDate, endDate))
                {
                    throw new Exception("", new Exception("Seçmiş olduğunuz akış için ilgili Tarihlerde size atanmış ve tarafınızdan onaylanmış farklı bir kullanıcı için delegasyon bulunmaktadır."));
                }

                // Ben bu formu delege etmiş miyim?
                if (!Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.CheckForDelegationOwn(entity.CreatedBy, ConvertionHelper.ConvertValue<long>(item), startDate, endDate))
                {
                    throw new Exception("", new Exception("Seçmiş olduğunuz akış ilgili Tarihlerde Farklı bir kullanıcıya delege edilmiştir"));
                }
                if (WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursiveDateValidation(entity.CreatedBy, ConvertionHelper.ConvertValue<long>(entity.DelegatedLoginId), ConvertionHelper.ConvertValue<long>(item), startDate, endDate) == entity.CreatedBy)
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("İlgili Tarihlerde Delegesi Olduğunuz kişiye akışlarınızı delege edemezsiniz");
                }

                // Göndereceğim kişi formu delege etmiş mi?
                if (!Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.CheckForDelegationOwn(ConvertionHelper.ConvertValue<long>(entity.DelegatedLoginId), ConvertionHelper.ConvertValue<long>(item), startDate, endDate))
                {
                    throw new Exception("", new Exception("Seçmiş olduğunuz akış için ilgili kullanıcı ilgili Tarihlerde akışı farklı bir kullanıcıya delege etmiştir"));
                }
            }

            if (startDate == DateTime.Today)
            {
                startDate = DateTime.Now;
            }

            return base.CreateValidate(entityBase);   
        }


        new public bool ApproveValidate(EntityBase entityBase)
        {
            var entity = (DelegationRequest)entityBase;
            string[] kontrolListesi = entity.WorkFlowIds.Split(',');

            foreach (string defId in kontrolListesi)
            {
                string WfDefName = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetDefinitionName(ConvertionHelper.ConvertValue<int>(defId));
                if (!Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.CheckForDelegation(
              entity.OwnerLoginId,
            ConvertionHelper.ConvertValue<int>(defId),
              entity.StartTime,
              entity.EndTime))
                {
                    // Todo Delegasyon Change
                    throw new Exception("", new Exception("İlgili akış (" + WfDefName + ") için ilgili Tarihlerde Talep yapan kullanıcıya atanmış farklı bir delegasyon bulunduğundan dolayı talebi onaylayamazsınız."));
                }

                // Talebi yapan kullanıcı bu formu delege etmiş mi?
                if (!Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.CheckForDelegationOwn(
                    entity.OwnerLoginId,
                   ConvertionHelper.ConvertValue<int>(defId),
                    entity.StartTime,
                    entity.EndTime))
                {
                    throw new Exception("", new Exception("ilgili akış (" + WfDefName + ") ilgili Tarihlerde farklı bir kullanıcıya delege edildiğinden dolayı bu talebi onaylayamazsınız"));
                }

                // Ben bu Formu delege etmiş miyim?
                if (!Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.CheckForDelegationOwn(
                    entity.DelegatedLoginId,
                    ConvertionHelper.ConvertValue<int>(defId),
                    entity.StartTime,
                    entity.EndTime))
                {
                    throw new Exception("", new Exception("ilgili akışı (" + WfDefName + ")  ilgili Tarihlerde akışı farklı bir kullanıcıya delege ettiğinizden dolayı bu talebi onaylayamazsınız"));
                }

                if (WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursiveDateValidation(entity.OwnerLoginId, entity.DelegatedLoginId, ConvertionHelper.ConvertValue<long>(defId), entity.StartTime, entity.EndTime) == entity.OwnerLoginId)
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.ValidationError("İlgili Tarihlerde Delegesi Olduğunuz kişiye akışlarınızı delege edemezsiniz");
                }

            }

            return base.ApproveValidate(entityBase);    
        }


        public override bool RejectValidate(EntityBase entityBase)
        {
            return base.RejectValidate(entityBase);
        }


        public override bool CancelValidate(EntityBase entityBase)
        {
            return base.CancelValidate(entityBase);
        }
    }
}