﻿using DevExpress.Web.ASPxGridView;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.WebCore;
using System;
using System.Data;
using System.Web.UI;

public partial class WorkflowAdmins : YYSSecurePage
{
    /// <summary>
    /// İş Akış yöneticilerinin görüntülendiği ekran
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        #region İş Akışlarını combobox a doldurur

        ((MasterPage)Master).PageTitle = "Akış Yöneticileri";
        ((MasterPage)Master).ShowMenu(true);
        try
        {
            if (!Page.IsPostBack)
            {
                if (AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAdmin || AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAndFlowAdmin)
                {
                    //Kullanıcının admini olduğu iş akışları doldurulur
                    WorkFlowCombobox = Common.FillCombobox("NAME", "WF_WORKFLOW_DEF_ID", WorkFlowCombobox, WorkflowHelper.GetLiveWorkFlows(), "İş Akışı Seçiniz", "0");
                }
                else
                {
                    Response.Redirect("Error.aspx", false);
                }
            }
            if (Page.IsCallback)
            {
                #region Sayfalamada grid i tekrar yenilemek için kullanılır

                if (Session["WfDefId"] != null)
                {
                    //ManagersGridView.DataSource = WorkflowHelper.GetAdminsOfWorkflowDataTable(ConvertionHelper.ConvertValue<long>(Session["WfDefId"]));
                    ManagersGridView.DataSource = (DataTable)Session["ManagersGridView"];
                    ManagersGridView.DataBind();
                }

                #endregion Sayfalamada grid i tekrar yenilemek için kullanılır
            }
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowError("Hata", "Sayfa yüklenirken beklenmeyen bir hata ile karşılaşıldı", ex.Message);
        }

        #endregion İş Akışlarını combobox a doldurur
    }

    /// <summary>
    /// İş akış adminlerini doldurur
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowCombobox_SelectedIndexChanged(object sender, EventArgs e)
    {
        //Seçime göre iş akış yöneticileri görüntülenir
        FillDataGrid();
    }

    /// <summary>
    /// Seçime göre iş akış yöneticileri görüntülenir
    /// </summary>
    private void FillDataGrid()
    {
        string value = WorkFlowCombobox.SelectedItem.Value.ToString();
        Session["WfDefId"] = value;
        DataTable dtAdmins = WorkflowHelper.GetAdminsOfWorkflowDataTable(ConvertionHelper.ConvertValue<long>(value));
        ManagersGridView.DataSource = dtAdmins;
        Session["ManagersGridView"] = dtAdmins;
        ManagersGridView.DataBind();
    }

    /// <summary>
    /// Kullanıcının admin tipini getiriri
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    public static string GetLevel(object val)
    {
        string ret = string.Empty;

        switch (val.ToString())
        {
            case "1":
                ret = "-";
                break;

            case "2":
                ret = "Akış Yöneticisi";
                break;

            case "3":
                ret = "Sistem Yöneticisi";
                break;

            case "4":
                ret = "Sistem ve Akış Yöneticisi";
                break;
        }

        return ret;
    }

    /// <summary>
    /// Kullanıcının admin tipini gridview de değiştirir
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void ManagersGridView_HtmlDataCellPrepared(object sender, DevExpress.Web.ASPxGridView.ASPxGridViewTableDataCellEventArgs e)
    {
        if (e.DataColumn.Caption == "Kullanıcı Seviyesi")
        {
            e.Cell.Text = GetLevel(e.CellValue);
        }
        else if (e.DataColumn.Caption == "Aktif")
        {
            if (ConvertionHelper.ConvertValue<Boolean>(e.CellValue))
            {
                e.Cell.Text = "Aktif";
            }
            else
            {
                e.Cell.Text = "Pasif";
            }
        }
    }

    /// <summary>
    /// Silme işlemini yapar, eğer silinebilir olarak işaretlendi ise silinir, eğer bir iş kuralına bağlı ise silinemez
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void ManagersGridView_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
    {
        #region Admin in akış yöneticisi olduğu akışı Silme İşlemi

        //long WfOfId = ConvertionHelper.ConvertValue<long>(e.Values[4]);
        //ASPxGridView gridView = (ASPxGridView)sender;
        //WorkflowAdminHelper.DeleteTheFlowOfTheAdmin(WfOfId);
        //((MasterPage)Master).ShowPopup(false, "Akış Yöneticisi Silme", "İstenilen aış yöneticinin akış yöneticiliğinden silindi!", false, "");
        //FillDataGrid();
        //gridView.CancelEdit();
        //e.Cancel = true;

        #endregion Admin in akış yöneticisi olduğu akışı Silme İşlemi
    }

    /// <summary>
    /// Delegasyon Düzenleme işini yapar
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DeleteButton_Click(object sender, EventArgs e)
    {
        long WfOfId = ConvertionHelper.ConvertValue<long>(Session["DeletingWfAdminId"]);
        WorkflowAdminHelper.DeleteTheFlowOfTheAdmin(WfOfId);
        ((MasterPage)Master).ShowPopup(false, "Akış Yöneticisi Silme", "Akış Yöneticisi silindi", false, "");
        FillDataGrid();
    }

    /// <summary>
    /// Silmek için buradan ID değeri alınır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void ManagersGridView_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
    {
        Session["DeletingWfAdminId"] = e.KeyValue.ToString();
    }

    /// <summary>
    /// Sayfalamada datayı daha hızlı getirmek için kullanılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void ManagersGridViewPageIndexChanging(object sender, EventArgs e)
    {
        DataTable dtInboxGridView = new DataTable();
        if (Session["ManagersGridView"] != null)
        {
            dtInboxGridView = (DataTable)Session["ManagersGridView"];
        }
        else
        {
            dtInboxGridView = WorkflowHelper.GetAdminsOfWorkflowDataTable(ConvertionHelper.ConvertValue<long>(Session["WfDefId"]));
            Session["ManagersGridView"] = dtInboxGridView;
        }
        ManagersGridView.DataSource = dtInboxGridView;
        ManagersGridView.DataBind();
        dtInboxGridView.Dispose();
        dtInboxGridView = null;
    }
}