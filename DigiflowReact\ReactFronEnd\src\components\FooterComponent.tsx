import * as React from 'react'
import { WGrid,WAppBar, WLink } from '@wface/components'
import { useTranslation } from "react-i18next"

function FooterComponent() {
    const { t } = useTranslation()

    return (
        <WAppBar
            id=""
            position="static">
            <WGrid container alignItems='center' direction='row' style={{margin: 10, justifyContent: "center"}}>
                <div> Copyright © 2022 - {t('workflows_system')}</div>
            </WGrid>
        </WAppBar>
    )
}

export default FooterComponent
