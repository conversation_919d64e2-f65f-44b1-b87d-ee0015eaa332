﻿
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual Studio 2010
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "DigiFlowYYS", "..\DigiFlowYYS", "{0AB1006F-C92F-4FC9-8D00-AC715155B77E}"
	ProjectSection(WebsiteProperties) = preProject
		SccProjectName = "SAK"
		SccAuxPath = "SAK"
		SccLocalPath = "SAK"
		SccProvider = "SAK"
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv3.5"
		Debug.AspNetCompiler.VirtualPath = "/DigiFlowYYS"
		Debug.AspNetCompiler.PhysicalPath = "..\DigiFlowYYS\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\DigiFlowYYS\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/DigiFlowYYS"
		Release.AspNetCompiler.PhysicalPath = "..\DigiFlowYYS\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\DigiFlowYYS\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "4670"
	EndProjectSection
EndProject
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "Digiturk.Workflow.Digiflow.Entities", "..\..\Digiturk.Workflow.Digiflow.Entities", "{20E1AE09-B7E1-4AE7-850D-E52744802D4B}"
	ProjectSection(WebsiteProperties) = preProject
		SccProjectName = "SAK"
		SccAuxPath = "SAK"
		SccLocalPath = "SAK"
		SccProvider = "SAK"
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.0"
		Debug.AspNetCompiler.VirtualPath = "/Digiturk.Workflow.Digiflow.Entities"
		Debug.AspNetCompiler.PhysicalPath = "..\..\Digiturk.Workflow.Digiflow.Entities\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\Digiturk.Workflow.Digiflow.Entities\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/Digiturk.Workflow.Digiflow.Entities"
		Release.AspNetCompiler.PhysicalPath = "..\..\Digiturk.Workflow.Digiflow.Entities\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\Digiturk.Workflow.Digiflow.Entities\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "51138"
	EndProjectSection
EndProject
Global
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 3
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://dtl1tfssrv1:8080/
		SccLocalPath0 = .
		SccWebProject1 = true
		SccProjectUniqueName1 = .
		SccLocalPath1 = .
		SccProjectEnlistmentChoice1 = 2
		SccWebProject2 = true
		SccProjectUniqueName2 = ..\\..\\Digiturk.Workflow.Digiflow.Entities
		SccProjectName2 = ../../Digiturk.Workflow.Digiflow.Entities
		SccLocalPath2 = ..\\..\\Digiturk.Workflow.Digiflow.Entities
		SccProjectEnlistmentChoice2 = 2
	EndGlobalSection
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0AB1006F-C92F-4FC9-8D00-AC715155B77E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0AB1006F-C92F-4FC9-8D00-AC715155B77E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20E1AE09-B7E1-4AE7-850D-E52744802D4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20E1AE09-B7E1-4AE7-850D-E52744802D4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
