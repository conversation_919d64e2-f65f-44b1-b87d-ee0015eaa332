import * as React from 'react'
import {useEffect, useState} from "react"
import { useTranslation } from "react-i18next"
import CommandListComponent from "../../components/CommandListComponent";
import HistoryComponent from "../../components/HistoryComponent";
import {WGrid} from "@wface/components";
import DelegationDetailComponent from "../../components/DelegationDetailComponent";
import {getUser} from "../../services/wface-helper";


function WorkflowDetailScreen() {
    const { t } = useTranslation()

    const user = getUser()

    return(
        <WGrid style={{margin: 20}}>
            <DelegationDetailComponent />
            <CommandListComponent />
            <HistoryComponent />
        </WGrid>
    )
}

export default WorkflowDetailScreen
