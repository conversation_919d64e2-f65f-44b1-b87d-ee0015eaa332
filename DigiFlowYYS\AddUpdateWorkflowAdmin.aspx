﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true"
    CodeFile="AddUpdateWorkflowAdmin.aspx.cs" Inherits="AddUpdateWorkflowAdmin" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">

    <style type="text/css">
        .style1 {
            width: 96%;
        }

        .style2 {
            width: 141px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" cellpadding="0" cellspacing="0" width="65%" align="center">
     <%--   <asp:Panel ID="UserSelectionPanel" runat="server">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Kullanıcı Seç</b>
                </td>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxComboBox ID="UsersASPxComboBox" runat="server" OnSelectedIndexChanged="UsersASPxComboBox_SelectedIndexChanged"
                        Width="250px" AutoPostBack="true">
                    </dx:ASPxComboBox>
                </td>
            </tr>
        </asp:Panel>--%>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <asp:Panel ID="UserSelectionPanel" runat="server">
                    <table class="style1">
                        <tr>
                            <td class="style3">
                                <b>Kullanıcı Adı&nbsp;&nbsp; </b>
                            </td>
                            <td>
                                <asp:DropDownList ID="UsersASPxComboBox" runat="server"
                                    Width="250px" AutoPostBack="True"
                                    onselectedindexchanged="UsersASPxComboBox_SelectedIndexChanged">
                                </asp:DropDownList>
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <dx:ASPxLabel ID="NameASPxLabel" runat="server">
                </dx:ASPxLabel>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <b>&nbsp;</b><asp:Panel ID="Panel1" runat="server">
                    <table class="ui-accordion">
                        <tr>
                            <td class="style2">
                                <dx:ASPxCheckBox ID="IsSystemManagerASPxCheckBox" runat="server"
                                    Text="Sistem Yöneticisi">
                                </dx:ASPxCheckBox>
                            </td>
                            <td>
                                <dx:ASPxCheckBox ID="IsActiveASPxCheckBox" runat="server" Checked="True"
                                    Text="Aktif">
                                </dx:ASPxCheckBox>
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <b>&nbsp;Yönetici Olduğu Akışlar</b>
            </td>
        </tr>
        <tr>
            <td align="left" style="width:"100%">
                <asp:CheckBoxList ID="WorkflowListCheckBoxList" runat="server" RepeatColumns="3">
                </asp:CheckBoxList>
            </td>
        </tr>
        <tr>
            <td align="center" style="width:"100%">
                <table align="center" class="style1" width="50%">
                    <tr>
                        <td align="right">
                <dx:ASPxButton ID="SelectButton" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                    CssPostfix="Office2010Silver" OnClick="lbTumunuSec_Click" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                    Text="Tümünü Seç" Width="120px">
                </dx:ASPxButton>
                        </td>
                        <td align="left">
                <dx:ASPxButton ID="SelectAllButton" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                    CssPostfix="Office2010Silver" OnClick="lbTumunuKaldir_Click" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                    Text="Tümünü Kaldır" Width="120px">
                </dx:ASPxButton>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td align="center" style="padding: 5px" valign="top" width="100%">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="center" style="padding: 5px" valign="top" width="100%">
                <dx:ASPxButton ID="SaveASPxButton" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                    CssPostfix="Office2010Silver" OnClick="SaveASPxButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                    Text="Kaydet">
                </dx:ASPxButton>
            </td>
        </tr>
    </table>
</asp:Content>