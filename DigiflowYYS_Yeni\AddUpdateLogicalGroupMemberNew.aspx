﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site1.Master" AutoEventWireup="true" CodeBehind="AddUpdateLogicalGroupMemberNew.aspx.cs" Inherits="DigiflowYYS_Yeni.AddUpdateLogicalGroupMemberNew" %>

<%@ MasterType VirtualPath="~/Site1.Master" %>
<%@ Register Src="~/UserControls/OrganizationTreeWebUserControl.ascx" TagPrefix="uc2" TagName="OrganizationTreeWebUserControl" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajaxtoolkit" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v12.1.Export, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView.Export" TagPrefix="dx" %>



<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <script type="text/javascript" src="../js/jquery-3.3.1.min.js"></script>
    <script type="text/javascript" src="../js/select2.min.js"></script>
    <script type="text/javascript" src="../js/jquery-ui.min.js"></script>
    <link href="../css/select2.min.css" rel="stylesheet" />
    <link href="../css/bootstrap@4.4.1_dist_css_bootstrap.min.css" rel="stylesheet" />
    <link href="../css/jquery-ui.css" rel="stylesheet" />

    <script>
        function pageLoad() {
            $('.classDrpAdGroup').select2();
        }
    </script>
    <style>
        .drpMantiksal, .txtOutsource, .txtParam {
            width: 420px;            
        }

        .select2 {
            min-width: 455px;
        }

        .classDrpAdDomain {
            width: 250px;
            padding: 5px;
        }
        .drpMantiksal{
            padding:5px;
        }
    </style>


</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    
    <div class="container">
        <div class="row mt-3">
            <div class="col-md-9">
                <div class="col-md-3 d-inline-block">
                    <asp:Literal Text="Akış Listesi" runat="server" />
                </div>
                <div class="col-md-6 d-inline-block">
                    <asp:DropDownList ID="drpAkisListesi" OnSelectedIndexChanged="drpAkisListesi_SelectedIndexChanged" CssClass="drpMantiksal" AutoPostBack="true" runat="server">
                        <asp:ListItem Text="text1" />
                        <asp:ListItem Text="text2" />
                    </asp:DropDownList>
                </div>
            </div>

        </div>
        <div class="row mt-3">
            <div class="col-md-9">
                <div class="col-md-3 d-inline-block">
                    <asp:Literal Text="Mantıksal Grup İsmi" runat="server" />
                </div>
                <div class="col-md-6 d-inline-block">
                    <asp:DropDownList ID="drpMantiksalGrupİsmi" OnSelectedIndexChanged="drpMantiksalGrupİsmi_SelectedIndexChanged" CssClass="drpMantiksal" AutoPostBack="True" runat="server"></asp:DropDownList>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-9">
                <div class="col-md-3 d-inline-block">
                    <asp:Literal Text="Mantıksal Grup Açıklama" runat="server" />
                </div>
                <div class="col-md-6 d-inline-block">
                    <asp:TextBox ID="txtMantiksalGrupAciklama" TextMode="MultiLine" Style="resize: none; width: 420px; padding: 5px;" runat="server" />
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-9">
                <div class="col-md-3 mt-3">
                    <h6>
                        <asp:Literal Text="Kullanıcı Tipi" runat="server" />
                    </h6>
                </div>
                <div class="col-md-6 ml-3">
                    <asp:RadioButtonList ID="rdbListKullaniciTipleri" OnSelectedIndexChanged="rdbListKullaniciTipleri_SelectedIndexChanged" AutoPostBack="true" runat="server">
                        <asp:ListItem Text="KULLANICI" Value="1" />
                        <asp:ListItem Text="TÜM KULLANICILAR" Value="2" />
                        <asp:ListItem Text="PARAMETRE" Value="3" />
                        <asp:ListItem Text="OUTSOURCE" Value="4" />
                        <asp:ListItem Text="AD KULLANICILARI" Value="5" />
                    </asp:RadioButtonList>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-9">
                <h6>
                    <asp:Literal Text="İşlemlerin Kaydedilmesi İçin Aşağıdaki Kaydet Butonuna Basmalısınız !" runat="server" />
                </h6>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-9">
                <asp:Panel ID="pnlKullanici" Visible="false" runat="server">
                    <div class="col-md-3 mb-2">
                        <h6>
                            <asp:Literal Text="Kullanıcılar" runat="server" />
                        </h6>
                    </div>
                    <div class="col-md-6">
                        <asp:DropDownList ID="drpKullanicilar" runat="server">
                            <asp:ListItem Text="text1" />
                            <asp:ListItem Text="text2" />
                        </asp:DropDownList>
                    </div>
                </asp:Panel>
            </div>
        </div>
        <asp:Panel ID="pnlTumKullanıcılar" Visible="false" runat="server">
            <div class="row mt-3">
                <div class="col-md-9">
                    <div class="col-md-3">
                        <h6>
                            <asp:Literal Text="Tüm Kullanıcılar" runat="server" />
                        </h6>
                    </div>
                </div>
            </div>
        </asp:Panel>
        <asp:Panel ID="pnlParam" Visible="false" runat="server">
            <div class="row mt-3">
                <div class="col-md-12 mx-auto">
                    <div class="col-md-3">
                        <h6>
                            <asp:Literal Text="Parametre" runat="server" />
                        </h6>
                    </div>
                    <div class="col-md-3">
                        <asp:Literal Text="İçerik" runat="server" />
                    </div>
                    <div class="col-md-6">
                        <asp:TextBox ID="txtParamIcerik" CssClass="txtParam" runat="server" />
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-9">
                    <div class="col-md-3">
                        <asp:Literal Text="Açıklama" runat="server" />
                    </div>
                    <div class="col-md-6">
                        <asp:TextBox ID="txtParamAciklama" CssClass="txtParam" runat="server" />
                    </div>
                </div>
            </div>
        </asp:Panel>
        <asp:Panel ID="pnlOutsource" Visible="false" runat="server">
            <div class="row mt-3">
                <div class="col-md-12 mx-auto">
                    <div class="col-md-3">
                        <h6>
                            <asp:Literal Text="Outsource" runat="server" />
                        </h6>
                    </div>
                    <div class="col-md-3">
                        <asp:Literal Text="Ad - Soyad" runat="server" />
                    </div>
                    <div class="col-md-3">
                        <asp:TextBox ID="txtOutAdSoyad" CssClass="txtOutsource" runat="server" />
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-9">
                    <div class="col-md-3">
                        <asp:Literal Text="Email" runat="server" />
                    </div>
                    <div class="col-md-3">
                        <asp:TextBox ID="txtOutEmail" CssClass="txtOutsource" runat="server" />
                    </div>
                </div>
            </div>
        </asp:Panel>
        <div class="row mt-3">
            <div class="col-md-9">
                <div class="col-md-3">
                    <asp:Button ID="btnEkleGenel" Text="EKLE" Visible="false" OnClick="btnEkleGenel_Click" runat="server" />
                </div>
            </div>
        </div>
        <asp:Panel ID="pnlAdGroupPart" Visible="false" runat="server">
            <h6>
                <asp:Literal Text="AD Grupları" runat="server" /></h6>
            <div class="row mt-3">
                <div class="col-md-4">
                    <div style="font-size: medium">
                        <asp:Literal Text="Domain" runat="server" />
                    </div>
                    <asp:DropDownList ID="drpDomain" OnSelectedIndexChanged="drpDomain_SelectedIndexChanged" CssClass="classDrpAdDomain" AutoPostBack="true" runat="server">
                        <asp:ListItem Value="0">---Seçiniz---</asp:ListItem>
                        <asp:ListItem Value="DIGITURK">DIGITURK</asp:ListItem>
                        <asp:ListItem Value="DIGITURKCC">DIGITURKCC</asp:ListItem>
                    </asp:DropDownList>
                </div>
                <div class="col-md-6">
                    <div style="font-size: medium">
                        <asp:Literal Text="AD Grubu" runat="server" />
                    </div>
                    
                    <asp:DropDownList ID="drpAdGrubu" CssClass="classDrpAdGroup" runat="server">
                    </asp:DropDownList>
                </div>
                <div class="col-md-2" style="text-align:center;margin-top:25px;">
                    <asp:Button ID="btnAdGrupSorgula" Text="Sorgula" OnClick="btnAdGrupSorgula_Click" runat="server" />
                </div>
            </div>

            <div class="row mt-3 ">
                <div class="col-md-12 mx-auto">
                    <h6>
                        <asp:Literal Text="Ad Grubu Kullanıcıları" runat="server" />
                    </h6>
                    <dx:ASPxGridView ID="grdAdGrupKullanicilari" runat="server">
                        <Columns>
                            <dx:GridViewDataColumn Caption="KULLANICI ADI" FieldName="USERNAME"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="GÖRÜNEN ADI" FieldName="DISPLAYNAME"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="F_LOGIN_ID" FieldName="FLOGIN"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="F_LOGIN_STATUS" FieldName="FLOGINSTATUS"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="CHECKRESULT" FieldName="CHECKRESULT"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="DOMAIN" FieldName="DOMAIN"></dx:GridViewDataColumn>
                            <dx:GridViewDataColumn Caption="AD GRUBU ADI" FieldName="AD_GROUP"></dx:GridViewDataColumn>
                        </Columns>
                        <Styles>
                            <Header BackColor="#5c2d91" ForeColor="White"></Header>
                        </Styles>
                        <Settings ShowFilterRow="true" />
                    </dx:ASPxGridView>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <asp:Button ID="btnAdKullanicilariEkle" OnClick="btnAdKullanicilariEkle_Click" Text="EKLE" runat="server" />
                        </div>
                    </div>
                
                </div>
            </div>
        </asp:Panel>
        <div class="row mt-3">
            <div class="col-md-12 mx-auto">
                <dx:ASPxGridView ID="grdTumKaydedilecekler" KeyFieldName="ID" runat="server">
                    <Columns>
                        <dx:GridViewDataColumn Caption="AD SOYAD" FieldName="FULLNAME"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="F_LOGIN" FieldName="LOGIN_ID"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="PAR. İÇERİK" FieldName="CONTENT"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="PAR. AÇIKLAMA" FieldName="DESCRIPTION"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="OUT EMAİL" FieldName="EMAIL"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="DOMAİN" FieldName="AD_DOMAIN"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="AD GRUBU ADI" FieldName="AD_GROUP"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="TIP" FieldName="TIP"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="GUID" FieldName="GUID" Visible="false"></dx:GridViewDataColumn>
                        <dx:GridViewDataColumn Caption="SİL" FieldName="">
                            <DataItemTemplate>
                                <asp:LinkButton ID="lnkButSilTumGrd" Text="SİL" OnClick="lnkButSilTumGrd_Click" runat="server" />
                            </DataItemTemplate>
                        </dx:GridViewDataColumn>
                    </Columns>
                    <Settings ShowFilterRow="true" />
                    <Styles>
                        <Header BackColor="#5c2d91" ForeColor="White"></Header>
                    </Styles>
                </dx:ASPxGridView>
            </div>
               
            <dx:ASPxGridViewExporter ID="grdExcelExporter" GridViewID="grdTumKaydedilecekler"  runat="server"></dx:ASPxGridViewExporter>            

        </div>
        <div class="row mt-3 mb-3">
            <div class="col-md-9">
                <asp:Button ID="btnKaydetGenel" BackColor="#5c2d91" ForeColor="White" Text="KAYDET" OnClick="btnKaydetGenel_Click" runat="server" />

                <asp:Button ID="btnTumunuTemizle" BackColor="#5c2d91" ForeColor="White" OnClick="btnTumunuTemizle_Click" Text="TÜM AD USERLARI TEMİZLE" runat="server" />                

                <asp:Button ID="btnExceleAktar" BackColor="#5c2d91" ForeColor="White" Text="EXCELE AKTAR" OnClick="btnExceleAktar_Click" runat="server" />
            </div>
        </div>

    </div>
</asp:Content>
