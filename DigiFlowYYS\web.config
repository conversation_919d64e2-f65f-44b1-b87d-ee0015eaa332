﻿<?xml version="1.0"?>
<configuration>
  <configSections>
    <sectionGroup name="devExpress">
      <section name="settings" type="DevExpress.Web.ASPxClasses.SettingsConfigurationSection, DevExpress.Web.v10.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="compression" type="DevExpress.Web.ASPxClasses.CompressionConfigurationSection, DevExpress.Web.v10.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="themes" type="DevExpress.Web.ASPxClasses.ThemesConfigurationSection, DevExpress.Web.v10.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="errors" type="DevExpress.Web.ASPxClasses.ErrorsConfigurationSection, DevExpress.Web.v10.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
    </sectionGroup>
    <section name="bccMailList" type="Digiturk.Workflow.Common.WorkflowMailerSection, Digiturk.Workflow.Common"/>
    <section name="debugMailList" type="Digiturk.Workflow.Common.WorkflowMailerSection, Digiturk.Workflow.Common"/>
  </configSections>
  <appSettings>
    <add key="DatabaseMode" value="Oracle"/>
    <add key="debugMode" value="true"/>
    <add key="IsSIDControls" value="True"/>
    <add key="PageTitle" value="Digiturk İş Akışları"/>
    <add key="hibernate.use_reflection_optimizer" value="false"/>
    <add key="ServiceName" value="ApplicationServer1"/>
    <add key="IsYYSActive" value="false"/>
    <add key="DefinitionConfigration" value="\\dtl1sp1\Digiturk.Workflow.DigiFlow\WFPages\Definiton.xml"/>
    <add key="SharePointRaporTalepUploadFolder" value="http://belgeler/DigiFlowDocs/RaporTalepDocs/"/>
    <!--email settings-->
    <add key="Workflow.Mail.EmbeddedImagesPath" value="D:\\ITTPDev\\Deployment\\MailImages"/>
    <add key="Workflow.Mail.Params" value="ad9bBOUpHG1st9IlCOvZA9DCTJKj7XTlewXqZpa4xWo/m0f/ZXwzFpTy9cdYK53Hx2MQqWxlyxSVT5lg5waY6LC3p5i77oc4pHAEGgnKFbAuL48SNlMELo9dIiUOo2RmdTprZ/SAkyKF03+gmRGRexw3+qCFnr/iVOx/58S075o="/>
    <add key="Workflow.Mail.Server" value="smtp.digiturk.local"/>
    <add key="Workflow.Mail.FromAddress" value="<EMAIL>"/>
    <add key="Workflow.Mail.IsMailDebugMode" value="True"/>
    <add key="Web.Services.UserName" value="Digiflow_sa"/>
    <add key="Web.Services.Password" value="Digif16up+-"/>
    <add key="ESSBDurum" value="E"/>
    <!--E YADA H-->
    <add key="DBSLIVE_INQUIRY" value="********"/>
    <add key="DBSTEST_INQUIRY" value="INQUIRY"/>
    <add key="ITTPTEST_INQUIRY" value="INQUIRY"/>
    <add key="DBSLIVE_INQUIRY_APPSTR" value="Digiflow"/>
    <add key="DBSLIVE_INQUIRY_UNIQUE" value="4!fedL0w"/>
    <add key="DBSTEST_INQUIRY_APPSTR" value="Digiflow"/>
    <add key="DBSTEST_INQUIRY_UNIQUE" value="4!fedL0w"/>
    <add key="ITTPTEST_INQUIRY_APPSTR" value="Digiflow"/>
    <add key="ITTPTEST_INQUIRY_UNIQUE" value="4!fedL0w"/>
    <!--email settings-->
    <!-- ADPortal Config-->
    <add key="AdPortalServices" value="http://dtl4sptst1:3212/AdPortalServices.asmx"/>
    <add key="AdPortalCCServices" value="http://dtl4sptst1:3212/AdPortalServices.asmx"/>
  </appSettings>
  <connectionStrings>
    <add name="connStrESSB_TEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ITTPTEST)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient"/>
    <add name="connStrESSB_LIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient"/>
    <add name="DBSLIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id={0};Password=***" providerName="System.Data.OracleClient"/>
    <add name="DBSTEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=DBSTEST)));User Id={0};Password=***" providerName="System.Data.OracleClient"/>
    <add name="ITTPTEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ITTPTEST)));User Id={0};Password=***" providerName="System.Data.OracleClient"/>
    <add name="DBSConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SID=DBSLIVE)));User Id=INQUIRY;Password=********;Self Tuning=false"/>
    <add name="DefaultConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR;Pooling=true;Statement Cache Size=10"/>
    <add name="ReportConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=true;Statement Cache Size=10"/>
    <add name="FrameworkConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=FRAMEWORK;Password=FRAMEWORK;Pooling=true;Statement Cache Size=10"/>
    <!--<add name="DefaultConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVER=dedicated)(SERVICE_NAME=DSSFLOW)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR;Pooling=true;Statement Cache Size=10;Self Tuning=false" />
    <add name="ReportConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVER=dedicated)(SERVICE_NAME=DSSFLOW)));User Id=***********;Password=***********;Pooling=true;Self Tuning=false" />
    <add name="FrameworkConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVER=dedicated)(SERVICE_NAME=DSSFLOW)));User Id=FRAMEWORK;Password=FRAMEWORK;Pooling=true;Statement Cache Size=10;Self Tuning=false" />-->
  </connectionStrings>
  <system.web>
    <compilation debug="true" targetFramework="4.0">
      <assemblies>
        <add assembly="DevExpress.Data.v10.2, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.DirectoryServices, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Web, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
      </assemblies>
    </compilation>
    <authentication mode="Windows"/>
    <customErrors mode="Off"/>
    <httpModules>
      <add type="DevExpress.Web.ASPxClasses.ASPxHttpHandlerModule, DevExpress.Web.v10.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
    </httpModules>
    <pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID"/>
  </system.web>
  <!--
        The system.webServer section is required for running ASP.NET AJAX under Internet
        Information Services 7.0.  It is not necessary for previous version of IIS.
    -->
  <system.webServer>
    <modules>
      <add type="DevExpress.Web.ASPxClasses.ASPxHttpHandlerModule, DevExpress.Web.v10.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
    </modules>
    <httpProtocol>
      <customHeaders>
        <clear/>
        <add name="X-UA-Compatible" value="IE=7"/>
      </customHeaders>
    </httpProtocol>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <qualifyAssembly partialName="Oracle.DataAccess" fullName="Oracle.DataAccess, Version=2.111.7.20, Culture=neutral,                 PublicKeyToken=89b483f429c47342"/>
    </assemblyBinding>
    <assemblyBinding appliesTo="v2.0.50727" xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Oracle.DataAccess" publicKeyToken="89b483f429c47342"/>
        <bindingRedirect oldVersion="1.1.0.0-3.112.2.0" newVersion="2.111.7.20"/>
      </dependentAssembly>
    </assemblyBinding>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Oracle.DataAccess" publicKeyToken="89b483f429c47342"/>
        <bindingRedirect oldVersion="1.1.0.0-3.112.2.0" newVersion="2.111.7.20"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <devExpress>
    <settings rightToLeft="false"/>
    <compression enableHtmlCompression="false" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="false"/>
    <themes enableThemesAssembly="true"/>
    <errors callbackErrorRedirectUrl=""/>
  </devExpress>
  <debugMailList/>
  <bccMailList/>
</configuration>