﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Actions.AssigmentClass;
using Digiturk.Workflow.Digiflow.DataAccessLayer;
using Digiturk.Workflow.Digiflow.Entities.Enums;
using Digiturk.Workflow.Digiflow.GenericMailHelper;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using System.Collections.Generic;

namespace Digiturk.Workflow.Digiflow.Actions
{
    /// <summary>
    /// Sözleşme Akışında Görev Atama Fonksiyonundaki Seçilen kişiye Assigment Yapar
    /// </summary>
    public class ContratAssigmentRoting : CommonAssignment, IActionClass, IWorkflowDiagramAssignmentHelper
    {
        private List<string> ListNameSurname = new List<string>();
        /// <summary>
        /// Sözleşme Akışında Görev Atama Fonksiyonundaki Seçilen kişiye Assigment Yapar
        /// </summary>
        /// <param name="wfContext">Parametre Listesi</param>
        /// <param name="actionInstance">Aksiyon</param>
        /// <returns></returns>
        public bool Execute(WFContext wfContext, FWfActionInstance actionInstance)
        {
            #region EskiKod
            /*
             if (!DigiFlowRollBackHelper.IsByPassed(actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId, actionInstance.WfStateInstance.WfStateInstanceId))
            {
                ActionType CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                ActionType CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
                string assigmentType = "TASKINBOX";
                string loginType = "LOGIN";
                long WorkflowInstanceId = actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                long AssignToLoginId = long.Parse(wfContext.Parameters["ContractForwardPersonel"].ToString());
                long LastActionToLoginId = CheckingWorker.GetLastActionToLoginId(WorkflowInstanceId);
                long NewAssignLoginId = AssignToLoginId;
                CurrentActionType = ActionType.ONWFAPPROVEASSIGNER;
                CurrentActionOwnerType = ActionType.ONWFAPPROVEOWNER;
                Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(NewAssignLoginId, loginType, actionInstance.WfActionInstanceId, assigmentType);
                var viewList = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetViewList(NewAssignLoginId.ToString());
                if (viewList.Count == 0 || viewList.IndexOf(actionInstance.WfStateInstance.WfWorkflowInstance.ToString()) < 0)
                {
                    Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(NewAssignLoginId, loginType, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId, "WFVIEW");
                }
                #region Email Action İçin Instance Parametreleri Ekleniyor
                base.EmailActionsAddition(wfContext, actionInstance, false);
                #endregion
                Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionType, WorkflowInstanceId, LastActionToLoginId, NewAssignLoginId, wfContext);
                Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionOwnerType, WorkflowInstanceId, LastActionToLoginId, NewAssignLoginId, wfContext);
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.Execute(actionInstance, NewAssignLoginId, NewAssignLoginId, WorkflowHistoryActionType.ASSIGN, "");
                wfContext.Parameters.AddOrChangeItem("ContractForwardPersonel", "");

                wfContext.Save();

                #region Object Disopsing

                assigmentType = null;
                loginType = null;

                #endregion Object Disopsing
            }
            return true;
            */
            #endregion
            return DoExecution(wfContext, actionInstance, null, true);
        }

        private bool DoExecution(WFContext wfContext, FWfActionInstance actionInstance, FWfWorkflowInstance wfInstance, bool doExecution)
        {
            if (actionInstance == null || !DigiFlowRollBackHelper.IsByPassed(actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId, actionInstance.WfStateInstance.WfStateInstanceId))
            {
                long AssignToLoginId = long.Parse(wfContext.Parameters["ContractForwardPersonel"].ToString());
                long NewAssignLoginId = AssignToLoginId;
                if (doExecution)
                {
                    ActionType CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                    ActionType CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
                    string assigmentType = "TASKINBOX";
                    string loginType = "LOGIN";
                    long WorkflowInstanceId = actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                    long LastActionToLoginId = CheckingWorker.GetLastActionToLoginId(WorkflowInstanceId);
                    
                    CurrentActionType = ActionType.ONWFAPPROVEASSIGNER;
                    CurrentActionOwnerType = ActionType.ONWFAPPROVEOWNER;
                    Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(NewAssignLoginId, loginType, actionInstance.WfActionInstanceId, assigmentType);
                    var viewList = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetViewList(NewAssignLoginId.ToString());
                    if (viewList.Count == 0 || viewList.IndexOf(actionInstance.WfStateInstance.WfWorkflowInstance.ToString()) < 0)
                    {
                        Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(NewAssignLoginId, loginType, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId, "WFVIEW");
                    }
                    #region Email Action İçin Instance Parametreleri Ekleniyor
                    base.EmailActionsAddition(wfContext, actionInstance, false);
                    #endregion
                    Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionType, WorkflowInstanceId, LastActionToLoginId, NewAssignLoginId, wfContext);
                    Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionOwnerType, WorkflowInstanceId, LastActionToLoginId, NewAssignLoginId, wfContext);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.Execute(actionInstance, NewAssignLoginId, NewAssignLoginId, WorkflowHistoryActionType.ASSIGN, "");
                    wfContext.Parameters.AddOrChangeItem("ContractForwardPersonel", "");

                    wfContext.Save();

                    #region Object Disopsing

                    assigmentType = null;
                    loginType = null;

                    #endregion Object Disopsing
                }
                else
                {
                    if (NewAssignLoginId > 0)
                    {
                        string nameSurname = Digiflow.Authorization.WflowDataHelpers.GetLoginNameSurname(NewAssignLoginId);
                        ListNameSurname.Add(nameSurname);
                    }
                    ListNameSurname.Sort();
                }
            }
            return true;
        }

        public List<string> GetAssignmentListNames(WFContext wfContext, FWfWorkflowInstance wfInstance, long WfStateDefId)
        {
            DoExecution(wfContext, null, wfInstance, false);
            return ListNameSurname;
        }
    }
}