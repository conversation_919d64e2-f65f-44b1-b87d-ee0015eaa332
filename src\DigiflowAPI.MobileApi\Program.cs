using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using DigiflowMobileAPI.Interfaces;
using DigiflowMobileAPI.Services;
using DigiflowMobileAPI.Models.Auth;
using DigiflowMobileAPI.Models.Configuration;
using DigiflowMobileAPI.Middleware;
using DigiflowAPI.Security.Authentication.Services;

var builder = WebApplication.CreateBuilder(args);

// ─── Configuration ──────────────────────────────────────────────────────────────
var jwtIssuer = builder.Configuration["Jwt:Issuer"] ?? "DigiflowAPI";
var jwtAudience = builder.Configuration["Jwt:Audience"] ?? "DigiflowAPI";
var jwtSecret = builder.Configuration["Jwt:Secret"] ?? "3KvxnOU+fW0HNCsuv2SHRjIqBn+rN15pf7EomQPkQ1M=";
var jwtExpiryMinutes = int.Parse(builder.Configuration["Jwt:ExpiryMinutes"] ?? "129600");

// ─── Services ───────────────────────────────────────────────────────────────────
builder.Services.AddControllers(options =>
{
    // Add route conventions if needed
});
builder.Services.AddEndpointsApiExplorer();

// Configure Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "DigiHR Mobile API", Version = "v1" });

    // Define JWT Bearer auth security scheme
    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.Http,
        Scheme = "Bearer",
        BearerFormat = "JWT"
    });

    // Apply security requirements globally
    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Custom schema IDs to avoid conflicts
    c.CustomSchemaIds(type => type.FullName?.Replace("+", "."));

    // Ignore obsolete endpoints
    c.IgnoreObsoleteActions();
    c.IgnoreObsoleteProperties();
});

// Configure authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = "Bearer";
    options.DefaultChallengeScheme = "Bearer";
    options.DefaultAuthenticateScheme = "Bearer";
})
.AddJwtBearer("Bearer", options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSecret)),
        ValidateIssuer = true,
        ValidIssuer = jwtIssuer,
        ValidateAudience = true,
        ValidAudience = jwtAudience,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.FromMinutes(5)
    };

    // Don't throw exceptions, let the middleware handle them
    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            // Log authentication failures but don't stop the request pipeline
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogWarning("Authentication failed: {Message}", context.Exception.Message);
            return Task.CompletedTask;
        }
    };
});

builder.Services.AddAuthorization(options =>
{
    // Don't use a fallback policy - let endpoints decide
    // options.FallbackPolicy = options.DefaultPolicy;
});

builder.Services.AddHttpContextAccessor();

// Register custom services
builder.Services.Configure<JwtOptions>(builder.Configuration.GetSection("Jwt"));
builder.Services.Configure<DigiflowApiOptions>(builder.Configuration.GetSection(DigiflowApiOptions.SectionName));
builder.Services.AddSingleton<WindowsAuthService>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<DigiflowAPI.MobileAPI.Interfaces.IDigiflowService, DigiflowAPI.MobileAPI.Services.DigiflowService>();
builder.Services.AddScoped<DigiflowAPI.MobileAPI.Interfaces.IFrameworkService, DigiflowAPI.MobileAPI.Services.FrameworkService>();

// Add HttpClient for HomeService with environment-specific configuration
builder.Services.AddHttpClient<IHomeService, HomeService>((serviceProvider, client) =>
{
    var configuration = serviceProvider.GetRequiredService<IConfiguration>();
    var environment = serviceProvider.GetRequiredService<IWebHostEnvironment>();

    // Get the base URL from configuration based on environment
    string? baseUrl = configuration["DigiflowApi:BaseUrl"];

    if (string.IsNullOrEmpty(baseUrl))
    {
        if (environment.IsDevelopment())
        {
            baseUrl = "http://digiflowtest.digiturk.com.tr/api";
        }
        else if (environment.IsEnvironment("Test"))
        {
            baseUrl = "https://digiflowtest.digiturk.com.tr/api";
        }
        else
        {
            baseUrl = "http://digiflow.digiturk.com.tr/api";
        }
    }

    client.BaseAddress = new Uri(baseUrl);
    client.DefaultRequestHeaders.Add("User-Agent", "DigiflowMobileApi");
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
{
    UseDefaultCredentials = true,
    AllowAutoRedirect = true,
    AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate
});

// Configure CORS with specific allowed origins
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(
        policy =>
        {
            // Define allowed origins from configuration
            var allowedOrigins = builder.Configuration.GetSection("AllowedOrigins").Get<string[]>() ??
                new[] {
                    "http://localhost:3000",
                    "http://localhost:5173",
                    "http://digiflowtest.digiturk.com.tr",
                    "https://digiflowtest.digiturk.com.tr",
                    "http://digiflow.digiturk.com.tr",
                    "https://digiflow.digiturk.com.tr"
                };

            policy.WithOrigins(allowedOrigins)
                  .AllowAnyHeader()
                  .AllowAnyMethod();
        });

    // Keep a specific policy for endpoints that need credentials
    options.AddPolicy("AllowWithCredentials",
        policyBuilder => policyBuilder
            .WithOrigins(
                builder.Configuration.GetSection("AllowedOrigins").Get<string[]>() ??
                new[] {
                    "http://localhost:3000",
                    "http://localhost:5173",
                    "http://digiflowtest.digiturk.com.tr",
                    "https://digiflowtest.digiturk.com.tr",
                    "http://digiflow.digiturk.com.tr",
                    "https://digiflow.digiturk.com.tr"
                })
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials());
});

// Add HTTP client for calling DigiflowAPI if needed
builder.Services.AddHttpClient("DigiflowApi", client =>
{
    // Get the base URL from configuration based on environment
    string? baseUrl = null;

    // First try to get the direct BaseUrl setting
    baseUrl = builder.Configuration["DigiflowApi:BaseUrl"];

    // If not found, use environment-specific setting
    if (string.IsNullOrEmpty(baseUrl))
    {
        if (builder.Environment.IsDevelopment())
        {
            baseUrl = builder.Configuration["DigiflowApi:Development"];
        }
        else
        {
            baseUrl = builder.Configuration["DigiflowApi:Production"];
        }
    }

    // Fallback to HTTP for internal server communication
    baseUrl ??= "https://digiflowtest.digiturk.com.tr/api";

    client.BaseAddress = new Uri(baseUrl);
    client.DefaultRequestHeaders.Add("User-Agent", "MobileApi-Proxy");

    // Log the selected base URL
    Console.WriteLine($"DigiflowApi BaseUrl: {baseUrl}");
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
{
    UseDefaultCredentials = true,
    AllowAutoRedirect = true,
    AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

// Enable Swagger for all environments (can be restricted later if needed)
app.UseSwagger(c =>
{
    //c.SerializeAsV2 = false;
});
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "DigiHR Mobile API v1");
    c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    c.DefaultModelsExpandDepth(-1); // Hide models section by default
    c.DisplayRequestDuration();
    c.EnableDeepLinking();
    c.EnableFilter();
    c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
});

// Apply CORS policy before authentication
app.UseCors();

// Diagnostic middleware for request logging
app.Use(async (context, next) =>
{
    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
    logger.LogInformation("Request started: {Method} {Path}, ContentType: {ContentType}",
        context.Request.Method,
        context.Request.Path,
        context.Request.ContentType);

    // Check specifically for login paths to ensure they are accessible
    var path = context.Request.Path.Value?.ToLower() ?? "";
    if (path.EndsWith("/auth/login") || path.EndsWith("/mobile/auth/login"))
    {
        logger.LogInformation("Login endpoint access: {Path}", path);
    }

    await next();

    logger.LogInformation("Response completed: {StatusCode} for {Method} {Path}",
        context.Response.StatusCode,
        context.Request.Method,
        context.Request.Path);
});

// Add middleware in correct order for authentication flow
app.UseMiddleware<JwtAuthenticationMiddleware>();
app.UseAuthentication();
app.UseAuthorization();

// Add controllers with explicit routes - make sure public endpoints are accessible
app.MapControllers().AllowAnonymous();

app.Run();