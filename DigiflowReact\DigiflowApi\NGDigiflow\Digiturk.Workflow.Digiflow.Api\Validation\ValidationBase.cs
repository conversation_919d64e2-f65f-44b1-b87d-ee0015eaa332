﻿using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Digiturk.Workflow.Digiflow.Validation
{
    public abstract class ValidationBase
    {
        public virtual bool CreateValidate(EntityBase entityBase)
        {

            return true;
        }

        public virtual bool ApproveValidate(EntityBase entityBase)
        {

            return true;
        }

        public virtual bool RejectValidate(EntityBase entityBase)
        {

            return true;
        }

        public virtual bool CancelValidate(EntityBase entityBase)
        {

            return true;
        }


        public string CreateValidationError { get; set; }

        public string AproveValidationError { get; set; }

        public string RejectValidationError { get; set; }

        public string CancelValidationError { get; set; }
    }
}