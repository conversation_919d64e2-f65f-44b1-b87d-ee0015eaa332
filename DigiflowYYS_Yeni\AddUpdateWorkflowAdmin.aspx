﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site1.Master" AutoEventWireup="true" CodeBehind="AddUpdateWorkflowAdmin.aspx.cs" Inherits="DigiflowYYS_Yeni.AddUpdateWorkflowAdmin" %>
<%@ MasterType VirtualPath="~/Site1.Master" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">

    <style type="text/css">
        .style1 {
            width: 96%;
        }

        .style2 {
            width: 141px;
        }
    </style>
    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="js/select2.min.js"></script>
    <link href="css/select2.min.css" rel="stylesheet" />
    <script type="text/javascript">    
        function pageLoad() {
            debugger
            $(".usersDrop").select2();           
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" cellpadding="0" cellspacing="0" width="65%" align="center">
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <asp:Panel ID="UserSelectionPanel" runat="server">
                    <table class="style1">
                        <tr>
                            <td class="style3">
                                <b>Kullanıcı Adı&nbsp;&nbsp; </b>
                            </td>
                            <td>
                                <asp:DropDownList ID="UsersASPxComboBox" CssClass="usersDrop" runat="server"
                                    Width="250px" AutoPostBack="True"
                                    onselectedindexchanged="UsersASPxComboBox_SelectedIndexChanged">
                                </asp:DropDownList>
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                
                <dx:ASPxLabel ID="NameASPxLabel" runat="server">
                </dx:ASPxLabel>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <b>&nbsp;</b><asp:Panel ID="Panel1" runat="server">
                    <table class="ui-accordion">
                        <tr>
                            <td class="style2">
                                <dx:ASPxCheckBox ID="IsSystemManagerASPxCheckBox" runat="server"
                                    Text="Sistem Yöneticisi">
                                </dx:ASPxCheckBox>
                            </td>
                            <td>
                                <dx:ASPxCheckBox ID="IsActiveASPxCheckBox" runat="server" Checked="True"
                                    Text="Aktif">
                                </dx:ASPxCheckBox>
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <b>&nbsp;Yönetici Olduğu Akışlar</b>
            </td>
        </tr>
        <tr>
            <td align="left" style="width:"100%">
                <asp:CheckBoxList ID="WorkflowListCheckBoxList" runat="server" RepeatColumns="3">
                </asp:CheckBoxList>
            </td>
        </tr>
        <tr>
            <td align="center" style="width:"100%">
                <table align="center" class="style1" width="50%">
                    <tr>
                        <td align="right">
                <dx:aspxbutton ID="SelectButton" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                    CssPostfix="Office2010Silver" OnClick="lbTumunuSec_Click" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                    Text="Tümünü Seç" Width="120px">
                </dx:aspxbutton>
                        </td>
                        <td align="left">
                <dx:aspxbutton ID="SelectAllButton" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                    CssPostfix="Office2010Silver" OnClick="lbTumunuKaldir_Click" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                    Text="Tümünü Kaldır" Width="120px">
                </dx:aspxbutton>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td align="center" style="padding: 5px" valign="top" width="100%">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="center" style="padding: 5px" valign="top" width="100%">
                <dx:aspxbutton ID="SaveASPxButton" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                    CssPostfix="Office2010Silver" OnClick="SaveASPxButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                    Text="Kaydet">
                </dx:aspxbutton>
            </td>
        </tr>
    </table>


    
</asp:Content>