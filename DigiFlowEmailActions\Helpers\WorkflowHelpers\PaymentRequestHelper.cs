﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class PaymentRequestHelper
    {
        internal static void CancelEndFlow(FWfWorkflowInstance currentWfIns)
        {
            Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.PaymentInformationHelper.SetOdemeNetsisDurum(ConvertionHelper.ConvertValue<string>(currentWfIns.WfWorkflowInstanceId), "IPTAL");
        }
    }
}
