import { apiUrl } from "../../configs/digiflow/digiflow.config";
import axios from "axios";
interface IResponse {
    ResponseCode: number;
    ResponseErrorMessage: string;
    ResponseMessage: string;
    EntityData: any;
    EntityDataList: any;
}
const Api = () => {
    const api = axios.create({
        baseURL: apiUrl,
        timeout: 60000
    });

    const GetData = (method: string) => {
        return new Promise((resolve, reject) => {
            api
                .get(method, {
                    headers: {
                        "Content-Type": "application/json",
                        // IrisVerificationToken: token()
                    }
                })
                .then(r => resolve(r.data))
                .catch(e => {
                    if (e.response && e.response.status === 401) {
                        // logOut();
                    } else {
                        reject("Servise erişilirken bir hata oluştu.");
                    }
                });
        });
    };

    const PostData = (method: string, postData?: any) => {
        return new Promise((resolve, reject) => {
            api
                .post(method, postData, {
                    headers: {
                        "Content-Type": "application/json",
                        // IrisVerificationToken: token()
                    }
                })
                .then(r => resolve(r.data))
                .catch(e => {
                    if (e.response && e.response.status === 401) {
                        // logOut();
                    } else {
                        reject("Servise erişilirken bir hata oluştu.");
                    }
                });
        });
    };
    const DownloadDocument = (method: string, postData?: any) => {
        return new Promise((resolve, reject) => {
            api
                .post(method, postData, {
                    headers: {
                        "Content-Type": "application/json",
                        // IrisVerificationToken: token()
                    },
                    responseType: "blob"
                })
                .then(r => {
                    resolve(r.data);
                })
                .catch(e => {
                    if (e.response && e.response.status === 401) {
                        // logOut();
                    } else {
                        reject("Servise erişilirken bir hata oluştu.");
                    }
                });
        });
    };
    const UploadDocument = (
        method: string,
        postData?: any
    ): Promise<IResponse> => {
        return new Promise((resolve, reject) => {
            api
                .post(method, postData, {
                    headers: {
                        "Content-Type": "multipart/form-data",
                        // IrisVerificationToken: token()
                    }
                })
                .then(r => resolve(r.data))
                .catch(e => {
                    if (e.response && e.response.status === 401) {
                        // logOut();
                    } else {
                        reject("Servise erişilirken bir hata oluştu.");
                    }
                });
        });
    };
    return { GetData, PostData, DownloadDocument, UploadDocument };
};
export default Api();
