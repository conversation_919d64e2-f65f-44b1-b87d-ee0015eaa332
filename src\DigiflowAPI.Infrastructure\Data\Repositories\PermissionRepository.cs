﻿using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Domain.DTOs.SOAP;
using DigiflowAPI.Application.DTOs.Common;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class PermissionRepository(IPermissionProcessService permissionProcess) : IPermissionRepository
    {
        public async Task<IEnumerable<(string Value, string Text)>?> GetLocationsAsync()
        {
            var result = await permissionProcess.ExecuteProcessAsync<GetLocationDto, SelectOptionDto>(
                "GetLocation",
                "LOCATION",
                null,
                serviceDto => new SelectOptionDto
                {
                    Value = serviceDto.LOCATION,
                    Label = serviceDto.LOCATION,
                    LabelEn = serviceDto.LOCATION,
                });

            return result?.Select(dto => (dto.Value, dto.Label));
        }
        public async Task<IEnumerable<(string Value, string Text)>?> GetSuggestersAsync(string location)
        {
            var result = await permissionProcess.ExecuteProcessAsync<GetLocationTeamDto, SelectOptionDto>(
                "GetLocationTeam",
                "",
                new Dictionary<string, string> { { "mtLocation", location } },
                serviceDto => new SelectOptionDto
                {
                    Value = serviceDto.USERNAME,
                    Label = serviceDto.NAME_SURNAME,
                    LabelEn = serviceDto.NAME_SURNAME
                });

            return result?.Select(dto => (dto.Value, dto.Label));
        }
        public async Task<string> GetSuggestersTeamAsync(string suggester)
        {
            return await permissionProcess.ExecuteProcessValueAsync<string>(
                "GetCCUserInfo",
                "",
                "TEAM_NAME",
                new Dictionary<string, string> { { "login", suggester } });
        }
    }
}
