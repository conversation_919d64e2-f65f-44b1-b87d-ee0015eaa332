﻿<%@ Application Language="C#" %>

<script runat="server">

    void Application_Start(object sender, EventArgs e) 
    {
        // Code that runs on application startup
        System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("tr-TR");
        System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("tr-TR");
        System.Threading.Thread.CurrentThread.CurrentCulture = System.Globalization.CultureInfo.GetCultureInfo("tr-TR");
        System.Threading.Thread.CurrentThread.CurrentUICulture = System.Globalization.CultureInfo.GetCultureInfo("tr-TR");
    }
    
    void Application_End(object sender, EventArgs e) 
    {
        //  Code that runs on application shutdown

    }
        
    void Application_Error(object sender, EventArgs e) 
    { 
        // Code that runs when an unhandled error occurs

    }

    void Session_Start(object sender, EventArgs e) 
    {
      #region PasswordBox Connection String Değişikliği
        var DBSConnection_settings = ConfigurationManager.ConnectionStrings["DBSConnection"];
     
        var fi = typeof(ConfigurationElement).GetField("_bReadOnly", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
        fi.SetValue(DBSConnection_settings, false);


        if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"].ToString() == "True")
        {
            //Test Ortamındayız
            //DBSConnection_settings.ConnectionString = Digiturk.Workflow.Digiflow.DataAccessLayer.PasswordBoxConnectionString.WebConfigConnectionString("DBSTEST", "INQUIRY", "TEST");
            //DBSConnection_settings.ConnectionString = Digiturk.Workflow.Digiflow.DataAccessLayer.PasswordBoxConnectionString.WebConfigConnectionString("ITTPTEST", "INQUIRY", "TEST");
        }
        else
        {
            //Live Ortamdayız
            DBSConnection_settings.ConnectionString = Digiturk.Workflow.Digiflow.DataAccessLayer.PasswordBoxConnectionString.WebConfigConnectionString("DBSLIVE", "INQUIRY", "LIVE");
        }
  
        #endregion

    }

    void Session_End(object sender, EventArgs e) 
    {
        // Code that runs when a session ends. 
        // Note: The Session_End event is raised only when the sessionstate mode
        // is set to InProc in the Web.config file. If session mode is set to StateServer 
        // or SQLServer, the event is not raised.

    }
       
</script>
