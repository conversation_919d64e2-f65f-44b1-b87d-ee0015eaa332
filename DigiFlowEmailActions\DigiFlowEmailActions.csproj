﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{BDECDA3B-CCC1-4C4A-898A-FCBDFB3B945F}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>DigiFlowEmailActions</RootNamespace>
    <AssemblyName>DigiFlowEmailActions</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>C:\TFS\EmailActionsPublish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>4</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>FC1146D909C8DDDDF067D616F7BEC3E408B1410A</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>DigiFlowEmailActions_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Digiturk.Common.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Common.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.BaseServices.DAL">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.BaseServices.DAL.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Library">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.Library.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Repository">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.BaseServices">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.BaseServices.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.BaseServices.DAL">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.BaseServices.DAL.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.BaseServices.Interfaces">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.BaseServices.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Common">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Common.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.AssignmentBase">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.AssignmentBase.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.DigiFlow.Framework">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.DigiFlow.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.GenericMailHelper">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.GenericMailHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WebCore">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WebCore.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Engine">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Engine.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Repository">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Exchange.WebServices, Version=2.2.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Exchange.WebServices.Managed.Api.2.2.1.2\lib\net35\Microsoft.Exchange.WebServices.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate">
      <HintPath>\\dtl1iis3\Deployment\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate.ByteCode.LinFu">
      <HintPath>\\dtl1iis3\Deployment\NHibernate.ByteCode.LinFu.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=4.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\ODPNET\x86\Net2\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.DirectoryServices.AccountManagement" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Helpers\ActionProcessHelper.cs" />
    <Compile Include="Helpers\ExchangeHelper.cs" />
    <Compile Include="Helpers\MyWorkflowHelpers.cs" />
    <Compile Include="Helpers\WorkflowHelpers\AlternatifYoneticiAkisHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\BayiPersonelFotografRequestFlowHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\DelegasyonFormHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\IsMaasAvansHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\JobQuitFormFlowHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\MasrafFormuHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\PaymentRequestHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\PerformansHedefGirisHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\PerformansRevizyonHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\PurchaseForPaymentRequestHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\SodexoHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\SozlesmeOnayHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\UyeTicariBayiYetkilendirmeRequestFlowHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\UyeTicariFiyatIstisnaRequestFlowHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\UyeTicariGrupDegisiklikRequestFlowHelper.cs" />
    <Compile Include="Helpers\WorkflowHelpers\YillikIzinHelper.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="AppLive.config" />
    <None Include="DigiFlowEmailActions_TemporaryKey.pfx" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.5.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.5.2 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Notes.txt" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>