﻿using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Interfaces.Services.Workflow;
using DigiflowAPI.Application.Interfaces.DataAccess;
using Microsoft.AspNetCore.Http;
using DigiflowAPI.Application.Resources;
using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Domain.Entities.Workflow;
using DigiflowAPI.Application.DTOs;
using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Application.DTOs.Organization;
using System.Data;
using System.Text.Json;
using DigiflowAPI.Application.Interfaces.Configuration;
using System.Text.Json.Nodes;
using DigiflowAPI.Domain.Interfaces.Repositories;

namespace DigiflowAPI.Infrastructure.Services.Workflow
{
    public class ContractWorkflowService : BaseWorkflowService<ContractWorkflowResponseDto>
    {
        private int _hukukMuduruYonlendirme;
        private int _talepOlusturanDuzeltme;
        private int _finansGMYOnayi;
        private int _imzaliSozlesmeYuklemeAdimi;
        private readonly IOracleDataAccessRepositoryFactory _repositoryFactory;
        private readonly IWorkflowHelperService _workflowHelperService;
        private readonly IUserService _userService;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ISharePointConfiguration _sharePointConfiguration;
        private readonly IWebServicesConfiguration _webServicesConfiguration;

        public ContractWorkflowService(
            IOracleDataAccessRepositoryFactory repositoryFactory,
            IWorkflowHelperService workflowHelperService,
            IUserService userService,
            IHttpContextAccessor httpContextAccessor,
            IOrganizationRepository organizationRepository,
            ILogicalGroupService logicalGroupService,
            IHttpService httpService,
            ISharePointService sharePointService,
            IGlobalHelpers globalHelpers,
            ISharePointConfiguration sharePointConfiguration,
            IWebServicesConfiguration webServicesConfiguration) : base(
                repositoryFactory,
                httpService,
                httpContextAccessor,
                globalHelpers,
                userService)
        {
            _repositoryFactory = repositoryFactory;
            _workflowHelperService = workflowHelperService;
            _userService = userService;
            _httpContextAccessor = httpContextAccessor;
            _sharePointConfiguration = sharePointConfiguration;
            _webServicesConfiguration = webServicesConfiguration;
            _organizationRepository = organizationRepository;
            _repositoryFactory = repositoryFactory;
            _workflowHelperService = workflowHelperService;
            _userService = userService;
            _httpContextAccessor = httpContextAccessor;
            _organizationRepository = organizationRepository;
            _sharePointConfiguration.LoadSharePointPaths();
        }

        private async Task InitializeWorkflowData()
        {
            _hukukMuduruYonlendirme = await _workflowHelperService.GetStateDefId("ContratRequest_HukukMuduruYonlendirme");
            _talepOlusturanDuzeltme = await _workflowHelperService.GetStateDefId("ContratRequest_TalepOlusturanDuzeltme");
            _finansGMYOnayi = await _workflowHelperService.GetStateDefId("ContratRequest_FinansGMYOnayi");
            _imzaliSozlesmeYuklemeAdimi = await _workflowHelperService.GetStateDefId("Sozlesme_FinalAdimi");
        }

        public override async Task<WorkflowResponseDto<ContractWorkflowResponseDto>> NewWorkflowLoading(params object[] parameters)
        {
            await InitializeWorkflowData();
            var response = new WorkflowResponseDto<ContractWorkflowResponseDto>
            {
                Detail = new ContractWorkflowResponseDto()
            };

            try
            {
                long? wfInstanceId = parameters.Length > 0 && parameters[0] is long id ? id : null;
                var newWorkflowLoadingDto = new ContractNewWorkflowLoadingDto
                {
                    Firms = new List<SelectOptionDto>(),
                    ContractCategories = new List<SelectOptionDto>(),
                    WorkflowAdmins = new WorkflowAdmin(),
                    SharePointPath = string.Empty
                };
                string workflowAdmins = await _workflowHelperService.GetWorkflowAdmins("Sözleşme Onay Akışı");
                newWorkflowLoadingDto.WorkflowAdmins = new WorkflowAdmin
                {
                    Tr = "Workflow Admin Message (TR)", // TODO: Implement proper resource localization
                    En = "Workflow Admin Message (EN)" // TODO: Implement proper resource localization
                };
                string sharePointPath = _sharePointConfiguration.GetSharePointPath("SharePointList");

                newWorkflowLoadingDto.Firms = await GetFirmsData();
                newWorkflowLoadingDto.ContractCategories = await GetContractCategoriesData();
                newWorkflowLoadingDto.SharePointPath = sharePointPath;

                try
                {
                    var orgHierarchy = await _organizationRepository.GetOrganizationHierarchy(wfInstanceId);
                    newWorkflowLoadingDto.OrganizationHierarchy = orgHierarchy;
                }
                catch (Exception orgEx)
                {
                    newWorkflowLoadingDto.OrganizationHierarchy = new OrganizationSchemaParamsDto();
                }

                response.Detail.NewWorkflowLoadingDto = newWorkflowLoadingDto;
            }
            catch (Exception ex)
            {
                response.ErrorMessage = "An error occurred while loading the new workflow. Please try again later.";
            }

            return response;
        }

        public override async Task<WorkflowResponseDto<ContractWorkflowResponseDto>> EnableControls(params object[] parameters)
        {
            var response = new WorkflowResponseDto<ContractWorkflowResponseDto>
            {
                Detail = new ContractWorkflowResponseDto()
            };

            try
            {
                long? wfInstanceId = parameters.Length > 0 && parameters[0] is long id ? id : null;
                bool isCopyInstance = parameters.Length > 2 && parameters[2] is bool isCopy ? isCopy : false;
                var enableControlsDto = new ContractEnableControlsDto
                {
                    EnabilitySettings = new ContractEnabilityControlsDto(),
                    VisibilitySettings = new ContractVisiblityControlsDto()
                };

                if (isCopyInstance)
                {
                    SetAllEnabilityControls(enableControlsDto.EnabilitySettings, true);
                }
                else if (wfInstanceId.HasValue)
                {
                    var frameworkRepository = _repositoryFactory.Create("FrameworkConnection");
                    var wfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(wfInstanceId.Value);
                    if (wfIns?.WfCurrentState != null)
                    {
                        SetAllEnabilityControls(enableControlsDto.EnabilitySettings, false);
                        enableControlsDto.EnabilitySettings.CanPressUploadButton = true;
                        var currentState = await frameworkRepository.GetEntityAsync<FWfStateDef>(wfIns.WfCurrentState.WfStateDefId);

                        if (currentState.WfStateDefId == _talepOlusturanDuzeltme)
                        {
                            SetAllEnabilityControls(enableControlsDto.EnabilitySettings, true);
                        }
                    }
                    else if (wfIns?.WfWorkflowStatusTypeCd == "COMPLETED")
                    {
                        SetAllEnabilityControls(enableControlsDto.EnabilitySettings, false);
                    }
                }
                else
                {
                    SetAllEnabilityControls(enableControlsDto.EnabilitySettings, true);
                }

                response.Detail.EnableControlsDto = enableControlsDto;
            }
            catch (Exception ex)
            {
                response.ErrorMessage = "An error occurred while configuring controls. Please try again later.";
            }

            return response;
        }

        public override async Task<WorkflowResponseDto<ContractWorkflowResponseDto>> LoadDataBinding(params object[] parameters)
        {
            var response = new WorkflowResponseDto<ContractWorkflowResponseDto>
            {
                Detail = new ContractWorkflowResponseDto()
            };

            try
            {
                var frameworkRepository = _repositoryFactory.Create("FrameworkConnection");
                long? wfInstanceId = parameters.Length > 0 && parameters[0] is long id ? id : null;

                string workflowState = "";
                if (wfInstanceId.HasValue)
                {
                    workflowState = "ApprovalState";
                    var CurrentWfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(wfInstanceId);

                    if (CurrentWfIns?.WfCurrentState != null)
                    {
                        FWfStateDef CurrentStateDef = await frameworkRepository.GetEntityAsync<FWfStateDef>(CurrentWfIns.WfCurrentState.WfStateDefId)
                            ?? throw new InvalidOperationException($"Workflow state definition not found for StateDefId: {CurrentWfIns.WfCurrentState.WfStateDefId}");

                        if (CurrentStateDef.WfStateDefId == _imzaliSozlesmeYuklemeAdimi)
                            workflowState = "SozlesmeFinalAdim";
                        else if (CurrentStateDef.WfStateDefId == _hukukMuduruYonlendirme)
                            workflowState = "HukukMuduruYonlendirme";
                        else if (CurrentStateDef.WfStateDefId == _talepOlusturanDuzeltme)
                            workflowState = "TalepOlusturanDuzeltme";
                        else if (CurrentStateDef.WfStateDefId == _finansGMYOnayi)
                            workflowState = "FinansGMYOnayi";
                    }

                }

                string sharePointFinalPath = _sharePointConfiguration.GetSharePointPath("SharePointFinalList");

                var loadDataBindingDto = new ContractLoadDataBindingDto
                {
                    Firms = await GetFirmsData(),
                    ContractCategories = await GetContractCategoriesData(),
                    WorkflowState = workflowState,
                    SharePointFinalPath = sharePointFinalPath,
                };

                response.Detail.LoadDataBindingDto = loadDataBindingDto;
            }
            catch (Exception ex)
            {
                response.ErrorMessage = "An error occurred while loading data. Please try again later.";
            }

            return response;
        }

        public override async Task<WorkflowResponseDto<ContractWorkflowResponseDto>> LoadEntityToControls(params object[] parameters)
        {
            var response = new WorkflowResponseDto<ContractWorkflowResponseDto>
            {
                Detail = new ContractWorkflowResponseDto()
            };

            try
            {
                long? wfInstanceId = parameters.Length > 0 && parameters[0] is long id ? id : null;

                if (wfInstanceId.HasValue)
                {
                    var queryParams = new Dictionary<string, object>
                    {
                        { ":RequestId", wfInstanceId }
                    };

                    var repository = _repositoryFactory.Create("DT_WORKFLOW");
                    var entity = await repository.ExecuteSingleQueryAsync<ContractEntity>(
                        @"SELECT contract.*, vwUser.NAME_SURNAME as OWNER_LOGIN_NAME
                          FROM DT_WORKFLOW.WF_DF_CONTRACTSREQUEST contract
                          INNER JOIN FRAMEWORK.F_WF_WORKFLOW_INSTANCE wfIns ON contract.CONTRACTS_REQUEST_ID = wfIns.ENTITY_REF_ID
                          INNER JOIN DT_WORKFLOW.VW_USER_INFORMATION vwUser ON vwUser.LOGIN_ID = contract.CREATED_BY
                          WHERE wfIns.WF_WORKFLOW_INSTANCE_ID = :RequestId",
                        queryParams
                    );

                    if (entity == null)
                    {
                        return new WorkflowResponseDto<ContractWorkflowResponseDto>
                        {
                            ErrorMessage = "Workflow not found!"
                        };
                    }

                    var loadEntityToControlsDto = new ContractLoadEntityToControlsDto
                    {
                        Entity = entity
                    };

                    response.Detail.LoadEntityToControlsDto = loadEntityToControlsDto;
                }
            }
            catch (Exception ex)
            {
                response.ErrorMessage = "An error occurred while loading workflow details. Please try again later.";
            }

            return response;
        }

        public new async Task<WorkflowResponseDto<ContractWorkflowResponseDto>> CreateWorkflow(CreateWorkflowRequestDto request)
        {
            return await base.CreateWorkflow(request);
        }

        public new async Task<WorkflowResponseDto<ContractWorkflowResponseDto>> ApprovalWorkflow(ApprovalWorkflowRequestDto request)
        {
            var workflowRepository = _repositoryFactory.Create("DT_WORKFLOW");
            var frameworkRepository = _repositoryFactory.Create("FrameworkConnection");

            var wfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(request.InstanceId)
                ?? throw new InvalidOperationException($"Workflow instance not found for InstanceId: {request.InstanceId}");

            var contractEntity = await workflowRepository.GetEntityAsync<ContractEntity>(wfIns.EntityRefId)
                ?? throw new InvalidOperationException($"Contract record not found for EntityRefId: {wfIns.EntityRefId}");

            var taskIns = await frameworkRepository.GetEntityAsync<FWfActionTaskInstance>(wfIns.WfCurrentState.WfCurrentActionInstanceId)
                ?? throw new InvalidOperationException($"Action task instance not found for ActionInstanceId: {wfIns.WfCurrentState.WfCurrentActionInstanceId}");

            if (wfIns.WfCurrentState.WfStateDefId == _hukukMuduruYonlendirme)
            {
                // Logic for HukukMuduruYonlendirme state
            }
            else if (wfIns.WfCurrentState.WfStateDefId == _imzaliSozlesmeYuklemeAdimi)
            {
                if (string.IsNullOrEmpty(contractEntity.ContractSignedSoftFile))
                {
                    throw new InvalidOperationException("Signed document must be uploaded before approval.");
                }
            }

            var currentWfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(request.InstanceId);

            if (request.UpdateEntity != null)
            {
                JsonObject propertiesObject = request.UpdateEntity.Properties.Deserialize<JsonObject>();

                propertiesObject["LastComment"] = request.Comment;
                JsonElement updatedProperties = JsonSerializer.SerializeToElement(propertiesObject);
                request.UpdateEntity.Properties = updatedProperties;
                await _workflowHelperService.UpdateEntity(request.UpdateEntity, request.WorkflowName);
            }
            else
            {
                var jsonObject = JsonSerializer.SerializeToDocument(new
                {
                    LastComment = request.Comment
                });

                var updateEntityData = new UpdateEntityRequestDto
                {
                    Id = (long)currentWfIns.EntityRefId,
                    Properties = jsonObject.RootElement,
                };
                await _workflowHelperService.UpdateEntity(updateEntityData, request.WorkflowName);
            }

            request.ActionTaskInstanceId = taskIns.WfActionTaskInstanceId;
            return await base.ApprovalWorkflow(request);
        }

        private async Task<IEnumerable<OrgTreeSelectOptionDto>> GetFirmsData()
        {
            var dbsRepository = _repositoryFactory.Create("DBSConnection");
            var firms = await dbsRepository.ExecuteQueryAsync<SelectOptionDto>(
                "SELECT INQUIRY.DIGITURK_FIRMALAR.FIRMA_ID as Value, INQUIRY.DIGITURK_FIRMALAR.FIRMA as Label, INQUIRY.DIGITURK_FIRMALAR.FIRMA as LabelEn FROM INQUIRY.DIGITURK_FIRMALAR WHERE FIRMA_ID <> 'KRM' order by FIRMA",
                reader => new SelectOptionDto
                {
                    Value = reader["VALUE"].ToString(),
                    Label = reader["LABEL"].ToString(),
                    LabelEn = reader["LABELEN"].ToString(),
                });
            return ConvertToOrgTreeSelectOptions(firms);
        }

        private async Task<IEnumerable<OrgTreeSelectOptionDto>> GetContractCategoriesData()
        {
            var dbsRepository = _repositoryFactory.Create("DT_WORKFLOW");
            var categories = await dbsRepository.ExecuteQueryAsync<SelectOptionDto>(
                "SELECT ID as Value, NAME as Label, NAME as LabelEn from DT_WORKFLOW.DF_CONTRACT_CATEGORY ORDER BY NAME",
                reader => new SelectOptionDto
                {
                    Value = reader["VALUE"].ToString(),
                    Label = reader["LABEL"].ToString(),
                    LabelEn = reader["LABELEN"].ToString(),
                });
            return ConvertToOrgTreeSelectOptions(categories);
        }

        private async Task<List<long>> GetDepartmentHierarchy(long departmentId)
        {
            var hierarchy = new List<long>();
            long? currentDepartmentId = departmentId;

            while (currentDepartmentId.HasValue && currentDepartmentId.Value != 99)
            {
                hierarchy.Add(currentDepartmentId.Value);
                var department = await _userService.GetDepartmentById(currentDepartmentId.Value);
                currentDepartmentId = department?.UstBolumId;
            }

            hierarchy.Reverse();
            return hierarchy;
        }

        private async Task<List<SelectOptionDto>> FetchDepartments(long parentId)
        {
            var departments = await _organizationRepository.GetSubDepartments(parentId);
            return departments.Select(d => new SelectOptionDto
            {
                Value = d.Id.ToString(),
                Label = d.Bolum,
                LabelEn = d.DepsEn,
            }).ToList();
        }

        private async Task<List<OrgTreeSelectOptionDto>> GetSubTeamsRecursively(long parentId)
        {
            var subTeams = await _organizationRepository.GetSubDepartments(parentId);
            var result = new List<OrgTreeSelectOptionDto>();

            foreach (var subTeam in subTeams)
            {
                var subTeamOption = new OrgTreeSelectOptionDto
                {
                    Value = subTeam.Id.ToString(),
                    Label = subTeam.Bolum,
                    LabelEn = subTeam.DepsEn,
                };

                var subSubTeams = await GetSubTeamsRecursively(subTeam.Id);
                if (subSubTeams.Any())
                {
                    subTeamOption.SubTeams = subSubTeams;
                }

                result.Add(subTeamOption);
            }

            return result;
        }

        private List<OrgTreeSelectOptionDto> ConvertToOrgTreeSelectOptions(IEnumerable<SelectOptionDto> options)
        {
            return options.Select(o => new OrgTreeSelectOptionDto
            {
                Value = o.Value,
                Label = o.Label,
                LabelEn = o.LabelEn
            }).ToList();
        }

        private void SetAllEnabilityControls(ContractEnabilityControlsDto controls, bool value)
        {
            foreach (var prop in typeof(ContractEnabilityControlsDto).GetProperties())
            {
                prop.SetValue(controls, value);
            }
        }
    }
}