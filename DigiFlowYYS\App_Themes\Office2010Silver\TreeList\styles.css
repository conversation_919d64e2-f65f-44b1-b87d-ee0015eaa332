.dxtlControl_Office2010Silver
{
	cursor: default;
	font: 8pt Verdana;
	color: Black;
	border: 1px solid #868b91;
}
.dxtlControl_Office2010Silver caption
{
	font-size: 8pt;
	font-weight: normal;
	color: Black;
	text-align: center;
	padding: 3px 3px 5px;
	text-align: center;
	background: #e8ebee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.TreeList.CssImages.TitleBack.png")%>') repeat-x left top;
	border: 1px solid #868b91;
	border-bottom: 0;
}

/* Indent cells */
.dxtlIndent_Office2010Silver,
.dxtlIndentWithButton_Office2010Silver
{
	background: white;
	vertical-align: top;
	background-position: center top;
	background-repeat: no-repeat;
}
.dxtlIndent_Office2010Silver
{
	padding: 0 11px;
}
.dxtlIndentWithButton_Office2010Silver
{
	padding: 4px 5px;
}
.dxtlSelectionCell_Office2010Silver
{
	padding: 0 2px;
	border-width: 0;
}

/* Tree-lines cells */
.dxtlLineRoot_Office2010Silver,
.dxtlLineFirst_Office2010Silver,
.dxtlLineMiddle_Office2010Silver,
.dxtlLineLast_Office2010Silver,
.dxtlLineFirstRtl_Office2010Silver,
.dxtlLineMiddleRtl_Office2010Silver,
.dxtlLineLastRtl_Office2010Silver
{
    background-color: Transparent;
}

.dxtlIndent_Office2010Silver,
.dxtlIndentWithButton_Office2010Silver
{
    background-color: White;
}

/* Headers */
.dxtlHeader_Office2010Silver
{
	background: #e7eaee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.TreeList.CssImages.HeaderBack.png")%>') left top repeat-x;
	border: 1px solid #868b91;
	padding: 5px 6px;
	font-weight: normal;
	color: #3b3b3b;
}
.dxtlHeader_Office2010Silver table.dxtl
{
	border-collapse: collapse;
	width: 100%;
}
.dxtlHeader_Office2010Silver td.dxtl
{
	padding: 0;
}
.dxtlHeader_Office2010Silver,
.dxtlHeader_Office2010Silver td.dxtl
{
	font: 8pt Verdana;
	white-space: nowrap;
	text-align: left;
}

/* Nodes */
.dxtlNode_Office2010Silver
{
	background: white;
}
.dxtlAltNode_Office2010Silver
{
	background: #f7f7f8;
}
.dxtlAltNode_Office2010Silver .dxtlIndent_Office2010Silver,
.dxtlSelectedNode_Office2010Silver .dxtlIndent_Office2010Silver,
.dxtlFocusedNode_Office2010Silver .dxtlIndent_Office2010Silver
{
    background: White;
}
.dxtlSelectedNode_Office2010Silver
{
	background: #faedb6;
	color: Black;
}
.dxtlFocusedNode_Office2010Silver
{
	background: #fdf7d9;
    color: Black;
}
.dxtlInlineEditNode_Office2010Silver
{
	background: white;
}
.dxtlEditFormDisplayNode_Office2010Silver
{
	background: #f9fafb;
}

.dxtlNode_Office2010Silver td.dxtl,
.dxtlAltNode_Office2010Silver  td.dxtl,
.dxtlSelectedNode_Office2010Silver td.dxtl,
.dxtlFocusedNode_Office2010Silver td.dxtl,
.dxtlEditFormDisplayNode_Office2010Silver td.dxtl,
.dxtlCommandCell_Office2010Silver
{
	padding: 4px 6px;
	border-width: 0;
	white-space: nowrap;
	font-size: 8pt;
	font-family: Verdana;
}
.dxtlEditFormDisplayNode_Office2010Silver td.dxtl,
.dxtlEditFormDisplayNode_Office2010Silver td.dxtl__cc
{
    border-bottom: 1px solid #dbdee1;
}
.dxtlInlineEditNode_Office2010Silver td.dxtl
{
	border-width: 0;
	padding: 1px;
}

/* Preview */
.dxtlPreview_Office2010Silver
{
	background: #efeff0;
	color: #777777;
	padding: 14px;
	border-width: 0;
	font: 8pt Verdana;
}

/* Footers */
.dxtlGroupFooter_Office2010Silver,
.dxtlFooter_Office2010Silver
{
	background-color: #f6f7f9;
}
.dxtlGroupFooter_Office2010Silver td.dxtl,
.dxtlFooter_Office2010Silver td.dxtl
{
	padding: 6px;
	white-space: nowrap;
	border: 1px solid #dbdee1;
	border-left-width: 0 !important;
	font: 8pt Verdana;
}

/* Pagers */
.dxtlPagerTopPanel_Office2010Silver,
.dxtlPagerBottomPanel_Office2010Silver
{
    background: #e7eaee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.TreeList.CssImages.PagerPanelBack.png")%>') repeat-x left top;
}
.dxtlPagerTopPanel_Office2010Silver
{
	border-bottom: 1px solid #b0b3b7;
	padding-bottom: 2px;
}
.dxtlPagerBottomPanel_Office2010Silver
{
	border-top: 1px solid #b0b3b7;
	padding-bottom: 2px;
}

/* Editing */
.dxtlEditForm_Office2010Silver
{
	background: #f9fafb;
	border-width: 0;
	padding: 8px 10px 10px;
}
.dxtlEditFormCaption_Office2010Silver,
.dxtlEditFormEditCell_Office2010Silver
{
	padding: 4px;
}
.dxtlEditFormCaption_Office2010Silver
{
	padding-left: 10px;
	white-space: nowrap;
}
.dxtlError_Office2010Silver
{
	background: #e9edf1;
	color: #ba1717;
	padding: 6px 10px;
	border-width: 0;
	font: 8pt Verdana;
}
.dxtlPopupEditForm_Office2010Silver
{
    padding: 12px;
}

/* Links */
.dxtlControl_Office2010Silver a
{
	color: #5a9ddb;
	text-decoration: none;
}
.dxtlControl_Office2010Silver a:hover
{
	text-decoration: underline;
}

.dxtlSelectedNode_Office2010Silver a,
.dxtlFocusedNode_Office2010Silver a
{
	color: #5a9ddb;
	text-decoration: none;
}
.dxtlSelectedNode_Office2010Silver a:hover,
.dxtlFocusedNode_Office2010Silver a:hover
{
	text-decoration: underline;
}
.dxtlCommandCell_Office2010Silver a
{
	margin-right: 3px;
}

/* Loading panel */
.dxtlLoadingPanel_Office2010Silver
{
	font: 8pt Verdana;
    color: #585e68;
    background: White;
	border: 1px solid #80858d;
}
.dxtlLoadingPanel_Office2010Silver td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxtlLoadingDiv_Office2010Silver
{
	background: white;
	opacity: 0.01;
	filter: alpha(opacity=1);
}

/* Disabled */
.dxtlDisabled_Office2010Silver,
.dxtlDisabled_Office2010Silver .dxtl
{
	color: #989898;
	cursor: default;
}