﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class UyeTicariGrupDegisiklikRequestFlowHelper
    {
        internal static void SetProsedur(string durum,Digiturk.Workflow.Common.WFContext CurrentWFContext, Digiturk.Workflow.Entities.FWfWorkflowInstance CurrentWfIns, FWfActionTaskInstance taskInstance, FLogin LoginObject, string Commend, ref string errorUserMsg_Tr, ref string errorUserMsg_En)
        {
            TicariGrupDegisiklikRequest ReqObj = WFRepository<TicariGrupDegisiklikRequest>.GetEntity(CurrentWfIns.EntityRefId);
            string prosedurDurum = UyeTicariFiyatIstisnaRequestHelper.SetProsedur(durum, ReqObj.IRIS_ID.ToString(), CurrentWfIns.WfWorkflowInstanceId.ToString(), CurrentWfIns.EntityRefId.ToString(), ReqObj.UYE_NO.ToString(), taskInstance, LoginObject.DomainUserName, LoginObject.Email, Commend, CurrentWFContext, ReqObj.KAYNAK);
            if (prosedurDurum != "")
            {
                errorUserMsg_Tr = prosedurDurum + "Hata düzeltildikten sonra " + (durum == "Yes" ? "onay" : "red") + " işlemi yapılabilir, İlgili kişiler ile iletişime geçiniz.";
                errorUserMsg_En = prosedurDurum + (durum == "Yes" ? "Approval" : "Rejection") + " process can be completed when the error is corrected.Please contact related staff.";
                throw new Exception();
            }
        }
    }
}
