﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class MasrafFormuHelper
    {
        internal static void CancelEndFlow(FWfWorkflowInstance CurrentWfIns)
        {
            ExpenseFormRequest RequestObject = WFRepository<Digiturk.Workflow.Digiflow.Entities.ExpenseFormRequest>.GetEntity(CurrentWfIns.EntityRefId);
            string LoginName = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(RequestObject.LoginId);
            Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.ExpenseFormRequestHelper.NDECancel(RequestObject.CurrentAccountNo, LoginName, RequestObject.FirmExpense);
        }
    }
}
