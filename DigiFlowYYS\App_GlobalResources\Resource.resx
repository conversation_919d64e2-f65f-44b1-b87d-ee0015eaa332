﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="izin_admin" xml:space="preserve">
    <value>Yıllık İzin Talep Formu Akış Yöneticileri (CİHAN BAYRAKTAR,NUR BAŞEĞMEZER,ŞENİZ ÖZYAPAR)</value>
  </data>
  <data name="izin_bakiye" xml:space="preserve">
    <value>İzin Bakiye Süresi (Gün)</value>
  </data>
  <data name="izin_basla" xml:space="preserve">
    <value>İzin Başlangıç Tarihi  </value>
  </data>
  <data name="izin_bitis" xml:space="preserve">
    <value>İzin Bitiş Tarihi  </value>
  </data>
  <data name="izin_delegasyon" xml:space="preserve">
    <value>Tüm Onaylarla İzinden Dönünce Bizzat ilgilenmek istemekteyim.İznim Sürecince onayımı gerektirecek herhangi bir akış delege etmek istemiyorum.</value>
  </data>
  <data name="izin_delege" xml:space="preserve">
    <value>İzin Süresince Onayınıza gidecek hangi akışları kimlere delege etmek istersiniz?</value>
  </data>
  <data name="izin_delege_form" xml:space="preserve">
    <value>Lütfen Tıklayınız</value>
  </data>
  <data name="izin_delege_uyari" xml:space="preserve">
    <value>Seçim yapmamanız durumunda kurumsal süreçlerde aksaklık yaşanacaktır.</value>
  </data>
  <data name="izin_gun" xml:space="preserve">
    <value>Gün</value>
  </data>
  <data name="izin_nedeni" xml:space="preserve">
    <value>İzin Nedeni</value>
  </data>
  <data name="izin_sure" xml:space="preserve">
    <value>İzin Süresi ( Gün ) </value>
  </data>
  <data name="izin_turu" xml:space="preserve">
    <value>İzin Türü</value>
  </data>
  <data name="izin_yarim_gun" xml:space="preserve">
    <value>Yarım Gün</value>
  </data>
  <data name="Main_aciklama" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="Main_Aciklama_Giriniz" xml:space="preserve">
    <value>Lütfen bir açıklama giriniz</value>
  </data>
  <data name="Main_Askıya_al" xml:space="preserve">
    <value>Askıya Al</value>
  </data>
  <data name="Main_Geri_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Talebi geri göndermek istediğinize emin misiniz ?','vG30')</value>
  </data>
  <data name="Main_History" xml:space="preserve">
    <value>İşlem Geçmişi</value>
    <comment>Ana Sayfada menülerdeki İşlem Geçmişi</comment>
  </data>
  <data name="Main_Inbox" xml:space="preserve">
    <value>Üzerimdeki İşlemler</value>
    <comment>Ana Sayfada Menülerdeki Üzerimdeki İşlemler</comment>
  </data>
  <data name="Main_Iptal" xml:space="preserve">
    <value>İptal Et</value>
  </data>
  <data name="Main_Onayla" xml:space="preserve">
    <value>Onayla</value>
  </data>
  <data name="Main_Onay_Red" xml:space="preserve">
    <value>Onay / Red</value>
  </data>
  <data name="Main_Onay_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('İş akışını onaylamak istediğinizden emin misiniz?','vG30')</value>
  </data>
  <data name="Main_Reddet" xml:space="preserve">
    <value>Reddet</value>
  </data>
  <data name="Main_Red_Onay" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('İş akışını reddetmek istediğinizden emin misiniz?','vG31')</value>
  </data>
  <data name="Main_Seciniz" xml:space="preserve">
    <value>Seçiniz</value>
  </data>
  <data name="Main_Suspended" xml:space="preserve">
    <value>Askıda Bekleyenler</value>
    <comment>Ana Sayfada Menülerdeki Askıda Bekleyenler</comment>
  </data>
  <data name="Main_Talep_Olustur" xml:space="preserve">
    <value>Talep Oluştur</value>
  </data>
  <data name="Main_Talep_Olustur_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('İş akışını başlatmak istediğinize istediğinize emin misiniz ?','vG0')</value>
  </data>
  <data name="Main_Yonlendir" xml:space="preserve">
    <value>Yönlendir</value>
  </data>
  <data name="Main_yonlendir_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Akışı yönlendirmek istediğinize emin misiniz ?','vG1')</value>
  </data>
  <data name="Main_yonlendir_soru_mobile" xml:space="preserve">
    <value>Akışı yönlendirmek istediğinize emin misiniz ?</value>
  </data>
  <data name="Main_Yoruma_Gonder" xml:space="preserve">
    <value>Yoruma Gönder</value>
  </data>
  <data name="Main_Yorum_Ekle" xml:space="preserve">
    <value>Yorum Ekle</value>
  </data>
  <data name="Uc_Birim" xml:space="preserve">
    <value>Birim</value>
  </data>
  <data name="Uc_Bolum" xml:space="preserve">
    <value>Bölüm</value>
  </data>
  <data name="Uc_Departman" xml:space="preserve">
    <value>Departman</value>
    <comment>User Control Departman</comment>
  </data>
  <data name="Uc_Kullanici" xml:space="preserve">
    <value>Kullanıcı</value>
  </data>
  <data name="Uc_KullaniciHata" xml:space="preserve">
    <value>Kullanıcı alanı boş bırakılamaz.</value>
  </data>
  <data name="Uc_Takim" xml:space="preserve">
    <value>Takım</value>
  </data>
  <data name="Alt_Birim1" xml:space="preserve">
    <value>Alt Birim 1</value>
  </data>
  <data name="Alt_Birim2" xml:space="preserve">
    <value>Alt Birim 2</value>
  </data>
  <data name="Alt_Birim3" xml:space="preserve">
    <value>Alt Birim 3</value>
  </data>
  <data name="Alt_Birim4" xml:space="preserve">
    <value>Alt Birim 4</value>
  </data>
  <data name="Alt_Birim5" xml:space="preserve">
    <value>Alt Birim 5</value>
  </data>
  <data name="Uc_Yonetici" xml:space="preserve">
    <value>Yönetici</value>
  </data>
  <data name="Main_aciklama_duzeltme" xml:space="preserve">
    <value>Açıklama Düzeltme</value>
  </data>
  <data name="Main_aciklama_oneri" xml:space="preserve">
    <value>Açıklama&lt;br&gt;Öneri</value>
  </data>
  <data name="Main_askiya_al_aciklama" xml:space="preserve">
    <value>Tarihine kadar askıya al</value>
  </data>
  <data name="Main_Askıya_al_devam" xml:space="preserve">
    <value>Beklet / Devam Et</value>
  </data>
  <data name="Main_Delegasyon_Iptal" xml:space="preserve">
    <value>Delegasyon İptali</value>
  </data>
  <data name="Main_Devam_Et" xml:space="preserve">
    <value>Devam Ettir</value>
  </data>
  <data name="Main_Geri_al" xml:space="preserve">
    <value>Geri Al</value>
  </data>
  <data name="Main_Geri_al_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('İş akışını geri almak istediğinize emin misiniz ? ','vG6')</value>
  </data>
  <data name="Main_Görüntüleme_Iptal" xml:space="preserve">
    <value>Görüntüleme İptali</value>
  </data>
  <data name="Main_iptal_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('İş akışını iptal etmek istediğinizden emin misiniz ?','vG4')</value>
  </data>
  <data name="Main_islem" xml:space="preserve">
    <value>İşlem</value>
  </data>
  <data name="Main_islem_listesi" xml:space="preserve">
    <value>İşlem Listesi</value>
  </data>
  <data name="Main_Lutfen_Bekleyiniz" xml:space="preserve">
    <value>Lütfen bekleyiniz</value>
  </data>
  <data name="Main_state" xml:space="preserve">
    <value>Adım Adı</value>
  </data>
  <data name="Main_tarih" xml:space="preserve">
    <value>Tarih</value>
  </data>
  <data name="Main_user" xml:space="preserve">
    <value>Kullanıcı</value>
  </data>
  <data name="Main_WF_Admin" xml:space="preserve">
    <value>İş akış Yönetimi</value>
  </data>
  <data name="main_yoruma_gonder_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Talebi yoruma göndermek istediğinize emin misiniz ?','vG2')</value>
  </data>
  <data name="main_yorum_duzeltme" xml:space="preserve">
    <value>Yorum Düzeltme</value>
  </data>
  <data name="Main_yorum_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('İş akışına yorum girmek istediğinize emin misiniz ? ','vG5')</value>
  </data>
  <data name="Main_devam_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('İş akışını devam ettirmek istediğinizden emin misiniz?','vG1')</value>
  </data>
  <data name="Main_Suspend_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('İş akışını askıya almak istediğinize emin misiniz ?','vG3')</value>
  </data>
  <data name="izin_prosedur" xml:space="preserve">
    <value>Yıllık İzin Prosedürü</value>
  </data>
  <data name="Mobil_Talep_Sahibi" xml:space="preserve">
    <value>Talep Sahibi</value>
  </data>
  <data name="izin_basla_uyari" xml:space="preserve">
    <value>Lütfen Başlangıç Tarihini Giriniz</value>
  </data>
  <data name="izin_bitis_uyari" xml:space="preserve">
    <value>Lütfen Bitiş Tarihini Giriniz</value>
  </data>
  <data name="Main_Gorev_Personeli" xml:space="preserve">
    <value>Görevlendirilecek Personel</value>
  </data>
  <data name="Main_tarih_format" xml:space="preserve">
    <value>Tarih formatı gg.aa.yyyy olmalı</value>
  </data>
  <data name="Main_tarih_format_aralik" xml:space="preserve">
    <value>Girilen tarih bugünden büyük olmalıdır.</value>
  </data>
  <data name="Main_Activity_Type" xml:space="preserve">
    <value>Aktivite Tipi</value>
  </data>
  <data name="izin_delegasyon_uyari" xml:space="preserve">
    <value>Bu izin talebini yapabilmek için belirlediğiniz tarihler arasında bir delegasyonunuz bulunmalıdır. Tüm onaylarla izin dönüşü bizzat ilgilenmek istiyorsanız aşağıdaki kırmızı kutucuğu işaretleyiniz.</value>
  </data>
  <data name="main_grid_all" xml:space="preserve">
    <value>Tümü</value>
  </data>
  <data name="main_grid_back" xml:space="preserve">
    <value>Geri</value>
  </data>
  <data name="main_grid_first" xml:space="preserve">
    <value>İlk</value>
  </data>
  <data name="main_grid_flow_aciklama" xml:space="preserve">
    <value>Açıklama</value>
  </data>
  <data name="main_grid_flow_birim" xml:space="preserve">
    <value>Birim</value>
  </data>
  <data name="main_grid_flow_forwarder" xml:space="preserve">
    <value>Gönderen</value>
  </data>
  <data name="main_grid_flow_name" xml:space="preserve">
    <value>İş Akışı Adı</value>
  </data>
  <data name="main_arama_sozcugu" xml:space="preserve">
    <value>Aranacak kelime...</value>
  </data>
  <data name="main_filtre" xml:space="preserve">
    <value>Filtre</value>
  </data>
  <data name="main_grid_flow_owner" xml:space="preserve">
    <value>Başlatan</value>
  </data>
  <data name="main_grid_flow_state" xml:space="preserve">
    <value>Adım</value>
  </data>
  <data name="main_grid_flow_tarih" xml:space="preserve">
    <value>Tarih</value>
  </data>
  <data name="main_grid_flow_tutar" xml:space="preserve">
    <value>Tutar</value>
  </data>
  <data name="main_grid_group" xml:space="preserve">
    <value>Gruplamak istediğiniz alanları buraya sürükleyin</value>
  </data>
  <data name="main_grid_last" xml:space="preserve">
    <value>Son</value>
  </data>
  <data name="main_grid_next" xml:space="preserve">
    <value>İleri</value>
  </data>
  <data name="main_akislari_onayla" xml:space="preserve">
    <value>Seçili Akışları Onayla</value>
  </data>
  <data name="main_toplu_onay_bilgi" xml:space="preserve">
    <value>***Toplu Onay Fonksiyonu belirli akışlar için geçerlidir.</value>
  </data>
  <data name="main_comment_inbox" xml:space="preserve">
    <value>Bana yoruma gönderilmiş iş akışları</value>
  </data>
  <data name="main_delege_inbox" xml:space="preserve">
    <value>Bana delege edilmiş iş akışları</value>
  </data>
  <data name="main_grid_clear" xml:space="preserve">
    <value>Temizle</value>
  </data>
  <data name="main_grid_devami" xml:space="preserve">
    <value>Devamı Sonraki Sayfada</value>
  </data>
  <data name="main_grid_no_row" xml:space="preserve">
    <value>Herhangi bir kayıt bulunmamaktadır</value>
  </data>
  <data name="main_inbox_header" xml:space="preserve">
    <value>Bana atanmış iş akışları</value>
  </data>
  <data name="main_grid_last_action" xml:space="preserve">
    <value>Son Yapılan İşlem</value>
  </data>
  <data name="main_grid_last_modified" xml:space="preserve">
    <value>Son İşlem Yapan</value>
  </data>
  <data name="main_grid_atanan" xml:space="preserve">
    <value>Atanan</value>
  </data>
  <data name="main_grid_atlat" xml:space="preserve">
    <value>Akış Atlatma Talebi Yap</value>
  </data>
  <data name="main_grid_flow_action" xml:space="preserve">
    <value>İşlem</value>
  </data>
  <data name="main_grid_hatirlat" xml:space="preserve">
    <value>Hatırlat</value>
  </data>
  <data name="main_grid_kopyala" xml:space="preserve">
    <value>Akış Kopyala</value>
  </data>
  <data name="main_akis_bitir" xml:space="preserve">
    <value>Akışı Bitir</value>
  </data>
  <data name="main_akis_yoneticisi_bildirim" xml:space="preserve">
    <value>Şu anda &lt;b&gt;akış yöneticisi&lt;/b&gt; olarak işlem yapmaktasınız.</value>
  </data>
  <data name="main_askiya_alma_tarihiri_gir" xml:space="preserve">
    <value>Lütfen Askıya alma tarihini giriniz.</value>
  </data>
  <data name="main_askiya_alma_uyari" xml:space="preserve">
    <value>Bugüne ait askıya alma tarihi girilemez.</value>
  </data>
  <data name="main_comment_bilgi" xml:space="preserve">
    <value>Talebi yoruma gönderilmiştir.</value>
  </data>
  <data name="main_emir_var_uyari" xml:space="preserve">
    <value>Bu akış için sistemde bekleyen emir mevcuttur.İşlem yapmak için bir süre sonra lütfen tekrar deneyiniz.</value>
  </data>
  <data name="main_emir_var_uyari1" xml:space="preserve">
    <value>Bu akış için sistemde bekleyen emir mevcuttur.Lütfen ekranı yenileyerek (F5) tekrar deneyiniz.</value>
  </data>
  <data name="main_finalize_bilgi" xml:space="preserve">
    <value>Talebi sonlandırılmıştır</value>
  </data>
  <data name="main_forward_bilgi" xml:space="preserve">
    <value>Talebi yönlendirilmiştir.</value>
  </data>
  <data name="main_gecerli_tarih_formati" xml:space="preserve">
    <value>Lütfen geçerli bir tarih formatı (gg.aa.yyyy) giriniz.</value>
  </data>
  <data name="main_gecmis_askiya_alma" xml:space="preserve">
    <value>Geçmiş tarihe askıya alma işlemi yapılamaz.</value>
  </data>
  <data name="main_geri_alindi_bilgi" xml:space="preserve">
    <value>İşlem Geri Alındı</value>
  </data>
  <data name="main_IE_uyari" xml:space="preserve">
    <value>Lütfen Internet Explorer tarayıcısı ile işlemlerinizi yapınız !</value>
  </data>
  <data name="main_iptal_bilgi" xml:space="preserve">
    <value>talebi başarıyla iptal edilmiştir.</value>
  </data>
  <data name="main_iptal_bilgi1" xml:space="preserve">
    <value>Talebi iptal edilmiştir.</value>
  </data>
  <data name="main_islem_hata_bilgi" xml:space="preserve">
    <value>İşlem sırasında bir hata ile karşılaşıldı. Lütfen yeniden deneyin.</value>
  </data>
  <data name="main_lutfen_yorum_gririniz" xml:space="preserve">
    <value>Hata - Lütfen yorum giriniz.</value>
  </data>
  <data name="main_reddet_bilgi" xml:space="preserve">
    <value>Talep reddedilmiştir</value>
  </data>
  <data name="main_red_karakter_siniri" xml:space="preserve">
    <value>Reddetmek için açıklama alanına en fazla 200 karakter girilebilir.</value>
  </data>
  <data name="main_red_soru" xml:space="preserve">
    <value>İş akışını reddetmek istediğinizden emin misiniz?</value>
  </data>
  <data name="main_resume_bilgi" xml:space="preserve">
    <value>İşlem başarıyla devam ettirildi.</value>
  </data>
  <data name="main_resume_bilgi1" xml:space="preserve">
    <value>Talebi devam ettirilmiştir</value>
  </data>
  <data name="main_sendback_bilgi" xml:space="preserve">
    <value>Talebine geri alınmıştır.</value>
  </data>
  <data name="main_sendowner_bilgi" xml:space="preserve">
    <value>Talebi talep eden kişiye geri gönderilmiştir.</value>
  </data>
  <data name="main_state_uyusmuyor_uyari" xml:space="preserve">
    <value>Bu akış, artık ekranı açtığınız adımda değil.Lütfen ekranı yenileyerek (F5) tekrar deneyiniz.</value>
  </data>
  <data name="main_suspend_bilgi" xml:space="preserve">
    <value>Talebi beklemeye alınmıştır.</value>
  </data>
  <data name="main_talepolustu_bilgi" xml:space="preserve">
    <value>Talebiniz başarıyla oluşturuldu</value>
  </data>
  <data name="main_Tarihformat_bilgi" xml:space="preserve">
    <value>Tarih formatı gg.aa.yyyy olmalı</value>
  </data>
  <data name="main_toplam_saniye_bilgi" xml:space="preserve">
    <value>Bu işlem Toplam  xxx  sn sürmüştür.</value>
  </data>
  <data name="main_user_seciniz" xml:space="preserve">
    <value>Hata - Lütfen yönlendirilecek kullanıcıyı seçiniz.</value>
  </data>
  <data name="main_yazdir_kapat" xml:space="preserve">
    <value>Yazdır ve Kapat</value>
  </data>
  <data name="main_yorumekle_bilgi" xml:space="preserve">
    <value>Talebine yorum  eklenmiştir.</value>
  </data>
  <data name="main_yorum_eklendi_bilgi" xml:space="preserve">
    <value>Yorumunuz başarıyla kaydedildi.</value>
  </data>
  <data name="main_bekle_bilgi" xml:space="preserve">
    <value>İşlem başarıyla beklemeye alındı.</value>
  </data>
  <data name="main_bilgi" xml:space="preserve">
    <value>Bilgi</value>
  </data>
  <data name="main_comment_islem" xml:space="preserve">
    <value>İşlem Yoruma Gönderildi.</value>
  </data>
  <data name="main_hata" xml:space="preserve">
    <value>Hata</value>
  </data>
  <data name="inbox_akislar_onaylandi" xml:space="preserve">
    <value>Seçilen akışlar onaylandı.</value>
  </data>
  <data name="inbox_akis_yok" xml:space="preserve">
    <value>Seçili akış bulunamadı</value>
  </data>
  <data name="inbox_hata_olustu" xml:space="preserve">
    <value>Inbox oluşturulurken bir hata oluştu.</value>
  </data>
  <data name="topluonaysoru" xml:space="preserve">
    <value>javascript:return confirm('Toplu onaylamak istediğinize emin misiniz ?');</value>
  </data>
  <data name="izin_kayit_bulunamadi" xml:space="preserve">
    <value>İzin kayıt bilgileriniz bulunamadı, lütfen sicil numaranız ve izin bilgilerinizin uyuşması için Insan Kaynakları ile iletişime geçiniz.</value>
  </data>
  <data name="main_akis_yoneticisi" xml:space="preserve">
    <value>Akış Yöneticisi</value>
  </data>
  <data name="main_guncelle_don" xml:space="preserve">
    <value>Güncelle Sayfasına Geri Dön</value>
  </data>
  <data name="main_kapat" xml:space="preserve">
    <value>Kapat</value>
  </data>
  <data name="main_basla_tarih" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="main_bitis_tarih" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="main_combo_aktif_delegasyonlar" xml:space="preserve">
    <value>Aktif Delegasyonlar</value>
  </data>
  <data name="main_combo_devam_eden" xml:space="preserve">
    <value>Devam Eden İş Akışları</value>
  </data>
  <data name="main_combo_durdurulan" xml:space="preserve">
    <value>Durdurulan İş Akışları</value>
  </data>
  <data name="main_combo_gecmis_delegasyonlar" xml:space="preserve">
    <value>Geçmiş Delegasyonlar</value>
  </data>
  <data name="main_combo_iptal" xml:space="preserve">
    <value>İptal Edilen İş Akışları</value>
  </data>
  <data name="main_combo_onaylanan" xml:space="preserve">
    <value>Onaylanan İş Akışları</value>
  </data>
  <data name="main_combo_reddedilen" xml:space="preserve">
    <value>Reddedilen İş Akışları</value>
  </data>
  <data name="main_combo_tamamlanan" xml:space="preserve">
    <value>Tamamlanan İş Akışları</value>
  </data>
  <data name="main_combo_tum" xml:space="preserve">
    <value>Tüm İş Akışları</value>
  </data>
  <data name="main_target" xml:space="preserve">
    <value>Hedef Kullanıcı</value>
  </data>
  <data name="Main_edit" xml:space="preserve">
    <value>Düzenle</value>
  </data>
  <data name="Main_goruntule" xml:space="preserve">
    <value>Görüntüle</value>
  </data>
  <data name="main_goruntuleme_turu" xml:space="preserve">
    <value>Görüntüleme Türü</value>
  </data>
  <data name="main_grid_flow_no" xml:space="preserve">
    <value>Workflow No</value>
  </data>
  <data name="Main_sonlandir_delegasyon" xml:space="preserve">
    <value>Sonlandır</value>
  </data>
  <data name="main_4000_sinir" xml:space="preserve">
    <value>Açıklama alanına maximum 4000 karakter girebilirsiniz</value>
  </data>
  <data name="main_delege_uyari" xml:space="preserve">
    <value>Yetkilerinizi delege ettiğiniz için bu akış üzerinde işlem yapamazsınız</value>
  </data>
  <data name="HistoryState_1FinansOnayıYurtDışı" xml:space="preserve">
    <value>1. Finans Onayı Yurt Dışı</value>
  </data>
  <data name="HistoryState_1FinansOnayıYurtiçi" xml:space="preserve">
    <value>1. Finans Onayı Yurtiçi</value>
  </data>
  <data name="HistoryState_1KontratYönetimiOnayı" xml:space="preserve">
    <value>1. Kontrat Yönetimi Onayı</value>
  </data>
  <data name="HistoryState_2FinansOnayıYurtDışı" xml:space="preserve">
    <value>2. Finans Onayı Yurt Dışı</value>
  </data>
  <data name="HistoryState_2FinansOnayıYurtİçi" xml:space="preserve">
    <value>2. Finans Onayı Yurt İçi</value>
  </data>
  <data name="HistoryState_2GirişAdımı" xml:space="preserve">
    <value>2.Giriş Adımı</value>
  </data>
  <data name="HistoryState_2KontratYönetimiOnayı" xml:space="preserve">
    <value>2. Kontrat Yönetimi Onayı</value>
  </data>
  <data name="HistoryState_2ÜstYöneticisiOnayı" xml:space="preserve">
    <value>2. Üst Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_3KontratYönetimiOnayı" xml:space="preserve">
    <value>3. Kontrat Yönetimi Onayı</value>
  </data>
  <data name="HistoryState_AccOluşturma" xml:space="preserve">
    <value>Acc Oluşturma</value>
  </data>
  <data name="HistoryState_AkışAtlatmaTalebi" xml:space="preserve">
    <value>Akış Atlatma Talebi</value>
  </data>
  <data name="HistoryState_AnalizAşaması" xml:space="preserve">
    <value>Analiz Aşaması</value>
  </data>
  <data name="HistoryState_AraDepartmanOnayı" xml:space="preserve">
    <value>Ara Departman Onayı</value>
  </data>
  <data name="HistoryState_AraDepartmanYöneticiOnayı" xml:space="preserve">
    <value>Ara Departman Yönetici Onayı</value>
  </data>
  <data name="HistoryState_AraDepartmanYöneticisi" xml:space="preserve">
    <value>Ara Departman Yöneticisi</value>
  </data>
  <data name="HistoryState_AraDepartmanYöneticisiOnayı" xml:space="preserve">
    <value>Ara Departman Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_AraOnay" xml:space="preserve">
    <value>Ara Onay</value>
  </data>
  <data name="HistoryState_AraOnay1" xml:space="preserve">
    <value>Ara Onay 1</value>
  </data>
  <data name="HistoryState_AraOnay2" xml:space="preserve">
    <value>Ara Onay 2</value>
  </data>
  <data name="HistoryState_AraState" xml:space="preserve">
    <value>Ara State</value>
  </data>
  <data name="HistoryState_AraçAdımı" xml:space="preserve">
    <value>Araç Adımı</value>
  </data>
  <data name="HistoryState_AvansKRMYetkiliOnayı" xml:space="preserve">
    <value>Avans KRM Yetkili Onayı</value>
  </data>
  <data name="HistoryState_AvansYetkiliOnayı" xml:space="preserve">
    <value>Avans Yetkili Onayı</value>
  </data>
  <data name="HistoryState_AvansYetkiOnayı" xml:space="preserve">
    <value>Avans Yetki Onayı</value>
  </data>
  <data name="HistoryState_BarterYetkiliOnayı" xml:space="preserve">
    <value>Barter Yetkili Onayı</value>
  </data>
  <data name="HistoryState_BatıUlusalBölgeMüdürüOnayı" xml:space="preserve">
    <value>Batı Ulusal Bölge Müdürü Onayı</value>
  </data>
  <data name="HistoryState_BatıUlusalBölgeSatış" xml:space="preserve">
    <value>Batı Ulusal Bölge Satış</value>
  </data>
  <data name="HistoryState_BirimMüdürüOnayı" xml:space="preserve">
    <value>Birim Müdürü Onayı</value>
  </data>
  <data name="HistoryState_BirimOnayı" xml:space="preserve">
    <value>Birim Onayı</value>
  </data>
  <data name="HistoryState_BirimYöneticiOnayı" xml:space="preserve">
    <value>Birim Yönetici Onayı</value>
  </data>
  <data name="HistoryState_BirimYöneticisi" xml:space="preserve">
    <value>Birim Yöneticisi</value>
  </data>
  <data name="HistoryState_BirimYöneticisiOnay" xml:space="preserve">
    <value>Birim Yöneticisi Onay</value>
  </data>
  <data name="HistoryState_BirimYöneticisiOnayı" xml:space="preserve">
    <value>Birim Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_BroadcastSoftwareTanımlamaveOnayı" xml:space="preserve">
    <value>Broadcast Software Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_BölgeMüdürüOnayı" xml:space="preserve">
    <value>Bölge Müdürü Onayı</value>
  </data>
  <data name="HistoryState_BölgeYöneticisiOnayı" xml:space="preserve">
    <value>Bölge Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_BölümYöneticiOnayı" xml:space="preserve">
    <value>Bölüm Yönetici Onayı</value>
  </data>
  <data name="HistoryState_BölümYöneticisi" xml:space="preserve">
    <value>Bölüm Yöneticisi</value>
  </data>
  <data name="HistoryState_BölümYöneticisiOnayı" xml:space="preserve">
    <value>Bölüm Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_BütçeYöneticisi" xml:space="preserve">
    <value>Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_ConditionalAccessTanımlamaveOnayı" xml:space="preserve">
    <value>Conditional Access Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_CRMGrubuOnayı" xml:space="preserve">
    <value>CRM Grubu Onayı</value>
  </data>
  <data name="HistoryState_DelegasyonTalebiCevap" xml:space="preserve">
    <value>Delegasyon Talebi Cevap</value>
  </data>
  <data name="HistoryState_DelegasyonTalep" xml:space="preserve">
    <value>Delegasyon Talep</value>
  </data>
  <data name="HistoryState_DepartmanOnayı" xml:space="preserve">
    <value>Departman Onayı</value>
  </data>
  <data name="HistoryState_DepartmanYöneticiOnayı" xml:space="preserve">
    <value>Departman Yönetici Onayı</value>
  </data>
  <data name="HistoryState_DepartmanYöneticisi" xml:space="preserve">
    <value>Departman Yöneticisi</value>
  </data>
  <data name="HistoryState_DepartmanYöneticisiOnayı" xml:space="preserve">
    <value>Departman Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_DeğerlendirmeTamamlandı" xml:space="preserve">
    <value>Değerlendirme Tamamlandı</value>
  </data>
  <data name="HistoryState_DigiturkFinansOnayı" xml:space="preserve">
    <value>Digiturk Finans Onayı</value>
  </data>
  <data name="HistoryState_DiğerBilgiToplama" xml:space="preserve">
    <value>Diğer Bilgi Toplama</value>
  </data>
  <data name="HistoryState_DoğuUlusalBölgeMüdürüOnayı" xml:space="preserve">
    <value>Doğu Ulusal Bölge Müdürü Onayı</value>
  </data>
  <data name="HistoryState_DoğuUlusalBölgeSatış" xml:space="preserve">
    <value>Doğu Ulusal Bölge Satış</value>
  </data>
  <data name="HistoryState_DtsOnayı" xml:space="preserve">
    <value>Dts Onayı</value>
  </data>
  <data name="HistoryState_DTSReadyOnayı" xml:space="preserve">
    <value>DTS Ready Onayı</value>
  </data>
  <data name="HistoryState_DublajArşivTanımlamveOnayı" xml:space="preserve">
    <value>Dublaj Arşiv Tanımlam ve Onayı</value>
  </data>
  <data name="HistoryState_Düzeltme" xml:space="preserve">
    <value>Düzeltme</value>
  </data>
  <data name="HistoryState_EgitimSonrasıDeğerlendirmeSüreci" xml:space="preserve">
    <value>Egitim Sonrası Değerlendirme Süreci</value>
  </data>
  <data name="HistoryState_EkipYöneticisiOnayı" xml:space="preserve">
    <value>Ekip Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_EkSatınalmaSahibiOnayı" xml:space="preserve">
    <value>Ek Satınalma Sahibi Onayı</value>
  </data>
  <data name="HistoryState_EkİhtiyaçlarınDüzenlenmesi" xml:space="preserve">
    <value>Ek İhtiyaçların Düzenlenmesi</value>
  </data>
  <data name="HistoryState_FaturaDüzenleme" xml:space="preserve">
    <value>Fatura Düzenleme</value>
  </data>
  <data name="HistoryState_FaturaDüzenlemeJüpiter" xml:space="preserve">
    <value>Fatura Düzenleme(Jüpiter)</value>
  </data>
  <data name="HistoryState_FaturaDüzenlemePluton" xml:space="preserve">
    <value>Fatura Düzenleme(Pluton)</value>
  </data>
  <data name="HistoryState_FaturaDüzenlendi" xml:space="preserve">
    <value>Fatura Düzenlendi</value>
  </data>
  <data name="HistoryState_FinansBütçe" xml:space="preserve">
    <value>Finans Bütçe</value>
  </data>
  <data name="HistoryState_FinansBütçeYöneticisi" xml:space="preserve">
    <value>Finans Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_FinansGenelMüdürYardımcısıOnayı" xml:space="preserve">
    <value>Finans Genel Müdür Yardımcısı Onayı</value>
  </data>
  <data name="HistoryState_FinansGMY" xml:space="preserve">
    <value>Finans GMY</value>
  </data>
  <data name="HistoryState_FinansGMYOnayı" xml:space="preserve">
    <value>Finans GMY Onayı</value>
  </data>
  <data name="HistoryState_FinansKontrolOnayı" xml:space="preserve">
    <value>Finans Kontrol Onayı</value>
  </data>
  <data name="HistoryState_FinansKontrolüOnayı" xml:space="preserve">
    <value>Finans Kontrolü Onayı</value>
  </data>
  <data name="HistoryState_FinansYetkiliOnayı" xml:space="preserve">
    <value>Finans Yetkili Onayı</value>
  </data>
  <data name="HistoryState_FirmaBeklemeOnay" xml:space="preserve">
    <value>Firma Bekleme Onay</value>
  </data>
  <data name="HistoryState_FirmaOnayı" xml:space="preserve">
    <value>Firma Onayı</value>
  </data>
  <data name="HistoryState_FormKontrolveOnayı" xml:space="preserve">
    <value>Form Kontrol ve Onayı</value>
  </data>
  <data name="HistoryState_GeliştirmeAşaması" xml:space="preserve">
    <value>Geliştirme Aşaması</value>
  </data>
  <data name="HistoryState_GenelMüdürOnayı" xml:space="preserve">
    <value>Genel Müdür Onayı</value>
  </data>
  <data name="HistoryState_GenelMüdürYardımcısıOnayı" xml:space="preserve">
    <value>Genel Müdür Yardımcısı Onayı</value>
  </data>
  <data name="HistoryState_GMOnayı" xml:space="preserve">
    <value>GM Onayı</value>
  </data>
  <data name="HistoryState_GMonayı1" xml:space="preserve">
    <value>GM onayı</value>
  </data>
  <data name="HistoryState_GMYOnayı" xml:space="preserve">
    <value>GMY Onayı</value>
  </data>
  <data name="HistoryState_GSMTalebiTalepOluşturma" xml:space="preserve">
    <value>GSM Talebi- Talep Oluşturma</value>
  </data>
  <data name="HistoryState_GörüntülemeTalebi" xml:space="preserve">
    <value>Görüntüleme Talebi</value>
  </data>
  <data name="HistoryState_HazineOnayı" xml:space="preserve">
    <value>Hazine Onayı</value>
  </data>
  <data name="HistoryState_Hazineonayı1" xml:space="preserve">
    <value>Hazine onayı</value>
  </data>
  <data name="HistoryState_HazineOnayı2" xml:space="preserve">
    <value>Hazine Onayı 2</value>
  </data>
  <data name="HistoryState_HazineÜstOnayı" xml:space="preserve">
    <value>Hazine Üst Onayı</value>
  </data>
  <data name="HistoryState_HeadEndBütçeDepartmanYöneticisi" xml:space="preserve">
    <value>Head End Bütçe Departman Yöneticisi</value>
  </data>
  <data name="HistoryState_HeadEndBütçeGMY" xml:space="preserve">
    <value>Head End Bütçe GMY</value>
  </data>
  <data name="HistoryState_HeadEndBütçeYöneticisi" xml:space="preserve">
    <value>Head End Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_HukukDepartmanıHukukuÇalışanı" xml:space="preserve">
    <value>Hukuk Departmanı - Hukuku Çalışanı</value>
  </data>
  <data name="HistoryState_HukukGMYOnayı" xml:space="preserve">
    <value>Hukuk GMY Onayı</value>
  </data>
  <data name="HistoryState_HukukMüdürüOnayı" xml:space="preserve">
    <value>Hukuk Müdürü Onayı</value>
  </data>
  <data name="HistoryState_HukukMüdürüYönlendirme" xml:space="preserve">
    <value>Hukuk Müdürü Yönlendirme</value>
  </data>
  <data name="HistoryState_IKBölümYöneticisiOnayı" xml:space="preserve">
    <value>IK Bölüm Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_IKOnayı" xml:space="preserve">
    <value>IK Onayı</value>
  </data>
  <data name="HistoryState_IKOperasyonOnayı" xml:space="preserve">
    <value>IK Operasyon Onayı</value>
  </data>
  <data name="HistoryState_IKOperasyonOnayı1" xml:space="preserve">
    <value>IK-Operasyon Onayı</value>
  </data>
  <data name="HistoryState_IKYöneticisiOnayı" xml:space="preserve">
    <value>IK Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_IlgiliOnayı" xml:space="preserve">
    <value>Ilgili Onayı</value>
  </data>
  <data name="HistoryState_IlkOnayGrubuOnayi" xml:space="preserve">
    <value>Ilk Onay Grubu Onayi</value>
  </data>
  <data name="HistoryState_ISOnayı" xml:space="preserve">
    <value>IS Onayı</value>
  </data>
  <data name="HistoryState_KanalTipiSporise" xml:space="preserve">
    <value>Kanal Tipi Spor ise</value>
  </data>
  <data name="HistoryState_KanalTipiUlusalise" xml:space="preserve">
    <value>Kanal Tipi Ulusal ise</value>
  </data>
  <data name="HistoryState_KartvizitBasımAdımı" xml:space="preserve">
    <value>Kartvizit Basım Adımı</value>
  </data>
  <data name="HistoryState_KontrolGrubuOnayı" xml:space="preserve">
    <value>Kontrol Grubu Onayı</value>
  </data>
  <data name="HistoryState_KontrolOnayı" xml:space="preserve">
    <value>Kontrol Onayı</value>
  </data>
  <data name="HistoryState_KRMOperasyonOnayı" xml:space="preserve">
    <value>KRM Operasyon Onayı</value>
  </data>
  <data name="HistoryState_KırtasiyeYetkilisi" xml:space="preserve">
    <value>Kırtasiye Yetkilisi</value>
  </data>
  <data name="HistoryState_LIGTVBUTCE1" xml:space="preserve">
    <value>LIG TV BUTCE-1</value>
  </data>
  <data name="HistoryState_LIGTVBUTCE2" xml:space="preserve">
    <value>LIG TV BUTCE-2</value>
  </data>
  <data name="HistoryState_LigTVOperasyonOnayı" xml:space="preserve">
    <value>Lig TV Operasyon Onayı</value>
  </data>
  <data name="HistoryState_LigTVYöneticisiOnayı" xml:space="preserve">
    <value>Lig TV Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_MaintenanceTanımlamaveOnayı" xml:space="preserve">
    <value>Maintenance Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_MaliyetveKıymetliEvraklarMuhasebeOnayı" xml:space="preserve">
    <value>Maliyet ve Kıymetli Evraklar Muhasebe Onayı</value>
  </data>
  <data name="HistoryState_MCRTanımlamaveOnayı" xml:space="preserve">
    <value>MCR Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_MuhasebeOnayı" xml:space="preserve">
    <value>Muhasebe Onayı</value>
  </data>
  <data name="HistoryState_OnayKontrol" xml:space="preserve">
    <value>Onay Kontrol</value>
  </data>
  <data name="HistoryState_OrganizasyonelGelişimOnayı" xml:space="preserve">
    <value>Organizasyonel Gelişim Onayı</value>
  </data>
  <data name="HistoryState_OrganizasyonelGelişimOnayı2" xml:space="preserve">
    <value>Organizasyonel Gelişim Onayı 2</value>
  </data>
  <data name="HistoryState_OrganizasyonelGelişimOnayı3" xml:space="preserve">
    <value>Organizasyonel Gelişim Onayı 3</value>
  </data>
  <data name="HistoryState_OrganizasyonelGelişimOnayı4" xml:space="preserve">
    <value>Organizasyonel Gelişim Onayı 4</value>
  </data>
  <data name="HistoryState_OtomatikOnay" xml:space="preserve">
    <value>Otomatik Onay</value>
  </data>
  <data name="HistoryState_PazarlamaDeğerlendirme" xml:space="preserve">
    <value>Pazarlama Değerlendirme</value>
  </data>
  <data name="HistoryState_PazarlamaGMYOnayı" xml:space="preserve">
    <value>Pazarlama GMY Onayı</value>
  </data>
  <data name="HistoryState_PersonelDüzeltme" xml:space="preserve">
    <value>Personel Düzeltme</value>
  </data>
  <data name="HistoryState_RaporKontrolAdımı" xml:space="preserve">
    <value>Rapor Kontrol Adımı</value>
  </data>
  <data name="HistoryState_RegülasyonOnayi" xml:space="preserve">
    <value>Regülasyon Onayi</value>
  </data>
  <data name="HistoryState_SatınalmaSahibiOnayı" xml:space="preserve">
    <value>Satınalma Sahibi Onayı</value>
  </data>
  <data name="HistoryState_SatışBütçeYöneticisi" xml:space="preserve">
    <value>Satış Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_SatışGenelMüdürOnayı" xml:space="preserve">
    <value>Satış Genel Müdür Onayı</value>
  </data>
  <data name="HistoryState_SatışYetkiliOnayı" xml:space="preserve">
    <value>Satış Yetkili Onayı</value>
  </data>
  <data name="HistoryState_SiparişAşaması" xml:space="preserve">
    <value>Sipariş Aşaması</value>
  </data>
  <data name="HistoryState_SistemOnayı" xml:space="preserve">
    <value>Sistem Onayı</value>
  </data>
  <data name="HistoryState_SRBCYetkilisiOnayı" xml:space="preserve">
    <value>SRBC Yetkilisi Onayı</value>
  </data>
  <data name="HistoryState_TakımYöneticiOnayı" xml:space="preserve">
    <value>Takım Yönetici Onayı</value>
  </data>
  <data name="HistoryState_TakımYöneticisiOnayı" xml:space="preserve">
    <value>Takım Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_TalepBaşlat" xml:space="preserve">
    <value>Talep Başlat</value>
  </data>
  <data name="HistoryState_TalepBaşlatma" xml:space="preserve">
    <value>Talep Başlatma</value>
  </data>
  <data name="HistoryState_TalepOluştur" xml:space="preserve">
    <value>Talep Oluştur</value>
  </data>
  <data name="HistoryState_TalepOluşturanaDüzeltme" xml:space="preserve">
    <value>Talep Oluşturana Düzeltme</value>
  </data>
  <data name="HistoryState_TalepOluşturanDüzeltme" xml:space="preserve">
    <value>Talep Oluşturan Düzeltme</value>
  </data>
  <data name="HistoryState_TalepOluşturma" xml:space="preserve">
    <value>Talep Oluşturma</value>
  </data>
  <data name="HistoryState_TalepOnay" xml:space="preserve">
    <value>Talep Onay</value>
  </data>
  <data name="HistoryState_TalepOnaylandı" xml:space="preserve">
    <value>Talep Onaylandı</value>
  </data>
  <data name="HistoryState_TalepOnaylandıXXX" xml:space="preserve">
    <value>Talep OnaylandıXXX</value>
  </data>
  <data name="HistoryState_TalepOnaySatınAlındı" xml:space="preserve">
    <value>Talep Onay - Satın Alındı</value>
  </data>
  <data name="HistoryState_TalepOtomatikOnay" xml:space="preserve">
    <value>Talep Otomatik Onay</value>
  </data>
  <data name="HistoryState_TalepRed" xml:space="preserve">
    <value>Talep Red</value>
  </data>
  <data name="HistoryState_TalepReddedildi" xml:space="preserve">
    <value>Talep Reddedildi</value>
  </data>
  <data name="HistoryState_TalepSahibiDüzeltme" xml:space="preserve">
    <value>Talep Sahibi Düzeltme</value>
  </data>
  <data name="HistoryState_TalepSahibiGirişOnayı" xml:space="preserve">
    <value>Talep Sahibi Giriş Onayı</value>
  </data>
  <data name="HistoryState_TalepSahibiGmyOnayı" xml:space="preserve">
    <value>Talep Sahibi Gmy Onayı</value>
  </data>
  <data name="HistoryState_TalepSahibiGMYOnayı1" xml:space="preserve">
    <value>Talep Sahibi GMY Onayı</value>
  </data>
  <data name="HistoryState_TalepSahibiOnayı" xml:space="preserve">
    <value>Talep Sahibi Onayı</value>
  </data>
  <data name="HistoryState_TeklifAşaması" xml:space="preserve">
    <value>Teklif Aşaması</value>
  </data>
  <data name="HistoryState_TeknikServisYöneticisiOnayı" xml:space="preserve">
    <value>Teknik Servis Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_TeknikServisYönlendirmeOnayı" xml:space="preserve">
    <value>Teknik Servis Yönlendirme Onayı</value>
  </data>
  <data name="HistoryState_TestAşaması" xml:space="preserve">
    <value>Test Aşaması</value>
  </data>
  <data name="HistoryState_TicariSatışOnayı" xml:space="preserve">
    <value>Ticari Satış Onayı</value>
  </data>
  <data name="HistoryState_TransmissionTanımlamaveOnayı1" xml:space="preserve">
    <value>Transmission Tanımlama ve Onayı -1-</value>
  </data>
  <data name="HistoryState_TransmissionTanımlamaveOnayı2" xml:space="preserve">
    <value>Transmission Tanımlama ve Onayı -2-</value>
  </data>
  <data name="HistoryState_TurkmaxBütçeYöneticisi" xml:space="preserve">
    <value>Turkmax Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_TURKMAXFinansOnayı" xml:space="preserve">
    <value>TURKMAX Finans Onayı</value>
  </data>
  <data name="HistoryState_TURKMAXOperasyonOnayı" xml:space="preserve">
    <value>TURKMAX Operasyon Onayı</value>
  </data>
  <data name="HistoryState_UlusalBölgeMüdürüOnayı" xml:space="preserve">
    <value>Ulusal Bölge Müdürü Onayı</value>
  </data>
  <data name="HistoryState_UlusalSatışGMYOnayı" xml:space="preserve">
    <value>Ulusal Satış GMY Onayı</value>
  </data>
  <data name="HistoryState_UlusalSatışYöneticiOnayı" xml:space="preserve">
    <value>Ulusal Satış Yönetici Onayı</value>
  </data>
  <data name="HistoryState_YardımMasası" xml:space="preserve">
    <value>Yardım Masası</value>
  </data>
  <data name="HistoryState_YardımMasası1Adım" xml:space="preserve">
    <value>Yardım Masası 1. Adım</value>
  </data>
  <data name="HistoryState_YardımMasası1AdımOnayı" xml:space="preserve">
    <value>Yardım Masası 1.Adım Onayı</value>
  </data>
  <data name="HistoryState_YardımMasası2Adım" xml:space="preserve">
    <value>Yardım Masası 2. Adım</value>
  </data>
  <data name="HistoryState_YetkiliGrubuOnayı" xml:space="preserve">
    <value>Yetkili Grubu Onayı</value>
  </data>
  <data name="HistoryState_YetkiliOnayı" xml:space="preserve">
    <value>Yetkili Onayı</value>
  </data>
  <data name="HistoryState_YetkiliOnayı1" xml:space="preserve">
    <value>Yetkili Onayı 1</value>
  </data>
  <data name="HistoryState_YetkiliOnayıKullanilmiyor" xml:space="preserve">
    <value>Yetkili Onayı (Kullanilmiyor)</value>
  </data>
  <data name="HistoryState_YetkiliOnayıYurtDışı" xml:space="preserve">
    <value>Yetkili Onayı Yurt Dışı</value>
  </data>
  <data name="HistoryState_YetkiliOnayıYurtİçi" xml:space="preserve">
    <value>Yetkili Onayı Yurt İçi</value>
  </data>
  <data name="HistoryState_YetkiliyöneticiGirişOnayı" xml:space="preserve">
    <value>Yetkili yönetici Giriş Onayı</value>
  </data>
  <data name="HistoryState_YetkiliYöneticiOnayı" xml:space="preserve">
    <value>Yetkili Yönetici Onayı</value>
  </data>
  <data name="HistoryState_YetkiliYöneticiOnayı2" xml:space="preserve">
    <value>Yetkili Yönetici Onayı 2</value>
  </data>
  <data name="HistoryState_YetkiliYöneticiOnayı3" xml:space="preserve">
    <value>Yetkili Yönetici Onayı 3</value>
  </data>
  <data name="HistoryState_YetkiOnay" xml:space="preserve">
    <value>Yetki Onay</value>
  </data>
  <data name="HistoryState_YoneticiGrubu" xml:space="preserve">
    <value>Yonetici Grubu</value>
  </data>
  <data name="HistoryState_YTSTalepKapatmaİşlemi" xml:space="preserve">
    <value>YTS Talep Kapatma İşlemi</value>
  </data>
  <data name="HistoryState_YurtdışıFinansOnayı" xml:space="preserve">
    <value>Yurtdışı Finans Onayı</value>
  </data>
  <data name="HistoryState_YurtdışıSatışYöneticisiOnayı" xml:space="preserve">
    <value>Yurtdışı Satış Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_YöneticiDeğerlendirme" xml:space="preserve">
    <value>Yönetici Değerlendirme</value>
  </data>
  <data name="HistoryState_YöneticiOnayı" xml:space="preserve">
    <value>Yönetici Onayı</value>
  </data>
  <data name="HistoryState_ZincirMağazaYetkili" xml:space="preserve">
    <value>Zincir Mağaza Yetkili</value>
  </data>
  <data name="HistoryState_ÇalışanOnayı" xml:space="preserve">
    <value>Çalışan Onayı</value>
  </data>
  <data name="HistoryState_ÖzellikleriTanımlamaveOnayı" xml:space="preserve">
    <value>Özellikleri Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_ÜrünGeliştirmeVeOperGMYOnayı" xml:space="preserve">
    <value>Ürün Geliştirme Ve Oper. GMY Onayı</value>
  </data>
  <data name="HistoryState_ÜrünGeliştirmeVeOperGMYOnayı1" xml:space="preserve">
    <value>Ürün Geliştirme Ve Oper.GMY Onayı</value>
  </data>
  <data name="HistoryState_ÜstYöneticisiOnay" xml:space="preserve">
    <value>Üst Yöneticisi Onay</value>
  </data>
  <data name="HistoryState_ÜstYöneticisiOnayı" xml:space="preserve">
    <value>Üst Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_İnsanKaynaklarıAdımı" xml:space="preserve">
    <value>İnsan Kaynakları Adımı</value>
  </data>
  <data name="HistoryState_İnsanKaynaklarıYetkilsi" xml:space="preserve">
    <value>İnsan Kaynakları Yetkilsi</value>
  </data>
  <data name="HistoryState_İçerikBütçeYöneticisi" xml:space="preserve">
    <value>İçerik Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_İçerikBütçeYöneticisi2" xml:space="preserve">
    <value>İçerik Bütçe Yöneticisi-2</value>
  </data>
  <data name="HistoryState_İçHizmetler2YöneticiOnayı" xml:space="preserve">
    <value>İç Hizmetler 2. Yönetici Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerBirimYöneticisiOnayı" xml:space="preserve">
    <value>İç Hizmetler Birim Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerBölümYöneticisiOnayı" xml:space="preserve">
    <value>İç Hizmetler Bölüm Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerGenelMüdürYardımcısıOnayı" xml:space="preserve">
    <value>İç Hizmetler Genel Müdür Yardımcısı Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerGMYOnayı" xml:space="preserve">
    <value>İç Hizmetler GMY Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerYöneticisiOnayı" xml:space="preserve">
    <value>İç Hizmetler Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_İşeGirişİşlemleri" xml:space="preserve">
    <value>İşe Giriş İşlemleri</value>
  </data>
  <data name="HistoryState_İşGeliştirmeveStratejiGMYOnayı" xml:space="preserve">
    <value>İş Geliştirme ve Strateji GMY Onayı</value>
  </data>
  <data name="inbox_akislar_toplu_onay" xml:space="preserve">
    <value>Multiple Workflow is Approved</value>
  </data>
  <data name="main_aciklama_degistir" xml:space="preserve">
    <value>Change Description</value>
  </data>
  <data name="main_duzeltme_talebi" xml:space="preserve">
    <value>Düzeltme Talebi</value>
  </data>
  <data name="main_onay" xml:space="preserve">
    <value>Onay</value>
  </data>
  <data name="main_sartli_onay" xml:space="preserve">
    <value>Şartlı Onay</value>
  </data>
  <data name="main_yetki_uyari" xml:space="preserve">
    <value>Bu akışa erişim yetkiniz bulunmamaktadır.</value>
  </data>
  <data name="Main_Onay_Uyari" xml:space="preserve">
    <value>Şartlı Onay / Onay seçeneklerinden birini seçmediniz, Lütfen kontrol edip yeniden deneyiniz.</value>
  </data>
  <data name="Main_Dokuman_Uyari" xml:space="preserve">
    <value>Doküman alani bos gecilememektedir, kontrol edip yeniden deneyiniz.</value>
  </data>
  <data name="Main_Uyari" xml:space="preserve">
    <value>Uyarı</value>
  </data>
</root>