/* Common */
body {
    background-color: #A9ACB4;
    color: black;

    font-size: 8pt;
	padding: 0px;
	margin: 0px;
}
a {
    color: #5a9ddb;
    text-decoration: underline;
}
a:hover {
    color: #9aceff;
}
a:visited {
    color: #c983e4;
    text-decoration: underline;
}
div.PageContent {
    padding: 0px 0px 0px 0px;
}
/* Action Container Panel */
.ACPanel
{
	background-color: #9B9EA7;
	border-top: solid 1px #696969;
	padding: 2px 5px 2px 20px;
	height: 25px;
}
/* Main table */
.Main {
	background: white;
}
h1 {
	color: Black;

	font-size: 200%;
	font-family: Tahoma;
	font-weight: normal;
	margin: 0px 0px 0px 0px;
	padding: 0px;
}
/* Footer */
.FooterCopyright {
	font-family: Tahoma, Arial, Helvetica, sans-serif;
	font-size: 8pt;
	text-align: right;

	color: #E5E9ED;
}
/* Dialog */
body.Dialog {
	background-color: White !important;
}
div.DialogPageContent {
	margin: 0px;
}
div.Header {
	width: 100%;
	height: 80px;
	margin: 0px;
}
.DialogContent {
	background-color: White;

	height: 300px;
}
/* Error */
.Error {
	color: Black;
}
/* Links on dark background */
.Footer .menuLinks_Office2010Silver a,
.Footer .menuLinks_Office2010Silver a.dx,
.Security .menuLinks_Office2010Silver a,
.Security .menuLinks_Office2010Silver a.dx,
.TabsContainer .menuLinks_Office2010Silver a,
.TabsContainer .menuLinks_Office2010Silver a.dx,
.Footer .menuLinks_Office2010Silver a:visited,
.Footer .menuLinks_Office2010Silver a.dx:visited,
.Security .menuLinks_Office2010Silver a:visited,
.Security .menuLinks_Office2010Silver a.dx:visited,
.TabsContainer .menuLinks_Office2010Silver a:visited,
.TabsContainer .menuLinks_Office2010Silver a.dx:visited
{
	color: White !important;
}
/* Controls customization */
.dxnbControl_Office2010Silver,
.dxnbLite_Office2010Silver {
	width: 100% !important;
}
.dxgvControl_Office2010Silver {
	width: 100%;
}
.dxgvHeader_Office2010Silver td {
    white-space:normal;
}
.dxtcControl_Office2010Silver,
.dxtcLite_Office2010Silver {
    width: 100% !important;
}
.NavigationTabsActionContainer .dxtcControl_Office2010Silver,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver {
	background-color: #9B9EA7;
	border-top: solid 1px #696969;
}
.NavigationTabsActionContainer .dxtcPageContent_Office2010Silver,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-content {
	padding-left: 18px;
    border: none !important;
}
.NavigationTabsActionContainer .dxtcLeftIndentCell_Office2010Silver div,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-leftIndent {
	width: 15px !important;
}
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-tab,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-activeTab,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-leftIndent,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-spacer,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-rightIndent,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-sbWrapper,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-sbIndent,
.NavigationTabsActionContainer .dxtcLite_Office2010Silver .dxtc-sbSpacer
{
	height: 25px;
}
.ACV .dxmVerticalMenuItemWithImage_Office2010Silver,
.ACV .dxmLite_Office2010Silver .menuLinks_Office2010Silver .dxm-content,
.ACV .dxmLite_Office2010Silver .menuLinks_Office2010Silver .dxm-hovered .dxm-content,
.ACV .dxmLite_Office2010Silver .menuLinks_Office2010Silver .dxm-disabled .dxm-content
{
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}
.ACH .dxmLite_Office2010Silver .dxmtb .dxm-hasText,
.ToolBar .dxmLite_Office2010Silver .dxmtb .dxm-hasText
{
	padding-top: 5px !important;
	padding-bottom: 1px !important;
}
/* NavigationHistory (NavigationHistoryActionContainer) */
.NavigationHistoryLinks {
	color: #86888D;
	padding: 0px 0px 0px 2px;
}
.NavigationHistoryLinks a:hover {
	text-decoration: underline;
    color: #9aceff;
}
.NavigationHistoryLinks a, .NavigationHistoryLinks a:visited {
	text-decoration: underline;
    color: #5a9ddb;
}
.NavigationHistoryLinks a.Current, .NavigationHistoryLinks a.Current:hover, .NavigationHistoryLinks a.Current:visited {
	text-decoration: none;
	color: #86888D;
}
.Security .dxmMenuSeparator_Office2010Silver {
	padding: 0px 4px 0px 11px;
}
.TabsContainer .dxmMenuSeparator_Office2010Silver {
	padding: 0px 10px 0px 16px;
}
.LayoutTabContainerWithNestedFrame > .Item:first-child > .NestedFrame > .ToolBar .dxmMenu_Office2010Silver,
.LayoutTabContainerWithNestedFrame > .Item:first-child > .NestedFrame > .ToolBar .dxmLite_Office2010Silver .dxm-main {
	border-top: 0px;
}
.LayoutTabContainer > .Item > .NestedFrame > .ToolBar .dxmMenu_Office2010Silver,
.LayoutTabContainer > .Item > .NestedFrame > .ToolBar .dxmLite_Office2010Silver .dxm-main {
	border-bottom-width: 0px;
}
.LayoutTabContainerWithNestedFrame > .Item > .NestedFrame > .ToolBar .dxmMenu_Office2010Silver,
.LayoutTabContainerWithNestedFrame > .Item > .NestedFrame > .ToolBar .dxmLite_Office2010Silver .dxm-main
{
	border-left: 0px;
	border-right: 0px;
	border-bottom-width: 1px !important;
}
.Layout .TabControlContent {
	padding: 0px !important;
}
.Layout .TabControlContent > div {
	padding: 0px !important;
}
.HorizontalTemplateHeader,
.VerticalTemplateHeader,
.Header {
	background-color: #A9ACB4;
}
.RecordsNavigationContainer .menuLinks_Office2010Silver {
	padding-left: 8px;
}
.RecordsNavigationContainer .menuLinks_Office2010Silver .dxmMenuItemWithImage_Office2010Silver {
	padding-left: 3px;
}