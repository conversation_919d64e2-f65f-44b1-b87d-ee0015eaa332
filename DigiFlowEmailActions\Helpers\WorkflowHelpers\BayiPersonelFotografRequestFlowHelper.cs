﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class BayiPersonelFotografRequestFlowHelper
    {
        internal static void SetProsedur(string durum, Digiturk.Workflow.Common.WFContext CurrentWFContext, Digiturk.Workflow.Entities.FWfWorkflowInstance CurrentWfIns, FWfActionTaskInstance taskInstance, FLogin LoginObject, string Commend, ref string errorUserMsg_Tr, ref string errorUserMsg_En)
        {
            Digiturk.Workflow.Entities.FWfStateInstance StateIns = WFRepository<FWfStateInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfStateInstanceId);
            Digiturk.Workflow.Entities.FWfStateDef StateDef = WFRepository<FWfStateDef>.GetEntity(StateIns.WfStateDef.WfStateDefId);
            if (StateDef.WfStateDefId == ConvertionHelper.ConvertValue<int>(Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDefinitionHelper.StateDefIDBul("BayiPersonelFotografAkisi_TeknikServisMudur")))
            {
                BayiFotografRequest ReqObj = WFRepository<BayiFotografRequest>.GetEntity(CurrentWfIns.EntityRefId);
                string prosedurDurum = BayiPersonelFotografRequestHelper.SetProsedur(durum, ReqObj.IrisID.ToString(), CurrentWfIns.WfWorkflowInstanceId.ToString(), CurrentWfIns.EntityRefId.ToString(), taskInstance, LoginObject.DomainUserName, LoginObject.Email, Commend, ReqObj.Created.ToShortDateString(), CurrentWFContext);
                if (prosedurDurum != "")
                {
                    errorUserMsg_Tr = prosedurDurum + "Hata düzeltildikten sonra " + (durum == "Yes" ? "onay" : "red") + " işlemi yapılabilir, İlgili kişiler ile iletişime geçiniz.";
                    errorUserMsg_En = prosedurDurum + (durum == "Yes" ? "Approval" : "Rejection") + " process can be completed when the error is corrected.Please contact related staff.";
                    throw new Exception();
                }
            }
        }
    }
}
