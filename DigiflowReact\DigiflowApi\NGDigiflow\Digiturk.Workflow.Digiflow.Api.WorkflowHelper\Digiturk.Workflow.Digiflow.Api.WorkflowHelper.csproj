﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5B9B9ED7-9429-4656-9FC3-3BD2B9A67124}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Digiturk.Workflow.Digiflow.Api.WorkflowHelper</RootNamespace>
    <AssemblyName>Digiturk.Workflow.Digiflow.Api.WorkflowHelper</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Digiturk.Workflow.Common">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Common.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Actions">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.Actions.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.AssignmentBase">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.AssignmentBase.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authentication">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.Authentication.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authorization">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Entities">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.ExceptionEntites">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.ExceptionEntites.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.DigiFlow.Framework">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.DigiFlow.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.GenericMailHelper">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.GenericMailHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WebCore">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.WebCore.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowServicesHelper">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Engine">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Engine.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Repository">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Repository.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ActionHelper.cs" />
    <Compile Include="IdentityHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.Api.Common\Digiturk.Workflow.Digiflow.Api.Common.csproj">
      <Project>{2D7F1806-4791-4C74-B0AE-5001DF76052A}</Project>
      <Name>Digiturk.Workflow.Digiflow.Api.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>