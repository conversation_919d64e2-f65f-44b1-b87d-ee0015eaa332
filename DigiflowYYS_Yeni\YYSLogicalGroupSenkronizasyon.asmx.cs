﻿using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.YYS.Core;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;

namespace DigiflowYYS_Yeni
{
    /// <summary>
    /// Summary description for YYSLogicalGroupSenkronizasyon
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [System.ComponentModel.ToolboxItem(false)]
    // To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
    [System.Web.Script.Services.ScriptService]
    public class YYSLogicalGroupSenkronizasyon : System.Web.Services.WebService
    {

        //[WebMethod]
        //public string SenkronizeLogicalGroup(Digiturk.Workflow.Digiflow.Entities.LogicalGroup item)
        //{
        //    string result = string.Empty;
        //    try
        //    {
        //        string FlowName = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.GetFlowName(item.WfDefId);
        //        Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupJobHelper.CompareLogicalGroup(item, FlowName);
        //        result = "0";
        //    }
        //    catch (Exception ex)
        //    {
        //        result = ex.Message + " " + ex.StackTrace;
        //        if (ex.InnerException != null)
        //        {
        //            result += " Inner Exception" + ex.InnerException.Message + " " + ex.InnerException.StackTrace;
        //        }
        //    }
        //    return result;
        //}

        /// <summary>
        /// 15.08.2024 AKTIF OLARAK KULLANILAN YYS LOGICAL GROUP İÇİNDEKI AD KULLANICILARINI 
        /// MEVCUT DATALARI CANLI DATALAR İLE SENKRONİZE EDEN SERVİS
        /// </summary>
        /// <param name="mapItem"></param>
        /// <returns></returns>
        [WebMethod]
        public string SenkronizeLogicalGroupNew(Digiturk.Workflow.Digiflow.Entities.YYS.LogicalGroup.LogicalGroupAdMap mapItem)
        {
            string result = "0";
            try
            {
                #region Databasede olmayan Ad Grubu kullanıcılarını servisten gelen güncel datalarla doldurma
                string adDomain = mapItem.AdDomain;
                string adGroup = mapItem.AdGroup;

                List<string> listOfMappedUsersUsernames = new List<string>();
                List<string> listOfRecentUsersUsernames = new List<string>();
                List<string> listOfUsersFLoginOlmayanlar = new List<string>();                

                result = "1.Hata---------Ad Portal Web servisinden (Adportal Projesi / AdPortalServices.asmx) AD Grubunun güncel üyelerini alırken alınan hata" + adDomain + adGroup + mapItem.YYYSAdMemberId.ToString() + mapItem;
                
                // AD PORTAL SERVİSİNDEN GELEN GÜNCEL KULLANICILARI DOMAIN VE AD GRUBU ADIYLA ALIYOR  ----------------GÜNCEL SERVİS DATASI-----------------
                ActiveDirectoryHelpers.AdPortalServices.UserInformation[] usersOfAdGroup = ActiveDirectoryHelpers.AdHelper.GetGroupOfUserListAllUsersNotFiltered(adDomain, adGroup);

                result = "2.Hata----------- Databaseden YYS_LOGICAL_GROUP_MEMBERS tablosundan mevcuttaki LG üyelerini (YYS_LG_AD_MEMBERS_ID / Ad group map Id) ile alırken alınan hata";

                //DATABASE'DE LOGICAL GRUP İÇERİSİNE MAP ID İLE KAYITLI OLAN AD GRUBU ÜYELERİNİ VERİR  ----------------İÇERDEN DB'DEN GELEN DATA-----------------
                List<LogicalGroupMember> usersOfMappedAdGroup = LogicalGroupMemberHelper.GetAllLogicalGroupMembersListByAdMapId(mapItem.YYYSAdMemberId);


                foreach (LogicalGroupMember mappedUser in usersOfMappedAdGroup)// ÇEKİLEN LOGİCAL GRUP İÇİNDEKİ MAPLİ AD GRUBU USERLARINI GEZİP KULLANICI ADLARINI BİR LİSTEYE ATAR
                {
                    result = "3.Hata---------- DB'den gelen mevcuttaki mapli ad userlarını gezip tek tek kişilerin bilgilerini çekerken alınan hata";
                    string usernameOfMappedUser = LogicalGroupMemberHelper.GetUsernameOfMappedUser(mappedUser.LoginId, mapItem.YYYSAdMemberId);
                    
                    if (!string.IsNullOrEmpty(usernameOfMappedUser))
                    {
                        listOfMappedUsersUsernames.Add(usernameOfMappedUser);
                    }                                        
                }

                // AD PORTAL SERVİSINDEN GELEN GÜNCEL DATADAKI KİŞİLERİ GEZİP KULLANICI ADLARINI BİR LİSTEYE ATAR ARDINDAN MAPLİ USERNAMELER İÇERİSİNDE VAR Mİ DİYE KONTROL EDER 
                foreach (var userRecent in usersOfAdGroup)
                {

                    result = "9.Hata------------- Ad portal web servisinden güncel ad grubunun üyeleri cekildikten sonra listeye atılmadan öncesındekı hata";
                    listOfRecentUsersUsernames.Add(userRecent.UserName);

                    //eğer güncel servisten gelen kullanıcı mapplenmis grubun ıcerısınde zaten varsa tekrar kayıda gerek yok
                    // eğer içeride yoksa ad grubuna yenı eklenmıstır, map tablosunda üyesi oldugu ad grubunun maplediği id ile member tablosuna insert atmalıyız.

                    if (!listOfMappedUsersUsernames.Contains(userRecent.UserName)) // MAPLI AD GRUBU USERLARI AD PORTAL GUNCEL SERVISTEN GELEN KİŞİYİ İÇERİYOR MU?
                    {
                        if (!LogicalGroupMemberHelper.IsUserExistInLogicalGroupInAdUserType(mapItem.LogicalGroupId, userRecent.LoginId)) // KİŞİ MAPLİ AD GRUBU USERLARI İÇERİSİNDE OLMAYABİLİR ANCAK BASKA BİR AD GRUBU İLE MANTIKSAL GRUP İÇERİSİNE EKLENMİŞ OLABİLİR BUNUN KONTROLÜ YAPILIR.
                        {
                            if (!LogicalGroupMemberHelper.IsUserExistInLogicalGroupInUserType(mapItem.LogicalGroupId, userRecent.LoginId)) // AD PORTAL SERVİSTEN GELEN KİŞİ MAPLİ DEĞİL KAYIT EDİCEZ ANCAK KULLANICI TİPİNDE KAYITLI MI? KAYITLI DEĞİLSE AD USER TİPİNDE KAYITI EDİLİR.
                            {
                                result = "4.Hata--------------- Ad portal Web servisinden yeni güncel datanın içerisinde Ad grubunun her bir üyesinin dp hr'dan bilgilerini alırken alınan hata" + userRecent.UserName.ToString() + userRecent.LoginId.ToString();

                                DataTable dtb = LogicalGroupMemberHelper.GetUserInfoByLoginId(userRecent.LoginId.ToString()); // AD PORTAL SERVİSTEN GELEN VE İÇERİDE OLMAYAN YENİ KULLANICININ BİLGİLERİ ÇEKİLİR.

                                if (dtb.Rows.Count > 0)
                                {
                                    if (!string.IsNullOrEmpty(dtb.AsEnumerable().Select(x => x["F_LOGIN_ID"]).FirstOrDefault().ToString()))// KULLANICIYI MANTIKSAL GRUBUN İÇERİSİNE AD USER OLARAK EKLEMEDEN ONCE LOGIN ID'Sİ MEVCUT MU? KONTROLÜ
                                    {
                                        result = "5.Hata--------------- Ad portal Web servisinden yeni güncel datanın içerisinde Ad grubunun her bir üyesinin dp hr'dan bilgileri alındıktan sonra alanları setlerken alınan hata";
                                        LogicalGroupMember lgm = new LogicalGroupMember();
                                        lgm.FullName = dtb.AsEnumerable().Select(x => x["NAME_SURNAME"]).FirstOrDefault().ToString();
                                        lgm.LoginId = userRecent.LoginId;
                                        lgm.LogicalGroupMemberTypeId = 5;
                                        lgm.LogicalGroupId = mapItem.LogicalGroupId;
                                        lgm.Content = "";
                                        lgm.Description = "";
                                        lgm.Email = "";
                                        lgm.Created = DateTime.Now;
                                        lgm.CreatedBy = 123456789; //DIGIFLOW_ADMIN
                                        lgm.LastUpdated = DateTime.MinValue;
                                        lgm.LastUpdatedBy = 123456789; //DIGIFLOW_ADMIN
                                        lgm.YYS_LG_AD_MEMBERS_ID = mapItem.YYYSAdMemberId; // KİŞİ KAYIT EDİLİRKEN KENDİ AD GRUBU O MANTIKSAL GRUP İÇİN KAYIT EDİLİRKEN KULLANILAN MAP İD KULLANILIR.



                                        result = "6.Hata--------------- Ad portal Web servisinden yeni güncel datanın içerisinde Ad grubunun her bir üyesinin dp hr'dan bilgileri alındıktan sonra alanları setlendikten sonra YYS_LOGICAL_GROUP_MEMBERS insertte alınan hata ";
                                        LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);

                                    }
                                    else // AD PORTAL GÜNCEL SERVİSTEN GELEN KİŞİNİNİ F LOGİN ID SI YOK İSE LOGİN IDSİ OLMAYANLAR LİSTESİNE EKLENİP MAİL ATILIR.
                                    {
                                        result = "7.Hata-------------Ad portal Web servisinden yeni güncel datanın içerisinde Ad grubunun her bir üyesinin dp hr'dan bilgilerini aldıktan sonra F logini olmayan kullanıcıyı f logını olmayanlar listesine eklerken alınan hata  " + userRecent.UserName.ToString() + userRecent.LoginId.ToString();
                                        listOfUsersFLoginOlmayanlar.Add(userRecent.UserName);                                        
                                    }
                                }
                                else // AD PORTAL GÜNCEL SERVİSTEN GELEN KİŞİNİNİN EKLENMEK ÜZERE F LOGİN ID'Sİ İLE BİLGİLERİ ALINIRKEN BULUNAMADIYSA F LOGİN İDSİ VE ÇALIŞANLAR ALTINDA KAYIDI YOKTU YİNE MAİL ATILMAK ÜZERE F LOGİNİ OLMAYANLAR LİSTESİNE EKLENİR
                                {
                                    result = "8.Hata------------Ad portal Web servisinden yeni güncel datanın içerisinde Ad grubunun her bir üyesinin dp hr'dan bilgilerini almak isteyip alınamadıgında alınan hata Flogini olmayanlara kayıt edılmeden oncesı" + "dışardaki else" + userRecent.UserName.ToString() + userRecent.LoginId.ToString();
                                    listOfUsersFLoginOlmayanlar.Add(userRecent.UserName);                                    
                                }
                            }
                        }
                                                
                    }
                    else 
                    {
                        result = "15.Hata------------AD kullanıcısı mantıksal grup icin mapli ancak kullanıcı tipinde kayıdı varmı kontrolünden onceki alınan hata" + "username: " + userRecent.UserName.ToString() + "  loginID: " + userRecent.LoginId.ToString() + " logicalGroupId: " + mapItem.LogicalGroupId;

                        if (LogicalGroupMemberHelper.IsUserExistInLogicalGroupInUserType(mapItem.LogicalGroupId, userRecent.LoginId))
                        {
                            result = "16.Hata------------AD kullanıcısı mantıksal grup icin mapli ancak kullanıcı tipinde kayıdı varsa mapli ad kullanıcı halini silmeden önceki alınan hata" + "username: "+userRecent.UserName.ToString() + "  loginID: "+userRecent.LoginId.ToString()+ " adMapId: "+mapItem.YYYSAdMemberId;

                            LogicalGroupMemberHelper.DeleteMappedAdUserByUsername(mapItem.YYYSAdMemberId, userRecent.UserName);
                        }
                    }

                }
                #endregion Databasede olmayan Ad Grubu kullanıcılarını servisten gelen güncel datalarla doldurma

                #region Databasede olup servisten gelen güncel Ad Grubu userlarında olmayan userların silinmesi
                
                foreach (var usernameOfMappedUsers in listOfMappedUsersUsernames)
                {
                    if (!listOfRecentUsersUsernames.Contains(usernameOfMappedUsers))//içerdeki kullanıcıyı gezip servisten gelen güncel data'da varmı yani kullanıcı hala ad grubuna üye mi kontrolü? değilse sil
                    {
                        result = "10.Hata--------------- Mevcuttaki ad grubunu userlarından yeni güncel servisten gelen data içerisinde olmayan kulllanıcıların Artık mevcuttan dbden silinmesinden önceki hata";
                        LogicalGroupMemberHelper.DeleteMappedAdUserByUsername(mapItem.YYYSAdMemberId, usernameOfMappedUsers);

                    }
                }
                #endregion Databasede olup servisten gelen güncel Ad Grubu userlarında olmayan userların silinmesi

                result = "11.Hata--------------- DT_WORKFLOW.YYS_LOGICAL_GROUPS ve FRAMEWORK.F_WF_WORKFLOW_DEF tablolarınından logical grup id ile akış adı alınırken alınan hata";
                string FlowName = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.GetFlowNameByLogicalGroupId(mapItem.LogicalGroupId);


                if (listOfUsersFLoginOlmayanlar.Count > 0) //EŞLEŞMEYEN KULLANICI VARSA
                {
                    result = "12.Hata---------- burayı zülfiye ablaya sor f logın olmaması ile eşleşmemesi farklı kavramlar olabilir";
                    LogicalGroupJobHelper.SendUnDefinedLoginIdNew(FlowName, mapItem.LogicalGroupId.ToString(), mapItem.AdGroup + "/", listOfUsersFLoginOlmayanlar);

                } 

                #region LOGIN IDLERI AYNI OLUP LogicalGroupMember VE DP_HR_USERS TABLOSUNDA NAME SURNAME'LERİ ESLESMEYEN KAYITLARIN NAME SURNAME ALANINI DP_HRDAN UPDATE EDER.         
                result = "13------------LOGICAL_GROUP_MEMBER_TYPE_ID 1 ve 5 yanı KULLANICI VE AD USER OLAN IKI TIP ICIN DP HRDAN MEMBERSA GUNCEL NAME SURNAME ALANINI UPDATE EDEN METHODDA ALINAN HATA";
                LogicalGroupMemberHelper.UpdateUsersNameSurnameFromDpHr();
                #endregion LOGIN IDLERI AYNI OLUP LogicalGroupMember VE DP_HR_USERS TABLOSUNDA NAME SURNAME'LERİ ESLESMEYEN KAYITLARIN NAME SURNAME ALANINI DP_HRDAN UPDATE EDER.
                result = "1";
            }
            catch (Exception ex)
            {

                Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "YYS Prj YYS senk servisi hata mesajı:"+ex.Message, " domain Adı id: " + mapItem.AdDomain + " Ad grubu : " + mapItem.AdGroup + "mapID" + mapItem.YYYSAdMemberId.ToString());

                //result = ex.Message + " " + ex.StackTrace;
                //if (ex.InnerException != null)
                //{
                //    result += " Inner Exception" + ex.InnerException.Message + " " + ex.InnerException.StackTrace;
                //}
            }


            return result;
        }

        [WebMethod]
        public List<Digiturk.Workflow.Digiflow.Entities.LogicalGroup> GetAdWithlogicalGroups()
        {
            // string str =  SenkronizeLogicalGroup(new Digiturk.Workflow.Digiflow.Entities.LogicalGroup { AdDomainName = "DIGITURK", AdGroupName = "DP GIRIS ISLEM" });
            return Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.GetAdWithlogicalGroups();
        }

        [WebMethod]
        public List<Digiturk.Workflow.Digiflow.Entities.YYS.LogicalGroup.LogicalGroupAdMap> GetAllMappedAdGroupsOfLogicalGroups()
        {
            // string str =  SenkronizeLogicalGroup(new Digiturk.Workflow.Digiflow.Entities.LogicalGroup { AdDomainName = "DIGITURK", AdGroupName = "DP GIRIS ISLEM" });
            return Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupAdMapHelper.GetAllMappedAdGroupsOfLogicalGroups();
        }

        [WebMethod]
        public List<Digiturk.Workflow.Digiflow.Entities.LogicalGroupMember> GetAllUsersOfMappedAdGroup(long adMapId)
        {
            // string str =  SenkronizeLogicalGroup(new Digiturk.Workflow.Digiflow.Entities.LogicalGroup { AdDomainName = "DIGITURK", AdGroupName = "DP GIRIS ISLEM" });
            return Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupMemberHelper.GetAllLogicalGroupMembersListByAdMapId(adMapId);
        }        
    }
}
