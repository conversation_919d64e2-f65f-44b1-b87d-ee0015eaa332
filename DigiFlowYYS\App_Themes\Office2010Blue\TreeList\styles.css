.dxtlControl_Office2010Blue
{
	cursor: default;
	font: 8pt Verdana;
	color: Black;
	border: 1px solid #8ba0bc;
}
.dxtlControl_Office2010Blue caption
{
	font-size: 8pt;
	font-weight: normal;
	color: #1e395b;
	text-align: center;
	padding: 3px 3px 5px;
	text-align: center;
	background: #bdd0e7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.TreeList.CssImages.TitleBack.png")%>') repeat-x left top;
	border: 1px solid #8ba0bc;
	border-bottom: 0;
}

/* Indent cells */
.dxtlIndent_Office2010Blue,
.dxtlIndentWithButton_Office2010Blue
{
	background: white;
	vertical-align: top;
	background-position: center top;
	background-repeat: no-repeat;
}
.dxtlIndent_Office2010Blue
{
	padding: 0 11px;
}
.dxtlIndentWithButton_Office2010Blue
{
	padding: 4px 5px;
}
.dxtlSelectionCell_Office2010Blue
{
	padding: 0 2px;
	border-width: 0;
}

/* Tree-lines cells */
.dxtlLineRoot_Office2010Blue,
.dxtlLineFirst_Office2010Blue,
.dxtlLineMiddle_Office2010Blue,
.dxtlLineLast_Office2010Blue,
.dxtlLineFirstRtl_Office2010Blue,
.dxtlLineMiddleRtl_Office2010Blue,
.dxtlLineLastRtl_Office2010Blue
{
    background-color: Transparent;
}

.dxtlIndent_Office2010Blue,
.dxtlIndentWithButton_Office2010Blue
{
    background-color: White;
}

/* Headers */
.dxtlHeader_Office2010Blue
{
	background: #e4eefa url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.TreeList.CssImages.HeaderBack.png")%>') left top repeat-x;
	border: 1px solid #8ba0bc;
	padding: 5px 6px;
	font-weight: normal;
	color: #1e395b;
}
.dxtlHeader_Office2010Blue table.dxtl
{
	border-collapse: collapse;
	width: 100%;
}
.dxtlHeader_Office2010Blue td.dxtl
{
	padding: 0;
}
.dxtlHeader_Office2010Blue,
.dxtlHeader_Office2010Blue td.dxtl
{
	font: 8pt Verdana;
	white-space: nowrap;
	text-align: left;
}

/* Nodes */
.dxtlNode_Office2010Blue
{
	background: white;
}
.dxtlAltNode_Office2010Blue
{
	background: #f4f6f9;
}
.dxtlAltNode_Office2010Blue .dxtlIndent_Office2010Blue,
.dxtlSelectedNode_Office2010Blue .dxtlIndent_Office2010Blue,
.dxtlFocusedNode_Office2010Blue .dxtlIndent_Office2010Blue
{
    background: White;
}
.dxtlSelectedNode_Office2010Blue
{
	background: #faedb6;
	color: Black;
}
.dxtlFocusedNode_Office2010Blue
{
	background: #fdf7d9;
    color: Black;
}
.dxtlInlineEditNode_Office2010Blue
{
	background: white;
}
.dxtlEditFormDisplayNode_Office2010Blue,
.dxtlEditFormDisplayNode_Office2010Blue td.dxtl
{
	background: #ecf2f9;
}

.dxtlNode_Office2010Blue td.dxtl,
.dxtlAltNode_Office2010Blue  td.dxtl,
.dxtlSelectedNode_Office2010Blue td.dxtl,
.dxtlFocusedNode_Office2010Blue td.dxtl,
.dxtlEditFormDisplayNode_Office2010Blue td.dxtl,
.dxtlCommandCell_Office2010Blue
{
	padding: 4px 6px;
	border-width: 0;
	white-space: nowrap;
	font-size: 8pt;
	font-family: Verdana;
}
.dxtlEditFormDisplayNode_Office2010Blue td.dxtl,
.dxtlEditFormDisplayNode_Office2010Blue td.dxtl__cc
{
    border-bottom: 1px solid #cfddee;
}
.dxtlInlineEditNode_Office2010Blue td.dxtl
{
	border-width: 0;
	padding: 1px;
}

/* Preview */
.dxtlPreview_Office2010Blue
{
	background: #e9edf3;
	color: #777777;
	padding: 14px;
	border-width: 0;
	font: 8pt Verdana;
}

/* Footers */
.dxtlGroupFooter_Office2010Blue,
.dxtlFooter_Office2010Blue
{
	background-color: #e2ebf6;
}
.dxtlGroupFooter_Office2010Blue td.dxtl,
.dxtlFooter_Office2010Blue td.dxtl
{
	padding: 6px;
	white-space: nowrap;
	border: 1px solid #cfddee;
	border-left-width: 0 !important;
	font: 8pt Verdana;
}

/* Pagers */
.dxtlPagerTopPanel_Office2010Blue,
.dxtlPagerBottomPanel_Office2010Blue
{
    background: #e4effa;
}
.dxtlPagerTopPanel_Office2010Blue
{
	border-bottom: 1px solid #8ba0bc;
	padding-bottom: 2px;
}
.dxtlPagerBottomPanel_Office2010Blue
{
	border-top: 1px solid #8ba0bc;
	padding-bottom: 2px;
}

/* Editing */
.dxtlEditForm_Office2010Blue
{
	background: #ecf2f9;
	border-width: 0;
	padding: 8px 10px 10px;
}
.dxtlEditFormCaption_Office2010Blue,
.dxtlEditFormEditCell_Office2010Blue
{
	padding: 4px;
}
.dxtlEditFormCaption_Office2010Blue
{
	padding-left: 10px;
	white-space: nowrap;
}
.dxtlError_Office2010Blue
{
	background: #cfddee;
	color: #ba1717;
	padding: 6px 10px;
	border-width: 0;
	font: 8pt Verdana;
}
.dxtlPopupEditForm_Office2010Blue
{
    padding: 12px;
}

/* Links */
.dxtlControl_Office2010Blue a
{
	color: #1e395b;
	text-decoration: none;
}
.dxtlControl_Office2010Blue a:hover
{
	text-decoration: underline;
}
.dxtlHeader_Office2010Blue a
{
    text-decoration: underline;
}
.dxtlSelectedNode_Office2010Blue a,
.dxtlFocusedNode_Office2010Blue a
{
	color: #1e395b;
	text-decoration: none;
}
.dxtlSelectedNode_Office2010Blue a:hover,
.dxtlFocusedNode_Office2010Blue a:hover
{
	text-decoration: underline;
}
.dxtlCommandCell_Office2010Blue a
{
	margin-right: 3px;
}

/* Loading panel */
.dxtlLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxtlLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxtlLoadingDiv_Office2010Blue
{
	background: white;
	opacity: 0.01;
	filter: alpha(opacity=1);
}

/* Disabled */
.dxtlDisabled_Office2010Blue,
.dxtlDisabled_Office2010Blue .dxtl
{
	color: #b2b7bd;
	cursor: default;
}