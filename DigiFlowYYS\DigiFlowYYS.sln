﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 14
VisualStudioVersion = 14.0.25420.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "DigiFlowYYS", "..\DigiFlowYYS", "{C3AA136F-8FAE-4DFF-A1DE-E2B7E42BA5F1}"
	ProjectSection(WebsiteProperties) = preProject
		SccProjectName = "SAK"
		SccAuxPath = "SAK"
		SccLocalPath = "SAK"
		SccProvider = "SAK"
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv3.5"
		Debug.AspNetCompiler.VirtualPath = "/DigiFlowYYS"
		Debug.AspNetCompiler.PhysicalPath = "..\DigiFlowYYS\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\DigiFlowYYS\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/DigiFlowYYS"
		Release.AspNetCompiler.PhysicalPath = "..\DigiFlowYYS\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\DigiFlowYYS\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "4670"
		VWDDynamicPort = "false"
	EndProjectSection
EndProject
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "Digiturk.Workflow.Digiflow.Entities", "..\..\Digiturk.Workflow.Digiflow.Entities", "{20E1AE09-B7E1-4AE7-850D-E52744802D4B}"
	ProjectSection(WebsiteProperties) = preProject
		SccProjectName = "SAK"
		SccAuxPath = "SAK"
		SccLocalPath = "SAK"
		SccProvider = "SAK"
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.0"
		Debug.AspNetCompiler.VirtualPath = "/Digiturk.Workflow.Digiflow.Entities"
		Debug.AspNetCompiler.PhysicalPath = "..\..\Digiturk.Workflow.Digiflow.Entities\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\Digiturk.Workflow.Digiflow.Entities\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/Digiturk.Workflow.Digiflow.Entities"
		Release.AspNetCompiler.PhysicalPath = "..\..\Digiturk.Workflow.Digiflow.Entities\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\Digiturk.Workflow.Digiflow.Entities\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "51138"
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C3AA136F-8FAE-4DFF-A1DE-E2B7E42BA5F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3AA136F-8FAE-4DFF-A1DE-E2B7E42BA5F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20E1AE09-B7E1-4AE7-850D-E52744802D4B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20E1AE09-B7E1-4AE7-850D-E52744802D4B}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 3
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://code.digiturk.net:8080/defaultcollection
		SccLocalPath0 = .
		SccWebProject1 = true
		SccProjectUniqueName1 = .
		SccLocalPath1 = .
		SccProjectEnlistmentChoice1 = 2
		SccWebProject2 = true
		SccProjectUniqueName2 = ..\\..\\Digiturk.Workflow.Digiflow.Entities
		SccProjectName2 = ../../Digiturk.Workflow.Digiflow.Entities
		SccLocalPath2 = ..\\..\\Digiturk.Workflow.Digiflow.Entities
		SccProjectEnlistmentChoice2 = 2
	EndGlobalSection
EndGlobal
