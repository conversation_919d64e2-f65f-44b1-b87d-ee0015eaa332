﻿using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Domain.Entities.Framework;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using DigiflowAPI.Application.DTOs.Common;

namespace DigiflowAPI.Infrastructure.Services
{
    public class UserService(IOracleDataAccessRepositoryFactory repositoryFactory, ILogger<UserService> logger, IUserRepository userRepository, IHttpContextAccessor httpContextAccessor, IGlobalHelpers globalHelpers) : IUserService
    {
        public async Task<long> GetUserInfo()
        {
            try
            {
                var loginIdHeader = httpContextAccessor.HttpContext.Request.Headers["X-Login-Id"].ToString();
                if (loginIdHeader == "0")
                {
                    loginIdHeader = (await globalHelpers.GetUserId()).ToString();
                }

                var windowsUserId = globalHelpers.GetUserId();
                var isAdmin = globalHelpers.IsSystemAdmin();

                var username = globalHelpers.GetUserName();
                Console.WriteLine($"Username: {username}"); // Logging

                // Check if user is authenticated
                if (string.IsNullOrEmpty(username))
                {
                    var currentUser = httpContextAccessor.HttpContext?.User;
                    if (currentUser?.Identity?.IsAuthenticated != true)
                    {
                        throw new UnauthorizedAccessException("User is not authenticated");
                    }

                    // Try to get username from claims for JWT users
                    if (currentUser.Identity.AuthenticationType == "AuthenticationTypes.Federation" ||
                        httpContextAccessor.HttpContext.Items.ContainsKey("AuthType") &&
                        httpContextAccessor.HttpContext.Items["AuthType"]?.ToString() == "JWT")
                    {
                        username = currentUser.Identity.Name;
                        if (!string.IsNullOrEmpty(username))
                        {
                            Console.WriteLine($"Got username from JWT claims: {username}");
                        }
                    }

                    if (string.IsNullOrEmpty(username))
                    {
                        throw new Exception($"Username is null or empty - authentication type: {currentUser.Identity.AuthenticationType}");
                    }
                }

                if (isAdmin && loginIdHeader != windowsUserId.ToString())
                {
                    // If loginIdHeader is not empty and not numeric, skip impersonation
                    if (!string.IsNullOrEmpty(loginIdHeader) && !long.TryParse(loginIdHeader, out var loginId))
                    {
                        logger.LogWarning($"Admin user provided non-numeric loginIdHeader: {loginIdHeader}, skipping impersonation");
                    }
                    else
                    {
                        loginIdHeader = loginIdHeader != "" ? loginIdHeader : (await globalHelpers.GetUserId()).ToString();
                        var userInfo = await GetByIdAsync(Convert.ToInt64(loginIdHeader));
                        if (userInfo == null)
                        {
                            throw new Exception("User info not found");
                        }
                        username = userInfo.Username;
                    }
                }

                var parts = username.Split('\\');
                var name = parts.Length > 1 ? parts[1] : username;

                var domainUserName = name.ToUpper()
                    .Replace("İ", "I")
                    .Replace("Ö", "O")
                    .Replace("Ü", "U");

                var parameters = new Dictionary<string, object>
                {
                    { "DomainUserName", domainUserName }
                };

                return await userRepository.GetUserInfo(parameters);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetUserInfo: {ex.Message}");
                throw; // Re-throw the exception after logging
            }
        }

        public async Task<DpHrUsers?> GetUserInfo(long? userId = null)
        {
            var queryParams = new Dictionary<string, object>
                {
                    { ":LoginId", userId ?? await GetUserInfo() }
                };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var result = await repository.ExecuteQueryAsync<DpHrUsers>(
                @"SELECT * FROM DT_WORKFLOW.DP_HR_USERS WHERE DT_WORKFLOW.DP_HR_USERS.F_LOGIN_ID = :LoginId",
                queryParams
            );

            if (result == null || result.Count == 0)
            {
                return null;
            }

            return result.FirstOrDefault();
        }

        public async Task<FLogin?> GetFLoginInfo(long? loginId)
        {
            long activeUserId = await globalHelpers.GetUserId();
            var queryParams = new Dictionary<string, object>
                {
                    { ":LoginId", loginId != null ? loginId : activeUserId }
                };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var result = await repository.ExecuteQueryAsync<FLogin>(
                @"SELECT * FROM FRAMEWORK.F_LOGIN WHERE FRAMEWORK.F_LOGIN.LOGIN_ID = :LoginId",
                queryParams
            );

            if (result == null || result.Count == 0)
            {
                return null;
            }

            return result.FirstOrDefault();
        }

        public async Task<string> GetActiveUserNameAsync()
        {
            var loginIdHeader = httpContextAccessor.HttpContext.Request.Headers["X-Login-Id"].ToString();
            if (loginIdHeader == "0")
            {
                loginIdHeader = (await globalHelpers.GetUserId()).ToString();
            }

            var windowsUserId = globalHelpers.GetUserId();
            var isAdmin = globalHelpers.IsSystemAdmin();

            if (isAdmin && loginIdHeader != windowsUserId.ToString())
            {
                var userInfo = await GetUserInfo(Convert.ToInt64(loginIdHeader));
                return userInfo.Username;
            }

            return await Task.FromResult(globalHelpers.GetUserName());
        }
        public async Task<long> GetActiveUserIdAsync()
        {
            var loginIdHeader = httpContextAccessor.HttpContext.Request.Headers["X-Login-Id"].ToString();
            if (loginIdHeader == "0")
            {
                loginIdHeader = (await globalHelpers.GetUserId()).ToString();
            }

            var windowsUserId = globalHelpers.GetUserId();
            var isAdmin = globalHelpers.IsSystemAdmin();

            if (isAdmin && loginIdHeader != windowsUserId.ToString())
            {
                var userInfo = await GetUserInfo(Convert.ToInt64(loginIdHeader));
                return (long)userInfo.FLoginId;
            }

            return await globalHelpers.GetUserId();
        }

        public async Task<DpHrDeps?> GetDepartmentById(decimal departmentId)
        {
            return await userRepository.GetDepartmentById(departmentId);
        }

        public async Task<IEnumerable<DpHrDeps>> GetDepartmentsAtLevel(long ustBolumId)
        {
            return await userRepository.GetDepartmentsAtLevel(ustBolumId);
        }

        public async Task<long> GetUserDepartmentId(long userId)
        {
            return await userRepository.GetUserDepartmentId(userId);
        }

        public async Task<IEnumerable<VwUserInformation>> GetUsersByDepartmentId(long departmentId, bool excludeActiveUser = false)
        {
            return await userRepository.GetUsersByDepartmentId(departmentId, excludeActiveUser);
        }


        public async Task<IEnumerable<VwUserInformation>> GetForwardPersonel(string username)
        {
            return await userRepository.GetForwardPersonel(username);
        }

        public async Task<IEnumerable<VwUserInformation?>> GetAllAsync()
        {
            return await userRepository.GetAllAsync();
        }

        public async Task<VwUserInformation?> GetByIdAsync(long id)
        {
            return await userRepository.GetByIdAsync(id);
        }

        public async Task<VwUserInformation?> GetByUsernameAsync(string username)
        {
            return await userRepository.GetByUsernameAsync(username);
        }

        public async Task<IEnumerable<SelectOptionDto>> GetUsersByDepartmentIdAsync(long departmentId, bool excludeActiveUser = false)
        {
            var users = await userRepository.GetUsersByDepartmentId(departmentId, excludeActiveUser);
            return users.Select(u => new SelectOptionDto
            {
                Value = u.LoginId.ToString(),
                Label = u.NameSurname,
                LabelEn = u.NameSurname
            });
        }
    }
}

