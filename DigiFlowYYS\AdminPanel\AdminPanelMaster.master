﻿<%@ Master Language="C#" AutoEventWireup="true" CodeFile="AdminPanelMaster.master.cs" Inherits="AdminPanel_AdminPanelMaster" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>
<%@ Register Assembly="DevExpress.Web.v10.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxMenu" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v10.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>DigiFlow</title>
    <script type="text/javascript" src="../js/digiturk.js"></script>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
    <link href="../css/digiturk.css" rel="stylesheet" type="text/css" />
    <link type="text/css" href="../css/blitzer/jquery-ui-1.8.5.custom.css" rel="stylesheet" />
</head>
<body>
    <form id="form1" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true"
            EnableScriptLocalization="true">
        </asp:ScriptManager>
        <asp:UpdatePanel ID="MainUpdatePanel" runat="server">
            <ContentTemplate>
                <asp:Button runat="server" ID="HiddenForModal" Style="display: none" />
                <asp:ModalPopupExtender ID="Modal1" runat="server" TargetControlID="HiddenForModal"
                    PopupControlID="PopupPanel" BackgroundCssClass="modalBackground" OkControlID="Button1" />
                <table border="0" width="955" style="height: 100%" cellspacing="0" cellpadding="0" bgcolor="#000000" align="center">
                    <tr>
                        <td>
                            <asp:Literal ID="StatusLiteral" runat="server"></asp:Literal></td>
                    </tr>
                    <tr>
                        <td align="left" width="100%" style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000; border-bottom: 1px solid #000000;"
                            bgcolor="#FFFFFF" height="400" valign="top">
                            <asp:Panel ID="PopupPanel" runat="server" CssClass="modalPopup">
                                <asp:Literal ID="Literal1" runat="server"></asp:Literal>
                                <br />
                                <p align="center">
                                    <asp:LinkButton ID="Button1" runat="server">Kapat</asp:LinkButton>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:HyperLink ID="HyperLink1" runat="server"
                                    NavigateUrl="javascript:history.go(-1);" Visible="false">Geri Dön</asp:HyperLink>
                                </p>
                            </asp:Panel>
                            <div>
                                <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                                </asp:ContentPlaceHolder>
                            </div>
                        </td>
                    </tr>
                </table>
                <br />
                <asp:UpdateProgress ID="up2" runat="server" AssociatedUpdatePanelID="MainUpdatePanel" DisplayAfter="100">
                    <ProgressTemplate>
                        <div id="progressBackgroundFilter"></div>
                        <div id="processMessage">
                            Lütfen bekleyin...<br />
                            <br />
                            <img alt="Lütfen bekleyin..." src="../images/processing.gif" />
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
            </ContentTemplate>
        </asp:UpdatePanel>
    </form>
</body>
</html>