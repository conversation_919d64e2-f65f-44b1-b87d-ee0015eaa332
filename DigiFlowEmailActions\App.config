﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<configSections>
		<section name="bccMailList" type="Digiturk.Workflow.Common.WorkflowMailerSection, Digiturk.Workflow.Common" />
		<section name="debugMailList" type="Digiturk.Workflow.Common.WorkflowMailerSection, Digiturk.Workflow.Common" />
	</configSections>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2" />
	</startup>
	<appSettings>
		<add key="WebServiceUrl" value="https://webmail.digiturk.com.tr/ews/Exchange.asmx"/>
		<add key="UserName_Live" value="digiflowemailactions"/>
		<add key="UserName_Test" value="dtflowactiontst"/>
		<add key="UserPassword" value="f+miP1tUP1v4r9G"/>
		<add key="EmailsTopCount" value="100"/>
		<add key="ProjectName" value="DigiflowEmailActions"/>
		<!--********************************************************************-->
		<add key="ReadConfigFilesFromXML" value="false" />
		<add key="DefinitionConfigration" value="C:\TFS\DigiFlowPM\Digiturk.Workflow.DigiFlow_v3\WFPages\Definiton.xml" />
		<add key="LogicalGroupDefinition" value="C:\TFS\DigiFlowPM\Digiturk.Workflow.DigiFlow_v3\WFPages\LogicalGroups.xml" />
		<add key="StateDefinition" value="C:\TFS\DigiFlowPM\Digiturk.Workflow.DigiFlow_v3\WFPages\StateDefinition.xml" />
		<!--email settings-->
		<add key="Workflow.Mail.EmbeddedImagesPath" value="\\dtl1iis3\Deployment\MailImages" />
		<add key="Workflow.Mail.Params" value="ad9bBOUpHG1st9IlCOvZA9DCTJKj7XTlewXqZpa4xWo/m0f/ZXwzFpTy9cdYK53Hx2MQqWxlyxSVT5lg5waY6LC3p5i77oc4pHAEGgnKFbAuL48SNlMELo9dIiUOo2RmdTprZ/SAkyKF03+gmRGRexw3+qCFnr/iVOx/58S075o=" />
		<add key="Workflow.Mail.Server" value="************" />
		<add key="Workflow.Mail.FromAddress" value="<EMAIL>" />
		<!--<add key="Workflow.Mail.LinkDomain" value="http://digiflowtest" />-->
		<add key="Workflow.Mail.LinkDomain" value="http://digiflowtest.digiturk.com.tr" />
		<!--email settings-->
		<!--Domain dışına email gönderim settings-->
		<add key="noreply_workflow.Mail.Server" value="************" />
		<add key="noreply_workflow.Mail.FromAddress" value="<EMAIL>" />
		<add key="noreply_workflow.Mail.FromAddressUserName" value="noreply_workflow" />
		<add key="noreply_workflow.Mail.FromAddressPsw" value="flow123456+" />
		<!--Domain dışına email gönderim settings-->
		<!-- Ticari akış hata bilgilendirme mailleri -->
		<add key="TicariCCAddress" value="<EMAIL>" />
		<add key="UzaktanCalismaLimit" value="14" />
		<add key="UzaktanCalismaSirket" value="1" />
		<add key="UzaktanCalismaResmiTarih" value="19.07.2021" />
		<add key="UzaktanCalismaEndDate" value="03.10.2022"/>
		<add key="yayinKurulumProcessType" value="57" />
		<add key="yayinProcessType" value="58" />
		<add key="yayinKurulumProcessTypeTest" value="52" />
		<add key="yayinProcessTypeTest" value="53" />
		<add key="KreaSakaryaSirket" value="1" />
		<!--Domain dışına email gönderim settings-->
		<add key="Workflow.Mail.IsMailDebugMode" value="True" />
		<add key="NotificationTemplateID" value="1000" />
		<add key="DBSTEST_*******" value="*******" />
		<add key="ITTPTEST_*******" value="*******" />
		<add key="DBSLIVE_*******" value="MARACANA" />
		<add key="DBSLIVE_*******_APPSTR" value="Digiflow" />
		<add key="DBSLIVE_*******_UNIQUE" value="4!fedL0w" />
		<add key="DBSTEST_*******_APPSTR" value="Digiflow" />
		<add key="DBSTEST_*******_UNIQUE" value="4!fedL0w" />
		<add key="ITTPTEST_*******_APPSTR" value="Digiflow" />
		<add key="ITTPTEST_*******_UNIQUE" value="4!fedL0w" />
		<add key="SUBSET15_*******_APPSTR" value="Digiflow" />
		<add key="SUBSET15_*******_UNIQUE" value="4!fedL0w" />
		<add key="SUBSET15_RAPOR_APPSTR" value="Rota"/>
		<add key="SUBSET15_RAPOR_UNIQUE" value="8up!5f0rrt"/>
		<add key="DBSLIVE_RAPOR_APPSTR" value="Rota"/>
		<add key="DBSLIVE_RAPOR_UNIQUE" value="8up!5f0rrt"/>
		<add key="ESSBDurum" value="E" />
		<add key="HrServicesEndPointDefaul" value="http://dtl1iis4:3331/PersonelBilgisi.asmx" />
		<add key="HrServicesEndPointMedia" value="http://dtl1iis4:3335/PersonelBilgisi.asmx" />
		<add key="AvansEndPointDefaul" value="http://dtl1iis4:20003/Service.asmx" />
		<add key="HizmetAlimEposta" value="<EMAIL>"/>
	</appSettings>
	<connectionStrings>
		<add name="FrameworkConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=FRAMEWORK;Password=FRAMEWORK;Pooling=true;Self Tuning=false" />
		<add name="DefaultConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR;Pooling=true;Self Tuning=false" />
		<add name="ReportConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=true;Self Tuning=false" />	
		<add name="DBSConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SID=KURUMSAL.DIGITURK.LOCAL)));User Id=*******;Password=*******;Self Tuning=false" />
		<add name="connStrESSB_TEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SUBSET15)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient" />
		<add name="connStrESSB_LIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = dbs.digiturk.local)(PORT = 1521))(CONNECT_DATA = (SERVICE_NAME = CORP)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient" />
		<add name="DBSLIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = dbs.digiturk.local)(PORT = 1521))(CONNECT_DATA = (SERVICE_NAME = CORP)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
		<add name="DBSTEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=DBSTEST)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
		<add name="ITTPTEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ITTPTEST)));User Id={0};Password=***" providerName="System.Data.OracleClient" />
		<add name="SUBSET15" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=SUBSET15)));User Id={0};Password=***" providerName="System.Data.OracleClient" />		
	</connectionStrings>
	<bccMailList>
		<mailAddresses>
			<add address="<EMAIL>" displayName="İş Akışı Test1" />
			<add address="<EMAIL>" displayName="İş Akışı Test2" />
			<add address="<EMAIL>" displayName="İş Akışı Test3" />
			<add address="<EMAIL>" displayName="İş Akışı Test4" />
			<add address="<EMAIL>" displayName="İş Akışı Test5" />
			<add address="<EMAIL>" displayName="İş Akışı Test6" />
		</mailAddresses>
	</bccMailList>
	<debugMailList>
		<mailAddresses>
			<add address="<EMAIL>" displayName="İş Akışı Test1" />
			<add address="<EMAIL>" displayName="İş Akışı Test2" />
			<add address="<EMAIL>" displayName="İş Akışı Test3" />
			<add address="<EMAIL>" displayName="İş Akışı Test4" />
			<add address="<EMAIL>" displayName="İş Akışı Test5" />
			<add address="<EMAIL>" displayName="İş Akışı Test6" />
		</mailAddresses>
	</debugMailList>
</configuration>