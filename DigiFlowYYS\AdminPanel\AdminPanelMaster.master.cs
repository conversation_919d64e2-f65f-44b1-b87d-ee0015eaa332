﻿using Digiturk.Workflow.Digiflow.WebCore.MasterPage;
using System;
using System.Web;
using System.Web.UI;

public partial class AdminPanel_AdminPanelMaster : AdminPanelMaster
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
        }
    }

    /// <summary>
    /// Sayfada popup mesaj görüntülemek için kull<PERSON>ı<PERSON>
    /// </summary>
    /// <param name="hyperlinkVisible"></param>
    /// <param name="title"></param>
    /// <param name="message"></param>
    /// <param name="isError"></param>
    /// <param name="trace"></param>
    public void ShowPopup(bool hyperlinkVisible, string title, string message, bool isError, string trace)
    {
        HyperLink1.Visible = hyperlinkVisible;
        if (isError)
        {
            if (string.IsNullOrEmpty(trace))
            {
                Literal1.Text = Common.ShowError(title, message);
            }
            else
            {
                Literal1.Text = Common.ShowError(title, message, trace);
            }
        }
        else
        {
            Literal1.Text = Common.ShowInformation(title, message);
        }
        Literal1.Visible = true;
        Modal1.Show();
    }

    /// <summary>
    /// Displays an error at master page
    /// </summary>
    /// <param name="title">Title of the message</param>
    /// <param name="message">Error message</param>
    public void ShowError(string title, string message)
    {
        StatusLiteral.Text = Common.ShowError(title, message);
    }

    /// <summary>
    /// Displays an error at master page
    /// </summary>
    /// <param name="title">Title of the message</param>
    /// <param name="details">Details of the message</param>
    /// <param name="message">Error message</param>
    public void ShowError(string title, string message, string details)
    {
        StatusLiteral.Text = Common.ShowError(title, message, details);
    }

    /// <summary>
    /// Displays an information at master page
    /// </summary>
    /// <param name="title">Title of the message</param>
    /// <param name="message">Error message</param>
    public void ShowInformation(string title, string message)
    {
        StatusLiteral.Text = Common.ShowInformation(title, message);
    }

    /// <summary>
    /// Displays an information at master page
    /// </summary>
    /// <param name="title">Title of the message</param>
    /// <param name="message">Error message</param>
    /// <param name="appendHomePageLink">Appends return to home page link to the information message</param>
    public void ShowInformation(string title, string message, bool appendHomePageLink)
    {
        if (appendHomePageLink)
        {
            message += "<p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><a href='Default.aspx'><b><- Anasayfaya Dön</b></a>";
        }

        StatusLiteral.Text = Common.ShowInformation(title, message);
    }

    public void ScriptManagerRegisterAsyncPostBackControl(Control Controls)
    {
        this.ScriptManager1.RegisterAsyncPostBackControl(Controls);
    }
}