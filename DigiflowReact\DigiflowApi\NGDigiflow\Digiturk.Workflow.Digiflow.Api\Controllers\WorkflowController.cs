﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Api.Common.DataTransferObjects.Workflow;
using Digiturk.Workflow.Digiflow.Api.Common.Delegation;
using Digiturk.Workflow.Digiflow.Api.Common.Results;
using Digiturk.Workflow.Digiflow.Authentication;
using Digiturk.Workflow.Digiflow.Authorization;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http;

namespace Digiturk.Workflow.Digiflow.Api.Controllers
{    
    public class WorkflowController : ApiController
    {
        public static List<DTOWorkflowDefinition> WorkflowDefinitionList {get;set;}

        public string UserName
        {
            get
            {
                if (HttpContext.Current.User.Identity.IsAuthenticated)
                {
                    return HttpContext.Current.User.Identity.Name.Replace(@"DIGITURK\", "");
                }
                else
                {
                    return HttpContext.Current.Request.LogonUserIdentity.Name.Replace(@"DIGITURK\", "");
                }
            }
        }


        public WorkflowController()
        {
            DataTable dt = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.DelegationHelper.GetWorkFlowDataTable();
            WorkflowDefinitionList = new List<DTOWorkflowDefinition>();
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                DTOWorkflowDefinition dTOWorkflowDefinition = new DTOWorkflowDefinition();
                dTOWorkflowDefinition.WfDefinitionId = long.Parse(dt.Rows[i]["WfDefId"].ToString());
                dTOWorkflowDefinition.WfDefinitionName = dt.Rows[i]["WfDefName"].ToString();
                WorkflowDefinitionList.Add(dTOWorkflowDefinition);
            }
        }


        [HttpGet]
        [Route("api/getworkflowlist")]
        public IEnumerable<DTOWorkflowDefinition> GetWorkflowList()
        {
            return WorkflowDefinitionList;
        }


        public IHttpActionResult GetSingle(int id)
        {
            //HouseEntity houseEntity = _houseRepository.GetSingle(id);

            //if (houseEntity == null)
            //{
            //    return NotFound();
            //}

            //return Ok(_houseMapper.MapToDto(houseEntity));
            return Ok();
        }


        [HttpPost]
        [Route("api/CreateWorkflow")]
        public bool CreateWorkflow(Digiturk.Workflow.Digiflow.Entities.DelegationRequest dlgReq, long workflowDefId,long LoginId)
        {
            Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.CreateWorkFlow(dlgReq, workflowDefId, LoginId);
            return true;
        }


        [HttpPost]
        [Route("api/CreateWorkflowDelegation")]
        public WorkflowResult CreateWorkflowDelegation(DTODelegation dTO)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {
                long LoginId = dTO.LoginId;
                long WorkflowDefId = dTO.WorkflowDefId;

                Digiturk.Workflow.Digiflow.Entities.DelegationRequest dlgReq = new Entities.DelegationRequest();
                dlgReq.Created = DateTime.Today;
                dlgReq.CreatedBy = dTO.LoginId;
                dlgReq.OwnerLoginId = dTO.OwnerLoginId;
                dlgReq.WorkflowDefId = dTO.WorkflowDefId;
                dlgReq.DelegationComment = dTO.DelegationComment;
                dlgReq.DelegatedLoginId = dTO.DelegatedLoginId;
                dlgReq.StartTime = dTO.StartTime;
                dlgReq.EndTime = dTO.EndTime;  
                dlgReq.WorkFlowIds = dTO.WorkflowIds;
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.CreateWorkFlow(dlgReq, dTO.WorkflowDefId, dTO.LoginId);
                result.Result = true;
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result; 
        }


        [HttpPost]
        [Route("api/CreateWorkflowDelegationWithValidation")]
        public WorkflowResult CreateWorkflowDelegationWithValidation(DTODelegation dTO)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {
                long LoginId = dTO.LoginId;
                long WorkflowDefId = dTO.WorkflowDefId;

                Digiturk.Workflow.Digiflow.Entities.DelegationRequest dlgReq = new Entities.DelegationRequest();
                dlgReq.Created = DateTime.Today;
                dlgReq.CreatedBy = dTO.LoginId;
                dlgReq.OwnerLoginId = dTO.OwnerLoginId;
                dlgReq.WorkflowDefId = dTO.WorkflowDefId;
                dlgReq.DelegationComment = dTO.DelegationComment;
                dlgReq.DelegatedLoginId = dTO.DelegatedLoginId;
                dlgReq.StartTime = dTO.StartTime;
                dlgReq.EndTime = dTO.EndTime;
                dlgReq.LastUpdated = dTO.LastUpdated;
                dlgReq.LastUpdatedBy = dTO.LastUpdatedBy;
                dlgReq.VersionID = dTO.VersionID;
                dlgReq.WorkFlowIds = dTO.WorkflowIds;
                //Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.CreateWorkFlow(dlgReq, WorkflowDefId, LoginId);
                Digiturk.Workflow.Digiflow.Api.ActionHelper.CreateWorkFlow(dlgReq, WorkflowDefId, LoginId);
                result.Result = true;
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result;
        }


        [HttpPost]
        [Route("api/AcceptWorkflow")]
        public WorkflowResult AcceptWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {
                long WorkflowInstanceId = dTOWorkflowProcesses.WorkflowInstanceId;
                string Comment = dTOWorkflowProcesses.Comment;
                FWfActionTaskInstance CurrentActionTaskInstance = null;
                FWfWorkflowInstance CurrentWfIns = null;
                WFContext CurrentWFContext = null;
                FLogin AssignedUser = null;
                FLogin LoginObject = null;
                using (UnitOfWork.Start(true))
                {
                    CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                    CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                    CurrentWFContext = new WFContext(CurrentWfIns);
                    AssignedUser = FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                    LoginObject = DomainAuthentication.GetCurrentLogin(true, dTOWorkflowProcesses.LoginId, UserName);
                    ActionHelpers.VersionUpdateToEntity(WorkflowInstanceId, CurrentActionTaskInstance.WfActionInstanceId);
                    Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(WorkflowInstanceId, CurrentActionTaskInstance, LoginObject, CurrentWFContext, AssignedUser, true, Comment);
                    result.Result = true;
                    result.Description = "Başarılı";
                    //UnitOfWork.Commit();
                }
                //FlowAdminOperationChecking(FlowAdminOprs);
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result;
        }


        [HttpPost]
        [Route("api/RejectWorkflow")]
        public WorkflowResult RejectWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {
                long WorkflowInstanceId = dTOWorkflowProcesses.WorkflowInstanceId;
                string Comment = dTOWorkflowProcesses.Comment;
                if (string.IsNullOrEmpty(Comment))
                {
                    throw Digiturk.Workflow.Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
                }
                FlowAdminOprObject FlowAdminOprs;
                using (UnitOfWork.Start(true))
                {
                    FWfWorkflowInstance CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                    FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
                    FWfActionTaskInstance CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                    WFContext CurrentWFContext = new WFContext(CurrentWfIns);
                    FLogin AssignedUser = FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                    FLogin LoginObject = DomainAuthentication.GetCurrentLogin(true, dTOWorkflowProcesses.LoginId, UserName);
                    Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.RejectWorkFlow(WorkflowInstanceId, CurrentActionTaskInstance, LoginObject, CurrentWFContext, AssignedUser, UserActionHistoryActionType.REJECTED, Comment);
                    //UnitOfWork.Commit();
                    result.Result = true;
                    result.Description = "Başarılı";
                }
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result;
        }


        [HttpPost]
        [Route("api/ForwardWorkflow")]
        public WorkflowResult ForwardWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {                
                WorkflowHelper.ActionHelper.ForwardWorkFlow(dTOWorkflowProcesses, UserName);
                result.Result = true;
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result;
        } 


        [HttpPost]
        [Route("api/SendCommentWorkFlow")]
        public WorkflowResult SendCommentWorkFlow(DTOWorkflowProcesses dTOWorkflowProcesses)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {
                WorkflowHelper.ActionHelper.SendCommentWorkFlow(dTOWorkflowProcesses, UserName);
                result.Result = true;
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result;
        }


        [HttpPost]
        [Route("api/Commented")]
        public WorkflowResult Commented(DTOWorkflowProcesses dTOWorkflowProcesses)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {
                WorkflowHelper.ActionHelper.CommentedWorkflow(dTOWorkflowProcesses, UserName);
                result.Result = true;
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result;
        }


        [HttpPost]
        [Route("api/SuspendWorkflow")]
        public WorkflowResult SuspendWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {
                WorkflowHelper.ActionHelper.SuspendWorkflow(dTOWorkflowProcesses, UserName);
                result.Result = true;
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result;
        }


        [HttpPost]
        [Route("api/ResumeWorkflow")]
        public WorkflowResult ResumeWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {
                WorkflowHelper.ActionHelper.ResumeWorkflow(dTOWorkflowProcesses, UserName);
                result.Result = true;
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result;
        }


        [HttpPost]
        [Route("api/CancelWorkflow")]
        public WorkflowResult CancelWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses)
        {
            WorkflowResult result = new WorkflowResult();
            try
            {
                WorkflowHelper.ActionHelper.CancelWorkflow(dTOWorkflowProcesses, UserName);
                result.Result = true;
            }
            catch (Exception ex)
            {
                result.Result = false;
                result.Description = ex.Message;
            }
            return result;
        }


        [HttpPost]
        [Route("api/AddCommentWorkflow")]
        public bool AddCommentWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses)
        {
            return true;
        }




        //Todo Her bir Fonksiyon için bir post methodu yazılacak.
        // TAMAM //1 - Create : Akışı Oluşturuyor
        // TAMAM //2 - Accept : Onaylama İşlemi Yapılıyor. (WorkflowInstanceId, UserId, Commend) //BaseDTO
        // TAMAM //3 - Reject : Reddetme işlemi(WorkflowInstanceId, UserId, Commend)
        // TAMAM //4 - Forward : Yönlendir(WorkflowInstanceId, ForwardUserId, UserId, Commend);
        // TAMAM //5 - SendToCommend : Yoruma Gönder(WorkflowInstanceId, SendToCommendUserId, UserId, Commend)
        // TAMAM //6 - Commented : Yorumla(WorkflowInstanceId, UserId, Commend)
        // TAMAM //7 - Suspend : Beklet(WorkflowInstanceId, ResumeDate, UserId, Commend)
        // TAMAM //8 - Resume : Devam Ettir(WorkflowInstanceId, UserId, Commend)
        // TAMAM //9 - Cancel : İptal Et(WorkflowInstanceId, UserId, Commend)

        //10 - AddToComment : Yorum Ekle(WorkflowInstanceId, SendToCommendUserId, UserId, Commend)

    }
}
