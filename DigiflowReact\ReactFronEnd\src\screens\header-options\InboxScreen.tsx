import * as React from 'react'
import {WButton, WGrid, WSelect, WTable} from "@wface/components"
import { useTranslation } from "react-i18next"
import {useEffect, useState} from "react"
import Table from "react-responsive-data-table";
import Api from "../../services/http-service"
import {getUser} from "../../services/wface-helper"
import WorkflowDetailScreen from "../detail/WorkflowDetailScreen";
import {openScreen} from "../../../configs/wface/wface.config";
import HeaderComponent from "../../components/HeaderComponent";

function InboxScreen() {
    function goWorkflowDetailScreen() {
        openScreen("WorkflowDetailScreen")
    }

    const { t } = useTranslation()
    const user = getUser();
    const [inbox, setInbox] = useState([])
    const [userList, setUserList] = useState([])
    const [selectedUser, setSelectedUser] = useState(user.LoginDtoObject.LoginId)

    const GetInboxList = () => {
        Api.GetData("GetInboxList?UserId=" + selectedUser).then((result : [])  => {
            setInbox(result)
            // alert(JSON.stringify(result))
        })
    }

    const GetUserList = () => {
        Api.GetData("GetUserList").then(result => {
            setUserList(result as any)
        })
    }

    useEffect(() => {
        GetInboxList()
        GetUserList()
    }, [])

    useEffect(()=>{
        GetInboxList()
    },[selectedUser])
    // const onClickGetInbox = () => {
    //     GetInboxList();
    // }

    return (
        <WGrid style={{margin: 20,}}>
            <HeaderComponent />

            <WGrid container alignItems='center' direction='row' style={{marginTop: 20, marginBottom: 20}}>
                <WGrid item md={12} xs={12}>
                    <WSelect
                        id=""
                        style={{marginTop: 20,}}
                        label={t("users")}
                        options={userList.map(repo => {
                            return {
                                label: repo.userNameSurname,
                                value: repo.userId
                            };
                        })}
                        onChange={user => setSelectedUser(user)}
                    />
                </WGrid>
            </WGrid>

            <h3 style={{textAlign: "center", color: "black", fontSize: 16, marginTop: 20}}>{t('my_inbox')}</h3>
            <WTable
                id=""
                onRowClick = {goWorkflowDetailScreen}
                columns={[
                    {title: 'ID', field: 'id'},
                    {title: 'Workflow Name', field: 'workflow_name'},
                    {title: 'Owner', field: 'owner'},
                    {title: 'Description', field: 'description'},
                    {title: 'Amount', field: 'amount'},
                    {title: 'Currency', field: 'currency'}, // filtering: false
                    {title: 'State', field: 'state'},
                    {title: 'Forwarder', field: 'forwarder'},
                    {title: 'Date', field: 'date'},
                    {title: 'Department', field: 'department'},
                ]}
                data={inbox.map(repo => {
                    return {
                        id: repo.WfInsId,
                        workflow_name: repo.FlowName,
                        owner: repo.WfOwner,
                        description: repo.FlowDesc,
                        amount: repo.Amount,
                        currency: repo.Currency,
                        state: repo.StateName,
                        forwarder: repo.FlowName,
                        date: repo.WfDate,
                        department: repo.Bolum
                    };
                })}
                title=""
                options={{
                    filtering: true,
                    search: false,
                    grouping: true
                }}
            />

            <br/> <br/>

            <h3 style={{textAlign: "center", color: "black", fontSize: 16, marginTop: 20}}>{t('delegate_inbox')}</h3>
            <WTable
                id=""
                onRowClick = {goWorkflowDetailScreen}
                columns={[
                    {title: 'ID', field: 'id'},
                    {title: 'Workflow Name', field: 'workflow_name'},
                    {title: 'Owner', field: 'owner'},
                    {title: 'Amount', field: 'amount'},
                    {title: 'Currency', field: 'currency'},
                    {title: 'State', field: 'state'},
                    {title: 'Forwarder', field: 'forwarder'},
                    {title: 'Date', field: 'date'},
                    {title: 'Department', field: 'department'},
                ]}
                data={inbox.map(repo => {
                    return {
                        id: repo.WfInsId,
                        workflow_name: repo.FlowName,
                        owner: repo.WfOwner,
                        amount: repo.Amount,
                        currency: repo.Currency,
                        state: repo.StateName,
                        forwarder: repo.FlowName,
                        date: repo.WfDate,
                        department: repo.Bolum
                    };
                })}
                title=""
                options={{
                    filtering: true,
                    search: false,
                    grouping: true
                }}
            />

            <br/> <br/>

            <h3 style={{textAlign: "center", color: "black", fontSize: 16, marginTop: 20}}>{t('comment_inbox')}</h3>
            <WTable
                id=""
                onRowClick = {goWorkflowDetailScreen}
                columns={[
                    {title: 'ID', field: 'id'},
                    {title: 'Workflow Name', field: 'workflow_name'},
                    {title: 'Owner', field: 'owner'},
                    {title: 'State', field: 'state'},
                    {title: 'Forwarder', field: 'forwarder'},
                    {title: 'Date', field: 'date'},
                    {title: 'Department', field: 'department'},
                ]}
                data={inbox.map(repo => {
                    return {
                        id: repo.WfInsId,
                        workflow_name: repo.FlowName,
                        owner: repo.WfOwner,
                        state: repo.StateName,
                        forwarder: repo.FlowName,
                        date: repo.WfDate,
                        department: repo.Bolum
                    };
                })}
                title=""
                options={{
                    filtering: true,
                    search: false,
                    grouping: true
                }}
            />
        </WGrid>
    )
}

export default InboxScreen
