﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace DigiflowYYS_Yeni
{
    public partial class AdminTestPage : System.Web.UI.Page
    {

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!Page.IsPostBack)
            {
                string SQL = @"Select 0 as LOGIN_ID, ' ---Se<PERSON><PERSON>z---' as NAME_SURNAME from dual Union Select  LOGIN_ID,NAME_SURNAME  from DT_WORKFLOW.VW_USER_INFORMATION Order By NAME_SURNAME ASC";
                DataTable DtbSonuc = Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", SQL);
                //DrpUserName.DataTextField = "NAME_SURNAME";
                //DrpUserName.DataValueField = "LOGIN_ID";
                //DrpUserName.DataSource = DtbSonuc;
                //DrpUserName.DataBind();
                for (int i = 0; i < DtbSonuc.Rows.Count; i++)
                {
                    string NAME_SURNAME = DtbSonuc.Rows[i]["NAME_SURNAME"].ToString();
                    string LOGIN_ID = DtbSonuc.Rows[i]["LOGIN_ID"].ToString();
                    DrpUserName.Items.Add(new ListItem(NAME_SURNAME, LOGIN_ID));
                }
            }
        }

        protected void BtnStart_Click(object sender, EventArgs e)
        {
            if (txtInstanceId.Text == string.Empty && DrpUserName.SelectedIndex != 0)
            {
                if (!DrpPageName.SelectedValue.Contains("?"))
                {
                    Response.Redirect(DrpPageName.SelectedValue + "?LoginId=" + DrpUserName.SelectedValue);
                }
                else
                {
                    Response.Redirect(DrpPageName.SelectedValue + "&LoginId=" + DrpUserName.SelectedValue);
                }
            }
            else
            {
            }
        }

        protected void BtnInstance_Click(object sender, EventArgs e)
        {
            if (txtInstanceId.Text != "" && DrpUserName.SelectedValue != "")
            {
                Response.Redirect(DrpPageName.SelectedValue + "?LoginId=" + DrpUserName.SelectedValue + "&wfInstanceId=" + txtInstanceId.Text + "");
            }
        }

        protected void BtnDelegationList_Click(object sender, EventArgs e)
        {
        }

        protected void BtnGetHistory_Click(object sender, EventArgs e)
        {
        }

        protected void BtnGetHistory0_Click(object sender, EventArgs e)
        {
        }

        protected void BtnExecAction_Click(object sender, EventArgs e)
        {
        }
    }
}