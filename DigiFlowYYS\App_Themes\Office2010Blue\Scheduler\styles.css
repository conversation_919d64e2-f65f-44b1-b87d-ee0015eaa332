.dxscControl_Office2010Blue
{
    background: #8ba0bc;
    border: 1px solid #8ba0bc;
}
.dxscLoadingPanel_Office2010Blue
{
    font: 8pt Verdana;
    color: #1e395b;
    background: White;
    border: 1px solid #859ebf;
}
.dxscLoadingPanel_Office2010Blue td.dx
{
    white-space: nowrap;
    text-align: center;
    padding: 10px 14px;
}
.dxscLoadingDiv_Office2010Blue
{
    background: White;
    opacity: 0.25;
    filter: alpha(opacity=25);
    cursor: wait;
}

/* Headers */
.dxscDateHeader_Office2010Blue,
.dxscAlternateDateHeader_Office2010Blue,
.dxscDayHeader_Office2010Blue,
.dxscDateCellHeader_Office2010Blue,
.dxscTodayCellHeader_Office2010Blue,
.dxscTimelineDateHeader_Office2010Blue,
.dxscHorizontalResourceHeader_Office2010Blue,
.dxscVerticalResourceHeader_Office2010Blue
{
    color: #1e395b;
    background: #a5bfe1;
    border: 1px solid #8ba0bc;
    border-width: 0 1px 1px 0;
    padding: 4px;
    font: 8pt Verdana;
    text-align: center;
    vertical-align: top;
    cursor: default;
    overflow: hidden;
    white-space: nowrap;
}

.dxscAlternateTimelineDateHeader_Office2010Blue,
.dxscAlternateDateHeader_Office2010Blue
{
    background: #ffea77 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Scheduler.CssImages.DateHeaderAltBack.png")%>') left top repeat-x;
    color: #1e395b;
}
.dxscDayHeader_Office2010Blue
{
    border-width: 1px 1px 0 0;
}
.dxscDateCellHeader_Office2010Blue
{
    border-width: 1px;
}
.dxscTodayCellHeader_Office2010Blue
{
    border-top-width: 1px;
    background: #ffdc7a;
    color: #1e395b;
}
.dxscTimelineDateHeader_Office2010Blue
{
    border-width: 1px 1px 1px 0;
}
.dxscHorizontalResourceHeader_Office2010Blue
{
    border-color: #8ba0bc;
    border-width: 1px 1px 1px 0;
    background: #a5bfe1;
}
.dxscVerticalResourceHeader_Office2010Blue
{
    border-width: 1px 1px 0 0;
    border-top-color: #8ba0bc;
    background: #a5bfe1;
    white-space: normal;
}
.dxscSelectionBar_Office2010Blue
{
    border-style: solid;
    border-width: 0 1px 0 0;
    height: 30px;
}

/* Corners */
.dxscLeftTopCorner_Office2010Blue,
.dxscRightTopCorner_Office2010Blue
{
    background-color: #c1d3ea;
    width: 1px;
    border-style: solid;
    border-color: #8ba0bc;
    border-width: 0 0 1px;
}
.dxscLeftTopCorner_Office2010Blue
{
    border-width: 1px 1px 1px 0;
    font: 8pt Verdana;
    text-align: center;
    padding: 2px;
}

/* Separators */
.dxscGroupSeparatorVertical_Office2010Blue,
.dxscGroupSeparatorHorizontal_Office2010Blue
{
    background: #c1d3ea;
    border: 1px solid #8ba0bc;
}
.dxscDayHdrsTbl .dxscGroupSeparatorVertical_Office2010Blue,
.dxscDayHdrsTbl .dxscGroupSeparatorHorizontal_Office2010Blue
{
    background: White;
    border-color: #8ba0bc;
}
.dxscGroupSeparatorVertical_Office2010Blue
{
    width: 1px;
    border-width: 0 1px;
}
.dxscGroupSeparatorHorizontal_Office2010Blue
{
    height: 1px;
    border-width: 1px 0 0;
    font-size: 1px;
}

/* Apts Area */
.dxscAllDayArea_Office2010Blue
{
    background-color: #d5e8ff;
    border: 1px solid #8ba0bc;
    border-width: 0 1px 1px 0;
}
.dxscDateCellBody_Office2010Blue
{
    height: 100px;
    border: solid 1px;
    border-width: 0 1px 0 0;
}
.dxscTimeCellBody_Office2010Blue
{
    border: solid 1px;
    font: 8pt Verdana;
}
.dxscTimelineCellBody_Office2010Blue
{
    height: 300px;
    border-style: solid;
    border-width: 1px 1px 0 0;
}

.dxscAppointment_Office2010Blue
{
    color: #1e395b;
    font: 8pt Verdana;
    border: 1px solid #abbad0;
    padding: 0;
    margin: 0;
    cursor: default;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.dxscAppointmentInnerBorders_Office2010Blue
{
    border: 1px solid #abbad0;
    padding: 0px;
}
.dxscMoreButton_Office2010Blue
{
    font: 8pt Verdana;
    color: #1e395b;
    text-decoration: underline;
    padding: 0;
}
.dxscAppointmentHorizontalSeparator_Office2010Blue
{
    height: 1px;
    width: 100%;
    overflow: hidden;
    border-bottom-style: none;
    border-left-style: none;
    border-right-style: none;
}
/* Rulers */
.dxscTimeRulerHoursItem_Office2010Blue, .dxscTimeRulerMinuteItem_Office2010Blue
{
    color: #1e395b;
    background: White;
    border: 1px solid #8ba0bc;
    border-width: 0px 1px 1px 0;
    vertical-align: top;
    white-space: nowrap;
}
.dxscTimeRulerHoursItem_Office2010Blue
{
    border-right-width: 0;
    border-left-color: #8ba0bc;
    font: 11pt Verdana;
    text-align: right;
    width: 45px;
    padding: 4px 4px 0px;
}
.dxscTimeRulerMinuteItem_Office2010Blue
{
    border-left-width: 0px;
    text-align: center;
    width: 18px;
    font: 7pt Verdana;
    padding: 4px;
    padding: 5px 1px 5px 1px;
}

.dxscTimeRulerHeaderHourItem_Office2010Blue
{
    width: 55px;
}
.dxscTimeRulerHeaderMinuteItem_Office2010Blue
{
    width: 16px;
}
.dxscScrollHeaderItem_Office2010Blue
{
    width: 16px;
}

/* Control elements */
.dxscToolbarContainer_Office2010Blue
{
    border-bottom: 1px solid #859ebf;
    border-right: 1px solid #859ebf;
}
.dxscToolbar_Office2010Blue
{
    background-color: #c1d3ea;
    padding: 3px 0 3px 3px;
}
.dxscViewSelector_Office2010Blue
{
    background-color: #c1d3ea;
    padding: 0;
}
.dxscResourceNavigator_Office2010Blue
{
    background: White;
    padding: 3px 6px 3px 3px;
    border: 1px solid #8ba0bc;
}
.dxscViewVisibleInterval_Office2010Blue
{
    color: #1e395b;
    font: 8pt Verdana;
    padding: 0 20px;
    white-space: nowrap;
}
.dxscInplaceEditor_Office2010Blue
{
    background: #c1d3ea;
    border: solid 3px black;
    padding: 0;
    font: 8pt Verdana;
    color: Black;
}
.dxscErrorInfo_Office2010Blue
{
    background: #f1abab;
    color: #853a3a;
    padding: 10px;
    border: 1px solid #8ba0bc;
    border-width: 1px 0;
}

/* Buttons */
.dxscViewNavigatorButton_Office2010Blue,
.dxscViewNavigatorGotoDateButton_Office2010Blue
{
    background: #d0dfee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Scheduler.CssImages.ButtonBack.png")%>') left top repeat-x;
    border: 1px solid #abbad0;
    height: 21px;
    padding: 0 6px;
    font: 8pt Verdana;
    color: #1e395b;
    cursor: pointer;
}
.dxscViewNavigatorButton_Office2010Blue span,
.dxscViewNavigatorGotoDateButton_Office2010Blue span
{
    padding: 0 22px;
}
.dxscViewNavigatorButtonHover_Office2010Blue,
.dxscViewNavigatorGotoDateButtonHover_Office2010Blue
{
    background: #fcf8e5 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Scheduler.CssImages.ButtonHoverBack.png")%>') left top repeat-x;
}
.dxscViewNavigatorButtonPressed_Office2010Blue,
.dxscViewNavigatorGotoDateButtonPressed_Office2010Blue
{
    background: #fee287 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Scheduler.CssImages.ButtonPressedBack.png")%>') left top repeat-x;
}

.dxscViewSelectorButton_Office2010Blue
{
    font: 8pt Verdana;
    background: #b4cae3;
    border: 1px solid #859ebf;
    border-width: 0 0 0 1px;
    padding: 8px 18px;
    cursor: pointer;
}
.dxscViewSelectorButtonHover_Office2010Blue
{
    background: #d4e3f6;
    border-color: #859ebf;
}
.dxscViewSelectorButtonChecked_Office2010Blue
{
    background: White;
    border-color: #859ebf;
}
.dxscViewSelectorButtonPressed_Office2010Blue
{
    background: #95afd0;
    border-color: #859ebf;
}

.dxscViewSelector_Office2010Blue.dxscVSHorz,
.dxscViewSelector_Office2010Blue.dxscVSVert
{
    border-style: solid;
    border-color: #859ebf;
    border-width: 1px 0 0 1px;
}
.dxscToolbar_Office2010Blue .dxscViewSelector_Office2010Blue
{
    border-width: 0;
}
.dxscVSHorz .dxscViewSelectorButton_Office2010Blue,
.dxscVSVert .dxscViewSelectorButton_Office2010Blue
{
    border-width: 0 1px 1px 0;
}
.dxscToolbar_Office2010Blue .dxscViewSelectorButton_Office2010Blue
{
    border-width: 0 0 0 1px;
}

.dxscResourceNavigatorButton_Office2010Blue
{
    background: none;
    cursor: pointer;
}
.dxscResourceNavigatorButtonHover_Office2010Blue
{
    background: none;
}
.dxscResourceNavigatorButtonPressed_Office2010Blue
{
    background: none;
}

.dxscNavigationButton_Office2010Blue
{
    background: none;
    cursor: pointer;
}
.dxscNavigationButtonHover_Office2010Blue
{
    background: none;
}
.dxscNavigationButtonPressed_Office2010Blue
{
    background: none;
}

.dxscSmartTagButton_Office2010Blue
{
    background: #d0dfee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Scheduler.CssImages.ButtonBack.png")%>') left top repeat-x;
    border: 1px solid #abbad0;
    padding: 4px 5px;
    cursor: pointer;
}

.dxscDVBottomMoreButton_Office2010Blue,
.dxscDVTopMoreButton_Office2010Blue
{
    cursor: pointer;
}

.dxscNoBorderButton_Office2010Blue
{
    cursor: pointer;
    border: 1px solid #859ebf;
}
.dxscNoBorderButtonHover_Office2010Blue
{
    background: #d3e3f6;
    border-color: #859ebf;
}
.dxscNoBorderButtonPressed_Office2010Blue
{
    background: #95afd0;
    border-color: #859ebf;
}
.dxscControlAreaForm_Office2010Blue
{
    font: 12pt Verdana;
    color: Black;
    white-space: normal;
    padding: 9px 12px 13px 12px;
    background-color: white;
}
.dxscToolTipRoundedCornersTopBottomRow_Office2010Blue
{
    font-size: 1pt;
}
.dxscToolTipRoundedCornersTopSide_Office2010Blue
{
    border-top: 1px solid Black;
    vertical-align: bottom;
    height: 1px;
    background-color: #fafad2;
}
.dxscToolTipRoundedCornersLeftSide_Office2010Blue
{
    border-left: 1px solid Black;
    vertical-align: bottom;
    background-color: #fafad2;
}
.dxscToolTipRoundedCornersRightSide_Office2010Blue
{
    border-right: 1px solid Black;
    vertical-align: bottom;
    background-color: #fafad2;
}
.dxscToolTipRoundedCornersBottomSide_Office2010Blue
{
    border-bottom: 1px solid Black;
    vertical-align: bottom;
    height: 1px;
    background-color: #fafad2;
}
.dxscToolTipRoundedCornersContent_Office2010Blue
{
    background-color: #fafad2;
    padding: 0;
}
.dxscToolTipSquaredCorners_Office2010Blue
{
    background: #f9f9cd;
    padding: 0;
    font: 8pt Verdana;
    color: #303030;
    white-space: nowrap;
    border: 1px solid Black;
}
.dxscTimeMarker_Office2010Blue
{
    top: -8px;
}
.dxscTimeMarkerLine_Office2010Blue
{
    top: -2px;
    height: 3px;
    font-size: 1pt;
}
.dxscTimeMarkerLineV_Office2010Blue
{
    position: absolute;
    background-color: White;
    border: 1px solid #8ba0bc;
    width: 1px;
    font-size: 1pt;
    border-top-width: 0;
    border-bottom-width: 0;
}
.dxscTimeMarkerLineH_Office2010Blue
{
    position: absolute;
    top: -2px;
    background-color: White;
    border: 1px solid #8ba0bc;
    height: 1px;
    font-size: 1pt;
    border-left-width: 0;
    border-right-width: 0;
}