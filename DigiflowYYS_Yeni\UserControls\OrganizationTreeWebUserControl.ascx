﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="OrganizationTreeWebUserControl.ascx.cs" Inherits="DigiflowYYS_Yeni.OrganizationTreeWebUserControl" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>

<asp:UpdatePanel ID="OrganizationUpdatePanel" runat="server"
    UpdateMode="Conditional">
    <ContentTemplate>
        <asp:UpdateProgress ID="UP1" AssociatedUpdatePanelID="OrganizationUpdatePanel" runat="server">
            <ProgressTemplate>
                <%--<img src="images/ajax-loader.gif" />--%>
            </ProgressTemplate>
        </asp:UpdateProgress>

        <asp:Panel ID="OrganizationTreePanel" runat="server">
            <asp:Label ID="TitleLabel" runat="server" Text="" Font-Bold="true"></asp:Label>
            <table border="0" width="100%" cellspacing="0" cellpadding="0">
                <asp:Panel ID="DepartmentPanel" runat="server">
                    <tr onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="ltDept" runat="server" Text="<%$Resources:Resource, Uc_Departman%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList Width="350" ID="DepartmentDropDownList" runat="server"
                                AutoPostBack="True" CssClass="DropDown" Enabled="False"
                                OnSelectedIndexChanged="DepartmentDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                        </td>
                    </tr>
                </asp:Panel>

                <asp:Panel ID="DivisionPanel" runat="server">
                    <tr onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal1" runat="server" Text="<%$Resources:Resource, Uc_Bolum%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList
                                Width="350"
                                ID="DivisionDropDownList"
                                runat="server"
                                AutoPostBack="True"
                                CssClass="DropDown"
                                Enabled="False"
                                OnSelectedIndexChanged="DivisionDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                        </td>
                    </tr>
                </asp:Panel>

                <asp:Panel ID="UnitPanel" runat="server">
                    <tr class="printoutyok" onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal2" runat="server" Text="<%$Resources:Resource, Uc_Birim%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList Width="350" ID="UnitDropDownList" runat="server" AutoPostBack="True"
                                CssClass="DropDown" Enabled="False"
                                OnSelectedIndexChanged="UnitDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                        </td>
                    </tr>
                </asp:Panel>

                <asp:Panel ID="TeamPanel" runat="server">
                    <tr class="printoutyok" onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal3" runat="server" Text="<%$Resources:Resource, Uc_Takim%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList Width="350" ID="TeamDropDownList" runat="server" AutoPostBack="True"
                                CssClass="DropDown" Enabled="False"
                                OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                        </td>
                    </tr>
                </asp:Panel>

                <asp:Panel ID="SubTeamPanel1" runat="server">
                    <tr class="printoutyok" onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal6" runat="server" Text="<%$Resources:Resource, Alt_Birim1%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList Width="350" ID="SubTeamDropDownList1" runat="server" AutoPostBack="True"
                                CssClass="DropDown" Enabled="False"
                                OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                        </td>
                    </tr>
                </asp:Panel>


                <asp:Panel ID="SubTeamPanel2" runat="server">
                    <tr class="printoutyok" onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal7" runat="server" Text="<%$Resources:Resource, Alt_Birim2%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList Width="350" ID="SubTeamDropDownList2" runat="server" AutoPostBack="True"
                                CssClass="DropDown" Enabled="False"
                                OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                        </td>
                    </tr>
                </asp:Panel>


                <asp:Panel ID="SubTeamPanel3" runat="server">
                    <tr class="printoutyok" onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal8" runat="server" Text="<%$Resources:Resource, Alt_Birim3%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList Width="350" ID="SubTeamDropDownList3" runat="server" AutoPostBack="True"
                                CssClass="DropDown" Enabled="False"
                                OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                        </td>
                    </tr>
                </asp:Panel>


                <asp:Panel ID="SubTeamPanel4" runat="server">
                    <tr class="printoutyok" onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal9" runat="server" Text="<%$Resources:Resource, Alt_Birim4%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList Width="350" ID="SubTeamDropDownList4" runat="server" AutoPostBack="True"
                                CssClass="DropDown" Enabled="False"
                                OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                        </td>
                    </tr>
                </asp:Panel>


                <asp:Panel ID="SubTeamPanel5" runat="server">
                    <tr class="printoutyok" onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal10" runat="server" Text="<%$Resources:Resource, Alt_Birim5%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList Width="350" ID="SubTeamDropDownList5" runat="server" AutoPostBack="True"
                                CssClass="DropDown" Enabled="False"
                                OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                        </td>
                    </tr>
                </asp:Panel>


                

                <asp:Panel ID="UserPanel" runat="server">
                    <tr onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal4" runat="server" Text="<%$Resources:Resource, Uc_Kullanici%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:DropDownList Width="350" ID="UserDropDownList" runat="server" AutoPostBack="True"
                                CssClass="DropDown" Enabled="False" OnSelectedIndexChanged="UserDropDownList_SelectedIndexChanged">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="UserDropdownRequiredFieldValidator" runat="server" ErrorMessage="<%$Resources:Resource, Uc_KullaniciHata%>" ControlToValidate="UserDropDownList" Display="None" ValidationGroup="vG0" Enabled="False"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                </asp:Panel>

                <asp:Panel ID="ManagerPanel" runat="server" Visible="false">
                    <tr onmouseover="style.backgroundColor='#f3f3f3'" onmouseout="style.backgroundColor='#fff'">
                        <td style="padding: 3px" align="left" width="15%" valign="top">
                            <asp:Literal ID="Literal5" runat="server" Text="<%$Resources:Resource, Uc_Yonetici%>"></asp:Literal>
                        </td>
                        <td style="padding: 3px" align="left" width="84%" valign="top">
                            <asp:Label ID="ManagerLabel" runat="server"></asp:Label>
                        </td>
                    </tr>
                </asp:Panel>

                <asp:Panel ID="pnlOzet" runat="server">
                    <tr>
                        <td colspan="2">
                            <asp:Literal ID="ltrOzet" runat="server"></asp:Literal>
                        </td>
                    </tr>

                </asp:Panel>

            </table>
        </asp:Panel>

    </ContentTemplate>
    <Triggers>
        <asp:AsyncPostBackTrigger ControlID="UnitDropDownList" EventName="SelectedIndexChanged" />
        <asp:AsyncPostBackTrigger ControlID="DivisionDropDownList" EventName="SelectedIndexChanged" />
        <asp:AsyncPostBackTrigger ControlID="DepartmentDropDownList" EventName="SelectedIndexChanged" />
    </Triggers>
</asp:UpdatePanel>

