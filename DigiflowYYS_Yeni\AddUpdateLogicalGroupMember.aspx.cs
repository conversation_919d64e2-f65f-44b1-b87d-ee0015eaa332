﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WebCore;
//using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Digiflow.YYS.Core;
//using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Net.Mail;
using System.Web.UI;
using System.Web.UI.WebControls;
using DevExpress.Web;
using DevExpress.Web.ASPxEditors;
using DevExpress.Web.ASPxGridView;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;

namespace DigiflowYYS_Yeni
{
    public partial class AddUpdateLogicalGroupMember : YYSSecurePage
    {
        /// <summary>
        /// Mantıksal Grup Üyeleri Ekleme ve Düzenleme Ekranı
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Page_Load(object sender, EventArgs e)
        {
            //if (!Page.IsPostBack)
            //{
            //    #region Mantıksal Grup Combobox doldurulur ve mantıksal grup un üyeleri var ise getirilir

            //   this.Master.ShowMenu(true);
            //   this.Master.PageTitle = "Mantıksal Grup Üyeleri Ekleme";
            //    Session["LogicalGroupId"] = Request.QueryString["LogicalGroupId"];
            //    LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
            //    // ASPxComboBoxLogicalGroup1 = null;

            //    //ASPxComboBoxLogicalGroup1 = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillComboboxLogicalGroup("Name", "RequestId", ASPxComboBoxLogicalGroup1, Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.GetAllByWorkflowId(ConvertionHelper.ConvertValue<long>(lg.WfDefId)), "Mantıksal Grup Seçiniz", "0");
            //    ASPxComboBoxLogicalGroup1 = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownLogicalGroup("Name", "RequestId", ASPxComboBoxLogicalGroup1, Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.GetAllByWorkflowId(ConvertionHelper.ConvertValue<long>(lg.WfDefId)), "Mantıksal Grup Seçiniz", "0");

            //    long IsAdmin = 0;

            //    if (Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAndFlowAdmin.ToString() || Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAdmin.ToString())
            //    {
            //        IsAdmin = 1;
            //    }

            //    WorkFlowCombobox1 = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownList("Name", "WorkflowDefId", WorkFlowCombobox1, Digiturk.Workflow.Digiflow.YYS.Core.WorkflowHelper.GetWorkflowsOfAdmin(UserInformation.LoginObject.LoginId, IsAdmin), "İş Akışı Seçiniz", "0");

            //    if (Session["LogicalGroupId"] != null)
            //    {
            //        //ASPxComboBoxLogicalGroup1.Value = Convert.ToInt64(Session["LogicalGroupId"]);
            //        ASPxComboBoxLogicalGroup1.SelectedValue = Session["LogicalGroupId"].ToString();
            //    }
            //    //WorkFlowCombobox1.Value = Convert.ToInt64(lg.WfDefId);
            //    WorkFlowCombobox1.SelectedValue = lg.WfDefId.ToString();
            //    UsersASPxComboBox = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownList("NAME_SURNAME", "LOGIN_ID", UsersASPxComboBox, HrHelper.GetUsers(), "KULLANICI SEÇİNİZ", "0");
            //    txtDescription.Text = lg.Description;
            //    rblMemberType.DataTextField = "DescriptionTR";
            //    rblMemberType.DataValueField = "RequestId";
            //    rblMemberType.DataSource = LogicalGroupMemberTypeHelper.GetAllLogicalGroupMemberTypes();
            //    rblMemberType.DataBind();

            //    if(!string.IsNullOrEmpty(lg.AdGroupName))
            //    {
            //        rblMemberType.SelectedValue = "5";
            //        rblMemberType_SelectedIndexChanged(this, null);
            //        //DrpDomain.SelectedValue = lg.AdDomainName;
            //        //DrpDomain_TextChanged(this, null);
            //        //DrpGrupList.SelectedValue = lg.AdGroupName;
            //        //BtnAdSorgula_Click(this, null);
            //    }

            //    //   Mantıksal Grup Üyeleri çekilir ve doldurulur
            //    if (Session["LogicalGroupId"] != null)
            //    {
            //        #region Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur

            //       this.Master.PageTitle = "Mantıksal Grup Üyeleri Düzenleme";
            //        GetAllLogicalGroupMembers();
            //        BindAllADLogicalGroupList();
            //        BindViewADLogicalGroupList();
            //        BtnSearchUserList_Click(this, null);
            //        LinkButtonRemoveAll.Visible = true;

            //        #endregion Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur
            //    }

            //    #endregion Mantıksal Grup Combobox doldurulur ve mantıksal grup un üyeleri var ise getirilir
            //}
            //else if (Page.IsCallback)
            //{
            //    if (Session["LogicalGroupId"] != null)
            //    {
            //        #region Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur

            //       this.Master.PageTitle = "Mantıksal Grup Üyeleri Düzenleme";
            //        GetAllLogicalGroupMembers();

            //        LinkButtonRemoveAll.Visible = true;

            //        FillAdGrid();

            //        FillAdGroupGrid();

            //        //FillAdMembers();

            //        BindViewADLogicalGroupList();

            //        #endregion Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur
            //    }
            //}
        }

        //public bool sendMail(string vfrom, string vTo, string vCC, string vBody, string vSubject, string FlowName, string YYSGrupName, string AdGrupName)
        //{
        //    SmtpClient smtpClient = new SmtpClient();
        //    MailMessage message = new MailMessage();
        //    string bodyHtmlHeader = "";
        //    string bodyHtmlFooter = "";
        //    try
        //    {
        //        MailAddress fromAddress = new MailAddress(vfrom, "DIGIPORT");
        //        message.From = fromAddress;
        //        if (vCC != "")
        //        {
        //            message.CC.Add(vCC);
        //        }
        //        message.To.Add(vTo);

        //        bodyHtmlHeader = "<html>";
        //        bodyHtmlHeader += "<head>";
        //        bodyHtmlHeader += "<meta http-equiv=Content-Type content=text/html; charset=windows-1254>";
        //        bodyHtmlHeader += "<style>TD{ font-family:verdana; font-size:9pt; }</style>";
        //        bodyHtmlHeader += "</head>";
        //        bodyHtmlHeader += "<body>";
        //        bodyHtmlHeader += "<table><tr><td><font face=verdana size=2>";
        //        bodyHtmlHeader += " Akışın Adı: " + FlowName + " <br>";
        //        bodyHtmlHeader += " YYS Grubun Adı: " + YYSGrupName + " <br>";
        //        bodyHtmlHeader += " AD Grubun Adı: " + AdGrupName + " <br>";

        //        bodyHtmlFooter = "</font></td></tr></table>";

        //        message.Subject = vSubject;
        //        message.IsBodyHtml = true;
        //        message.Body = bodyHtmlHeader + vBody + bodyHtmlFooter;
        //        smtpClient.Host = "smtp.digiturk.local";
        //        //smtpClient.Host = "************";
        //        smtpClient.Send(message);
        //        return true;

        //    }
        //    catch (Exception ex)
        //    {
        //        string hata = ex.ToString();
        //        hata = hata.Substring(0, hata.Length);
        //        return false;
        //    }
        //}

        //public static string ConvertDataTableToHTML(DataTable dt)
        //{
        //    string html = "<table>";
        //    //add header row
        //    html += "<tr>";
        //    for (int i = 0; i < dt.Columns.Count; i++)
        //        html += "<td>" + dt.Columns[i].ColumnName + "</td>";
        //    html += "</tr>";
        //    //add rows
        //    for (int i = 0; i < dt.Rows.Count; i++)
        //    {
        //        html += "<tr>";
        //        for (int j = 0; j < dt.Columns.Count; j++)
        //            html += "<td>" + dt.Rows[i][j].ToString() + "</td>";
        //        html += "</tr>";
        //    }
        //    html += "</table>";
        //    return html;
        //}

        //public void SendUnDefinedLoginId()
        //{
        //    DataView wv = DtbAdUserList.DefaultView;
        //    wv.RowFilter = "FloginStatus=0";
        //    if (wv.Count > 0)
        //    {
        //        //
        //        //var lg = LogicalGroupHelper.Get(long.Parse(Session["LogicalGroupId"].ToString()));
        //        string FlowName = WorkFlowCombobox1.SelectedItem.Text;
        //        string YYSGrupName = ASPxComboBoxLogicalGroup1.SelectedItem.Text;
        //        string AdGrupName = DrpDomain.SelectedItem.Text + "/" + DrpGrupList.SelectedItem.Text;

        //        string HtmlTable = ConvertDataTableToHTML(wv.ToTable());
        //        sendMail("<EMAIL>", "<EMAIL>", "<EMAIL>", HtmlTable, "AD Portalde Olup FLogin Eşleşmeyen Kullanıcılar", FlowName, YYSGrupName, AdGrupName);
        //    }
        //    wv.RowFilter = "";
        //}

        ///// <summary>
        ///// Belirtilen Mantıksal Grubun üyelerini getirir
        ///// </summary>
        //private void GetAllLogicalGroupMembers()
        //{
        //    long logicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);

        //    List<LogicalGroupMember> allLgm = LogicalGroupMemberHelper.GetByLogicalGroupId(logicalGroupId);

        //    //DataTable dtLogicalMembers = fn_FormMemberTable();
        //    //foreach (LogicalGroupMember lgm in allLgm)
        //    //{
        //    //    DataRow dr = dtLogicalMembers.NewRow();
        //    //    dr[0] = lgm.Content;
        //    //    dr[1] = lgm.Description;
        //    //    dr[2] = lgm.LoginId.ToString();
        //    //    dr[3] = lgm.FullName;
        //    //    dr[4] = lgm.Email;
        //    //    dr[5] = LogicalGroupMemberTypeHelper.GetLogicalGroupMemberTypeName(lgm.LogicalGroupMemberTypeId);
        //    //    dr[6] = lgm.RequestId;
        //    //    dtLogicalMembers.Rows.Add(dr);
        //    //}

        //    gvLogicalMembers.DataSource = allLgm;
        //    gvLogicalMembers.DataBind();
        //}

        //public void BindAllADLogicalGroupList()
        //{
        //    if (DtbAdGroupList.Columns.Count == 0)
        //    {
        //        BuildAdGroupListDataTable();
        //    }
        //    DtbAdGroupList= LogicalGroupAdMapHelper.GetMapListForLogicalGroup(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
        //    GrdAdLogicalGroupMap.DataSource = DtbAdGroupList;
        //    GrdAdLogicalGroupMap.DataBind();
        //}

        //public void BindViewADLogicalGroupList()
        //{
        //    GrdYYSAdGroupMember.DataSource = LogicalGroupAdMapHelper.GetMapListForLogicalGroup(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
        //    GrdYYSAdGroupMember.DataBind();
        //}

        //#region Eski kod grid e elemanları veritabanına eklemeden işlem yaptığımız için gridteki elemanları bir for içinde kaydediyorduk

        /////// <summary>
        /////// Grid te seçilen üyeyi çıkarmayı sağlar
        /////// </summary>
        /////// <param name="sender"></param>
        /////// <param name="e"></param>
        ////protected void lnkRemove_Click(object sender, EventArgs e)
        ////{
        ////    #region Seçilen üye Grid ten silinir
        ////    List<object> keyValues = gvLogicalMembers.GetSelectedFieldValues(gvLogicalMembers.KeyFieldName);
        ////    //for (int i = 0; i < keyValues.Count; i++)
        ////    //    gvLogicalMembers.DeleteRow(gvLogicalMembers.FindVisibleIndexByKeyValue(keyValues[i]));

        ////    if (gvLogicalMembers.VisibleRowCount != 0)
        ////    {
        ////        if (keyValues.Count > 0)
        ////        {
        ////            for (int i = 0; i < keyValues.Count; i++)
        ////            {
        ////                //dtLogicalMembers.Rows.RemoveAt(gvLogicalMembers.FocusedRowIndex);
        ////                dtLogicalMembers.Rows.RemoveAt(ConvertionHelper.ConvertValue<int>(keyValues[i]));
        ////            }
        ////            gvLogicalMembers.DataSource = dtLogicalMembers;
        ////            gvLogicalMembers.DataBind();

        ////            ((MasterPage)Master).ShowPopup(true, "Seçilen Üye Silindi", "Seçtiğiniz üye silindi", false, "");
        ////        }
        ////        else
        ////        {
        ////            ((MasterPage)Master).ShowPopup(false, "Hata", "Silmek istediğiniz üyeyi Seç tikten sonra silebilirsiniz ", true, "NO TRACE");
        ////        }
        ////    }
        ////    #endregion
        ////    fn_VisibilityControlOfRemoveButtons();
        ////}
        /////// <summary>
        /////// Mantıksal Grup Üyelerini kaydetmeyi sağlar
        /////// </summary>
        /////// <param name="sender"></param>
        /////// <param name="e"></param>
        ////protected void SaveASPxButton_Click(object sender, EventArgs e)
        ////{
        ////    #region Mantıksal Grup Üyeleri kaydedilir
        ////    try
        ////    {
        ////        // if (gvLogicalMembers.Rows.Count > 0)
        ////        int count = Convert.ToInt32(gvLogicalMembers.VisibleRowCount);
        ////        if (count > 0)
        ////        {
        ////            if (Session["LogicalGroupId"] != null)
        ////            {
        ////                long logicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);

        ////                LogicalGroup lg = LogicalGroupHelper.Get(logicalGroupId);
        ////                if (lg != null)
        ////                {
        ////                    List<LogicalGroupMember> allLgm = LogicalGroupMemberHelper.GetByLogicalGroupId(logicalGroupId);

        ////                    if (allLgm != null)
        ////                    {
        ////                        //Tüm mantıksal grup üyeleri silinir
        ////                        LogicalGroupMemberHelper.DeleteLogicalGroupMemberByLogicalGroupId(logicalGroupId);
        ////                    }
        ////                    for (int i = 0; i < count; i++)
        ////                    {
        ////                        object[] dataList = ((object[])(gvLogicalMembers.GetRowValues(i, "Content", "Description", "LoginId", "FullName", "Email", "LogicalGroupMemberTypeId")));

        ////                        LogicalGroupMember lgm = new LogicalGroupMember();
        ////                        lgm.LogicalGroupId = logicalGroupId;
        ////                        lgm.Content = dataList[0].ToString();
        ////                        lgm.Description = dataList[1].ToString();
        ////                        lgm.LoginId = ConvertionHelper.ConvertValue<long>(dataList[2]);
        ////                        lgm.FullName = dataList[3].ToString();
        ////                        lgm.Email = dataList[4].ToString();
        ////                        lgm.LogicalGroupMemberTypeId = LogicalGroupMemberTypeHelper.GetLogicalGroupMemberTypeId(dataList[5].ToString());
        ////                        lgm.Created = DateTime.Now;
        ////                        lgm.CreatedBy = UserInformation.LoginObject.LoginId;
        ////                        lgm.LastUpdated = DateTime.Now;
        ////                        lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        ////                        LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
        ////                    }
        ////                    dtLogicalMembers = fn_FormMemberTable();
        ////                    ((MasterPage)Master).ShowPopup(false, "Bitti", "Kayıt işlemi başarıyla tamamlanmıştır", false, "NO TRACE");
        ////                    Response.Redirect("LogicalGroups.aspx?WfDefId=" + lg.WfDefId.ToString(), false);
        ////                }
        ////            }
        ////        }
        ////        else
        ////        {
        ////            ((MasterPage)Master).ShowPopup(false, "Hata", "Mantıksal gruba üye eklemeden kaydetme yapılamaz!", false, "");
        ////        }
        ////    }
        ////    catch (Exception ex)
        ////    {
        ////        ((MasterPage)Master).ShowPopup(false, "Hata", "Kayıt işlemi esnasında bir hata ile karşılaşıldı. Lütfen tekrar deneyin.", true, ex.Message);
        ////    }
        ////    #endregion
        ////}

        //#endregion Eski kod grid e elemanları veritabanına eklemeden işlem yaptığımız için gridteki elemanları bir for içinde kaydediyorduk

        ///// <summary>
        ///// Seçime göre panelleri gösterip göstermeme durumunu ayarlar
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void rblMemberType_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    if (rblMemberType.Items[0].Selected)
        //    {
        //        UserSelectionPanel.Visible = true;
        //        OutsourceUserPanel.Visible = false;
        //        ParameterPanel.Visible = false;
        //        AllUserSelectionPanel.Visible = false;
        //    }
        //    if (rblMemberType.Items[1].Selected)
        //    {
        //        UserSelectionPanel.Visible = false;
        //        OutsourceUserPanel.Visible = false;
        //        ParameterPanel.Visible = false;
        //        AllUserSelectionPanel.Visible = true;
        //    }
        //    if (rblMemberType.Items[2].Selected)
        //    {
        //        UserSelectionPanel.Visible = false;
        //        ParameterPanel.Visible = true;
        //        OutsourceUserPanel.Visible = false;
        //        AllUserSelectionPanel.Visible = false;
        //    }
        //    if (rblMemberType.Items[3].Selected)
        //    {
        //        UserSelectionPanel.Visible = false;
        //        ParameterPanel.Visible = false;
        //        OutsourceUserPanel.Visible = true;
        //        AllUserSelectionPanel.Visible = false;
        //    }
        //    if (rblMemberType.Items[4].Selected)
        //    {
        //        //GrdAdMembersUsers
        //        UserSelectionPanel.Visible = false;
        //        ParameterPanel.Visible = false;
        //        OutsourceUserPanel.Visible = false;
        //        AllUserSelectionPanel.Visible = false;
        //        PnlAdMembers.Visible = true;
        //    }
        //}

        ///// <summary>
        ///// Üye olarak Flogin kullanıcısı eklemeyi sağlar
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void LinkButtonFlogin_click(object sender, EventArgs e)
        //{
        //    LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));

        //    //Kullanıcı seçilmeden mantıksal grup üyesi eklenemez
        //    if (UsersASPxComboBox.SelectedIndex == -1 || UsersASPxComboBox.SelectedIndex == 0)
        //    {
        //       this.Master.ShowError("Hata", "Lütfen listeden bir kullanıcı seçiniz.");
        //    }
        //    else
        //    {
        //        //Grupta tüm kullanıcılar var mı kontrolü yapılır
        //        if (!LogicalGroupMemberHelper.HasAllUsersInMembers(lg.RequestId))
        //        {
        //            string FullName = UsersASPxComboBox.SelectedItem.ToString();
        //            long loginId = ConvertionHelper.ConvertValue<long>(UsersASPxComboBox.SelectedItem.Value.ToString());

        //            bool contains = false;
        //            long IsOnePerson;
        //            IsOnePerson = lg.IsOnePersonGroup;
        //            //Tek kişilik grupsa ve o tek kişi de eklenmiş ise
        //            if (IsOnePerson == 1 && gvLogicalMembers.VisibleRowCount == 1)
        //            {
        //                this.Master.ShowPopup(false, "Hata", "Bu grup tek kişilik olarak tanımlanmış!", true, "Tek Kişilik Grup");
        //            }
        //            else
        //            {
        //                for (int a = 0; a < gvLogicalMembers.VisibleRowCount; a++)
        //                {
        //                    object[] dataList = ((object[])(gvLogicalMembers.GetRowValues(a, "LoginId", "FullName")));
        //                    if (dataList[0] != null && dataList[1] != null)
        //                    {
        //                        if (dataList[0].ToString() == loginId.ToString() && dataList[1].ToString() == FullName)
        //                        {
        //                            contains = true;
        //                            break;
        //                        }
        //                    }
        //                }
        //                if (!contains)
        //                {
        //                    LogicalGroupMember lgm = new LogicalGroupMember();
        //                    lgm.FullName = FullName;
        //                    lgm.LoginId = loginId;
        //                    lgm.LogicalGroupMemberTypeId = 1;
        //                    lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
        //                    gvLogicalMembers.Visible = true;
        //                    lgm.Created = DateTime.Now;
        //                    lgm.CreatedBy = UserInformation.LoginObject.LoginId;
        //                    lgm.LastUpdated = DateTime.Now;
        //                    lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        //                    LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
        //                    GetAllLogicalGroupMembers();
        //                    UsersASPxComboBox.SelectedIndex = 1;
        //                    this.Master.ShowPopup(false, "Üye Eklendi", "Belirttiğiniz üye eklendi", false, "");
        //                }
        //                else
        //                {
        //                    this.Master.ShowPopup(false, "Üye Ekleme Hata", "Eklemek istediğiniz üye bu mantıksal grupta var", true, "");
        //                }
        //            }
        //            fn_VisibilityControlOfRemoveButtons();
        //        }
        //        else
        //        {
        //            this.Master.ShowPopup(false, "Üye Ekleme Hata", "Üye eklemek istediğiniz grupta tüm kullanıcılar bulunmaktadır.", true, "");
        //        }
        //    }
        //}

        ///// <summary>
        ///// Üye olarak Outsource kullanıcısı eklemeyi sağlar
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void LinkButtonOutsource_click(object sender, EventArgs e)
        //{
        //    LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
        //    string userName = txtFullName.Text.Trim();
        //    string email = txtEmail.Text.Trim();

        //    bool contains = false;
        //    long IsOnePerson;
        //    //long.TryParse(Request.QueryString["IsOnePersonGroup"], out IsOnePerson);
        //    IsOnePerson = lg.IsOnePersonGroup;
        //    try
        //    {
        //        //Grup tek kişilik tanımlanmış ise gruba kişi eklenemez
        //        if (IsOnePerson == 1 && gvLogicalMembers.VisibleRowCount == 1)
        //        {
        //            this.Master.ShowPopup(false, "Hata", "Bu grup tek kişilik olarak tanımlanmış!", true, "Tek Kişilik Grup");
        //        }
        //        else
        //        {
        //            for (int a = 0; a < gvLogicalMembers.VisibleRowCount; a++)
        //            {
        //                object[] dataList = ((object[])(gvLogicalMembers.GetRowValues(a, "FullName", "Email")));
        //                if (dataList[0] != null && dataList[1] != null)
        //                {
        //                    if (dataList[0].ToString() == userName && dataList[1].ToString() == email)
        //                    {
        //                        contains = true;
        //                        break;
        //                    }
        //                }
        //            }
        //            //Aynı üye yeniden eklenemez!
        //            if (!contains)
        //            {
        //                LogicalGroupMember lgm = new LogicalGroupMember();
        //                lgm.FullName = userName;
        //                lgm.Email = email;
        //                lgm.LogicalGroupMemberTypeId = 4;
        //                lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
        //                gvLogicalMembers.Visible = true;
        //                lgm.Created = DateTime.Now;
        //                lgm.CreatedBy = UserInformation.LoginObject.LoginId;
        //                lgm.LastUpdated = DateTime.Now;
        //                lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        //                LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
        //                GetAllLogicalGroupMembers();
        //                this.Master.ShowPopup(false, "Üye Eklendi", "Belirttiğiniz üye eklendi", false, "");
        //            }
        //            else
        //            {
        //                this.Master.ShowPopup(false, "Üye Ekleme Hata", "Eklemek istediğiniz üye bu mantıksal grupta var", true, "");
        //            }
        //        }
        //    }
        //    catch
        //    {
        //    }
        //    txtFullName.Text = string.Empty;
        //    txtEmail.Text = string.Empty;
        //    fn_VisibilityControlOfRemoveButtons();
        //}

        ///// <summary>
        ///// Üye olarak parametre tipi eklemyi sağlar
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void LinkParameter_click(object sender, EventArgs e)
        //{
        //    LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
        //    string Content = txtContent.Text.Trim();
        //    string Description = txtDescriptions.Text.Trim();

        //    bool contains = false;
        //    long IsOnePerson;
        //    // long.TryParse(Request.QueryString["IsOnePersonGroup"], out IsOnePerson);
        //    IsOnePerson = lg.IsOnePersonGroup;
        //    try
        //    {
        //        if (IsOnePerson == 1 && gvLogicalMembers.VisibleRowCount == 1)
        //        {
        //            this.Master.ShowPopup(false, "Hata", "Bu grup tek kişilik olarak tanımlanmış!", true, "Tek Kişilik Grup");
        //        }
        //        else
        //        {
        //            for (int a = 0; a < gvLogicalMembers.VisibleRowCount; a++)
        //            {
        //                object[] dataList = ((object[])(gvLogicalMembers.GetRowValues(a, "Content", "Description")));
        //                if (dataList[0] != null && dataList[1] != null)
        //                {
        //                    if (dataList[0].ToString() == Content && dataList[1].ToString() == Description)
        //                    {
        //                        contains = true;
        //                        break;
        //                    }
        //                }
        //            }

        //            if (!contains)
        //            {
        //                LogicalGroupMember lgm = new LogicalGroupMember();
        //                lgm.Content = Content;
        //                lgm.Description = Description;
        //                lgm.LogicalGroupMemberTypeId = 3;
        //                lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
        //                gvLogicalMembers.Visible = true;
        //                lgm.Created = DateTime.Now;
        //                lgm.CreatedBy = UserInformation.LoginObject.LoginId;
        //                lgm.LastUpdated = DateTime.Now;
        //                lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        //                LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
        //                GetAllLogicalGroupMembers();
        //                this.Master.ShowPopup(false, "Üye Eklendi", "Belirttiğiniz üye eklendi", false, "");
        //            }
        //            else
        //            {
        //                this.Master.ShowPopup(false, "Üye Ekleme Hata", "Eklemek istediğiniz üye bu mantıksal grupta var", true, "");
        //            }
        //        }
        //    }
        //    catch
        //    {
        //    }
        //    txtContent.Text = string.Empty;
        //    txtDescriptions.Text = string.Empty;
        //    fn_VisibilityControlOfRemoveButtons();
        //}

        ///// <summary>
        ///// Bütün üyeleri üye olarak eklemeyi sağlar
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void LinkButtonAllUsers_click(object sender, EventArgs e)
        //{
        //    LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
        //    long LoginId = -1;
        //    string FullName = "Tüm Kullanıcılar";
        //    int count = Convert.ToInt32(gvLogicalMembers.VisibleRowCount);
        //    bool contains = false;
        //    long IsOnePerson;
        //    //  long.TryParse(Request.QueryString["IsOnePersonGroup"], out IsOnePerson);
        //    IsOnePerson = lg.IsOnePersonGroup;

        //    try
        //    {
        //        if (IsOnePerson == 1 && count == 1)
        //        {
        //            this.Master.ShowPopup(false, "Hata", "Bu grup tek kişilik olarak tanımlanmış!", true, "Tek Kişilik Grup");
        //        }
        //        else
        //        {
        //            for (int a = 0; a < count; a++)
        //            {
        //                object[] dataList = ((object[])(gvLogicalMembers.GetRowValues(a, "LoginId", "FullName")));
        //                if (dataList[0] != null && dataList[1] != null)
        //                {
        //                    if (dataList[0].ToString() == LoginId.ToString() && dataList[1].ToString() == FullName)
        //                    {
        //                        contains = true;
        //                        break;
        //                    }
        //                }
        //            }
        //            if (!contains)
        //            {
        //                LogicalGroupMember lgm = new LogicalGroupMember();
        //                lgm.FullName = FullName;
        //                lgm.LoginId = LoginId;
        //                lgm.LogicalGroupMemberTypeId = 2;
        //                lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
        //                gvLogicalMembers.Visible = true;
        //                lgm.Created = DateTime.Now;
        //                lgm.CreatedBy = UserInformation.LoginObject.LoginId;
        //                lgm.LastUpdated = DateTime.Now;
        //                lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        //                LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
        //                GetAllLogicalGroupMembers();
        //                this.Master.ShowPopup(false, "Üye Eklendi", "Belirttiğiniz üye eklendi", false, "");
        //            }
        //            else
        //            {
        //                this.Master.ShowPopup(false, "Üye Ekleme Hata", "Eklemek istediğiniz üye bu mantıksal grupta var", true, "");
        //            }
        //        }
        //    }
        //    catch
        //    {
        //    }
        //    fn_VisibilityControlOfRemoveButtons();
        //}


        //public void UpdateLogicalGrup(long LogicalGrupId, string AdDomainName, string AdGrupName)
        //{
        //    LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(LogicalGrupId);
        //    lg.AdDomainName = AdDomainName;
        //    lg.AdGroupName = AdGrupName;
        //    lg.LastUpdated = DateTime.Now;
        //    lg.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        //    Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.UpdateLogicalGroup(lg);
        //}


        ///// <summary>
        ///// Seçime göre mantıksal grupların üyelerini getirir
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void ASPxComboBoxLogicalGroup1_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    Session["LogicalGroupId"] = ASPxComboBoxLogicalGroup1.SelectedItem.Value;
        //    List<LogicalGroupMember> list = LogicalGroupMemberHelper.GetByLogicalGroupId(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"].ToString()));
        //    if (Session["LogicalGroupId"] != null)
        //    {
        //        #region Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur

        //        GetAllLogicalGroupMembers();
        //        BindAllADLogicalGroupList();
        //        BindViewADLogicalGroupList();
        //        BtnSearchUserList_Click(this, null);
        //        LinkButtonRemoveAll.Visible = true;

        //        #endregion Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur
        //    }
        //}

        ///// <summary>
        ///// İş akışı değiştiği zaman mantıksal gruplar combobox ı da bu akışlara göre doldurulur
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void WorkFlowCombobox1_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    //ASPxComboBoxLogicalGroup1 = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillComboboxLogicalGroup("Name", "RequestId", ASPxComboBoxLogicalGroup1, Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.GetAllByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkFlowCombobox1.SelectedItem.Value)), "Mantıksal Grup Seçiniz", "0");
        //    ASPxComboBoxLogicalGroup1 = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownLogicalGroup("Name", "RequestId", ASPxComboBoxLogicalGroup1, Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.GetAllByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkFlowCombobox1.SelectedItem.Value)), "Mantıksal Grup Seçiniz", "0");
        //    gvLogicalMembers.DataSource = null;
        //    gvLogicalMembers.DataBind();
        //}

        //#region Inner functions

        ///// <summary>
        ///// Grid teki üye sayısına göre butonları görünür görünmez yapar
        ///// </summary>
        //private void fn_VisibilityControlOfRemoveButtons()
        //{
        //    if (gvLogicalMembers.VisibleRowCount > 0)
        //    {
        //        LinkButtonRemoveAll.Visible = true;
        //    }
        //    else
        //    {
        //        LinkButtonRemoveAll.Visible = false;
        //    }
        //}

        ///// <summary>
        ///// Üye tablosu oluşturmak için kullanılır.
        ///// </summary>
        ///// <returns></returns>
        //private static DataTable fn_FormMemberTable()
        //{
        //    DataTable dt = new DataTable();
        //    dt.Columns.Add(new DataColumn("Content", typeof(string)));
        //    dt.Columns.Add(new DataColumn("Description", typeof(string)));
        //    dt.Columns.Add(new DataColumn("LoginId", typeof(string)));
        //    dt.Columns.Add(new DataColumn("FullName", typeof(string)));
        //    dt.Columns.Add(new DataColumn("Email", typeof(string)));
        //    dt.Columns.Add(new DataColumn("LogicalGroupMemberTypeId", typeof(string)));
        //    dt.Columns.Add(new DataColumn("RequestId", typeof(string)));
        //    dt.Columns[6].AutoIncrement = true;
        //    return dt;
        //}

        //#endregion Inner functions

        ///// <summary>
        ///// Row yaratılırken boşlukları yoketmemizi sağlar
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void gvLogicalMembers_RowCreated(object sender, GridViewRowEventArgs e)
        //{
        //    string s = "";
        //    for (int i = 0; i < e.Row.Cells.Count; i++)
        //    {
        //        if (e.Row.Cells[i].Text == "&nbsp;")
        //        {
        //            e.Row.Cells[i].Text = s;
        //        }
        //    }
        //}

        ///// <summary>
        ///// Gridteki tüm üyeleri grid ten çıkarır
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void LinkButtonRemoveAll_Click(object sender, EventArgs e)
        //{
        //    //Tüm mantıksal grup üyeleri silinir
        //    long logicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
        //    LogicalGroupMemberHelper.DeleteLogicalGroupMembersByLogicalGroupId(logicalGroupId);
        //    GetAllLogicalGroupMembers();
        //    fn_VisibilityControlOfRemoveButtons();
        //    this.Master.ShowPopup(false, "Üye Silme", "Tüm üyeler listeden çıkarıldı", false, "");
        //}

        ///// <summary>
        ///// Silme işlemini burada yapıyoruz
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void gvLogicalMembers_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
        //{
        //    //long RequestId = ConvertionHelper.ConvertValue<long>(e.Values[0]);
        //    //aspxgridview gridView = (aspxgridview)sender;
        //    //LogicalGroupMemberHelper.DeleteLogicalGroupMember(RequestId);
        //    //((MasterPage)Master).ShowPopup(false, "Mantıksal Grup Üye Silme", "İstenilen üye silindi!", false, "");
        //    //GetAllLogicalGroupMembers();
        //    //gridView.CancelEdit();
        //    //e.Cancel = true;
        //}

        ///// <summary>
        ///// Üyenin tipini getirir
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void gvLogicalMembers_HtmlDataCellPrepared(object sender, ASPxGridViewTableDataCellEventArgs e)
        //{
        //    if (e.DataColumn.Caption == "Mantıksal Grup Tipi")
        //    {
        //        e.Cell.Text = GetLogicalGroupMemberType(e.CellValue.ToString());
        //    }
        //}

        ///// <summary>
        ///// Silme işlemi burada yapılır
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void DeleteButton_Click(object sender, EventArgs e)
        //{
        //    long RequestId = ConvertionHelper.ConvertValue<long>(Session["DeletingLogicalGroupMemberId"]);
        //    LogicalGroupMemberHelper.DeleteLogicalGroupMember(RequestId);
        //    this.Master.ShowPopup(false, "Mantıksal Grup Üye Silme", "İstenilen üye silindi!", false, "");
        //    GetAllLogicalGroupMembers();
        //}

        ///// <summary>
        ///// Silmek için grubun Id si buradan alınır
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //protected void gvLogicalMembers_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
        //{
        //    Session["DeletingLogicalGroupMemberId"] = e.KeyValue.ToString();
        //}

        ///// <summary>
        ///// Belirtilen Üye tipini getirir
        ///// </summary>
        ///// <param name="LogicalGroupMemberTypeId"></param>
        ///// <returns></returns>
        //private string GetLogicalGroupMemberType(string LogicalGroupMemberTypeId)
        //{
        //    string Type = "";
        //    if (LogicalGroupMemberTypeId == "1")
        //    {
        //        Type = "KULLANICI";
        //    }
        //    else if (LogicalGroupMemberTypeId == "2")
        //    {
        //        Type = "TÜM KULLANICILAR";
        //    }
        //    else if (LogicalGroupMemberTypeId == "3")
        //    {
        //        Type = "PARAMETRE";
        //    }
        //    else if (LogicalGroupMemberTypeId == "4")
        //    {
        //        Type = "OUTSOURCE";
        //    }
        //    else if (LogicalGroupMemberTypeId == "5")
        //    {
        //        Type = "AD KULLANICISI";
        //    }
        //    return Type;
        //}


        //public void BuildAdPortalGrupList(string Domain)
        //{
        //    try
        //    {
        //        ApPortalServices.AdPortal.LdapServices.AdPortalServices client = new ApPortalServices.AdPortal.LdapServices.AdPortalServices();
        //        string Username = System.Configuration.ConfigurationSettings.AppSettings["Web.Services.UserName"];
        //        string Password = System.Configuration.ConfigurationSettings.AppSettings["Web.Services.Password"];
        //        client.Credentials = new System.Net.NetworkCredential(Username, Password);
        //        if (Domain == "DIGITURK")
        //        {
        //            client.Url = System.Configuration.ConfigurationSettings.AppSettings["AdPortalServices"];
        //        }
        //        else if (Domain == "DIGITURKCC")
        //        {
        //            client.Url = System.Configuration.ConfigurationSettings.AppSettings["AdPortalCCServices"];
        //        }
        //        ApPortalServices.AdPortal.LdapServices.GroupInformation[] gruplist = client.GetGroupList();
        //        DrpGrupList.Items.Clear();
        //        DrpGrupList.Items.Add(new ListItem("0", "---Seçiniz---"));
        //        foreach (var item in gruplist)
        //        {
        //            DrpGrupList.Items.Add(new ListItem(item.GroupName, item.GroupName));
        //        }
        //    }
        //    catch (Exception ex)
        //    {

        //        throw ex;
        //    }

        //}


        //public void KullaniciEkle(string Domain,string AdGrupName)
        //{
            
        //    ApPortalServices.AdPortal.LdapServices.AdPortalServices client = new ApPortalServices.AdPortal.LdapServices.AdPortalServices();
        //    string Username = System.Configuration.ConfigurationSettings.AppSettings["Web.Services.UserName"];
        //    string Password = System.Configuration.ConfigurationSettings.AppSettings["Web.Services.Password"];
        //    client.Credentials = new System.Net.NetworkCredential(Username, Password);
        //    if (Domain == "DIGITURK")
        //    {
        //        client.Url = System.Configuration.ConfigurationSettings.AppSettings["AdPortalServices"];
        //    }
        //    else if (Domain == "DIGITURKCC")
        //    {
        //        client.Url = System.Configuration.ConfigurationSettings.AppSettings["AdPortalCCServices"];
        //    }
        //    ApPortalServices.AdPortal.LdapServices.UserInformation[] userlist = client.GetGroupOfUserList(AdGrupName);
        //    FillAdUserList(userlist);
        //    FillAdGrid();
        //}

        //protected void BtnAdSorgula_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
                

        //    }
        //    catch (Exception ex)
        //    {

        //        throw ex;
        //    }

        //}
        ////mantıksal grupların üyeleri baslıklı gride userları eklıcak ama sorgulaya basılmadıysa burasaı bos gelıyor bos dt basıyor ıcerı ama uyelrı sorgulamak ıstemeyebılır
        //public void FillAdGrid()
        //{
        //    if (DtbAdUserList.Columns.Count == 0)
        //    {
        //        BuildAdUserListDataTable();
        //    }
        //    GrdAdMembersUsers.DataSource = DtbAdUserList;
        //    GrdAdMembersUsers.DataBind();
        //}

        //public void FillAdGroupGrid()// ilk grid akıs adı mantıksal grubun adı domaın vs ona basıyor 
        //{
        //    if (DtbAdGroupList.Columns.Count == 0)
        //    {
        //        BuildAdGroupListDataTable();
        //    }
        //    GrdAdLogicalGroupMap.DataSource = DtbAdGroupList;
        //    GrdAdLogicalGroupMap.DataBind();
        //}

        //public void FillAdMembers() //FillAdGroupGrid() ile aynı işi yapıyor
        //{
        //    GrdAdLogicalGroupMap.DataSource = DtbAdGroupList;
        //    GrdAdLogicalGroupMap.DataBind();
        //}


        //public bool ContainGroupItem(string FlowName, string LogicalGroupName, string Domain, string AdGroupName)
        //{
        //    bool result = false;
        //    for (int i = 0; i < DtbAdGroupList.Rows.Count; i++)
        //    {
        //        if(DtbAdGroupList.Rows[i]["FLOWNAME"].ToString()== FlowName && DtbAdGroupList.Rows[i]["LOGICALGROUPNAME"].ToString() == LogicalGroupName && DtbAdGroupList.Rows[i]["DOMAIN"].ToString() == Domain && DtbAdGroupList.Rows[i]["ADGROUPNAME"].ToString() == AdGroupName)
        //        {
        //            result = true;
        //        }
        //    }
        //    return result;
        //}
        //public void AddGroup(string FlowName,string LogicalGroupName,string Domain,string AdGroupName)
        //{
        //    if(DtbAdGroupList.Columns.Count==0)
        //    {
        //        BuildAdGroupListDataTable();
        //    }
        //    DataRow dr = DtbAdGroupList.NewRow();
        //    dr["FLOWNAME"] = FlowName;
        //    dr["LOGICALGROUPNAME"] = LogicalGroupName;
        //    dr["DOMAIN"] = Domain;
        //    dr["ADGROUPNAME"] =AdGroupName;
        //    DtbAdGroupList.Rows.Add(dr);
        //    //FillAdMembers();
        //    FillAdGroupGrid();
        //}

        //public void FillAdUserList(ApPortalServices.AdPortal.LdapServices.UserInformation[] userlist)
        //{
        //    try
        //    {
        //        foreach (var item in userlist)
        //        {
        //            if (!CheckExistUser(item.UserName))
        //            {
        //                DataTable dtbFLoginInfo = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetLoginNamewithNameSurname(item.UserName);
        //                DataRow dr = DtbAdUserList.NewRow();
        //                dr["UserName"] = item.UserName;
        //                dr["DisplayName"] = item.NameSurName;
        //                dr["FloginStatus"] = "0";
        //                //if()
        //                if (dtbFLoginInfo.Rows.Count > 0)
        //                {
        //                    dr["Flogin"] = dtbFLoginInfo.Rows[0]["F_LOGIN_ID"];
        //                    dr["DisplayName"] = dtbFLoginInfo.Rows[0]["NAME_SURNAME"];
        //                    dr["CheckResult"] = "F Login Bulundu";
        //                    dr["FloginStatus"] = "1";
        //                }
        //                else
        //                {
        //                    dr["CheckResult"] = "F Login Bulunamadı";
        //                }
        //                DtbAdUserList.Rows.Add(dr);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }

        //}

        //public bool CheckExistUser(string UserName)
        //{
        //    bool result = false;
        //    for (int i = 0; i < DtbAdUserList.Rows.Count; i++)
        //    {
        //        if(DtbAdUserList.Rows[i]["UserName"].ToString()==UserName)
        //        {
        //            result = true;
        //            break;
        //        }
        //    }
        //    return result;
        //}

        //public void BuildAdUserListDataTable()
        //{
        //    DtbAdUserList.Columns.Add("UserName");
        //    DtbAdUserList.Columns.Add("DisplayName");
        //    DtbAdUserList.Columns.Add("Flogin");
        //    DtbAdUserList.Columns.Add("CheckResult");
        //    DtbAdUserList.Columns.Add("FloginStatus");
        //}


        //public void BuildAdGroupListDataTable()
        //{
        //    DtbAdGroupList.Columns.Add("FLOWNAME");
        //    DtbAdGroupList.Columns.Add("LOGICALGROUPNAME");
        //    DtbAdGroupList.Columns.Add("DOMAIN");
        //    DtbAdGroupList.Columns.Add("ADGROUPNAME");
        //}
        //public DataTable DtbAdUserList
        //{
        //    get
        //    {
        //        if (Session["_AdUserList"] != null && Session["_AdUserList"] is DataTable)
        //        {
        //            return (DataTable)Session["_AdUserList"];
        //        }
        //        else
        //        {
        //            Session["_AdUserList"] = new DataTable();
        //            return (DataTable)Session["_AdUserList"];
        //        }
        //    }
        //    set
        //    {
        //        Session["_AdUserList"] = value;
        //    }
        //}

        //public DataTable DtbAdGroupList
        //{
        //    get
        //    {
        //        if (Session["_AdGroupList"] != null && Session["_AdGroupList"] is DataTable)
        //        {
        //            return (DataTable)Session["_AdGroupList"];
        //        }
        //        else
        //        {
        //            Session["_AdGroupList"] = new DataTable();
        //            return (DataTable)Session["_AdGroupList"];
        //        }
        //    }
        //    set
        //    {
        //        Session["_AdGroupList"] = value;
        //    }
        //}

        //protected void BtnAktar_Click(object sender, EventArgs e)
        //{
        //    //içeri kayıt atarken birden cok ad grbun eklenme ihtimali oldugu için ve map tablosuna sequence manuel atıvagımız ıcın bir
        //    //sequence ile map tablosuna attıktan sonra o ıd ile ad grubunun ıcıdnekılerı user tablosuna basmak gerekıyor aksı halde sequenceden gelen idlerin kontrolünü saglayamayız.
        //    // bir map userler, bir map userlar seklınde olmalı          

        //    #region Ekran Kontrolleri
        //        LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
        //        if (LogicalGroupMemberHelper.HasAllUsersInMembers(lg.RequestId))
        //        {
        //            this.Master.ShowPopup(false, "Üye Ekleme Hata", "Üye eklemek istediğiniz grupta tüm kullanıcılar bulunmaktadır.", true, "");
        //            return;
        //        }
        //        long IsOnePerson = lg.IsOnePersonGroup;
        //        //Tek kişilik grupsa ve o tek kişi de eklenmiş ise
        //        if (IsOnePerson == 1 && gvLogicalMembers.VisibleRowCount == 1)
        //        {
        //            this.Master.ShowPopup(false, "Hata", "Bu grup tek kişilik olarak tanımlanmış!", true, "Tek Kişilik Grup");
        //            return;
        //        }
        //    #endregion

        //    //yeni region baslatabılırsın buraya bak ıcı bos methodun
        //    try
        //    {
        //        //önce map sonra users
        //        MapAdGroupAndSaveMembers();
        //    }
        //    catch (Exception ex)
        //    {
        //        string a = ex.Message;
        //        throw;
        //    }


        //    #region Ad Grup Bazında Senkronize ediyoruz.
        //    // => Burda Ad Grupları Kaydedicez.
        //    try
        //    {
        //        //bunların hepsini kapsayan bi method yap
        //        //burda senkadGroup3'ü yaz

        //        LogicalGroupAdMapHelper.SenkAdGroup(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]), lg.Name, DtbAdGroupList);
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }

        //    #endregion
        //    //burda sorgulaya basılamdıgı ıcın DtbAdUserList null geliyo for donemıyı, sorgulaya basılmadıysa yanı DtbAdUserList nullsa onun ıcını doldurmalıyız
        //    #region Ad Grup Bazında Kullanıcı Ekleme 
        //    try
        //    {
        //            for (int i = 0; i < DtbAdUserList.Rows.Count; i++)
        //            {
        //                try
        //                {
        //                    if (DtbAdUserList.Rows[i]["Flogin"].ToString() != "")
        //                    {
        //                        LogicalGroupMember lgm = new LogicalGroupMember();
        //                        lgm.FullName = DtbAdUserList.Rows[i]["DisplayName"].ToString();
        //                        lgm.LoginId = long.Parse(DtbAdUserList.Rows[i]["Flogin"].ToString());
        //                        lgm.LogicalGroupMemberTypeId = 5;
        //                        lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
        //                        lgm.Created = DateTime.Now;
        //                        lgm.CreatedBy = UserInformation.LoginObject.LoginId;
        //                        lgm.LastUpdated = DateTime.Now;
        //                        lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        //                        //lgm.IsAdTransfer = 1;
        //                        bool OnePersonCheck = IsOnePerson == 1 && gvLogicalMembers.VisibleRowCount == 1;
        //                        bool ExistFLogin = LogicalGroupMemberHelper.CheckFLoginLogicalGroup(lgm.LogicalGroupId, lgm.LoginId);
        //                        if (!ExistFLogin && !OnePersonCheck)
        //                        {
        //                            LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
        //                            GetAllLogicalGroupMembers();
        //                        }
        //                    }
        //                }
        //                catch (Exception ex)
        //                {
        //                    throw ex;
        //                }
        //            }
        //            //UpdateLogicalGrup(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]), DrpDomain.SelectedValue, DrpGrupList.SelectedValue);
        //            GetAllLogicalGroupMembers();
        //            SendUnDefinedLoginId();
        //            //UpdateLogicalGrup(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]), DrpDomain.SelectedValue, DrpGrupList.SelectedValue);
        //            //Kullanıcı seçilmeden mantıksal grup üyesi eklenemez
        //            // Grupta Tüm Kullanıcılar Var mı kontrolü Yapılacak.
        //            // Grupta Eklenen Kullanıcı Var mı Kontrolü Yapılacak.
        //            // Tek Kişilik bir Grupsa ve O Tek Kişi Eklendi mi kontrolü yapılacak.
        //        }
        //        catch (Exception ex)
        //        {
        //            throw ex;
        //        }
        //    #endregion



        //    lblMessage.Text = "Kaydetme İşlemi Tamamlanmıştır."; 

        //    //if (UsersASPxComboBox.SelectedIndex == -1 || UsersASPxComboBox.SelectedIndex == 0)
        //    //{
        //    //    ((MasterPage)Master).ShowError("Hata", "Lütfen listeden bir kullanıcı seçiniz.");
        //    //}
        //    //else
        //    //{
        //    //    //Grupta tüm kullanıcılar var mı kontrolü yapılır
        //    //    if (!LogicalGroupMemberHelper.HasAllUsersInMembers(lg.RequestId))
        //    //    {
        //    //        string FullName = UsersASPxComboBox.SelectedItem.ToString();
        //    //        long loginId = ConvertionHelper.ConvertValue<long>(UsersASPxComboBox.SelectedItem.Value.ToString());

        //    //        bool contains = false;
        //    //        long IsOnePerson;
        //    //        IsOnePerson = lg.IsOnePersonGroup;
        //    //        //Tek kişilik grupsa ve o tek kişi de eklenmiş ise
        //    //        if (IsOnePerson == 1 && gvLogicalMembers.VisibleRowCount == 1)
        //    //        {
        //    //            ((MasterPage)Master).ShowPopup(false, "Hata", "Bu grup tek kişilik olarak tanımlanmış!", true, "Tek Kişilik Grup");
        //    //        }
        //    //        else
        //    //        {
        //    //            for (int a = 0; a < gvLogicalMembers.VisibleRowCount; a++)
        //    //            {
        //    //                object[] dataList = ((object[])(gvLogicalMembers.GetRowValues(a, "LoginId", "FullName")));
        //    //                if (dataList[0] != null && dataList[1] != null)
        //    //                {
        //    //                    if (dataList[0].ToString() == loginId.ToString() && dataList[1].ToString() == FullName)
        //    //                    {
        //    //                        contains = true;
        //    //                        break;
        //    //                    }
        //    //                }
        //    //            }
        //    //            if (!contains)
        //    //            {
        //    //                LogicalGroupMember lgm = new LogicalGroupMember();
        //    //                lgm.FullName = FullName;
        //    //                lgm.LoginId = loginId;
        //    //                lgm.LogicalGroupMemberTypeId = 1;
        //    //                lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
        //    //                gvLogicalMembers.Visible = true;
        //    //                lgm.Created = DateTime.Now;
        //    //                lgm.CreatedBy = UserInformation.LoginObject.LoginId;
        //    //                lgm.LastUpdated = DateTime.Now;
        //    //                lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        //    //                LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
        //    //                GetAllLogicalGroupMembers();
        //    //                UsersASPxComboBox.SelectedIndex = 1;
        //    //                ((MasterPage)Master).ShowPopup(false, "Üye Eklendi", "Belirttiğiniz üye eklendi", false, "");
        //    //            }
        //    //            else
        //    //            {
        //    //                ((MasterPage)Master).ShowPopup(false, "Üye Ekleme Hata", "Eklemek istediğiniz üye bu mantıksal grupta var", true, "");
        //    //            }
        //    //        }
        //    //        fn_VisibilityControlOfRemoveButtons();
        //    //    }
        //    //    else
        //    //    {
        //    //        ((MasterPage)Master).ShowPopup(false, "Üye Ekleme Hata", "Üye eklemek istediğiniz grupta tüm kullanıcılar bulunmaktadır.", true, "");
        //    //    }
        //    //}
        //}

        //protected void DrpDomain_TextChanged(object sender, EventArgs e)
        //{
        //    BuildAdPortalGrupList(DrpDomain.SelectedValue);
        //}

        //protected void BtnJobs_Click(object sender, EventArgs e)
        //{
        //    LogicalGroupJobHelper.LogicalGroupJobExecute();
        //}

        //protected void BtnAddUserList_Click(object sender, EventArgs e)
        //{
           
        //}

        //protected void BtnSearchUserList_Click(object sender, EventArgs e)
        //{
        //    DtbAdUserList.Columns.Clear();
        //    DtbAdUserList.Rows.Clear();
        //    BuildAdUserListDataTable();
        //    for (int i = 0; i < DtbAdGroupList.Rows.Count; i++)
        //    {
        //        KullaniciEkle(DtbAdGroupList.Rows[i]["DOMAIN"].ToString(), DtbAdGroupList.Rows[i]["ADGROUPNAME"].ToString());
        //    }
        //    FillAdGrid();
        //    //FillAdMembers();
        //    FillAdGroupGrid();
        //}

        //protected void BtnAdEkle_Click(object sender, EventArgs e)
        //{
        //    if (!ContainGroupItem(WorkFlowCombobox1.SelectedItem.Text, ASPxComboBoxLogicalGroup1.SelectedItem.Text, DrpDomain.SelectedValue, DrpGrupList.SelectedValue))
        //    {
        //        AddGroup(WorkFlowCombobox1.SelectedItem.Text, ASPxComboBoxLogicalGroup1.SelectedItem.Text, DrpDomain.SelectedValue, DrpGrupList.SelectedValue);
        //    }
        //    FillAdGrid();//gereksiz cünku add groupun içinde bu var
        //    FillAdGroupGrid();
        //}

        //protected void DeleteGroupButton_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        for (int i = 0; i < DtbAdGroupList.Rows.Count; i++)
        //        {
        //            if (DtbAdGroupList.Rows[i]["ADGROUPNAME"].ToString() == Session["ADGROUPNAME"].ToString() && i.ToString() == Session["VISIBLEINDEX"].ToString())
        //            {
        //                DtbAdGroupList.Rows[i].Delete();
        //                DtbAdGroupList.AcceptChanges();
        //            }
        //        }
        //        FillAdGroupGrid();
        //        BtnSearchUserList_Click(this, null);
        //    }
        //    catch (Exception ex)
        //    {
        //        throw ex;
        //    }
        //}

        //protected void GrdAdLogicalGroupMap_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
        //{
        //    Session["ADGROUPNAME"] = e.KeyValue;
        //    Session["VISIBLEINDEX"] = e.VisibleIndex;
        //}

        //private void MapAdGroupAndSaveMembers()
        //{
        //    long adGroupId = 0;
        //    LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
        //    foreach (DataRow adGroupMapRow in DtbAdGroupList.Rows)
        //    {
        //        adGroupId = LogicalGroupAdMapHelper.SenkAdGroup3üncüMapTabloIdDönen(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]), adGroupMapRow);// bu methodu teke indir burda forda hepsını tek tek don
        //        if (adGroupId != 0)
        //        {


        //            #region Ad Grup Bazında Kullanıcı Ekleme 
        //            try
        //            {
        //                for (int i = 0; i < DtbAdUserList.Rows.Count; i++)
        //                {
        //                    try
        //                    {
        //                        if (DtbAdUserList.Rows[i]["Flogin"].ToString() != "")
        //                        {
        //                            LogicalGroupMember lgm = new LogicalGroupMember();
        //                            lgm.FullName = DtbAdUserList.Rows[i]["DisplayName"].ToString();
        //                            lgm.LoginId = long.Parse(DtbAdUserList.Rows[i]["Flogin"].ToString());
        //                            lgm.LogicalGroupMemberTypeId = 5;
        //                            lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
        //                            lgm.Created = DateTime.Now;
        //                            lgm.CreatedBy = UserInformation.LoginObject.LoginId;
        //                            lgm.LastUpdated = DateTime.Now;
        //                            lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
        //                            //lgm.IsAdTransfer = 1;
        //                            lgm.YYS_LG_AD_MEMBERS_ID = adGroupId;
        //                            bool OnePersonCheck = lg.IsOnePersonGroup == 1 && gvLogicalMembers.VisibleRowCount == 1;
        //                            bool ExistFLogin = LogicalGroupMemberHelper.CheckFLoginLogicalGroup(lgm.LogicalGroupId, lgm.LoginId);
        //                            if (!ExistFLogin && !OnePersonCheck)
        //                            {
        //                                LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
        //                                //GetAllLogicalGroupMembers();
        //                            }
        //                        }
        //                    }
        //                    catch (Exception ex)
        //                    {
        //                        throw ex;
        //                    }
        //                }
        //                //UpdateLogicalGrup(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]), DrpDomain.SelectedValue, DrpGrupList.SelectedValue);
        //                GetAllLogicalGroupMembers();
        //                SendUnDefinedLoginId();
        //                //UpdateLogicalGrup(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]), DrpDomain.SelectedValue, DrpGrupList.SelectedValue);
        //                //Kullanıcı seçilmeden mantıksal grup üyesi eklenemez
        //                // Grupta Tüm Kullanıcılar Var mı kontrolü Yapılacak.
        //                // Grupta Eklenen Kullanıcı Var mı Kontrolü Yapılacak.
        //                // Tek Kişilik bir Grupsa ve O Tek Kişi Eklendi mi kontrolü yapılacak.
        //            }
        //            catch (Exception ex)
        //            {
        //                throw ex;
        //            }
        //            #endregion
        //        }
        //    }


        //}
    }
}