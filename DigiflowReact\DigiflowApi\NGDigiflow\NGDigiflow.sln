﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.31205.134
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Digiturk.Workflow.Digiflow.Api.Core", "Digiturk.Workflow.Digiflow.Api.Core\Digiturk.Workflow.Digiflow.Api.Core.csproj", "{595809FB-92A0-461E-AF40-3562BF3C5CE6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Digiturk.Workflow.Digiflow.Api.Common", "Digiturk.Workflow.Digiflow.Api.Common\Digiturk.Workflow.Digiflow.Api.Common.csproj", "{2D7F1806-4791-4C74-B0AE-5001DF76052A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Digiturk.Workflow.Digiflow.Api.WorkflowHelper", "Digiturk.Workflow.Digiflow.Api.WorkflowHelper\Digiturk.Workflow.Digiflow.Api.WorkflowHelper.csproj", "{5B9B9ED7-9429-4656-9FC3-3BD2B9A67124}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Digiturk.Workflow.Digiflow.Api.Authentication", "Digiturk.Workflow.Digiflow.Api.Authentication\Digiturk.Workflow.Digiflow.Api.Authentication.csproj", "{D30D3DBF-44F3-4BB9-B6D9-76B4DBCC6428}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Digiturk.Workflow.Digiflow.Api.Authorization", "Digiturk.Workflow.Digiflow.Api.Authorization\Digiturk.Workflow.Digiflow.Api.Authorization.csproj", "{0CA20F0B-160F-44D9-93B9-1A3293597305}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Digiturk.Workflow.Digiflow.Api", "Digiturk.Workflow.Digiflow.Api\Digiturk.Workflow.Digiflow.Api.csproj", "{1BE0DF8D-A6B5-405F-9BB5-C525467EAE48}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Digiturk.Workflow.Digiflow.Controller", "Digiturk.Workflow.Digiflow.Controller\Digiturk.Workflow.Digiflow.Controller.csproj", "{D1EB21A4-0F35-437A-8723-49ADC3425B9A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{595809FB-92A0-461E-AF40-3562BF3C5CE6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{595809FB-92A0-461E-AF40-3562BF3C5CE6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{595809FB-92A0-461E-AF40-3562BF3C5CE6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{595809FB-92A0-461E-AF40-3562BF3C5CE6}.Release|Any CPU.Build.0 = Release|Any CPU
		{2D7F1806-4791-4C74-B0AE-5001DF76052A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2D7F1806-4791-4C74-B0AE-5001DF76052A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2D7F1806-4791-4C74-B0AE-5001DF76052A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2D7F1806-4791-4C74-B0AE-5001DF76052A}.Release|Any CPU.Build.0 = Release|Any CPU
		{5B9B9ED7-9429-4656-9FC3-3BD2B9A67124}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5B9B9ED7-9429-4656-9FC3-3BD2B9A67124}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5B9B9ED7-9429-4656-9FC3-3BD2B9A67124}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5B9B9ED7-9429-4656-9FC3-3BD2B9A67124}.Release|Any CPU.Build.0 = Release|Any CPU
		{D30D3DBF-44F3-4BB9-B6D9-76B4DBCC6428}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D30D3DBF-44F3-4BB9-B6D9-76B4DBCC6428}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D30D3DBF-44F3-4BB9-B6D9-76B4DBCC6428}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D30D3DBF-44F3-4BB9-B6D9-76B4DBCC6428}.Release|Any CPU.Build.0 = Release|Any CPU
		{0CA20F0B-160F-44D9-93B9-1A3293597305}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0CA20F0B-160F-44D9-93B9-1A3293597305}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0CA20F0B-160F-44D9-93B9-1A3293597305}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0CA20F0B-160F-44D9-93B9-1A3293597305}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BE0DF8D-A6B5-405F-9BB5-C525467EAE48}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BE0DF8D-A6B5-405F-9BB5-C525467EAE48}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BE0DF8D-A6B5-405F-9BB5-C525467EAE48}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BE0DF8D-A6B5-405F-9BB5-C525467EAE48}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1EB21A4-0F35-437A-8723-49ADC3425B9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1EB21A4-0F35-437A-8723-49ADC3425B9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1EB21A4-0F35-437A-8723-49ADC3425B9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1EB21A4-0F35-437A-8723-49ADC3425B9A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {ADB5D90B-A5BA-4ABB-B159-1A95C59F56AC}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 8
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://code.digiturk.net:8080/defaultcollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = Digiturk.Workflow.Digiflow.Api.Authentication\\Digiturk.Workflow.Digiflow.Api.Authentication.csproj
		SccProjectName1 = Digiturk.Workflow.Digiflow.Api.Authentication
		SccLocalPath1 = Digiturk.Workflow.Digiflow.Api.Authentication
		SccProjectUniqueName2 = Digiturk.Workflow.Digiflow.Api.Authorization\\Digiturk.Workflow.Digiflow.Api.Authorization.csproj
		SccProjectName2 = Digiturk.Workflow.Digiflow.Api.Authorization
		SccLocalPath2 = Digiturk.Workflow.Digiflow.Api.Authorization
		SccProjectUniqueName3 = Digiturk.Workflow.Digiflow.Api.Common\\Digiturk.Workflow.Digiflow.Api.Common.csproj
		SccProjectName3 = Digiturk.Workflow.Digiflow.Api.Common
		SccLocalPath3 = Digiturk.Workflow.Digiflow.Api.Common
		SccProjectUniqueName4 = Digiturk.Workflow.Digiflow.Api.Core\\Digiturk.Workflow.Digiflow.Api.Core.csproj
		SccProjectName4 = Digiturk.Workflow.Digiflow.Api.Core
		SccLocalPath4 = Digiturk.Workflow.Digiflow.Api.Core
		SccProjectUniqueName5 = Digiturk.Workflow.Digiflow.Api.WorkflowHelper\\Digiturk.Workflow.Digiflow.Api.WorkflowHelper.csproj
		SccProjectName5 = Digiturk.Workflow.Digiflow.Api.WorkflowHelper
		SccLocalPath5 = Digiturk.Workflow.Digiflow.Api.WorkflowHelper
		SccProjectUniqueName6 = Digiturk.Workflow.Digiflow.Api\\Digiturk.Workflow.Digiflow.Api.csproj
		SccProjectName6 = Digiturk.Workflow.Digiflow.Api
		SccLocalPath6 = Digiturk.Workflow.Digiflow.Api
		SccProjectUniqueName7 = Digiturk.Workflow.Digiflow.Controller\\Digiturk.Workflow.Digiflow.Controller.csproj
		SccProjectName7 = Digiturk.Workflow.Digiflow.Controller
		SccLocalPath7 = Digiturk.Workflow.Digiflow.Controller
	EndGlobalSection
EndGlobal
