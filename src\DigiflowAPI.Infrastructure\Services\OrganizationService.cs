using DigiflowAPI.Application.DTOs;
using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Application.DTOs.Organization;
using DigiflowAPI.Application.DTOs.User;
using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services;


namespace DigiflowAPI.Infrastructure.Services
{
    public class OrganizationService(IOrganizationRepository organizationRepository) : IOrganizationService
    {
        public string GetDepartmentName(long managerID)
        {
            return organizationRepository.GetDepartment(managerID);
        }

        public async Task<IEnumerable<SelectOptionDto>> GetDepartmentSelectAsync(long? id)
        {
            var departments = await organizationRepository.GetDepartmentSelectAsync(id);
            return departments.Select(d => new SelectOptionDto
            {
                Value = d.Id.ToString(),
                Label = d.Bolum,
                LabelEn = d.DepsEn
            });
        }

        public async Task<DPHRUsersDto?> GetDPHRUserByIdAsync(long? userId = null)
        {
            var user = await organizationRepository.GetDPHRUserByIdAsync(userId);
            if (user == null) return null;

            return new DPHRUsersDto
            {
                DeptId = (long)(user.DeptId ?? 0)
            };
        }

        public async Task<DepsPathSelectDto?> GetDPHRUserDepsPathByIdAsync(long? userId = null)
        {
            var depsPath = await organizationRepository.GetDPHRUserDepsPathByIdAsync(userId);
            if (depsPath == null) return null;

            return new DepsPathSelectDto
            {
                DepsEn = depsPath.DepsEn,
                DepsTr = depsPath.DepsTr,
                NameSurname = ""
            };
        }

        public async Task<IEnumerable<DpHrDeps>> GetSubDepartments(long departmentId)
        {
            return await organizationRepository.GetSubDepartments(departmentId);
        }

        public async Task<OrganizationSchemaParamsDto> GetOrganizationHierarchy(long? wfInstanceId)
        {
            return await organizationRepository.GetOrganizationHierarchy(wfInstanceId);
        }
    }
}
