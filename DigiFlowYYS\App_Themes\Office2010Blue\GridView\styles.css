.dxgvControl_Office2010Blue,
.dxgvDisabled_Office2010Blue
{
	border: 1px solid #8ba0bc;
	font: 8pt Verdana;
	background-color: #e4effa;
	color: Black;
	cursor: default;
}
.dxgvDisabled_Office2010Blue
{
	color: #b2b7bd;
}
.dxgvControl_Office2010Blue a
{
	color: #1e395b;
	text-decoration: none;
}
.dxgvControl_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxgvDisabled_Office2010Blue a
{
	color: #b2b7bd;
}
.dxgvLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxgvLoadingPanel_Office2010Blue td
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxgvLoadingPanelStatusBar_Office2010Blue
{
	background-color: Transparent;
	font: 8pt Verdana;
}
.dxgvLoadingPanelStatusBar_Office2010Blue td
{
	white-space: nowrap;
	text-align: center;
	padding: 0 2px;
}
.dxgvFilterPopupWindow_Office2010Blue
{
	color: Black;
	font: 8pt Verdana;
	border: 1px solid #8ba0bc;
}
.dxgvFilterPopupItemsArea_Office2010Blue
{
	color: Black;
	background-color: White;
	padding-top: 1px;
}
.dxgvFilterPopupButtonPanel_Office2010Blue
{
	font: 8pt Verdana;
	background-color: #cfddee;
	border-top: 1px solid #8ba0bc;
	color: #1e395b;
}

.dxgvFilterPopupItem_Office2010Blue td.dxgv,
.dxgvFilterPopupActiveItem_Office2010Blue td.dxgv,
.dxgvFilterPopupSelectedItem_Office2010Blue td.dxgv
{
	border-left: 1px solid white;
	border-right: 1px solid white;
	border-bottom: 1px solid white;
	padding: 3px 2px 3px 3px;
	cursor: default;
	white-space: nowrap;
}
.dxgvFilterPopupActiveItem_Office2010Blue
{
	background: #d5e8ff;
	color: Black;
}
.dxgvFilterPopupSelectedItem_Office2010Blue
{
	background: #e2ecf7;
	color: Black;
}

.dxgvTable_Office2010Blue
{
	background-color: White;
	border: 0;
	border-collapse: separate!important;
	overflow: hidden;
	font: 8pt Verdana;
	color: Black;
}
.dxgvInlineEditRow_Office2010Blue,
.dxgvDataRow_Office2010Blue
{
}
.dxgvInlineEditRow_Office2010Blue td.dxgv
{
	border-bottom: 1px solid #cfddee;
	border-right: 1px solid #cfddee;
}
.dxgvInlineEditRow_Office2010Blue .dxgvCommandColumn_Office2010Blue
{
    background: #e9edf3;
}
.dxgvDataRowAlt_Office2010Blue
{
	background-color: #f4f6f9;
}
.dxgvFilterRow_Office2010Blue
{
	background-color: White;
}
.dxgvEditForm_Office2010Blue
{
	background-color: #ecf2f9;
}
.dxgvEditForm_Office2010Blue td.dxgv
{
	border-bottom: 1px solid #cfddee;
	padding: 8px 10px 10px;
}
.dxgvEditForm_Office2010Blue td.dxgvIndentCell
{
    background: White;
	border-right: Solid 1px #CFDDEE;
	border-left: Solid 1px #CFDDEE;
	border-top: 0px;
}
.dxgvSelectedRow_Office2010Blue
{
	background-color: #fdf7d9;
    color: Black;
}
.dxgvFocusedRow_Office2010Blue
{
	background: #faedb6;
    color: Black;
}
.dxgvSelectedRow_Office2010Blue .dxgvCommandColumn_Office2010Blue a,
.dxgvFocusedRow_Office2010Blue .dxgvCommandColumn_Office2010Blue a
{
    color:  #337cd4;
	text-decoration: none;
}
.dxgvSelectedRow_Office2010Blue .dxgvCommandColumn_Office2010Blue a:hover,
.dxgvFocusedRow_Office2010Blue .dxgvCommandColumn_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxgvSelectedRow_Office2010Blue .dxgvCommandColumn_Office2010Blue a:visited,
.dxgvFocusedRow_Office2010Blue .dxgvCommandColumn_Office2010Blue a:visited
{
    color:  #8467b2;
}

.dxgvPreviewRow_Office2010Blue
{
	background-color: #e9edf3;
	color: #777777;
}
.dxgvDetailCell_Office2010Blue,
.dxgvPreviewRow_Office2010Blue td.dxgv,
.dxgvEmptyDataRow_Office2010Blue td.dxgv
{
	padding: 20px 2px 20px 4px;
	border-bottom: 1px solid #cfddee;
	border-top: 0;
	border-left: 0;
	border-right: 0;
}
.dxgvDetailCell_Office2010Blue
{
    border-bottom-color: #cfddee;
}
.dxgvPreviewRow_Office2010Blue td.dxgv
{
	padding: 10px 10px 10px 15px;
}
.dxgvDetailCell_Office2010Blue
{
	padding: 16px 18px;
}
.dxgvDetailRow_Office2010Blue td.dxgvIndentCell
{
    padding-right: 0px;
    border-bottom: 1px solid #cfddee;
}
.dxgvEmptyDataRow_Office2010Blue
{
	color: #777777;
}
.dxgvEmptyDataRow_Office2010Blue td.dxgv
{
    border-bottom: 1px solid #cfddee;
	text-align: center;
}

.dxgvEditFormDisplayRow_Office2010Blue td.dxgv,
.dxgvDataRow_Office2010Blue td.dxgv,
.dxgvDataRowAlt_Office2010Blue td.dxgv,
.dxgvSelectedRow_Office2010Blue td.dxgv,
.dxgvFocusedRow_Office2010Blue td.dxgv
{
	overflow: hidden;
	border-bottom: 1px solid #cfddee;
	border-right: 1px solid #cfddee;
	border-top: 0;
	border-left: 0;
	padding: 4px 6px;
}
.dxgvEditFormDisplayRow_Office2010Blue
{
}
.dxgvEditFormDisplayRow_Office2010Blue td.dxgv
{
}
.dxgvEditFormDisplayRow_Office2010Blue td.dxgvIndentCell
{
    background: white;
	border-right: Solid 1px #CFDDEE;
	border-left: Solid 1px #CFDDEE;
	border-top: 0px;
}

.dxgvEditingErrorRow_Office2010Blue
{
	background-color: #cfddee;
	color: #ba1717;
}
.dxgvEditingErrorRow_Office2010Blue td.dxgv
{
	white-space: pre-wrap;
	border-bottom: 1px solid #cfddee;
	border-right: 0;
	border-top: 0;
	border-left: 0;
	padding: 6px 10px;
}

.dxgvFilterRow_Office2010Blue td.dxgv
{
	border-bottom: 1px solid #8ba0bc;
	border-right-width: 0;
	border-top: 0;
	border-left: 0;
	padding: 2px 3px 2px 2px;
	overflow: hidden;
}
.dxgvGroupRow_Office2010Blue
{
	background-color: White;
}
.dxgvFocusedGroupRow_Office2010Blue
{
	background: #faedb6;
    color: Black;
}
.dxgvGroupRow_Office2010Blue td.dxgv,
.dxgvFocusedGroupRow_Office2010Blue td.dxgv
{
	border: none 0;
	vertical-align: middle;
	white-space: nowrap;
	border-bottom: 1px solid #cfddee;
	padding: 4px 6px;
}
.dxgvFocusedRow_Office2010Blue td.dxgvIndentCell,
.dxgvFocusedGroupRow_Office2010Blue td.dxgvIndentCell,
.dxgvSelectedRow_Office2010Blue td.dxgvIndentCell
{
	background-color: White!important;
	border-right: 1px solid #cfddee;
	border-left: 1px solid #cfddee;
	border-top: 0px;
}
.dxgvHeaderPanel_Office2010Blue
{
	background-color: #cfddee;
	color: Black;
	padding: 8px 6px;
	border-bottom: 1px solid #8ba0bc;
}

.dxgvHeader_Office2010Blue
{
	cursor: pointer;
	white-space: nowrap;
	padding: 5px 6px;
	border: 1px solid #8ba0bc;
	background: #e4effa url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.GridView.gvHeaderBack.png")%>') repeat-x left top;
	overflow: hidden;
	font-weight: normal;
	text-align: left;
}
.dxgvHeader_Office2010Blue,
.dxgvHeader_Office2010Blue table
{
	color: #1e395b;
	font: 8pt Verdana;
}
.dxgvHeader_Office2010Blue td
{
	white-space: nowrap;
}
.dxgvHeader_Office2010Blue a
{
	color: #1e395b;
	text-decoration: underline;
}
.dxgvCustomization_Office2010Blue,
.dxgvPopupEditForm_Office2010Blue
{
	width: 100%;
	padding: 0 0 0 0;
	margin: 0 0 0 0;
}
.dxgvPopupEditForm_Office2010Blue .dxgvEditingErrorRow_Office2010Blue .dxgv
{
    border-bottom-width: 0;
}
.dxgvGroupPanel_Office2010Blue
{
	white-space: nowrap;
	font-size: 8pt;

	background-color: #cfddee;
	color: #1e395b;
	border-bottom: 1px solid #8ba0bc;
	padding: 7px 4px 10px 6px;
}
.dxgvFooter_Office2010Blue
{
	background-color: #e2ebf6;
	white-space: nowrap;
}
.dxgvFooter_Office2010Blue td.dxgv
{
	padding: 6px;
	border-bottom: 1px solid #cfddee;
	border-right: 0;
}
.dxgvGroupFooter_Office2010Blue
{
	background-color: #e2ebf6;
}
.dxgvGroupFooter_Office2010Blue td.dxgv
{
    padding: 6px;
    border-bottom: 1px solid #cfddee;
    border-right: 0;
    white-space: nowrap;
}
.dxgvDataRow_Office2010Blue td.dxgvIndentCell,
.dxgvGroupRow_Office2010Blue td.dxgvIndentCell,
.dxgvGroupFooter_Office2010Blue td.dxgvIndentCell
{
    background-color: White;
	border-right: 1px solid #cfddee;
	border-left: 1px solid #cfddee;
	border-top: 0px;
}

.dxgvTitlePanel_Office2010Blue,
.dxgvTable_Office2010Blue caption
{
    font-size: 8pt;
	font-weight: normal;
	padding: 3px 3px 5px;
	text-align: center;
	background: #bdd0e7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.GridView.gvTitleBack.png")%>') repeat-x left top;
	color: #1e395b;
	border-bottom: 1px solid #8ba0bc;
}
.dxgvLoadingDiv_Office2010Blue
{
	background-color:Gray;
	opacity: 0.01;
	filter: alpha(opacity=1);
}
.dxgvStatusBar_Office2010Blue
{
    background: #cfddee;
	border-top: 1px solid #8ba0bc;
}
.dxgvStatusBar_Office2010Blue tr.dxgv
{
	height: 20px;
}
.dxgvCommandColumn_Office2010Blue
{
	padding: 2px;
}
.dxgvCommandColumn_Office2010Blue a
{
	margin: 0px 3px 0px 0px;
	color:  #337cd4;
	text-decoration: none;
}
.dxgvCommandColumn_Office2010Blue a:hover
{
	text-decoration: underline;
}
.dxgvCommandColumn_Office2010Blue a:visited
{
	color:  #8467b2;
}

.dxgvCommandColumnItem_Office2010Blue
{
}
.dxgvEditFormTable_Office2010Blue
{
	padding: 2px 6px 6px 4px;
	font: 8pt Verdana;
	color: Black;
}
.dxgvEditFormTable_Office2010Blue a
{
    color:  #337cd4;
	text-decoration: none;
}
.dxgvEditFormTable_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxgvEditFormTable_Office2010Blue a:visited
{
    color:  #8467b2;
}

.dxgvEditFormCaption_Office2010Blue
{
	padding: 4px 4px 4px 10px;
    white-space: nowrap;
}

.dxgvInlineEditCell_Office2010Blue
{
	padding: 1px;
}

.dxgvEditFormCell_Office2010Blue
{
	padding: 4px;
	border: 0;
}

.dxgvPagerTopPanel_Office2010Blue,
.dxgvPagerBottomPanel_Office2010Blue
{
    background: #e4effa;
}
.dxgvDetailButton_Office2010Blue
{
}

.dxgvFilterBar_Office2010Blue
{
	border-top: 1px solid #8ba0bc;
	background: #cfddee;
}
.dxgvFilterBar_Office2010Blue a
{
	color: #337cd4;
	text-decoration: underline;
}
.dxgvFilterBarCheckBoxCell_Office2010Blue
{
	padding: 0 3px;
	padding-right: 7px;
}
.dxgvFilterBarImageCell_Office2010Blue
{
	padding: 0 3px;
	padding-right: 1px;
	cursor: pointer;
}
.dxgvFilterBarExpressionCell_Office2010Blue
{
	font-size: 8pt;
	padding: 5px 5px 8px 0;
	white-space: nowrap;
}
.dxgvFilterBarClearButtonCell_Office2010Blue
{
	font-size: 8pt;
	padding: 5px 6px 8px;
}
.dxgvFilterBuilderMainArea_Office2010Blue
{
	background: white;
	padding: 6px 2px;
}
.dxgvFilterBuilderButtonArea_Office2010Blue
{
	background: #cfddee;
	border-top: 1px solid #aec0d5;
	padding: 6px;
}

.dxgvDataRowHover_Office2010Blue
{
	background: #fbf4d7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.GridView.gvHoverBack.png")%>') repeat-x left top;
    color: Black;
}

.dxgvControl_Office2010Blue .dxpControl_Office2010Blue td.dxpCtrl_Office2010Blue,
.dxgvDisabled_Office2010Blue .dxpControl_Office2010Blue td.dxpCtrl_Office2010Blue,

.dxgvControl_Office2010Blue .dxpLite_Office2010Blue,
.dxgvDisabled_Office2010Blue .dxpLite_Office2010Blue
{
	padding-top: 4px;
}