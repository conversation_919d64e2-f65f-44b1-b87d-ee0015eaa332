using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Interfaces.Services.Workflow;
using Microsoft.AspNetCore.Http;
using System.Text;
using System.Text.Json;

namespace DigiflowAPI.Infrastructure.Services.Workflow
{
    public class ApproveWorkflowService(IHttpService httpService, IHttpContextAccessor httpContextAccessor, IGlobalHelpers globalHelpers, IUserService userService) : IApproveWorkflowService
    {
        public async Task<string> Execute(ApprovalWorkflowRequestDto workflowData)
        {
            JsonSerializerOptions options = new()
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };

            // Extract the Accept-Language header from the incoming request
            var language = httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en";
            var headers = new Dictionary<string, string>
            {
                {"Accept-Language", language}
            };

            // Using System.Text.Json for serialization
            var jsonContent = JsonSerializer.Serialize(workflowData, options);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");


            if (workflowData.ActionType.HasValue && workflowData.ActionType.Value == Domain.Enums.WorkflowHistoryActionType.SENDTASK && workflowData.SendTaskUserId != null)
            {

            }
            return await httpService.PostDataAsync(globalHelpers.CompatibilityApiUrl(), "api/workflow/approve", content, headers);
        }
    }
}