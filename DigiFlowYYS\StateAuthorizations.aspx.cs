﻿using DevExpress.Web.ASPxGridView;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Web.UI;

public partial class StateAuthorizations : YYSSecurePage
{
    /// <summary>
    /// Statik atama kurallarının görüntülendiği ekran
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        try
        {
            ((MasterPage)Master).ShowMenu(true);
            ((MasterPage)Master).PageTitle = "Adım Bazlı Akış Kuralları";
            if (!Page.IsPostBack)
            {
                //Kullanıcının iş akışları doldurulur
                long IsAdmin = 0;

                if (Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAndFlowAdmin.ToString() || Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAdmin.ToString())
                {
                    IsAdmin = 1;
                }
                WorkfowListASPxComboBox = Common.FillComboboxWorkFlow("Name", "WorkflowDefId", WorkfowListASPxComboBox, WorkflowHelper.GetWorkflowsOfAdmin(UserInformation.LoginObject.LoginId, IsAdmin), "İş Akışı Seçiniz", "0");
            }
            if (Page.IsCallback)
            {
                //List<StateAuthorization> items = StateAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(Session["WfDefId"]));
                if (Session["WorkFlowRulesGridView"] != null)
                {
                    WorkFlowRulesGridView.DataSource = (DataTable)Session["WorkFlowRulesGridView"];
                    WorkFlowRulesGridView.DataBind();
                }
            }
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowPopup(false, "Hata", "Sayfa yüklenirken beklenmeyen bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.Message);
        }
    }

    /// <summary>
    /// İş akışı değiştiği zaman o iş akışının kurallarını getirir
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkfowListASPxComboBox_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            Session["WfDefId"] = WorkfowListASPxComboBox.SelectedItem.Value;
            List<StateAuthorization> items = StateAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
            DataTable dtRules = ConvertToDataTable(items);
            WorkFlowRulesGridView.DataSource = dtRules;
            Session["WorkFlowRulesGridView"] = dtRules;
            WorkFlowRulesGridView.DataBind();
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowPopup(false, "Hata", "Kurallar listelenirken bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.Message);
        }
    }

    #region Inner Functions

    /// <summary>
    /// Grid teki int değerlerin isim karşılıklarını bulup değiştirir
    /// </summary>
    /// <param name="items">State authorization nesneleri</param>
    /// <returns></returns>
    private DataTable ConvertToDataTable(List<StateAuthorization> items)
    {
        DataTable dt = new DataTable();
        dt.Columns.Add("RequestId");
        dt.Columns.Add("State");
        dt.Columns.Add("Active");
        dt.Columns.Add("AssignmentTypeId");
        dt.Columns.Add("AssignmentOwnerId");
        dt.AcceptChanges();

        foreach (StateAuthorization item in items)
        {
            DataRow r = dt.NewRow();
            r["RequestId"] = item.RequestId;
            r["State"] = WorkflowHelper.GetState(item.StateDefId).Name;

            r["Active"] = ConvertIsActive(ConvertionHelper.ConvertValue<Boolean>(item.IsActive));
            r["AssignmentTypeId"] = ConvertAssignmentType(item.AssignmentTypeId);
            r["AssignmentOwnerId"] = ConvertUser(item.AssignmentOwnerId);
            dt.Rows.Add(r);
        }

        dt.AcceptChanges();
        return dt;
    }

    /// <summary>
    /// AssignmentType dönüşümü yapılır
    /// </summary>
    /// <param name="AssignmentTypeId"></param>
    /// <returns></returns>
    private string ConvertAssignmentType(long AssignmentTypeId)
    {
        return AssignmentTypeId.ToString();
    }

    /// <summary>
    /// Grid teki Int olan isActive değerini Aktif veya Pasif olarak değiştirir
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private string ConvertIsActive(bool val)
    {
        return val ? "Evet" : "Hayır";
    }

    /// <summary>
    /// Grid teki loginId değerinin karşılığı olan isme çevirir
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private string ConvertUser(long val)
    {
        string ret = string.Empty;

        var lg = LogicalGroupHelper.Get(val);
        ret = lg.Name;

        return ret;
    }

    #endregion Inner Functions

    protected void WorkFlowRulesGridView_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
    {
        //#region Admin in akış yöneticisi olduğu akışı Silme İşlemi
        //long ruleId = ConvertionHelper.ConvertValue<long>(e.Values[3]);
        //ASPxGridView gridView = (ASPxGridView)sender;

        //StateAuthorizationHelper.DeleteWorkflowRule(ruleId);
        //((MasterPage)Master).ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
        ////DeleteButton.Enabled = false;
        ////    SaveButton.Enabled = false;
        //FillGrid();

        //gridView.CancelEdit();
        //e.Cancel = true;
        //#endregion
    }

    /// <summary>
    /// İş Akışına göre grid doldurulur
    /// </summary>
    private void FillGrid()
    {
        #region O akışın tüm kuralları doldurulur

        List<StateAuthorization> items = StateAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
        WorkFlowRulesGridView.DataSource = ConvertToDataTable(items);
        WorkFlowRulesGridView.DataBind();

        #endregion O akışın tüm kuralları doldurulur
    }

    /// <summary>
    /// Silme işlemi burada yapılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DeleteRuleButton_Click(object sender, EventArgs e)
    {
        #region Admin in akış yöneticisi olduğu akışı Silme İşlemi

        long ruleId = ConvertionHelper.ConvertValue<long>(Session["DeletingWorkFlowRule"]);

        StateAuthorizationHelper.DeleteWorkflowRule(ruleId);
        ((MasterPage)Master).ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
        FillGrid();

        #endregion Admin in akış yöneticisi olduğu akışı Silme İşlemi
    }

    /// <summary>
    /// Düzenle butonuna basıldığı zaman üstteki combobox ları doldurur
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowRulesGridView_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
    {
        Session["DeletingWorkFlowRule"] = e.KeyValue.ToString();
    }

    /// <summary>
    /// Sayfalamada datayı daha hızlı getirmek için kullanılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowRulesGridViewPageIndexChanging(object sender, EventArgs e)
    {
        DataTable dtInboxGridView = new DataTable();
        if (Session["WorkFlowRulesGridView"] != null)
        {
            dtInboxGridView = (DataTable)Session["WorkFlowRulesGridView"];
        }
        else
        {
            List<StateAuthorization> items = StateAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
            dtInboxGridView = ConvertToDataTable(items);
            Session["WorkFlowRulesGridView"] = dtInboxGridView;
        }
        WorkFlowRulesGridView.DataSource = dtInboxGridView;
        WorkFlowRulesGridView.DataBind();
        dtInboxGridView.Dispose();
        dtInboxGridView = null;
    }
}