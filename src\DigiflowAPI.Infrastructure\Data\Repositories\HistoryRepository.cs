﻿using DigiflowAPI.Application.DTOs.Inbox;
using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Domain.Entities.History;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Domain.Structs;
using DigiflowAPI.Infrastructure.Data.SqlScripts.History;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Domain.Entities;
using AutoMapper;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class HistoryRepository(IOracleDataAccessRepositoryFactory repositoryFactory, IHttpService httpService, IConfiguration configuration, IGlobalHelpers globalHelpers, IHttpContextAccessor httpContextAccessor, IUserService userService, IMapper mapper) : IHistoryRepository
    {
        private bool IsDebugMode => configuration.GetValue<bool>("EmailSettings:IsMailDebugMode");

        public async Task<IEnumerable<TbWorkflowHistoryView>> GetAllAsync(string workflowType, string? filteredYears)
        {
            // Get the language from the HTTP context
            string language = httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en";

            // Get the user ID from the HTTP context or user service
            long userId;
            if (!long.TryParse(httpContextAccessor.HttpContext.Request.Headers["X-Login-Id"].ToString(), out userId))
            {
                userId = await userService.GetUserInfo();
            }

            // Initialize parameters
            string taskType = "0";
            string lastAction = "0";
            string wfLastAction = "0";

            string adjustedWorkflowType = workflowType;

            // Adjust parameters based on the workflowType
            if (!string.IsNullOrEmpty(workflowType))
            {
                switch (workflowType.ToUpper())
                {
                    case "SUSPENDED":
                        lastAction = "SUSPEND";
                        adjustedWorkflowType = "0";
                        break;
                    case "ACCEPTED":
                        wfLastAction = "ACCEPTED";
                        adjustedWorkflowType = "COMPLETED";
                        break;
                    case "REJECTED":
                        wfLastAction = "REJECTED";
                        adjustedWorkflowType = "COMPLETED";
                        break;
                    case "CANCELED":
                        wfLastAction = "CANCEL";
                        adjustedWorkflowType = "CANCELED";
                        break;
                    case "STARTED":
                        adjustedWorkflowType = "STARTED";
                        break;
                    default:
                        // Handle other cases if necessary
                        break;
                }
            }

            // Generate the SQL query
            string historyQuery = HistorySqlQueries.GetHistorySql(language, true, adjustedWorkflowType, lastAction, wfLastAction, filteredYears);

            // Prepare the parameters
            var parameters = new Dictionary<string, object>
            {
                { "loginValue", userId },
                { "WfStatusType", adjustedWorkflowType },
                { "TaskStatus", taskType },
                { "LastAction", lastAction },
            };

            // Include WF_LAST_ACTION parameter if necessary
            if (adjustedWorkflowType != "STARTED" && !string.IsNullOrEmpty(wfLastAction) && wfLastAction != "0")
            {
                parameters.Add("WF_LAST_ACTION", wfLastAction);
            }
            else
            {
                parameters.Add("WF_LAST_ACTION", "0");
            }

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            try
            {
                var result = await repository.ExecuteQueryAsync<TbWorkflowHistoryView>(historyQuery, parameters);
                return result;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return null;
            }
        }

        public async Task<IEnumerable<SuspendedInboxDTO>> GetSuspendedWorkflowsAsync()
        {
            string language = httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en";

            // Get the user ID from the HTTP context or user service
            long userId;
            if (!long.TryParse(httpContextAccessor.HttpContext.Request.Headers["X-Login-Id"].ToString(), out userId))
            {
                userId = await userService.GetUserInfo();
            }

            if (userId == 0)
            {
                userId = await userService.GetUserInfo();
            }

            string suspendedInboxQuery = HistorySqlQueries.GetSuspendedInbox;
            string takeOnInboxQuery = HistorySqlQueries.GetTakedOnInbox;


            var parameters = new Dictionary<string, object>
            {
                { "loginId", userId },
            };


            var repository = repositoryFactory.Create("FrameworkConnection");
            var suspended = await repository.ExecuteQueryAsync<SuspendedInboxDTO>(suspendedInboxQuery, parameters);
            var takedOn = await repository.ExecuteQueryAsync<SuspendedInboxDTO>(takeOnInboxQuery, parameters);

            IEnumerable<SuspendedInboxDTO> items = [];

            return await System.Threading.Tasks.Task.WhenAll(
                takedOn.Union(suspended).Select(async activeTask => new SuspendedInboxDTO
                {
                    wfInsId = activeTask.wfInsId,
                    name = activeTask.name,
                    wfInstanceLink = IsDebugMode
                        ? $"{activeTask.taskScreen}?LoginID={userId}&wfInstanceId={activeTask.wfInsId}"
                        : $"{activeTask.taskScreen}?wfInstanceId={activeTask.wfInsId}",
                    wfOwner = activeTask.wfOwner,
                    wfLastModifiedBy = await GetLastModifiedString(activeTask.wfInsId),
                    wfDate = activeTask.wfDate,
                })) ?? Enumerable.Empty<SuspendedInboxDTO>();
        }


        public async Task<string> GetLastModifiedString(long wfInstanceId)
        {
            var lastComment = await GetLastCommnetBeforeSuspend(wfInstanceId);

            if (lastComment != null)
            {
                return await GetCommentsLoginString(lastComment, wfInstanceId);
            }
            else
            {
                var repository = repositoryFactory.Create("FrameworkConnection");
                var wfIns = await repository.GetEntityAsync<FWfWorkflowInstance>(wfInstanceId);
                var parameters = new Dictionary<string, object>
                {
                    { "LoginId", wfIns.OwnerLogin.LoginId }
                };
                return await repository.ExecuteScalarAsync<string>("SELECT DT_WORKFLOW.VW_USER_INFORMATION.NAME_SURNAME FROM DT_WORKFLOW.VW_USER_INFORMATION WHERE DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID = :LoginId", parameters);
            }
        }


        private async Task<IEnumerable<FWfAssignment?>> GetAssignmentList(string assignmentTypeCd, long instanceId, long? definitionId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            if (definitionId != null)
            {
                var parameters = new Dictionary<string, object>
                {
                    { "ASSIGNMENT_TYPE_CD", assignmentTypeCd },
                    { "DEFINITION_ID", definitionId },
                    { "INSTANCE_ID", instanceId }
                };

                var result = await repository.ExecuteQueryAsync<FWfAssignment>(@"SELECT
                    assgn.*
                FROM
                    FRAMEWORK.F_WF_ASSIGNMENT assgn
                WHERE
                    assgn.WF_ASSIGNMENT_TYPE_CD = :ASSIGNMENT_TYPE_CD
                    AND (
                        (NVL(assgn.IS_DEF_ASSIGNMENT, 0) = 1 AND assgn.ASSIGNMENT_OWNER_REF_ID = :DEFINITION_ID)
                        OR
                        (NVL(assgn.IS_DEF_ASSIGNMENT, 0) = 0 AND assgn.ASSIGNMENT_OWNER_REF_ID = :INSTANCE_ID)
                    )", parameters);
                return result;
            }
            else
            {
                var parameters = new Dictionary<string, object>
                {
                    { "InstanceId", instanceId },
                    { "assignmentTypeCd", assignmentTypeCd },
                };
                return await repository.ExecuteQueryAsync<FWfAssignment>(@"SELECT
                    assgn.*
                FROM
                    FRAMEWORK.F_WF_ASSIGNMENT assgn
                WHERE
                    assgn.WF_ASSIGNMENT_TYPE_CD = :assignmentTypeCd
                    AND
                    COALESCE(assgn.IS_DEF_ASSIGNMENT, 0) = 0
                    AND assgn.ASSIGNMENT_OWNER_REF_ID = :InstanceId;", parameters);

            }
        }

        private async Task<string> GetCommentsLoginString(FWfActionTaskComment comment, long instanceId)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            string owner = string.Empty;

            try
            {
                // Get necessary entities
                var taskIns = await repository.GetEntityAsync<FWfActionTaskInstance>(comment.WfActionTaskInstanceId);
                if (taskIns == null)
                    throw new InvalidOperationException($"Task instance not found for ID: {comment.WfActionTaskInstanceId}");

                var assignmentDetail = await GetAssignmentList(
                    FWfAssignmentTypeValues.TaskInbox,
                    taskIns.WfActionTaskInstanceId,
                    taskIns.WfActionTaskDefId
                );

                if (!assignmentDetail.Any())
                    throw new InvalidOperationException($"No assignments found for task instance: {taskIns.WfActionTaskInstanceId}");

                var assigmentOwner = assignmentDetail.First().AssignedOwnerRefId;

                var workflowInstance = await repository.GetEntityAsync<FWfWorkflowInstance>(instanceId);
                if (workflowInstance == null)
                    throw new InvalidOperationException($"Workflow instance not found for ID: {instanceId}");

                // SQL query for getting user name - avoid repetition
                const string userNameQuery = @"
                    SELECT DT_WORKFLOW.VW_USER_INFORMATION.NAME_SURNAME
                    FROM DT_WORKFLOW.VW_USER_INFORMATION
                    WHERE DT_WORKFLOW.VW_USER_INFORMATION.LOGIN_ID = :LoginId";

                // Get delegation chain
                var formattedDate = comment.CreatedDate?.ToString("yyyy-MM-dd'T'HH:mm:ss")
                    ?? throw new InvalidOperationException("Comment created date is null");

                var delChain = await httpService.GetDataAsync(
                    globalHelpers.CompatibilityApiUrl(),
                    $"api/workflow/delegated-login?loginId={assigmentOwner}&wfDefId={workflowInstance.WfWorkflowDefId}&time={Uri.EscapeDataString(formattedDate)}"
                );

                if (string.IsNullOrEmpty(delChain))
                {
                    // If no delegation chain, just return the comment owner's name
                    return await repository.ExecuteScalarAsync<string>(
                        userNameQuery,
                        new Dictionary<string, object> { { "LoginId", comment.Login.LoginId } }
                    );
                }

                // Handle single delegation
                if (delChain.IndexOf(',') < 0)
                {
                    return await repository.ExecuteScalarAsync<string>(
                        userNameQuery,
                        new Dictionary<string, object> { { "LoginId", comment.Login.LoginId } }
                    );
                }

                // Handle multiple delegations
                var delegateIds = delChain.Split(',').Select(id => Convert.ToInt64(id));
                var delegateNames = new List<string>();

                foreach (var delegateId in delegateIds)
                {
                    var delegateName = await repository.ExecuteScalarAsync<string>(
                        userNameQuery,
                        new Dictionary<string, object> { { "LoginId", delegateId } }
                    );
                    if (!string.IsNullOrEmpty(delegateName))
                    {
                        delegateNames.Add($" on behalf of {delegateName}");
                    }
                }

                if (!delegateNames.Any())
                    return await repository.ExecuteScalarAsync<string>(
                        userNameQuery,
                        new Dictionary<string, object> { { "LoginId", comment.Login.LoginId } }
                    );

                // Combine delegate names
                owner = string.Concat(delegateNames);
                owner = owner.Remove(owner.Length - 8); // Remove last " on behalf of"

                // Check if comment owner's name needs to be added
                var commentOwnerName = await repository.ExecuteScalarAsync<string>(
                    userNameQuery,
                    new Dictionary<string, object> { { "LoginId", comment.Login.LoginId } }
                );

                if (!owner.Contains(commentOwnerName))
                {
                    owner = $"{commentOwnerName} on behalf of {owner}";
                }

                return owner;
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine("GetCommentsLoginString", $"Error processing comment {comment.WfActionTaskCommentId} for instance {instanceId}", ex);
                // Return a fallback value or rethrow based on your error handling strategy
                throw;
            }
        }




        private async Task<FWfActionTaskComment> GetLastCommnetBeforeSuspend(long wfInstanceId)
        {

            var repository = repositoryFactory.Create("FrameworkConnection");
            FWfWorkflowInstance wfIns = await repository.GetEntityAsync<FWfWorkflowInstance>(wfInstanceId);

            IEnumerable<FWfStateInstance> wfWorkflowInstanceFWfStateInstanceList = await repository.ExecuteQueryAsync<FWfStateInstance>("SELECT * FROM FRAMEWORK.F_WF_STATE_INSTANCE WHERE FRAMEWORK.F_WF_STATE_INSTANCE.WF_WORKFLOW_INSTANCE_ID = " + wfInstanceId);
            foreach (var state in wfWorkflowInstanceFWfStateInstanceList.OrderByDescending(t => t.WfStateInstanceId))
            {

                IEnumerable<FWfActionInstance> wfStateInstanceFWfActionInstanceList = await repository.ExecuteQueryAsync<FWfActionInstance>("SELECT * FROM FRAMEWORK.F_WF_ACTION_INSTANCE WHERE FRAMEWORK.F_WF_ACTION_INSTANCE.WF_STATE_INSTANCE_ID = " + state.WfStateInstanceId);

                foreach (var action in wfStateInstanceFWfActionInstanceList.OrderByDescending(t => t.WfActionInstanceId))
                {
                    var actionDef = await repository.GetEntityAsync<FWfActionDef>(action.WfActionDefId);
                    if (actionDef.WfActionType.WfActionTypeCd != FWfActionTypeValues.Task)
                    {
                        continue;
                    }
                    var prevAct = await repository.GetEntityAsync<FWfActionTaskInstance>(action.WfActionInstanceId);
                    if (prevAct.WfActionTaskStatusT.WfActionTaskStatusTCd == "SUSPENDED")
                    {
                        continue;
                    }
                    var prevTask = await GetPrevTaskInstance(prevAct);




                    IEnumerable<FWfActionTaskComment> taskCommentList = await repository.ExecuteQueryAsync<FWfActionTaskComment>("SELECT taskComment.* FROM FRAMEWORK.F_WF_ACTION_TASK_COMMENT taskComment INNER JOIN FRAMEWORK.F_WF_ACTION_TASK_INSTANCE taskInstance ON TASKCOMMENT.WF_ACTION_TASK_INSTANCE_ID = TASKINSTANCE.WF_ACTION_TASK_INSTANCE_ID  WHERE taskInstance.WF_ACTION_TASK_INSTANCE_ID = " + action.WfActionInstanceId);


                    if (taskCommentList.Count() > 0)
                    {
                        var cmt = taskCommentList.OrderByDescending(t => t.WfActionTaskCommentId).First();
                        if (prevTask != null && prevTask.OwnerLogin == cmt.Login)
                        {
                            continue;
                        }

                        return cmt;
                    }

                }
            }
            return null;
        }

        private async Task<FWfActionTaskInstance?> GetPrevTaskInstance(FWfActionTaskInstance f)
        {
            var repository = repositoryFactory.Create("FrameworkConnection");
            FWfActionTaskInstance? tIns = null;


            for (FWfStateInstance? state = f.WfStateInstance; state != null && state.WfStateInstanceId >= f.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceFWfStateInstanceList.OrderBy(t => t.WfStateInstanceId).First().WfStateInstanceId;)
            {
                IEnumerable<FWfActionInstance> wfStateInstanceFWfActionInstanceList = await repository.ExecuteQueryAsync<FWfActionInstance>("SELECT * FROM FRAMEWORK.F_WF_ACTION_INSTANCE WHERE FRAMEWORK.F_WF_ACTION_INSTANCE.WF_STATE_INSTANCE_ID = " + state.WfStateInstanceId);

                foreach (var item in wfStateInstanceFWfActionInstanceList.OrderByDescending(t => t.WfActionInstanceId))
                {
                    if (f.WfActionInstanceId <= item.WfActionInstanceId || item.WfActionStatusType.WfActionStatusTypeCd != FWfActionStatusTypeValues.Completed)
                    {
                        continue;
                    }
                    if (item.WfActionDef.WfActionType.WfActionTypeCd == FWfActionTypeValues.Task)
                    {
                        return await repository.GetEntityAsync<FWfActionTaskInstance>(item.WfActionInstanceId);
                    }
                }
                var stateList = state.WfWorkflowInstance.WfWorkflowInstanceFWfStateInstanceList.ToList();
                int index = stateList.IndexOf(state);
                if (index == 0)
                {
                    state = null;
                }
                else
                {
                    state = stateList[index - 1];
                }
            }
            return tIns;
        }


        public async Task<IEnumerable<Inbox>> GetAdminWorkflowsAsync(long wfDefId)
        {
            try
            {
                long userId;
                if (!long.TryParse(httpContextAccessor.HttpContext.Request.Headers["X-Login-Id"].ToString(), out userId))
                {
                    userId = await userService.GetUserInfo();
                }

                string inboxQuery = @"SELECT /*+ PARALLEL(16) */  *
                    FROM ( SELECT  REPLACE (COPYINSTANCELINK2, '00000', :loginValue) AS COPYINSTANCELINK,
                    REPLACE (WFINSTANCELINK2, '00000', :loginValue) AS WFINSTANCELINK,
                    REPLACE (MWFINSTANCELINK2, '00000', :loginValue) AS MWFINSTANCELINK,A.LOGINID AS ATANANID,
                    A1.*,
                    A1.ASSIGNEDNAMESURNAME as ATANAN,
                    B.ROUTE AS ROUTE,
                    ROW_NUMBER() OVER (PARTITION BY a1.WFINSID
                    ORDER BY WFDATE DESC) AS rownumber
                    from DT_WORKFLOW.TB_WORKFLOW_HISTORY_VIEW A1
                    left join dt_workflow.vw_assigneduser a on A.WFINSID= A1.WFINSID
                    left join dt_workflow.wf_xml_definition B on B.VALUE= A1.WF_WORKFLOW_DEF_ID
                    where A1.WF_WORKFLOW_DEF_ID = :wfDefId
                    AND A1.WF_WORKFLOW_DEF_ID in (select WF_DEF_ID + 0
                    from DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS A2
                    WHERE A2.ADMIN_ID = :loginValue
                    AND A1.WF_WORKFLOW_DEF_ID = WF_DEF_ID)
                    ORDER BY a1.WFINSID DESC)
                    WHERE rownumber = 1";

                // Prepare parameters
                var parameters = new Dictionary<string, object>
                {
                    { "loginValue", userId },
                    { "wfDefId", wfDefId }
                };

                var repository = repositoryFactory.Create("DT_WORKFLOW");
                var result = await repository.ExecuteQueryAsync<InboxDto>(inboxQuery, parameters);

                // Map DTOs to domain entities
                var inboxEntities = mapper.Map<IEnumerable<Inbox>>(result);
                return inboxEntities ?? Enumerable.Empty<Inbox>();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return Enumerable.Empty<InboxDto>();
            }
        }

        public async Task<IEnumerable<VwWorkflowHistory>> GetAdminHistoryAsync(long wfDefId, long loginId, int pageNo, int pageSize, string sortColumn, string sortDirection)
        {
            string language = httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en";

            string historyQuery = HistorySqlQueries.GetAdminHistorySQL();

            // Add pagination and sorting
            string orderByClause = !string.IsNullOrEmpty(sortColumn)
                ? $"ORDER BY {sortColumn} {sortDirection}"
                : "ORDER BY WF_HISTORY_ID DESC";

            int offset = (pageNo - 1) * pageSize;
            historyQuery += $" {orderByClause} OFFSET {offset} ROWS FETCH NEXT {pageSize} ROWS ONLY";

            // Prepare parameters
            var parameters = new Dictionary<string, object>
            {
                { "wfDefId", wfDefId },
                { "loginId", loginId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");

            try
            {
                var result = await repository.ExecuteQueryAsync<VwWorkflowHistory>(historyQuery, parameters);
                return result ?? Enumerable.Empty<VwWorkflowHistory>();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return Enumerable.Empty<VwWorkflowHistory>();
            }
        }

        public async Task<IEnumerable<Inbox>> GetSuspendedWorkflowsAsync(long userId)
        {
            try
            {
                string suspendedInboxQuery = HistorySqlQueries.GetSuspendedInbox;
                string takeOnInboxQuery = HistorySqlQueries.GetTakedOnInbox;

                var parameters = new Dictionary<string, object>
                {
                    { "loginId", userId },
                };

                var repository = repositoryFactory.Create("FrameworkConnection");
                var suspended = await repository.ExecuteQueryAsync<SuspendedInboxDTO>(suspendedInboxQuery, parameters);
                var takedOn = await repository.ExecuteQueryAsync<SuspendedInboxDTO>(takeOnInboxQuery, parameters);

                // Convert SuspendedInboxDTO to Inbox entities
                var allSuspended = suspended.Union(takedOn);
                var inboxEntities = allSuspended.Select(dto => new Inbox
                {
                    WfInsId = long.Parse(dto.wfInsId),
                    FlowName = dto.name,
                    TaskScreen = dto.wfInstanceLink,
                    WfOwner = dto.wfOwner?.FirstOrDefault(),
                    WfDate = DateTime.TryParse(dto.wfDate, out var date) ? date : null,
                    StartTime = DateTime.TryParse(dto.wfDate, out var startDate) ? startDate : null
                });

                return inboxEntities ?? Enumerable.Empty<Inbox>();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return Enumerable.Empty<Inbox>();
            }
        }
    }
}
