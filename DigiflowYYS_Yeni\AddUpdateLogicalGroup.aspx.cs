﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
//using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
//using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Digiflow.YYS.Core;
using System;
using System.Web.UI;

namespace DigiflowYYS_Yeni
{

    public partial class AddUpdateLogicalGroup : YYSSecurePage
    {
        /// <summary>
        /// Mantıksal Grup Ekleme ve Düzeltme Ekranı
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                if (!Page.IsPostBack)
                {
                    #region Combobox ı doldurulur, eğer mantıksal grup düzenleme yapılacaksa bilgileri doldurulur

                    this.Master.ShowMenu(true);
                    this.Master.PageTitle = "Mantıksal Grup Oluşturma";
                    long IsAdmin = 0;

                    if (Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAndFlowAdmin.ToString() || Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAdmin.ToString())
                    {
                        IsAdmin = 1;
                    }

                    //Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon => Digiturk.Workflow.Digiflow.YYS.Core
                    //WorkFlowCombobox1 = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillComboboxWorkFlow("Name", "WorkflowDefId", WorkFlowCombobox1, Digiturk.Workflow.Digiflow.YYS.Core.WorkflowHelper.GetWorkflowsOfAdmin(UserInformation.LoginObject.LoginId, IsAdmin), "İş Akışı Seçiniz", "0");
                    WorkFlowCombobox1 = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownList("Name", "WorkflowDefId", WorkFlowCombobox1, Digiturk.Workflow.Digiflow.YYS.Core.WorkflowHelper.GetWorkflowsOfAdmin(UserInformation.LoginObject.LoginId, IsAdmin), "İş Akışı Seçiniz", "0");

                    //Sayfaya Düzenleme için giriliyor ise
                    if (Request.QueryString["LogicalGroupId"] != null)
                    {
                        #region Mantıksal Grubun bilgilerini getiriyoruz

                        this.Master.PageTitle = "Mantıksal Grup Düzenleme";
                        //WorkflowPanel.Visible = false;
                        long logicalGroupId = ConvertionHelper.ConvertValue<long>(Request.QueryString["LogicalGroupId"]);
                        LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(logicalGroupId);
                        //WorkFlowCombobox1.Value = lg.WfDefId;
                        WorkFlowCombobox1.SelectedValue = lg.WfDefId.ToString();
                        txtGroupName.Text = lg.Name;
                        txtDescription.Text = lg.Description;
                        cbCanBeDeleted.Checked = ConvertionHelper.ConvertValue<Boolean>(lg.CanBeDeleted);
                        cbIsOnePersonGroup.Checked = ConvertionHelper.ConvertValue<Boolean>(lg.IsOnePersonGroup);

                        #endregion Mantıksal Grubun bilgilerini getiriyoruz
                    }

                    #region Mantıksal Grubun silinebilirliğini sadece Sistem Yöneticisi görebilir

                    Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType AdminType = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowInformationHelper.GetFlowAdminType(UserInformation.LoginObject.LoginId);
                    if (AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAdmin || AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAndFlowAdmin)
                    {
                        cbCanBeDeleted.Visible = true;
                    }
                    else
                    {
                        cbCanBeDeleted.Visible = false;
                        cbCanBeDeleted.Checked = true;
                    }

                    #endregion Mantıksal Grubun silinebilirliğini sadece Sistem Yöneticisi görebilir

                    #endregion Combobox ı doldurulur, eğer mantıksal grup düzenleme yapılacaksa bilgileri doldurulur
                }
            }
            catch (Exception ex)
            {

                this.Master.ShowPopup(false, "Hata", "Sayfa yüklenirken beklenmeyen bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.StackTrace);
            }
        }

        /// <summary>
        /// MAntıksal grup kaydetmemizi sağlar
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void SaveASPxButton_Click(object sender, EventArgs e)
        {
            //Hiçbir iş akışı seçilmeden mantıksal grup tanımlaması yapılamaz
            if (WorkFlowCombobox1.SelectedIndex == 0 && Request.QueryString["LogicalGroupId"] == null)
            {
                this.Master.ShowError("Hata", "Lütfen bir iş akışı seçiniz.");
            }
            else
            {
                try
                {
                    if (Request.QueryString["LogicalGroupId"] != null)
                    {
                        #region Update İşlemi

                        long logicalGroupId = ConvertionHelper.ConvertValue<long>(Request.QueryString["LogicalGroupId"]);
                        LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(logicalGroupId);
                        //Mantıksal Grup db de var ise
                        if (lg != null)
                        {
                            string groupName = txtGroupName.Text.Trim();
                            if (txtDescription.Text.Length > 1024)
                            {
                                lg.Description = txtDescription.Text.Trim().Substring(0, 1024);
                            }
                            else
                            {
                                lg.Description = txtDescription.Text.Trim();
                            }
                            lg.RequestId = logicalGroupId;
                            lg.Name = groupName;

                            lg.CanBeDeleted = ConvertionHelper.ConvertValue<long>(cbCanBeDeleted.Checked);
                            lg.IsOnePersonGroup = ConvertionHelper.ConvertValue<long>(cbIsOnePersonGroup.Checked);
                            lg.LastUpdated = DateTime.Now;
                            lg.LastUpdatedBy = UserInformation.LoginObject.LoginId;
                            Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.UpdateLogicalGroup(lg);
                            this.Master.ShowPopup(false, "Bitti", "Kayıt işlemi başarıyla tamamlanmıştır", false, "NO TRACE");
                            Response.Redirect("AddUpdateLogicalGroupMemberNew.aspx?LogicalGroupId=" + lg.RequestId.ToString() + "&WfId=" + lg.WfDefId.ToString() + "&IsOnePersonGroup=" + lg.IsOnePersonGroup.ToString());
                        }

                        #endregion Update İşlemi
                    }
                    else
                    {
                        #region Yeni Insert İşlemi

                        //O akışa ait aynı isimde logical group varmı kontrolü
                        if (Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.IsSameGroupExistsInSameWorkflow(txtGroupName.Text.Trim(), ConvertionHelper.ConvertValue<long>(WorkFlowCombobox1.SelectedItem.Value)))
                        {
                            this.Master.ShowPopup(false, "Hata", "Bu iş akışı içinde bu isme sahip bir grup zaten mevcut", true, "NO TRACE");
                        }
                        else
                        {
                            LogicalGroup lg = new LogicalGroup();
                            lg.Name = txtGroupName.Text.Trim();
                            if (txtDescription.Text.Length > 1024)
                            {
                                lg.Description = txtDescription.Text.Trim().Substring(0, 1024);
                            }
                            else
                            {
                                lg.Description = txtDescription.Text.Trim();
                            }
                            lg.CanBeDeleted = ConvertionHelper.ConvertValue<long>(cbCanBeDeleted.Checked);
                            lg.IsOnePersonGroup = ConvertionHelper.ConvertValue<long>(cbIsOnePersonGroup.Checked);
                            lg.Created = DateTime.Now;
                            lg.CreatedBy = UserInformation.LoginObject.LoginId;
                            lg.WfDefId = ConvertionHelper.ConvertValue<long>(WorkFlowCombobox1.SelectedItem.Value);

                            //Save Process
                            lg.RequestId = ConvertionHelper.ConvertValue<long>(Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.AddNewLogicalGroup(lg));

                            this.Master.ShowPopup(false, "Bitti", "Kayıt işlemi başarıyla tamamlanmıştır", false, "NO TRACE");

                            Response.Redirect("AddUpdateLogicalGroupMemberNew.aspx?LogicalGroupId=" + lg.RequestId.ToString() + "&WfId=" + lg.WfDefId.ToString() + "&IsOnePersonGroup=" + lg.IsOnePersonGroup.ToString());
                        }

                        #endregion Yeni Insert İşlemi
                    }
                }
                catch (Exception ex)
                {
                    Digiturk.Workflow.Repository.UnitOfWork.Rollback();
                    this.Master.ShowPopup(false, "Hata", "Kayıt işlemi esnasında bir hata ile karşılaşıldı. Lütfen tekrar deneyin.", true, ex.Message);
                }
            }
        }
    }
}