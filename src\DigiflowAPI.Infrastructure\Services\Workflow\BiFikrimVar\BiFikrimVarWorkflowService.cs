using DigiflowAPI.Application.DTOs.Workflow.Operation;
using DigiflowAPI.Application.DTOs.Workflow.Responses;
using DigiflowAPI.Application.DTOs.Common;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Interfaces.Services.Workflow;
using DigiflowAPI.Application.Interfaces.DataAccess;
using Microsoft.AspNetCore.Http;
using DigiflowAPI.Application.Resources;
using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Application.DTOs;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Domain.Entities.Workflow;
using System.Text.Json;

namespace DigiflowAPI.Infrastructure.Services.Workflow
{
    public class BiFikrimVarWorkflowService(
        IOracleDataAccessRepositoryFactory repositoryFactory,
        IWorkflowHelperService workflowHelperService,
        IUserService userService,
        IPermissionRepository permissionRepository,
        IHttpContextAccessor httpContextAccessor,
        ILogicalGroupService logicalGroupService,
        IHttpService httpService,
        ISharePointService sharePointService,
        IGlobalHelpers globalHelpers) : BaseWorkflowService<BiFikrimVarWorkflowResponseDto>(
                repositoryFactory,
                httpService,
                httpContextAccessor,
                globalHelpers,
                userService)
    {

        private int heyetDegerlendirmeGrubu;
        private int degerlendirmeHavuzuGrubu;
        private long degerlendirmeHavuzuAdimi;
        private long heyetDegerlendirmeAdimi;

        private async Task InitializeWorkflowData()
        {
            heyetDegerlendirmeGrubu = await logicalGroupService.GetLogicalGroupID("BiFikrimVar_HeyetDegerlendirme");
            degerlendirmeHavuzuGrubu = await logicalGroupService.GetLogicalGroupID("BiFikrimVar_DegerlendirmeHavuzu");
            degerlendirmeHavuzuAdimi = await workflowHelperService.GetStateDefId("BiFikrimVar_DegerlendirmeHavuzu"); //State
            heyetDegerlendirmeAdimi = await workflowHelperService.GetStateDefId("BiFikrimVar_HeyetDegerlendirme"); //State
        }

        public override async Task<WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>> NewWorkflowLoading(params object[] parameters)
        {
            await InitializeWorkflowData();
            var response = new WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>
            {
                Detail = new BiFikrimVarWorkflowResponseDto()
            };

            try
            {
                var newWorkflowLoadingDto = new BiFikrimVarNewWorkflowLoadingDto();
                string language = httpContextAccessor.HttpContext.Items["UserLanguage"].ToString();
                string workflowAdmins = await workflowHelperService.GetWorkflowAdmins("Bi Fikrim Var");
                newWorkflowLoadingDto.WorkflowAdmins = new WorkflowAdmin
                {
                    Tr = "Workflow Admin Message (TR)", // TODO: Implement proper resource localization
                    En = "Workflow Admin Message (EN)" // TODO: Implement proper resource localization
                };

                var locationsData = await permissionRepository.GetLocationsAsync();
                IEnumerable<SelectOptionDto>? locations = locationsData?.Select(l => new SelectOptionDto
                {
                    Value = l.Value,
                    Label = l.Text,
                    LabelEn = l.Text
                });

                newWorkflowLoadingDto.Locations = locations;

                response.Detail.NewWorkflowLoadingDto = newWorkflowLoadingDto;
            }
            catch (Exception ex)
            {
                response.ErrorMessage = ex.Message;
            }

            return response;
        }

        public override async Task<WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>> EnableControls(params object[] parameters)
        {
            var response = new WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>
            {
                Detail = new BiFikrimVarWorkflowResponseDto()
            };

            try
            {
                long? wfInstanceId = parameters.Length > 0 && parameters[0] is long id ? id : null;
                bool isCopyInstance = parameters.Length > 2 && parameters[2] is bool isCopy ? isCopy : false;
                if (!long.TryParse(httpContextAccessor.HttpContext?.Request.Headers["X-Login-Id"].ToString(), out long selectedUserId))
                {
                    selectedUserId = await userService.GetUserInfo();
                }

                var enableControlsDto = new BiFikrimVarEnableControlsDto
                {
                    EnabilitySettings = new BiFikrimVarEnabilityControlsDto
                    {
                        CanEditSubject = true,
                        CanEditSuggestionDetail = true,
                        CanEditBenefit = true,
                        CanEditLocation = true,
                        CanEditSuggester = true,
                        CanEditPlanDate = false,
                        CanEditExecutionDate = false,
                        CanEditDelegationNote = false,
                        CanPressUpdateButton = false,
                        CanPressUploadButton = true,
                        CanEditUpdateComponent = true,
                    },
                    VisibilitySettings = new BiFikrimVarVisiblityControlsDto
                    {
                        CanSeeSubject = true,
                        CanSeeSuggestionDetail = true,
                        CanSeeBenefit = true,
                        CanSeeLocation = true,
                        CanSeeSuggester = true,
                        CanSeePlanDate = false,
                        CanSeeExecutionDate = false,
                        CanSeeDelegationNote = false,
                        CanSeeUpdateButton = false,
                        CanSeeUploadButton = true
                    }
                };

                var frameworkRepository = repositoryFactory.Create("FrameworkConnection");

                if (isCopyInstance == false)
                {
                    FWfWorkflowInstance CurrentWfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(wfInstanceId);


                    if (CurrentWfIns != null && CurrentWfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd == "COMPLETED")
                    {
                        foreach (var prop in typeof(BiFikrimVarEnabilityControlsDto).GetProperties())
                        {
                            prop.SetValue(enableControlsDto.EnabilitySettings, false);
                        }

                        if (await logicalGroupService.IsExistLogicalGroup(heyetDegerlendirmeGrubu, selectedUserId) &&
                            (CurrentWfIns.WfCurrentState == null))
                        {
                            enableControlsDto.VisibilitySettings.CanSeeDelegationNote = true;
                            if (CurrentWfIns.WfCurrentState != null)
                            {
                                enableControlsDto.EnabilitySettings.CanEditDelegationNote = true;
                            }
                        }
                    }
                    else if (CurrentWfIns != null && CurrentWfIns.WfCurrentState != null)
                    {
                        enableControlsDto.EnabilitySettings.CanPressUploadButton = false;
                        FWfStateDef CurrentStateDef = await frameworkRepository.GetEntityAsync<FWfStateDef>(CurrentWfIns.WfCurrentState.WfStateDefId)
                            ?? throw new InvalidOperationException($"Workflow state definition not found for StateDefId: {CurrentWfIns.WfCurrentState.WfStateDefId}");
                        if (CurrentWfIns.WfCurrentState != null)
                        {

                            if (await logicalGroupService.IsExistLogicalGroup(heyetDegerlendirmeGrubu, selectedUserId))
                            {
                                enableControlsDto.EnabilitySettings.CanPressUpdateButton = true;
                                enableControlsDto.EnabilitySettings.CanEditPlanDate = true;
                                enableControlsDto.EnabilitySettings.CanEditExecutionDate = true;
                            }

                            if (CurrentStateDef.WfStateDefId == heyetDegerlendirmeAdimi)
                            {
                                enableControlsDto.EnabilitySettings.CanEditSubject = false;
                                enableControlsDto.EnabilitySettings.CanEditBenefit = false;
                                enableControlsDto.EnabilitySettings.CanEditSuggestionDetail = false;
                                enableControlsDto.EnabilitySettings.CanEditLocation = false;
                                enableControlsDto.EnabilitySettings.CanEditSuggester = false;

                                enableControlsDto.VisibilitySettings.CanSeePlanDate = true;
                                enableControlsDto.VisibilitySettings.CanSeeExecutionDate = true;
                                enableControlsDto.VisibilitySettings.CanSeeUpdateButton = true;
                            }
                            else
                            {
                                foreach (var prop in typeof(BiFikrimVarEnabilityControlsDto).GetProperties())
                                {
                                    prop.SetValue(enableControlsDto.EnabilitySettings, false);
                                }
                            }
                        }
                        else
                        {
                            foreach (var prop in typeof(BiFikrimVarEnabilityControlsDto).GetProperties())
                            {
                                prop.SetValue(enableControlsDto.EnabilitySettings, false);
                            }
                        }

                        if (await logicalGroupService.IsExistLogicalGroup(heyetDegerlendirmeGrubu, selectedUserId) &&
                            (CurrentWfIns.WfCurrentState == null || CurrentStateDef.WfStateDefId == heyetDegerlendirmeAdimi))
                        {
                            enableControlsDto.VisibilitySettings.CanSeeDelegationNote = true;
                            if (CurrentWfIns.WfCurrentState != null)
                            {
                                enableControlsDto.EnabilitySettings.CanEditDelegationNote = true;
                            }
                        }
                    }
                }
                response.Detail.EnableControlsDto = enableControlsDto;
            }
            catch (Exception ex)
            {
                response.ErrorMessage = "An error occurred while configuring controls. Please try again later.";
            }

            return response;
        }

        public override async Task<WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>> LoadDataBinding(params object[] parameters)
        {
            var response = new WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>
            {
                Detail = new BiFikrimVarWorkflowResponseDto()
            };

            try
            {
                var frameworkRepository = repositoryFactory.Create("FrameworkConnection");
                long? wfInstanceId = parameters.Length > 0 && parameters[0] is long id ? id : null;
                if (wfInstanceId == null)
                {
                    throw new ArgumentException("Invalid workflow instance ID.");
                }
                if (wfInstanceId.HasValue)
                {

                    var queryParams = new Dictionary<string, object>
                    {
                        { ":RequestId", wfInstanceId }
                    };
                    var repository = repositoryFactory.Create("DT_WORKFLOW");
                    var entity = await repository.ExecuteSingleQueryAsync<BiFikrimVarEntity>(
                        @"SELECT flow.*, vwUser.NAME_SURNAME as OWNER_LOGIN_NAME FROM DT_WORKFLOW.WF_DF_BI_FIKRIM_VAR flow
                      INNER JOIN FRAMEWORK.F_WF_WORKFLOW_INSTANCE wfIns ON flow.BI_FIKRIM_VAR_REQUEST_ID = wfIns.ENTITY_REF_ID
                      INNER JOIN DT_WORKFLOW.VW_USER_INFORMATION vwUser ON vwUser.LOGIN_ID = flow.CREATED_BY
                      WHERE wfIns.WF_WORKFLOW_INSTANCE_ID = :RequestId",
                        queryParams
                    );


                    if (entity == null)
                    {
                        return new WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>
                        {
                            ErrorMessage = "Workflow not found!"
                        };
                    }

                    var locationsData = await permissionRepository.GetLocationsAsync();
                    IEnumerable<SelectOptionDto>? locations = locationsData?.Select(l => new SelectOptionDto
                    {
                        Value = l.Value,
                        Label = l.Text,
                        LabelEn = l.Text
                    });

                    var suggestersData = await permissionRepository.GetSuggestersAsync(entity.Location);
                    IEnumerable<SelectOptionDto>? suggesters = suggestersData?.Select(s => new SelectOptionDto
                    {
                        Value = s.Value,
                        Label = s.Text,
                        LabelEn = s.Text
                    });
                    string suggestersTeam = await permissionRepository.GetSuggestersTeamAsync(entity.Suggester);

                    FWfWorkflowInstance CurrentWfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(wfInstanceId);
                    FWfStateInstance CurrentStateInstance = await frameworkRepository.GetEntityAsync<FWfStateInstance>(CurrentWfIns.WfCurrentStateId);
                    IEnumerable<BiFikrimVarDetailDocEntity> uploadedFiles = await GetUploadedFiles(CurrentWfIns.EntityRefId);


                    string workflowState = "";

                    if (CurrentWfIns.WfCurrentState != null)
                    {
                        FWfStateDef CurrentStateDef = await frameworkRepository.GetEntityAsync<FWfStateDef>(CurrentWfIns.WfCurrentState.WfStateDefId)
                            ?? throw new InvalidOperationException($"Workflow state definition not found for StateDefId: {CurrentWfIns.WfCurrentState.WfStateDefId}");

                        if (CurrentStateDef.WfStateDefId == heyetDegerlendirmeAdimi)
                            workflowState = "HeyetDegerlendirme";
                        else if (CurrentStateDef.WfStateDefId == degerlendirmeHavuzuAdimi)
                            workflowState = "DegerlendirmeHavuzu";
                    }
                    else
                    {
                        //TODO: Burada akış tamamlandığında sadece heyetteki kişilerin tarih ve delegasyon notu bilgilerini görmeleri sağlanmalıdır
                        if (!long.TryParse(httpContextAccessor.HttpContext?.Request.Headers["X-Login-Id"].ToString(), out long selectedUserId))
                        {
                            selectedUserId = await userService.GetUserInfo();
                        }
                        if (await logicalGroupService.IsExistLogicalGroup(heyetDegerlendirmeGrubu, selectedUserId))
                        {
                            workflowState = "HeyetDegerlendirme";
                        }
                    }
                    var loadDataBindingDto = new BiFikrimVarLoadDataBindingDto
                    {
                        Locations = locations,
                        Suggesters = suggesters,
                        Files = uploadedFiles,
                        Team = suggestersTeam,
                        WorkflowState = workflowState
                    };

                    response.Detail.LoadDataBindingDto = loadDataBindingDto;

                }
            }
            catch (Exception ex)
            {
                response.ErrorMessage = "An error occurred while loading workflow details. Please try again later.";
            }
            return response;
        }

        public override async Task<WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>> LoadEntityToControls(params object[] parameters)
        {
            var response = new WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>
            {
                Detail = new BiFikrimVarWorkflowResponseDto()
            };

            try
            {
                var loadDataBindingDto = new BiFikrimVarLoadEntityToControlsDto();

                long? wfInstanceId = parameters.Length > 0 && parameters[0] is long id ? id : null;
                if (wfInstanceId == null)
                {
                    throw new ArgumentException("Invalid workflow instance ID.");
                }
                if (wfInstanceId.HasValue)
                {
                    var queryParams = new Dictionary<string, object>
                    {
                        { ":RequestId", wfInstanceId }
                    };

                    var repository = repositoryFactory.Create("DT_WORKFLOW");
                    var results = await repository.ExecuteQueryAsync<BiFikrimVarEntity>(
                        @"SELECT flow.*, vwUser.NAME_SURNAME as OWNER_LOGIN_NAME FROM DT_WORKFLOW.WF_DF_BI_FIKRIM_VAR flow
                      INNER JOIN FRAMEWORK.F_WF_WORKFLOW_INSTANCE wfIns ON flow.BI_FIKRIM_VAR_REQUEST_ID = wfIns.ENTITY_REF_ID
                      INNER JOIN DT_WORKFLOW.VW_USER_INFORMATION vwUser ON vwUser.LOGIN_ID = flow.CREATED_BY
                      WHERE wfIns.WF_WORKFLOW_INSTANCE_ID = :RequestId",
                        queryParams
                    );

                    if (results == null || results.Count == 0)
                    {
                        return new WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>
                        {
                            ErrorMessage = "Workflow not found!"
                        };
                    }

                    var entity = results.FirstOrDefault();
                    loadDataBindingDto.Entity = entity;
                }

                response.Detail.LoadEntityToControlsDto = loadDataBindingDto;

            }
            catch (Exception ex)
            {
                response.ErrorMessage = "An error occurred while loading workflow details. Please try again later.";
            }
            return response;
        }

        public new async Task<WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>> CreateWorkflow(CreateWorkflowRequestDto request)
        {
            string? suggester = null;
            string? suggesterFullname = null;

            // Check if the properties exist before trying to get their values
            if (request.EntityJson.TryGetProperty("Suggester", out var suggesterElement))
            {
                suggester = suggesterElement.GetString();
            }
            if (request.EntityJson.TryGetProperty("SuggesterFullname", out var suggesterFullnameElement))
            {
                suggesterFullname = suggesterFullnameElement.GetString();
            }

            // If suggester is null or empty, get the active user's information
            if (string.IsNullOrEmpty(suggester))
            {
                var activeUser = await userService.GetUserInfo(null);
                suggester = activeUser.Username;
                suggesterFullname = activeUser.NameSurname;

                // Create a new JsonElement with modified or added values
                var jsonObject = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(request.EntityJson.GetRawText());
                jsonObject["Suggester"] = JsonSerializer.SerializeToElement(suggester);
                jsonObject["SuggesterFullname"] = JsonSerializer.SerializeToElement(suggesterFullname);

                // Convert the dictionary back to JsonElement
                var options = new JsonSerializerOptions();
                string modifiedJson = JsonSerializer.Serialize(jsonObject, options);
                request.EntityJson = JsonDocument.Parse(modifiedJson).RootElement;
            }

            return await base.CreateWorkflow(request);
        }

        public new async Task<WorkflowResponseDto<BiFikrimVarWorkflowResponseDto>> ApprovalWorkflow(ApprovalWorkflowRequestDto request)
        {
            var workflowRepository = repositoryFactory.Create("DT_WORKFLOW");
            var frameworkRepository = repositoryFactory.Create("FrameworkConnection");

            var wfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(request.InstanceId) ?? throw new InvalidOperationException($"Workflow instance not found for InstanceId: {request.InstanceId}");
            var entity = await workflowRepository.GetEntityAsync<DelegationWorkflowEntity>(wfIns.EntityRefId) ?? throw new InvalidOperationException($"Delegation record not found for EntityRefId: {wfIns.EntityRefId}");
            var taskIns = await frameworkRepository.GetEntityAsync<FWfActionTaskInstance>(wfIns.WfCurrentState.WfCurrentActionInstanceId) ?? throw new InvalidOperationException($"Action task instance not found for ActionInstanceId: {wfIns.WfCurrentState.WfCurrentActionInstanceId}");
            request.AssignedUserId = (long)entity.DelegatedLoginId;
            request.ActionTaskInstanceId = taskIns.WfActionTaskInstanceId;
            WorkflowResponseDto<BiFikrimVarWorkflowResponseDto> response = await base.ApprovalWorkflow(request);
            if (response.SuccessMessage != "" && request.UpdateEntity != null)
            {
                var updateEntityData = new UpdateEntityRequestDto
                {
                    Id = request.UpdateEntity.Id,
                    Properties = request.UpdateEntity.Properties,
                    EmailRule = request.UpdateEntity.EmailRule,

                };
                var result = await workflowHelperService.UpdateEntity(updateEntityData, request.WorkflowName);
                Console.WriteLine(result);
            }
            return response;
        }

        private async Task<IEnumerable<BiFikrimVarDetailDocEntity>> GetUploadedFiles(long? biFikrimVarRequestId)
        {
            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var sharePointPath = sharePointService.GetPathFromSharePointAsync("SharePointBiFikrimVarDocs");
            var queryParams = new Dictionary<string, object>
            {
                { ":RequestId", biFikrimVarRequestId },
                { ":Path", sharePointPath },
            };
            var files = await repository.ExecuteQueryAsync<BiFikrimVarDetailDocEntity>(
                @"SELECT DT_WORKFLOW.WF_DF_BI_FIKRIM_VAR_DETAIL_DOC.*, (:Path || DT_WORKFLOW.WF_DF_BI_FIKRIM_VAR_DETAIL_DOC.ATTACHMENT_LINK) as COMPLETE_URL FROM DT_WORKFLOW.WF_DF_BI_FIKRIM_VAR_DETAIL_DOC
                      WHERE RELATED_REQUEST_ID = :RequestId",
                queryParams
            );

            return files;
        }
    }
}