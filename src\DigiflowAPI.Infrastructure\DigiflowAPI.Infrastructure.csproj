﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\DigiflowAPI.Domain\DigiflowAPI.Domain.csproj" />
    <ProjectReference Include="..\DigiflowAPI.Application\DigiflowAPI.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="9.0.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.8.0" />
    <PackageReference Include="PnP.Framework" Version="1.18.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="9.0.6" />
    <PackageReference Include="System.Formats.Asn1" Version="9.0.6" />
    <PackageReference Include="System.Security.Principal.Windows" Version="5.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Digiturk.Framework.BaseServices.DAL">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.BaseServices.DAL.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Repository">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.BaseServices.DAL">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.BaseServices.DAL.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Common">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Common.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authentication">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Authentication.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authorization">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.DigiFlow.Framework">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.DigiFlow.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.GenericMailHelper">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.GenericMailHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WebCore">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WebCore.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Engine">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Engine.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Repository">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Repository.dll</HintPath>
    </Reference>
    <Reference Include="KurumsalRazorClassLibrary">
      <HintPath>..\..\..\..\..\..\..\..\TFS\KurumsalRazorClassLibrary.publish\KurumsalRazorClassLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess">
      <HintPath>..\..\..\..\..\..\..\..\oracle_files\WINDOWS.X64_193000_client_home\ODP.NET\bin\4\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Entity">
      <HintPath>\\dtl1iis3\Deployment\System.Data.Entity.dll</HintPath>
    </Reference>
    <Reference Include="System.EnterpriseServices">
      <HintPath>\\dtl1iis3\Deployment\System.EnterpriseServices.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>
