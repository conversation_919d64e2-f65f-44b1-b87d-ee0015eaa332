﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="AdminTestPage.aspx.cs" Inherits="DigiflowYYS_Yeni.AdminTestPage" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
    <link href="css/select2.min.css" rel="stylesheet" />    
    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="js/select2.min.js"></script>    
    <script type="text/javascript">    
       
        $(document).ready(function () {
            debugger
            $(".classDrp").select2();
        })
    </script>
    
</head>
<body>
    <form id="form1" runat="server">
        <div>
            <table align="center" valign="middle" border="1" style="width: 261px">
                <tr>
                    <td>
                        <asp:TextBox ID="txtInstanceId" runat="server" Width="250px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:DropDownList ID="DrpPageName" CssClass="classDrp" runat="server" Width="250px">
                            <asp:ListItem>---Seçiniz----</asp:ListItem>
                            <asp:ListItem Value="AddUpdateWorkflowAdmin.aspx">Akış Yöneticisi Tanımlama</asp:ListItem>
                            <asp:ListItem Value="AddUpdateLogicalGroup.aspx">Mantıksal Grup Tanımlama</asp:ListItem>
                            <asp:ListItem Value="AddUpdateWorkflowRule.aspx">İş Akışı Yetki Yönetimi</asp:ListItem>
                            <asp:ListItem Value="AddUpdateStateAuthorization.aspx">Adım Bazlı Statik Atama Kuralları</asp:ListItem>
                        </asp:DropDownList>
                    </td>                   
                </tr>
                <tr>
                    <td>
                        <asp:DropDownList ID="DrpUserName" CssClass="classDrp" runat="server" style="width:auto">
                            <asp:ListItem Value="0">---Seçiniz---</asp:ListItem>
                            <asp:ListItem Value="1763">KEREM BAYRAKTAR-1763</asp:ListItem>
                            <asp:ListItem Value="2001">ZÜLFİYE TEZEMIR-2001</asp:ListItem>
                            <asp:ListItem Value="3012784">EMRE BAŞARAN-3012784 TEST</asp:ListItem>
                            <asp:ListItem Value="812904">EMRE BAŞARAN-812904 LIVE</asp:ListItem>

                            <asp:ListItem Value="85377">SELIM OZDOGAN-85377 TEST</asp:ListItem>
                            <asp:ListItem Value="8278">SELIM OZDOGAN-8278 LIVE</asp:ListItem>
                            <asp:ListItem>-------------TESTER----------------</asp:ListItem>
                            <asp:ListItem Value="1903">SEBİLE HOCAOGLU- 1903</asp:ListItem>
                            <asp:ListItem Value="671307">TUBA ÇİLİNGİR - 671307</asp:ListItem>
                            <asp:ListItem Value="2001">ZÜLFİYE TEZEMIR-2001</asp:ListItem>
                            <asp:ListItem>-------------YONETICI----------------</asp:ListItem>
                            <asp:ListItem Value="1946">TUĞÇE DENİZLERKURDU-1946</asp:ListItem>
                            <asp:ListItem Value="1561">OSMAN ÇAĞATAY DOĞAN-1561</asp:ListItem>
                            <asp:ListItem Value="1704">GÖKHAN ÖZTÜRK-1704</asp:ListItem>
                            <asp:ListItem Value="1751">MUTLU BUKELEK-1751</asp:ListItem>
                            <asp:ListItem Value="1748">ESEN SAYGIVAR-1748</asp:ListItem>

                            <asp:ListItem Value="2007">HATİCE MEMİGÜVEN-2007</asp:ListItem>

                            <asp:ListItem>-------------DIGER----------------</asp:ListItem>
                            <asp:ListItem Value="1932">SÜLEYMAN TALAŞ-1932</asp:ListItem>
                            <asp:ListItem Value="1571">CÜNEYT KOÇOĞLU-1571</asp:ListItem>
                            <asp:ListItem Value="1538">BURAK ONUR-1538</asp:ListItem>
                            <asp:ListItem Value="1686">FATMA ÜRÜM-1686</asp:ListItem>
                            <asp:ListItem Value="1499">ALTAN KIYCI-1499</asp:ListItem>
                            <asp:ListItem Value="652207">MAHİR ÇETİNKAYA-652207</asp:ListItem>
                            <asp:ListItem Value="2022">BORA KOÇYİĞİT-2022</asp:ListItem>
                            <asp:ListItem Value="1833">NEFİSE KULA-1833</asp:ListItem>
                            <asp:ListItem Value="627744">OKAN OKUTKAN-627744</asp:ListItem>
                            <asp:ListItem Value="1739">İDİL ÇETİN-1739</asp:ListItem>
                            <asp:ListItem Value="1734">İBRAHİM AYDIN-1734</asp:ListItem>
                        </asp:DropDownList>
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:Button ID="BtnStart" runat="server" Text="StartTest" Width="195px" OnClick="BtnStart_Click" />
                        <br />

                        <a href="http://belgeler/sites/it/Belgeler/Forms/AllItems.aspx?RootFolder=%2fsites%2fit%2fBelgeler%2fIT%5fDSS%2fIT%5fDSS%5fPM%2f%c4%b0%c5%9f%20Ak%c4%b1%c5%9flar%c4%b1&FolderCTID=0x01200028B8B87278114D45B70B5E73108AD613&View=%7b38458F25%2d8980%2d4181%2dB4DB%2d230F102D6C8F%7d">Test Dokümanı</a>
                        <br />
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:Button ID="BtnInstance" runat="server" Text="GetInstance" Width="195px"
                            OnClick="BtnInstance_Click" />
                    </td>
                </tr>

                <tr>
                    <td>
                        <asp:TextBox ID="txtLoginId" runat="server" Width="195px"></asp:TextBox>
                    </td>
                </tr>

                <tr>
                    <td>
                        <asp:Button ID="BtnDelegationList" runat="server"
                            OnClick="BtnDelegationList_Click" Text="Üst Yöneticisi Kim?" Width="197px" />
                    </td>
                </tr>

                <tr>
                    <td>
                        <asp:Button ID="BtnGetHistory" runat="server" OnClick="BtnGetHistory_Click"
                            Text="Yönetici Tipi Ne?" Width="197px" />
                    </td>
                </tr>

                <tr>
                    <td>
                        <asp:Button ID="BtnGetHistory0" runat="server" OnClick="BtnGetHistory0_Click"
                            Text="Sorunlu Case Test" Width="197px" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:Button ID="BtnExecAction" runat="server" OnClick="BtnExecAction_Click"
                            Text="DynamicAssignment" Width="195px" />
                    </td>
                </tr>
            </table>
        </div>
    </form>
    
</body>
</html>