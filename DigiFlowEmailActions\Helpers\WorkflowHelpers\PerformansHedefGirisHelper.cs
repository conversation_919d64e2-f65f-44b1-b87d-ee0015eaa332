﻿using Digiturk.Workflow.Digiflow.Framework;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class PerformansHedefGirisHelper
    {
        internal static void AkisiOnayla(Digiturk.Workflow.Common.WFContext CurrentWFContext,string comment, FLogin LoginObject, FWfActionTaskInstance taskInstance)
        {
            if (comment.Length > 200)
            {
                comment = comment.Substring(0, 197) + "...";
            }
            CurrentWFContext.Parameters.AddOrChangeItem("Yorum", comment);
            CurrentWFContext.Parameters.AddOrChangeItem("Onay", "Yes");

            CurrentWFContext.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(LoginObject, FormInformationHelper.GetAssignedUser(taskInstance)));

            try
            {
                CurrentWFContext.Parameters.Remove("Onayli");
            }
            catch (Exception) { }
            CurrentWFContext.Save();
            ActionTaskWorker.Send(taskInstance.WfActionInstanceId, LoginObject.LoginId);
            WorkflowHistoryWorker.Execute(taskInstance, LoginObject.LoginId, FormInformationHelper.GetAssignedUser(taskInstance).LoginId, (Digiturk.Workflow.Digiflow.Entities.Enums.WorkflowHistoryActionType)UserActionHistoryActionType.ACCEPTED, comment);
        }
    }
}
