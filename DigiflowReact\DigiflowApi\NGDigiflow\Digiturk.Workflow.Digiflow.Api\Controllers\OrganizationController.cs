﻿using Digiturk.Workflow.Digiflow.Authorization;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Newtonsoft.Json;
using Digiturk.Workflow.Digiflow.WebCore;
using System.Collections.Generic;
using System.Data;
using System.Web.Http;

namespace Digiturk.Workflow.Digiflow.Api.Controllers
{
    public class OrganizationController : ApiController
    {
        [HttpGet]
        [Route("api/GetUserDTO")]
        public LoginDto GetUserDTO(long LoginId)
        {
            //Secili Departmanları Her şeyi burdan bulacaksın.
            LoginDto LoginObj = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDetailsOfLogin(LoginId);
            return LoginObj;
        }

        [HttpGet]
        [Route("api/GetDepartment")]
        public Dictionary<long, string> GetDepartment(string lang)
        {
            Dictionary<long, string> departmentlist = new Dictionary<long, string>();
            DataTable dtdepartment;
            if (lang == "tr")
            {
                dtdepartment = WfDataHelpers.GetDeparmentList();
                for (int i = 0; i < dtdepartment.Rows.Count; i++)
                {
                    departmentlist.Add(long.Parse(dtdepartment.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtdepartment.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            else
            {
                dtdepartment = WfDataHelpers.GetDeparmentList();
                for (int i = 0; i < dtdepartment.Rows.Count; i++)
                {
                    departmentlist.Add(long.Parse(dtdepartment.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtdepartment.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            return departmentlist;
        }

        [HttpGet]
        [Route("api/GetDivision")]
        public Dictionary<long, string> GetDivision(long departmantId, string lang)
        {
            Dictionary<long, string> divisionlist = new Dictionary<long, string>();
            DataTable dtdivision;
            if (lang == "tr")
            {
                dtdivision = WfDataHelpers.GetDivisionList(departmantId);
                for (int i = 0; i < dtdivision.Rows.Count; i++)
                {
                    divisionlist.Add(long.Parse(dtdivision.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtdivision.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            else
            {
                dtdivision = WfDataHelpers.GetDivisionList(departmantId);
                for (int i = 0; i < dtdivision.Rows.Count; i++)
                {
                    divisionlist.Add(long.Parse(dtdivision.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtdivision.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            return divisionlist;
        }

        [HttpGet]
        [Route("api/GetUnit")]
        public Dictionary<long, string> GetUnitList(long divisionId, string lang)
        {
            Dictionary<long, string> unitlist = new Dictionary<long, string>();
            DataTable dtunit;
            if (lang == "tr")
            {
                dtunit = WfDataHelpers.GetUnitList(divisionId);
                for (int i = 0; i < dtunit.Rows.Count; i++)
                {
                    unitlist.Add(long.Parse(dtunit.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtunit.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            else
            {
                dtunit = WfDataHelpers.GetUnitListEng(divisionId);
                for (int i = 0; i < dtunit.Rows.Count; i++)
                {
                    unitlist.Add(long.Parse(dtunit.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtunit.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            return unitlist;
        }

        [HttpGet]
        [Route("api/GetTeam")]
        public Dictionary<long, string> GetTeams(long unitId, string lang)
        {
            Dictionary<long, string> teamlist = new Dictionary<long, string>();
            DataTable dtteam;
            if (lang == "tr")
            {
                dtteam = WfDataHelpers.GetTeams(unitId);
                for (int i = 0; i < dtteam.Rows.Count; i++)
                {
                    teamlist.Add(long.Parse(dtteam.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtteam.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            else
            {
                dtteam = WfDataHelpers.GetTeamsEng(unitId);
                for (int i = 0; i < dtteam.Rows.Count; i++)
                {
                    teamlist.Add(long.Parse(dtteam.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtteam.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            return teamlist;
        }

        [HttpGet]
        [Route("api/GetSubTeam1")]
        public Dictionary<long, string> GetSubTeam1(long teamId, string lang)
        {
            Dictionary<long, string> subteam1list = new Dictionary<long, string>();
            DataTable dtsubteam1;
            if (lang == "tr")
            {
                dtsubteam1 = WfDataHelpers.GetTeams(teamId);
                for (int i = 0; i < dtsubteam1.Rows.Count; i++)
                {
                    subteam1list.Add(long.Parse(dtsubteam1.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam1.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            else
            {
                dtsubteam1 = WfDataHelpers.GetTeamsEng(teamId);
                for (int i = 0; i < dtsubteam1.Rows.Count; i++)
                {
                    subteam1list.Add(long.Parse(dtsubteam1.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam1.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            return subteam1list;
        }

        [HttpGet]
        [Route("api/GetSubTeam2")]
        public Dictionary<long, string> GetSubTeam2(long subTeam1Id, string lang)
        {
            Dictionary<long, string> subteam2list = new Dictionary<long, string>();
            DataTable dtsubteam2;
            if (lang == "tr")
            {
                dtsubteam2 = WfDataHelpers.GetTeams(subTeam1Id);
                for (int i = 0; i < dtsubteam2.Rows.Count; i++)
                {
                    subteam2list.Add(long.Parse(dtsubteam2.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam2.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            else
            {
                dtsubteam2 = WfDataHelpers.GetTeamsEng(subTeam1Id);
                for (int i = 0; i < dtsubteam2.Rows.Count; i++)
                {
                    subteam2list.Add(long.Parse(dtsubteam2.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam2.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            return subteam2list;
        }

        [HttpGet]
        [Route("api/GetSubTeam3")]
        public Dictionary<long, string> GetSubTeam3(long subTeam2Id, string lang)
        {
            Dictionary<long, string> subteam3list = new Dictionary<long, string>();
            DataTable dtsubteam3;
            if (lang == "tr")
            {
                dtsubteam3 = WfDataHelpers.GetTeams(subTeam2Id);
                for (int i = 0; i < dtsubteam3.Rows.Count; i++)
                {
                    subteam3list.Add(long.Parse(dtsubteam3.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam3.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            else
            {
                dtsubteam3 = WfDataHelpers.GetTeamsEng(subTeam2Id);
                for (int i = 0; i < dtsubteam3.Rows.Count; i++)
                {
                    subteam3list.Add(long.Parse(dtsubteam3.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam3.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            return subteam3list;
        }

        [HttpGet]
        [Route("api/GetSubTeam4")]
        public Dictionary<long, string> GetSubTeam4(long subTeam3Id, string lang)
        {
            Dictionary<long, string> subteam4list = new Dictionary<long, string>();
            DataTable dtsubteam4;
            if (lang == "tr")
            {
                dtsubteam4 = WfDataHelpers.GetTeams(subTeam3Id);
                for (int i = 0; i < dtsubteam4.Rows.Count; i++)
                {
                    subteam4list.Add(long.Parse(dtsubteam4.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam4.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            else
            {
                dtsubteam4 = WfDataHelpers.GetTeamsEng(subTeam3Id);
                for (int i = 0; i < dtsubteam4.Rows.Count; i++)
                {
                    subteam4list.Add(long.Parse(dtsubteam4.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam4.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            return subteam4list;
        }

        [HttpGet]
        [Route("api/GetSubTeam5")]
        public Dictionary<long, string> GetSubTeam5(long subTeam4Id, string lang)
        {
            Dictionary<long, string> subteam5list = new Dictionary<long, string>();
            DataTable dtsubteam5;
            if (lang == "tr")
            {
                dtsubteam5 = WfDataHelpers.GetTeams(subTeam4Id);
                for (int i = 0; i < dtsubteam5.Rows.Count; i++)
                {
                    subteam5list.Add(long.Parse(dtsubteam5.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam5.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            else
            {
                dtsubteam5 = WfDataHelpers.GetTeamsEng(subTeam4Id);
                for (int i = 0; i < dtsubteam5.Rows.Count; i++)
                {
                    subteam5list.Add(long.Parse(dtsubteam5.Rows[i]["LOGIN_GROUP_ID"].ToString()), dtsubteam5.Rows[i]["LOGIN_GROUP_NAME"].ToString());
                }
            }
            return subteam5list;
        }

        [HttpGet]
        [Route("api/GetUserSelectList")]
        public Dictionary<long, string> GetUserSelectList(long deptId)
        {
            Dictionary<long, string> userslist = new Dictionary<long, string>();
            DataTable dtPersonel = WfDataHelpers.GetPersonel(deptId);

            for (int i = 0; i < dtPersonel.Rows.Count; i++)
            {
                userslist.Add(long.Parse(dtPersonel.Rows[i]["LOGINID"].ToString()), dtPersonel.Rows[i]["LOGINNAME"].ToString());
            }

            return userslist;
        }

        [HttpGet]
        [Route("api/GetOrgSchema")]
        public IHttpActionResult GetOrgSchema()
        {
            //string KatSekreterleriLogicalGroup = LogicalGroupHelper.LogicalGroupIDBul("KatSekreterleri");
            //if (LogicalGroupHelper.IsExistLogicalGroup(long.Parse(KatSekreterleriLogicalGroup), UserInformation.LoginObject.LoginId))
            //{
            //    string UstYoneticilerLogicalGroup = LogicalGroupHelper.LogicalGroupIDBul("UstYoneticiler");
            //    DataTable dtbUstYoneticiler = LogicalGroupHelper.GetPersonelList(long.Parse(UstYoneticilerLogicalGroup));
            //    return Ok(JsonConvert.SerializeObject(dtbUstYoneticiler.Rows));
            //}
            //else
            //{
            //    return Ok(WfDataHelpers.GetFullNameFromUserName(UserInformation.LoginObject.DomainUserName));
            //}
            return Ok();
        }


        //GetDepartment() 
        //DepartmentDropDownList => 
        //DepartmentDropDownList.DataSource = WfDataHelpers.GetDeparmentList();
        //DepartmentDropDownList.DataTextField = "LOGIN_GROUP_NAME";
        //DepartmentDropDownList.DataValueField = "LOGIN_GROUP_ID";

        //GetDepartment() 
        //GetDivision()
        //GetUnit()
        //GetTeam();
        //SubTeam1();
        //SubTeamDropDownList2();
        //SubTeamDropDownList3;
        //SubTeamDropDownList4;
        //SubTeamDropDownList5();

        //UserDropDownList();

    }
}
