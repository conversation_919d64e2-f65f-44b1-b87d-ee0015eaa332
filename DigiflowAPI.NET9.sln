Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{3D8A8F95-F890-4C73-B07D-7180E95E63D1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Domain", "src\DigiflowAPI.Domain\DigiflowAPI.Domain.csproj", "{1F8B8F76-0FB7-48BD-9ADE-68CB0CD5E85B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Application", "src\DigiflowAPI.Application\DigiflowAPI.Application.csproj", "{62A1F69D-2B92-4C4B-A5C9-2038F21FE40A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Infrastructure", "src\DigiflowAPI.Infrastructure\DigiflowAPI.Infrastructure.csproj", "{44D22225-7603-4F22-AD7C-A615539F6CF9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.WebApi", "src\DigiflowAPI.WebApi\DigiflowAPI.WebApi.csproj", "{23F2B88B-1E66-4DC7-876E-C3E6E08EC477}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.MobileAPI", "src\DigiflowAPI.MobileApi\DigiflowAPI.MobileAPI.csproj", "{66E8C2F9-4F36-4312-8D8C-F146E625744D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{F726F7AA-4111-40E3-ADD6-0776A64D7B47}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Domain.Tests", "tests\DigiflowAPI.Domain.Tests\DigiflowAPI.Domain.Tests.csproj", "{A5E2E1FB-44C1-4E77-8A77-B5F8FDD7D9A8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Application.Tests", "tests\DigiflowAPI.Application.Tests\DigiflowAPI.Application.Tests.csproj", "{BCAD97DE-FA78-4D96-BB78-00F1F616AA42}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Infrastructure.Tests", "tests\DigiflowAPI.Infrastructure.Tests\DigiflowAPI.Infrastructure.Tests.csproj", "{5F5A4B3A-91C8-4F4C-8B45-95C97DE32251}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.WebApi.Tests", "tests\DigiflowAPI.WebApi.Tests\DigiflowAPI.WebApi.Tests.csproj", "{78D74C7C-F06C-4721-8A10-0E0BD1DDD0A9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Logging.Tests", "tests\DigiflowAPI.Logging.Tests\DigiflowAPI.Logging.Tests.csproj", "{7E5C8C0F-E5D4-4F3C-8C9A-88A89D3EC925}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1F8B8F76-0FB7-48BD-9ADE-68CB0CD5E85B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F8B8F76-0FB7-48BD-9ADE-68CB0CD5E85B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1F8B8F76-0FB7-48BD-9ADE-68CB0CD5E85B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1F8B8F76-0FB7-48BD-9ADE-68CB0CD5E85B}.Release|Any CPU.Build.0 = Release|Any CPU
		{62A1F69D-2B92-4C4B-A5C9-2038F21FE40A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{62A1F69D-2B92-4C4B-A5C9-2038F21FE40A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{62A1F69D-2B92-4C4B-A5C9-2038F21FE40A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{62A1F69D-2B92-4C4B-A5C9-2038F21FE40A}.Release|Any CPU.Build.0 = Release|Any CPU
		{44D22225-7603-4F22-AD7C-A615539F6CF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44D22225-7603-4F22-AD7C-A615539F6CF9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44D22225-7603-4F22-AD7C-A615539F6CF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44D22225-7603-4F22-AD7C-A615539F6CF9}.Release|Any CPU.Build.0 = Release|Any CPU
		{23F2B88B-1E66-4DC7-876E-C3E6E08EC477}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23F2B88B-1E66-4DC7-876E-C3E6E08EC477}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23F2B88B-1E66-4DC7-876E-C3E6E08EC477}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23F2B88B-1E66-4DC7-876E-C3E6E08EC477}.Release|Any CPU.Build.0 = Release|Any CPU
		{66E8C2F9-4F36-4312-8D8C-F146E625744D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66E8C2F9-4F36-4312-8D8C-F146E625744D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66E8C2F9-4F36-4312-8D8C-F146E625744D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66E8C2F9-4F36-4312-8D8C-F146E625744D}.Release|Any CPU.Build.0 = Release|Any CPU
		{A5E2E1FB-44C1-4E77-8A77-B5F8FDD7D9A8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A5E2E1FB-44C1-4E77-8A77-B5F8FDD7D9A8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A5E2E1FB-44C1-4E77-8A77-B5F8FDD7D9A8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A5E2E1FB-44C1-4E77-8A77-B5F8FDD7D9A8}.Release|Any CPU.Build.0 = Release|Any CPU
		{BCAD97DE-FA78-4D96-BB78-00F1F616AA42}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BCAD97DE-FA78-4D96-BB78-00F1F616AA42}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BCAD97DE-FA78-4D96-BB78-00F1F616AA42}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BCAD97DE-FA78-4D96-BB78-00F1F616AA42}.Release|Any CPU.Build.0 = Release|Any CPU
		{5F5A4B3A-91C8-4F4C-8B45-95C97DE32251}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5F5A4B3A-91C8-4F4C-8B45-95C97DE32251}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5F5A4B3A-91C8-4F4C-8B45-95C97DE32251}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5F5A4B3A-91C8-4F4C-8B45-95C97DE32251}.Release|Any CPU.Build.0 = Release|Any CPU
		{78D74C7C-F06C-4721-8A10-0E0BD1DDD0A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{78D74C7C-F06C-4721-8A10-0E0BD1DDD0A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{78D74C7C-F06C-4721-8A10-0E0BD1DDD0A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{78D74C7C-F06C-4721-8A10-0E0BD1DDD0A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E5C8C0F-E5D4-4F3C-8C9A-88A89D3EC925}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E5C8C0F-E5D4-4F3C-8C9A-88A89D3EC925}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E5C8C0F-E5D4-4F3C-8C9A-88A89D3EC925}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E5C8C0F-E5D4-4F3C-8C9A-88A89D3EC925}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{1F8B8F76-0FB7-48BD-9ADE-68CB0CD5E85B} = {3D8A8F95-F890-4C73-B07D-7180E95E63D1}
		{62A1F69D-2B92-4C4B-A5C9-2038F21FE40A} = {3D8A8F95-F890-4C73-B07D-7180E95E63D1}
		{44D22225-7603-4F22-AD7C-A615539F6CF9} = {3D8A8F95-F890-4C73-B07D-7180E95E63D1}
		{23F2B88B-1E66-4DC7-876E-C3E6E08EC477} = {3D8A8F95-F890-4C73-B07D-7180E95E63D1}
		{66E8C2F9-4F36-4312-8D8C-F146E625744D} = {3D8A8F95-F890-4C73-B07D-7180E95E63D1}
		{A5E2E1FB-44C1-4E77-8A77-B5F8FDD7D9A8} = {F726F7AA-4111-40E3-ADD6-0776A64D7B47}
		{BCAD97DE-FA78-4D96-BB78-00F1F616AA42} = {F726F7AA-4111-40E3-ADD6-0776A64D7B47}
		{5F5A4B3A-91C8-4F4C-8B45-95C97DE32251} = {F726F7AA-4111-40E3-ADD6-0776A64D7B47}
		{78D74C7C-F06C-4721-8A10-0E0BD1DDD0A9} = {F726F7AA-4111-40E3-ADD6-0776A64D7B47}
		{7E5C8C0F-E5D4-4F3C-8C9A-88A89D3EC925} = {F726F7AA-4111-40E3-ADD6-0776A64D7B47}
	EndGlobalSection
EndGlobal