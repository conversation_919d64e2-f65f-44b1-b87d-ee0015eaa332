.dxeLoadingDiv_Office2010Silver
{
	background: White;
    opacity: 0.85;
    filter: alpha(opacity=85);
    cursor: wait;
}
.dxeLoadingDivWithContent_Office2010Silver
{
	background: White;
    opacity: 0.01;
    filter: alpha(opacity=1);
}

.dxeLoadingPanel_Office2010Silver
{
	font: 8pt Verdana;
    color: #585e68;
}
.dxeLoadingPanelWithContent_Office2010Silver
{
    font: 8pt Verdana;
    color: #585e68;
    background: White;
	border: 1px solid #80858d;
}

.dxeLoadingPanel_Office2010Silver td.dx,
.dxeLoadingPanelWithContent_Office2010Silver td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}

.dxeReadOnly_Office2010Silver
{
}
.dxeBase_Office2010Silver
{
    font-family: Verdana;
    font-size: 8pt;
}
/* -- ErrorFrame -- */
.dxeErrorCell_Office2010Silver,
.dxeErrorCell_Office2010Silver td
{
    font-family: Verdana;
    font-size: 8pt;
	color: Red;
}
.dxeErrorCell_Office2010Silver
{
	padding-left: 4px;
	padding-right: 5px;
}
.dxeErrorFrameWithoutError_Office2010Silver {
    border: 1px solid Red;
}
.dxeErrorFrameWithoutError_Office2010Silver .dxeControlsCell_Office2010Silver {
    padding: 2px;
}

.dxeEditArea_Office2010Silver
{
	font-family: Verdana;
	font-size: 8pt;
	border: 1px solid #a5acb5;
}
.dxeMemoEditArea_Office2010Silver,
input.dxeEditArea_Office2010Silver,
.dxeBase_Office2010Silver input
{
    outline: none;
}

/* -- Buttons -- */
.dxeButtonEditButton_Office2010Silver,
.dxeCalendarButton_Office2010Silver,
.dxeButtonEditButton_Office2010Silver td.dx,
.dxeCalendarButton_Office2010Silver td.dx,
.dxeSpinIncButton_Office2010Silver,
.dxeSpinDecButton_Office2010Silver,
.dxeSpinLargeIncButton_Office2010Silver,
.dxeSpinLargeDecButton_Office2010Silver,
.dxeSpinIncButton_Office2010Silver td.dx,
.dxeSpinDecButton_Office2010Silver td.dx,
.dxeSpinLargeIncButton_Office2010Silver td.dx,
.dxeSpinLargeDecButton_Office2010Silver td.dx
{
    font-family: Verdana;
    font-size: 8pt;
    font-weight: normal;
	text-align: center;
	white-space: nowrap;
}

.dxeButtonEditButton_Office2010Silver,
.dxeCalendarButton_Office2010Silver,
.dxeSpinIncButton_Office2010Silver,
.dxeSpinDecButton_Office2010Silver,
.dxeSpinLargeIncButton_Office2010Silver,
.dxeSpinLargeDecButton_Office2010Silver
{
	vertical-align: middle;
	cursor: pointer;
}
.dxeButtonEdit_Office2010Silver .dxeSBC,
.dxeCalendarButton_Office2010Silver,
.dxeButtonEditButton_Office2010Silver
{
    border-style: solid;
	border-color: #a5acb5;
}
.dxeCalendarButton_Office2010Silver
{
    border-width: 1px;
}
.dxeButtonEditButton_Office2010Silver,
.dxeButtonEdit_Office2010Silver .dxeSBC
{
    border-width: 0 0 0 1px;
}
.dxeButtonEdit_Office2010Silver .dxeButtonLeft,
.dxeButtonEdit_Office2010Silver .dxeSBC.dxeButtonLeft
{
    border-width: 0 1px 0 0;
}
.dxeSpinIncButton_Office2010Silver,
.dxeSpinDecButton_Office2010Silver,
.dxeSpinLargeIncButton_Office2010Silver,
.dxeSpinLargeDecButton_Office2010Silver
{
    border-width: 0;
}

.dxeButtonEditButton_Office2010Silver,
.dxeSpinLargeIncButton_Office2010Silver,
.dxeSpinLargeDecButton_Office2010Silver
{
    background: #e8ecee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtDropDownBtnBack.png")%>') repeat-x left top;
}
.dxeSpinIncButton_Office2010Silver
{
    background: #f0f3f6 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtSpinIncBtnBack.png")%>') repeat-x left top;
}
.dxeSpinDecButton_Office2010Silver
{
    background: #e8ebee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtSpinDecBtnBack.png")%>') repeat-x left top;
}

.dxeButtonEditButton_Office2010Silver table.dxbebt
{
    padding: 3px 2px 3px 3px;
}
.dxeSpinIncButton_Office2010Silver table.dxbebt,
.dxeSpinDecButton_Office2010Silver table.dxbebt,
.dxeSpinLargeIncButton_Office2010Silver table.dxbebt,
.dxeSpinLargeDecButton_Office2010Silver table.dxbebt
{
    padding: 0;
}

.dxeSpinIncButton_Office2010Silver
{
    padding: 1px 0 2px;
}
.dxeSpinDecButton_Office2010Silver
{
   padding: 3px 0 2px;
}
.dxeSpinLargeIncButton_Office2010Silver,
.dxeSpinLargeDecButton_Office2010Silver
{
    padding: 6px 0;
}

.dxeButtonEditButton_Office2010Silver table.dxbebt,
.dxeSpinLargeIncButton_Office2010Silver table.dxbebt,
.dxeSpinLargeDecButton_Office2010Silver table.dxbebt
{
	width: 9px;
}
.dxeSpinIncButton_Office2010Silver table.dxbebt,
.dxeSpinDecButton_Office2010Silver table.dxbebt
{
    width: 15px;
}
.dxeCalendarButton_Office2010Silver
{
	font-size: 8pt;
	background: #e7eaee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtBtnBack.png")%>') repeat-x left top;
	padding: 4px 11px;
	padding-top: 3px;
	width: 32px;
}
.dxeCalendarButton_Office2010Silver td.dx
{
	font-size: 8pt;
	text-align: center;
	white-space: nowrap;
}
.dxeCalendarButton_Office2010Silver table.dxbebt
{
	width: 100%;
}

/* -- Pressed -- */
.dxeCalendarButtonPressed_Office2010Silver
{
	border-color: #c2762b;
}
.dxeCalendarButtonPressed_Office2010Silver
{
    background: #fee287 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtBtnPBack.png")%>') repeat-x left top;
}
.dxeButtonEditButtonPressed_Office2010Silver,
.dxeSpinIncButtonPressed_Office2010Silver,
.dxeSpinDecButtonPressed_Office2010Silver,
.dxeSpinLargeIncButtonPressed_Office2010Silver,
.dxeSpinLargeDecButtonPressed_Office2010Silver
{
    background: #fee287 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtDropDownBtnPBack.png")%>') repeat-x left top;
}
/* -- Hover -- */
.dxeCalendarButtonHover_Office2010Silver
{
	border-color: #eecc53;
}
.dxeCalendarButtonHover_Office2010Silver
{
    background: #fcf8e5 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtBtnHBack.png")%>') repeat-x left top;
}
.dxeButtonEditButtonHover_Office2010Silver,
.dxeSpinLargeIncButtonHover_Office2010Silver,
.dxeSpinLargeDecButtonHover_Office2010Silver
{
    background: #f8e9ac url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtDropDownBtnHBack.png")%>') repeat-x left top;
}
.dxeSpinIncButtonHover_Office2010Silver,
.dxeSpinDecButtonHover_Office2010Silver
{
    background: #f8e18a url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtSpinIncDecBtnHBack.png")%>') repeat-x left top;
}

.dxeButtonEdit_Office2010Silver
{
    background-color: white;
    border: 1px solid #a5acb5;
    width: 170px;
}
.dxeButtonEdit_Office2010Silver .dxeEditArea_Office2010Silver {
    background-color: white;
}
.dxeButtonEdit_Office2010Silver .dxeEditArea_Office2010Silver,
.dxeButtonEdit_Office2010Silver td.dxic
{
	width: 100%;
}
.dxeButtonEdit_Office2010Silver td.dxic
{
    padding: 2px 4px 1px;
}
.dxeButtonEdit_Office2010Silver .dxeIIC,
.dxeButtonEdit_Office2010Silver .dxeIICR
{
    padding: 1px;
}
.dxeButtonEdit_Office2010Silver .dxeIIC img {
    padding-left: 3px;
}
.dxeButtonEdit_Office2010Silver .dxeIICR img {
    padding-right: 3px;
}

.dxeTextBox_Office2010Silver,
.dxeMemo_Office2010Silver
{
    background-color: white;
    border: 1px solid #a5acb5;
}
.dxeTextBox_Office2010Silver td.dxic
{
	padding: 2px 4px 1px;
	width: 100%;
}
.dxeTextBox_Office2010Silver .dxeEditArea_Office2010Silver
{
    background-color: white;
}
.dxeRadioButtonList_Office2010Silver
{
    border: 1px solid #a5acb5;
}
.dxeRadioButtonList_Office2010Silver,
.dxeRadioButtonList_Office2010Silver table
{
    font-family: Verdana;
    font-size: 8pt;
}
.dxeRadioButtonList_Office2010Silver td.dxe
{
    padding: 7px 25px 6px 11px;
}
.dxeRadioButtonList_Office2010Silver label
{
	margin-right: 6px;
}
/* Disabled */
.dxeDisabled_Office2010Silver .dxeButtonEditButton_Office2010Silver,
.dxeDisabled_Office2010Silver .dxeSpinLargeIncButton_Office2010Silver,
.dxeDisabled_Office2010Silver .dxeSpinLargeDecButton_Office2010Silver
{
    background: #f7f8fa url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtDropDownBtnDisabledBack.png")%>') repeat-x left top;
}
.dxeDisabled_Office2010Silver .dxeSpinIncButton_Office2010Silver
{
    background: #f9fbfc url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtSpinIncBtnDisabledBack.png")%>') repeat-x left top;
}
.dxeDisabled_Office2010Silver .dxeSpinDecButton_Office2010Silver
{
    background: #f7f8fa url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtSpinDecBtnDisabledBack.png")%>') repeat-x left top;
}

/* -- Memo -- */
.dxeMemo_Office2010Silver
{
}
.dxeMemoEditArea_Office2010Silver
{
	background-color: white;
	font-family: Verdana;
	font-size: 8pt;
}
.dxeMemo_Office2010Silver td
{
	padding: 0 0 0 1px;
	width: 100%;
}

/* -- Hyperlink -- */
.dxeHyperlink_Office2010Silver
{
    font-family: Verdana;
    font-size: 8pt;
    font-weight: normal;
    color: #5a9ddb;
    text-decoration: none;
}
a.dxeHyperlink_Office2010Silver:hover
{
    text-decoration: underline;
}
a.dxeHyperlink_Office2010Silver:visited
{
    color: #c983e4;
}

/* -- ListBox -- */
.dxeListBox_Office2010Silver
{
    font-family: Verdana;
    font-size: 8pt;
	background-color: white;
	border: 1px solid #a5acb5;
    width: 70px;
    height: 109px;
}
.dxeListBox_Office2010Silver div.dxlbd
{
    padding-top: 1px;
    height: 108px;
}
.dxeListBoxItemRow_Office2010Silver
{
    cursor: default;
}
.dxeListBoxItem_Office2010Silver
{
    font-family: Verdana;
    font-weight: normal;
    font-size: 8pt;
    color: Black;
    padding: 2px 5px;
    white-space: nowrap;
    text-align: left;
    border-style: solid;
    border-color: White;
    border-width: 0 1px 1px 1px;
}
.dxeListBoxItem_Office2010Silver em
{
    background: none repeat scroll 0 0 #e2ecf7;
    color: Black;
    font-weight:bold;
    font-style:normal;
}

.dxeListBox_Office2010Silver td.dxeI,
.dxeListBox_Office2010Silver td.dxeIM,
.dxeListBox_Office2010Silver .dxeHIC,
.dxeListBox_Office2010Silver td.dxeFTM,
.dxeListBox_Office2010Silver td.dxeTM,
.dxeListBox_Office2010Silver td.dxeC,
.dxeListBox_Office2010Silver td.dxeCM,
.dxeListBox_Office2010Silver td.dxeHCC,
.dxeListBox_Office2010Silver td.dxeMI,
.dxeListBox_Office2010Silver td.dxeMIM
{
    border-right: 0!important;
}

.dxeListBox_Office2010Silver td.dxeIR,
.dxeListBox_Office2010Silver td.dxeIMR,
.dxeListBox_Office2010Silver .dxeHICR,
.dxeListBox_Office2010Silver td.dxeFTMR,
.dxeListBox_Office2010Silver td.dxeTMR,
.dxeListBox_Office2010Silver td.dxeCR,
.dxeListBox_Office2010Silver td.dxeCMR,
.dxeListBox_Office2010Silver td.dxeHCCR,
.dxeListBox_Office2010Silver td.dxeMIR,
.dxeListBox_Office2010Silver td.dxeMIMR
{
    border-left: 0!important;
}

.dxeListBox_Office2010Silver td.dxeCM,
.dxeListBox_Office2010Silver td.dxeHCC,
.dxeListBox_Office2010Silver td.dxeCMR,
.dxeListBox_Office2010Silver td.dxeHCCR
{
    width: 25px;
}

.dxeListBox_Office2010Silver td.dxeIM,
.dxeListBox_Office2010Silver td.dxeIMR
{
    width: 0;
}

.dxeListBox_Office2010Silver td.dxeT
{
    width: 100%;
    padding-left: 0!important;
}

.dxeListBox_Office2010Silver td.dxeTR
{
    width: 100%;
    padding-right: 0!important;
}

.dxeListBox_Office2010Silver td.dxeT,
.dxeListBox_Office2010Silver td.dxeMI
{
    border-left: 0!important;
}

.dxeListBox_Office2010Silver td.dxeTR,
.dxeListBox_Office2010Silver td.dxeMIR
{
    border-right: 0!important;
}

.dxeListBox_Office2010Silver td.dxeFTM,
.dxeListBox_Office2010Silver td.dxeTM,
.dxeListBox_Office2010Silver td.dxeLTM,
.dxeListBox_Office2010Silver .dxeHFC,
.dxeListBox_Office2010Silver .dxeHC,
.dxeListBox_Office2010Silver .dxeHLC,
.dxeListBox_Office2010Silver td.dxeFTMR,
.dxeListBox_Office2010Silver td.dxeTMR,
.dxeListBox_Office2010Silver td.dxeLTMR,
.dxeListBox_Office2010Silver .dxeHFCR,
.dxeListBox_Office2010Silver .dxeHCR,
.dxeListBox_Office2010Silver .dxeHLCR
{
    overflow: hidden;
}

.dxeListBox_Office2010Silver td.dxeFTM,
.dxeListBox_Office2010Silver td.dxeTM,
.dxeListBox_Office2010Silver .dxeHFC,
.dxeListBox_Office2010Silver .dxeHC
{
    padding-right: 6px!important;
}

.dxeListBox_Office2010Silver td.dxeFTMR,
.dxeListBox_Office2010Silver td.dxeTMR,
.dxeListBox_Office2010Silver .dxeHFCR,
.dxeListBox_Office2010Silver .dxeHCR
{
    padding-left: 6px!important;
}

.dxeListBox_Office2010Silver td.dxeLTM,
.dxeListBox_Office2010Silver td.dxeTM,
.dxeListBox_Office2010Silver .dxeHC,
.dxeListBox_Office2010Silver .dxeHLC
{
    padding-left: 6px!important;
}

.dxeListBox_Office2010Silver td.dxeLTMR,
.dxeListBox_Office2010Silver td.dxeTMR,
.dxeListBox_Office2010Silver .dxeHCR,
.dxeListBox_Office2010Silver .dxeHLCR
{
    padding-right: 6px!important;
}

.dxeListBox_Office2010Silver .dxeFTM,
.dxeListBox_Office2010Silver .dxeTM,
.dxeListBox_Office2010Silver .dxeHFC,
.dxeListBox_Office2010Silver .dxeHC,
.dxeListBox_Office2010Silver .dxeLTM,
.dxeListBox_Office2010Silver .dxeTM,
.dxeListBox_Office2010Silver .dxeHC,
.dxeListBox_Office2010Silver .dxeHLC,
.dxeListBox_Office2010Silver td.dxeIM,
.dxeListBox_Office2010Silver td.dxeFTM,
.dxeListBox_Office2010Silver td.dxeTM,
.dxeListBox_Office2010Silver td.dxeCM,
.dxeListBox_Office2010Silver td.dxeMIM,
.dxeListBox_Office2010Silver .dxeFTMR,
.dxeListBox_Office2010Silver .dxeTMR,
.dxeListBox_Office2010Silver .dxeHFCR,
.dxeListBox_Office2010Silver .dxeHCR,
.dxeListBox_Office2010Silver .dxeLTMR,
.dxeListBox_Office2010Silver .dxeTMR,
.dxeListBox_Office2010Silver .dxeHCR,
.dxeListBox_Office2010Silver .dxeHLCR,
.dxeListBox_Office2010Silver td.dxeIMR,
.dxeListBox_Office2010Silver td.dxeFTMR,
.dxeListBox_Office2010Silver td.dxeTMR,
.dxeListBox_Office2010Silver td.dxeCMR,
.dxeListBox_Office2010Silver td.dxeMIMR
{
    border-top-width: 0;
    border-bottom-width: 0;
}

/*Grid lines*/

.dxeListBox_Office2010Silver td.dxeLTM,
.dxeListBox_Office2010Silver td.dxeTM,
.dxeListBox_Office2010Silver td.dxeMIM
{
    border-left: 1px solid #dbdee1 !important;
}

.dxeListBox_Office2010Silver td.dxeLTMR,
.dxeListBox_Office2010Silver td.dxeTMR,
.dxeListBox_Office2010Silver td.dxeMIMR
{
    border-right: 1px solid #dbdee1 !important;
}

.dxeListBox_Office2010Silver td.dxeIM,
.dxeListBox_Office2010Silver td.dxeFTM,
.dxeListBox_Office2010Silver td.dxeTM,
.dxeListBox_Office2010Silver td.dxeLTM,
.dxeListBox_Office2010Silver td.dxeCM,
.dxeListBox_Office2010Silver td.dxeMIM,
.dxeListBox_Office2010Silver td.dxeIMR,
.dxeListBox_Office2010Silver td.dxeFTMR,
.dxeListBox_Office2010Silver td.dxeTMR,
.dxeListBox_Office2010Silver td.dxeLTMR,
.dxeListBox_Office2010Silver td.dxeCMR,
.dxeListBox_Office2010Silver td.dxeMIMR
{
    border-bottom: solid 1px #dbdee1;
}

.dxeListBoxItemSelected_Office2010Silver     /* inherits dxeListBoxItem */
{
    color: Black;
    background: #ecedef;
}
.dxeListBoxItemHover_Office2010Silver        /* inherits dxeListBoxItem */
{
    color: Black;
    background: #e2ecf7;
}
.dxeListBoxItemHover_Office2010Silver em,
.dxeListBoxItemSelected_Office2010Silver em
{
    background: none;
}

/*Header*/

.dxeListBox_Office2010Silver .dxeHD
{
    background: #e7ebef url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.lbHeaderBack.png")%>') repeat-x left top;
    border-bottom: 1px solid #a5acb5;
}
.dxeHD .dxeListBoxItem_Office2010Silver
{
    color: #3b3b3b;
    border-width: 0 1px;
    padding-top: 3px;
    padding-bottom: 3px;
}

.dxeListBox_Office2010Silver .dxeHC,
.dxeListBox_Office2010Silver .dxeHLC,
.dxeListBox_Office2010Silver td.dxeHMIC
{
    border-left: 1px solid #a5acb5;
}

.dxeListBox_Office2010Silver .dxeHCR,
.dxeListBox_Office2010Silver .dxeHLCR,
.dxeListBox_Office2010Silver td.dxeHMICR
{
    border-right: 1px solid #a5acb5;
    text-align: right;
}

.dxeListBox_Office2010Silver .dxeHIC,
.dxeListBox_Office2010Silver .dxeHFC,
.dxeListBox_Office2010Silver .dxeHCC
{
    border-left: 1px solid #ebeef2;
}

.dxeListBox_Office2010Silver .dxeHICR,
.dxeListBox_Office2010Silver .dxeHFCR,
.dxeListBox_Office2010Silver .dxeHCCR
{
    border-right: 1px solid #ebeef2;
    text-align: right;
}

.dxeListBox_Office2010Silver .dxeHFC,
.dxeListBox_Office2010Silver .dxeHC,
.dxeListBox_Office2010Silver .dxeHMIC
{
    border-right: 0;
}

.dxeListBox_Office2010Silver .dxeHFCR,
.dxeListBox_Office2010Silver .dxeHCR,
.dxeListBox_Office2010Silver .dxeHMICR
{
    border-left: 0;
    text-align: right;
}

.dxeListBox_Office2010Silver .dxeHLC
{
    border-right: 1px solid #a5acb5;
}

.dxeListBox_Office2010Silver .dxeHLCR
{
    border-left: 1px solid #a5acb5;
    text-align: right;
}

/* -- Calendar -- */
.dxeCalendar_Office2010Silver
{
    font-weight: normal;
    color: #303030;
    border: 1px solid #868b91;
    background-color: #f6f7f9;
    cursor: default;
}
.dxeCalendar_Office2010Silver td.dxMonthGrid
{
    padding: 1px 8px;
}
.dxeCalendar_Office2010Silver td.dxMonthGridWithWeekNumbers
{
    padding: 1px 20px 6px 8px;
}
.dxeCalendar_Office2010Silver td.dxMonthGridWithWeekNumbersRtl
{
    padding: 1px 8px 1px 20px;
}
.dxeCalendarDayHeader_Office2010Silver
{
    font-family: Verdana;
    font-size: 8pt;
    padding: 2px 4px 6px;
    border-bottom: 1px solid #d1d2d4;
}
.dxeCalendarWeekNumber_Office2010Silver
{
    font-family: Verdana;
    font-size: 7pt;
    text-align: right;
    padding: 3px 8px 2px 4px;
    color: #939394;
}
.dxeCalendarDay_Office2010Silver
{
    font-family: Verdana;
    font-size: 8pt;
    padding: 2px 5px 3px;
    text-align: center;
}
.dxeCalendarWeekend_Office2010Silver        /* inherits dxeCalendarDay */
{
    color: #C00000;
}
.dxeCalendarOtherMonth_Office2010Silver     /* inherits dxeCalendarDay */
{
    color: #939394;
}
.dxeCalendarOutOfRange_Office2010Silver     /* inherits dxeCalendarDay */
{
    color: #939394;
}
.dxeCalendarSelected_Office2010Silver       /* inherits dxeCalendarDay */
{
    color: #303030;
    background-color: #e2ecf7;
    border: 1px solid #c7d0da;
    padding: 1px 4px 2px;
}
.dxeCalendarToday_Office2010Silver         /* inherits dxeCalendarDay */
{
    padding: 2px 5px 3px;
    background-color: #e1e2e4;
    border-width: 0;
}
.dxeCalendarHeader_Office2010Silver
{
    border-style: none;
    padding: 4px;
}
.dxeCalendarHeader_Office2010Silver td.dxe
{
    font-family: Verdana;
    font-size: 8pt;
    text-align: center;
	cursor: pointer;
}
.dxeCalendarFooter_Office2010Silver
{
    background-color: #f6f7f9;
    padding: 10px 0;
    border-top: 1px solid #d1d2d4;
}
.dxeCalendarFastNav_Office2010Silver
{
    color: #303030;
    background: #f6f7f9;
    border: 1px solid #868b91;
    border-bottom-width: 0;
    padding: 5px 8px;
    cursor: default;
}
.dxeCalendarFastNavMonthArea_Office2010Silver
{
    padding: 0px 9px;
}
.dxeCalendarFastNavYearArea_Office2010Silver
{
}
.dxeCalendarFastNavFooter_Office2010Silver
{
    color: #303030;
    background: #f6f7f9;
    padding: 8px 0 17px 0;
    border: 1px solid #868b91;
    border-top-width: 0;
}
.dxeCalendarFastNavMonth_Office2010Silver,
.dxeCalendarFastNavYear_Office2010Silver
{
    font: normal 8pt Verdana;
    color: #303030;
    padding: 3px 5px;
    text-align: center;
	cursor: pointer;
}
.dxeCalendarFastNavYear_Office2010Silver
{
    padding: 3px 5px;
}
.dxeCalendarFastNavMonth_Office2010Silver
{
	padding: 6px;
}

.dxeCalendarFastNavMonthHover_Office2010Silver,
.dxeCalendarFastNavYearHover_Office2010Silver
{
    color: #303030;
    padding: 3px 5px;
    background: #e1e2e4;
    border-width: 0;
}
.dxeCalendarFastNavMonthHover_Office2010Silver
{
	padding: 6px;
}

.dxeCalendarFastNavMonthSelected_Office2010Silver,
.dxeCalendarFastNavYearSelected_Office2010Silver
{
    color: #303030;
    background-color: #e2ecf7;
    border: 1px solid #c7d0da;
}
.dxeCalendarFastNavYearSelected_Office2010Silver
{
    padding: 2px 4px;
}
.dxeCalendarFastNavMonthSelected_Office2010Silver
{
    padding: 5px;
}
/* Disabled */
.dxeDisabled_Office2010Silver,
.dxeDisabled_Office2010Silver td.dxe
{
	color: #989898;
	cursor: default;
}
a.dxeDisabled_Office2010Silver:hover
{
    color: #989898;
}
.dxeButtonDisabled_Office2010Silver,
.dxeButtonDisabled_Office2010Silver td.dxe
{
	color: #989898;
	cursor: default;
}
/* -- Button -- */
.dxbButton_Office2010Silver
{
  	color: #3c3c3c;
  	font-weight:normal;
	font-size: 8pt;
	font-family: Verdana;
	vertical-align: middle;
	border: 1px solid #bbbfc4;
	background: #e7eaee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtBtnBack.png")%>') repeat-x left top;
    padding: 1px;
	cursor: pointer;
}
.dxbButtonHover_Office2010Silver
{
  	color: #3c3c3c;
	background: #fcf8e5 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtBtnHBack.png")%>') repeat-x left top;
	border: solid 1px #eecc53;
}
.dxbButtonChecked_Office2010Silver
{
    color: #3c3c3c;
	background: #fee287 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtBtnPBack.png")%>') repeat-x left top;
	border: solid 1px #c2762b;
}
.dxbButtonPressed_Office2010Silver
{
  	color: #3c3c3c;
	background: #fee287 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtBtnPBack.png")%>') repeat-x left top;
	border: solid 1px #c2762b;
}
.dxbButton_Office2010Silver div.dxb
{
    padding: 3px 15px;
	border: 0px;
}
.dxbButton_Office2010Silver div.dxbf
{
    padding: 2px 14px;
	border: dotted 1px black;
}
.dxbButton_Office2010Silver div.dxb table
{
  	color: #3c3c3c;
	font-size: 8pt;
	font-family: Verdana;
}
.dxbButton_Office2010Silver div.dxb td.dxb
{
    background-color: transparent!important;
    background-image: url('')!important;
    border-width: 0px!important;
    padding: 0px!important;
}
/* Disabled */
.dxbDisabled_Office2010Silver
{
    border-color: #d0d4d8;
	color: #989898;
	background-image: none;
	background-color: #eef1f4;
	cursor: default;
}
.dxbDisabled_Office2010Silver td.dxb
{
	color: #989898;
}
/* -- FilterControl -- */
.dxfcTable_Office2010Silver
{
	border-collapse: separate!important;
}
.dxfcTable_Office2010Silver td.dxfc
{
	padding: 0px 0px 0px 3px;
	vertical-align: middle;
	font: 8pt Verdana;
	color: Black;
}
a.dxfcPropertyName_Office2010Silver
{
	white-space: nowrap!important;
	color: Blue!important;
}
a.dxfcGroupType_Office2010Silver
{
	white-space: nowrap!important;
	padding: 0px 3px 0px 3px!important;
	color: Red!important;
}
a.dxfcOperation_Office2010Silver
{
	white-space: nowrap!important;
	color: Green!important;
}
a.dxfcValue_Office2010Silver
{
	white-space: nowrap!important;
	color: Gray!important;
}

.dxfcLoadingDiv_Office2010Silver
{
	background: white;
	opacity: 0.01;
	filter: alpha(opacity=1);
}
.dxfcLoadingPanel_Office2010Silver
{
	font: 8pt Verdana;
    color: #585e68;
    background: White;
	border: 1px solid #80858d;
}
.dxfcLoadingPanel_Office2010Silver td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}

.dxeMaskHint_Office2010Silver
{
    font: 8pt Verdana;
    color: #3c3c3c;
	background: #ffffe1;
	border: 1px solid Black;
	padding: 2px 5px 3px;
}

/* -- ProgressBar -- */
.dxeProgressBar_Office2010Silver
{
    background: #f6f7f8 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtProgressBack.png")%>') repeat-x left top;
    border: 1px solid #a5acb5;
}
.dxeProgressBar_Office2010Silver,
.dxeProgressBar_Office2010Silver td.dxe
{
    font-family: Verdana;
    font-size: 8pt;
   	color: #3c3c3c;
}
.dxeProgressBar_Office2010Silver .dxePBMainCell_Office2010Silver,
.dxeProgressBar_Office2010Silver td.dxe
{
    padding: 0;
}
.dxeProgressBarIndicator_Office2010Silver
{
    background: #dfe6ed url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.edtProgressIndicatorBack.png")%>') repeat-x left top;
}

/* -- DropDownWindow -- */
.dxeDropDownWindow_Office2010Silver
{
    background-color: white;
    border: 1px solid #a5acb5;
}

/*----------------- ColorTable -----------------*/
.dxeColorIndicator_Office2010Silver
{
    border: 1px solid #a5acb5;
    width: 15px;
    height: 15px;
    cursor: pointer;
}
.dxeColorTable_Office2010Silver
{
    background-color: White;
    border: 1px solid #a5acb5;
}
.dxeItemPicker_Office2010Silver
{
    background-color: White;
	border: 1px solid #a7abb0;
}
.dxeColorTable_Office2010Silver td.dx,
.dxeItemPicker_Office2010Silver td.dx
{
    padding: 4px;
}
.dxeColorTableCell_Office2010Silver,
.dxeItemPickerCell_Office2010Silver
{
    padding: 3px;
    cursor: pointer;
}
.dxeColorTableCellDiv_Office2010Silver
{
    border: 1px solid #808080;
    width: 12px;
    height: 12px;
    font-size: 0px;
}
.dxeColorTableCellSelected_Office2010Silver
{
    padding: 2px!important;
    background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Web.mItemSBack.png")%>') repeat-x left top;
	border: 1px solid #c2762b;
}
.dxeColorTableCellHover_Office2010Silver,
.dxeItemPickerCellHover_Office2010Silver
{
    padding: 2px!important;
    background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Web.mSubItemHBack.png")%>') repeat-x left top;
	border: 1px solid #f2ca58;
}

/* -- Invalid Style -- */
.dxeInvalid_Office2010Silver
{
}
.dxeInvalid_Office2010Silver .dxeEditArea_Office2010Silver,
.dxeInvalid_Office2010Silver .dxeMemoEditArea_Office2010Silver {
}

/* -- Focused Style -- */
.dxeFocused_Office2010Silver
{
	border: 1px solid #808891;
}

/* -- Null Text Style -- */
.dxeNullText_Office2010Silver .dxeEditArea_Office2010Silver,
.dxeNullText_Office2010Silver .dxeMemoEditArea_Office2010Silver
{
	color: #989898;
}

/* -- Captcha -- */
.dxcaRefreshButton_Office2010Silver
{
	font-family: Verdana;
	text-decoration: none;
	font-size: 8pt;
	color: #5a9ddb;
}

.dxcaDisabledRefreshButton_Office2010Silver
{
	color: #989898;
}

.dxcaRefreshButtonCell_Office2010Silver
{
	padding-left: 4px;
}

.dxcaRefreshButtonText_Office2010Silver
{
}

.dxcaDisabledRefreshButtonText_Office2010Silver
{
}

.dxcaTextBoxCell_Office2010Silver
{
	font-family: Verdana;
	font-size: 8pt;
}

.dxcaTextBoxCell_Office2010Silver,
.dxcaTextBoxCellNoIndent_Office2010Silver
{
	font-family: Verdana;
	font-size: 8pt;
}

.dxcaTextBoxCell_Office2010Silver .dxeErrorCell_Office2010Silver
{
}

.dxcaTextBoxCellNoIndent_Office2010Silver .dxeErrorCell_Office2010Silver
{
	padding-left: 0px;
	padding-top: 4px;
	color: Red;
}

.dxcaTextBoxLabel_Office2010Silver
{
	padding-bottom: 4px;
	display: block;
}

.dxcaLoadingPanel_Office2010Silver
{
	font: 8pt Verdana;
    color: #585e68;
    background: White;
	border: 1px solid #80858d;
}

.dxcaLoadingPanel_Office2010Silver td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}