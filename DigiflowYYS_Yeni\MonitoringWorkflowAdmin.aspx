﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site1.Master" AutoEventWireup="true" CodeBehind="MonitoringWorkflowAdmin.aspx.cs" Inherits="DigiflowYYS_Yeni.MonitoringWorkflowAdmin" %>
<%@ MasterType VirtualPath="~/Site1.Master" %>
<%@ Register Src="~/UserControls/OrganizationTreeWebUserControl.ascx" TagName="OrganizationTreeWebUserControl" TagPrefix="uc1" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style type="text/css">
        .style1 {
            width: 393px;
            font-weight: bold;
        }

        .auto-style11 {
            width: 100%;
        }

        .auto-style12 {
            width: 195px;
            font-weight: bold;
        }

        .style13 {
            height: 50px;
            font-weight: bold;
        }

        .auto-style13 {
            width: 393px;
            font-weight: bold;
        }

        .auto-style8 {
            width: 54%;
        }

        .auto-style14 {
            height: 50px;
            font-weight: bold;
            width: 262px;
        }

        .auto-style15 {
            height: 50px;
            font-weight: bold;
            width: 230px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:Panel ID="PnlContent" runat="server">
        <table border="0" align="center" cellpadding="0" cellspacing="0" width="100%">
            <tr class="style13">
                <td colspan="3" class="style13">
                    <table class="auto-style11">
                        <tr>
                            <td class="auto-style12">Görüntüleme Talebi Türü</td>
                            <td>
                                <asp:RadioButtonList ID="rdTalepTuru" runat="server" OnSelectedIndexChanged="RdFlowType_SelectedIndexChanged" RepeatDirection="Horizontal" Style="margin-left: 0px" AutoPostBack="True">
                                    <asp:ListItem Value="0">İş Akışı Numarası Girerek</asp:ListItem>
                                    <asp:ListItem Selected="True" Value="1">Kullanıcı Seçerek</asp:ListItem>
                                </asp:RadioButtonList>
                            </td>
                        </tr>
                    </table>
                </td>
               
            </tr>
            <tr class="style13">
               
                <td class="auto-style13">Yetkiyi Kullanmak İsteyen</td>
                <td rowspan="2" class="auto-style8">
                    <asp:Panel ID="PnlPurchaseOwner" runat="server">
                        <table class="ui-accordion">
                            <tr>
                                <td class="style13">&nbsp;Yetki Sahibi</td>
                            </tr>
                            <tr>
                                <td>
                                    <uc1:OrganizationTreeWebUserControl runat="server" id="OrgTreePurchaseOwner" />
                                    <br />
                                <center>Ya da<br />
                      <asp:DropDownList ID="DrpUserName" runat="server" Width="350px">
                                         </asp:DropDownList></center>
                                </td>
                            </tr>
                        </table>
                    </asp:Panel>
                </td>
           
            </tr>
            <tr>
               
                <td class="auto-style14">
                    <uc1:OrganizationTreeWebUserControl ID="OrgTreePurchaseUser" runat="server" />
                    <br />
                 
                </td>
            
            </tr>
            <tr>
                <td>&nbsp;</td>
             
            </tr>
            <tr>
              
                <td colspan="2">&nbsp;
                <asp:Panel ID="pnlWorkFlowList" runat="server">
                    <table class="ui-accordion">
                        <tr>
                            <td>&nbsp;
                            </td>
                            <td class="style13">İş Akış Listesi</td>
                            <td>
                                <asp:DropDownList ID="DrpWorkFlow" runat="server" Width="220px">
                                </asp:DropDownList>

                                &nbsp;
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="DrpWorkFlow" ErrorMessage="İş Akışı Seçiniz!" ValidationGroup="Kaydet">*</asp:RequiredFieldValidator>
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
                </td>
             
            </tr>
            <tr>
             
                <td colspan="2">&nbsp;
                <asp:Panel ID="pnlWorkFlowId" runat="server">
                    <table class="ui-accordion">
                        <tr>
                            <td>&nbsp;
                            </td>
                            <td class="auto-style15">İş Akış Numarası</td>
                            <td>
                                <asp:TextBox ID="txtWorkflowInstanceId" runat="server"
                                    Width="220px"></asp:TextBox>
                                &nbsp;
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txtWorkflowInstanceId" ErrorMessage="İş Akış Numarası Giriniz!" ValidationGroup="Kaydet">*</asp:RequiredFieldValidator>
                            </td>
                        </tr>
                    </table>
                </asp:Panel>
                </td>
           
                <tr>
                    <td colspan="3">
                        <div align="center">
                            <asp:Button ID="btnKaydet" runat="server" Text="KAYDET" Width="40%" OnClick="btnKaydet_Click" />
                    </td>
                    </div>
                </tr>
            </tr>
            <tr>
                <td colspan="3">&nbsp;</td>
            </tr>
           
        </table>
    </asp:Panel>
</asp:Content>