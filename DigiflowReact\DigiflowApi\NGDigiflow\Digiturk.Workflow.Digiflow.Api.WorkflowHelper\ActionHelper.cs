﻿using Digiturk.Workflow.Digiflow.Authentication;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Framework;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System.Linq;
using System;
using System.Collections;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites;
using System.Collections.Generic;
using Digiturk.Workflow.Digiflow.Api.Common.DataTransferObjects.Workflow;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Common;
using System.Data;

namespace Digiturk.Workflow.Digiflow.Api.WorkflowHelper
{
    public class ActionHelper
    {
        private static long logDefGroupIdAction = 659; //live

        public static long AssignToId (long InstanceId, long LoginId)
        {
            if (AssignToLoginIdCheck(InstanceId,LoginId))
            {
                return LoginId;
            }
            else
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(InstanceId);
            }
        }


        public static bool AssignToLoginIdCheck(long InstanceId, long LoginId)
        {
            return AssignToIdListLong(InstanceId).Contains(LoginId);
        }


        public static List<long> AssignToIdListLong(long InstanceId)
        {
            return Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(InstanceId);   
        }


        // Assign Edilen Kullanıcıların Listesini Bulunduran Fonksiyon
        public static List<FLogin> AssignToIdList(long InstanceId)
        {
                return Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginList(InstanceId);
        }

       
        // Akış Tamamlandıktan Sonra İptal Edilme işlemi Söz konusu ise bu method akış bazlı ovveride edilerek şekillendirilir ve CancelWorkFlow() methodu içinden çağrılır.
        public virtual void CancelEndFlow()
        {
        }


        public static void WfHistoryExec(UserActionHistoryActionType wfHistory, long InstanceId, long LoginId, string Comment)
        {
            FWfActionTaskInstance CurrentActionTaskInstance = null;
            FWfWorkflowInstance CurrentWfIns = null;

            CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
            CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);

            #region History Kaydı Atılıyor

            if (CurrentActionTaskInstance == null)
            {
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);
                WorkflowHistoryWorker.Execute(ActionInstance, LoginId, AssignToId(InstanceId, LoginId), wfHistory, Comment);
            }
            else
            {
                WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, LoginId, AssignToId(InstanceId, LoginId), wfHistory, Comment);
            }

            #endregion History Kaydı Atılıyor
        }


        // Mail Gönderme Fonksiyonu
        public static void SendMail(long ActionToLoginId, long MailTemplateId, string Action, string ActionEng, bool IsSave,long LoginId, long InstanceId, string Comment, string UserName)
        {
            List<FLogin> ToList = new List<FLogin>();
            FWfActionTaskInstance CurrentActionTaskInstance = null;
            FWfWorkflowInstance CurrentWfIns = null;
            WFContext CurrentWFContext = null;
            FLogin AssignedUser = null;
            FLogin LoginObject = null;
            ContextObject SenderObj = null;
            long AssignUserId = 0;
            long ActionUserId = LoginId;
            var assignLoginIdlist = Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(InstanceId);
            FlowAdminOprObject FlowAdminOprs;

            CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
            CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
            FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            CurrentWFContext = new WFContext(CurrentWfIns);
            AssignedUser = FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
            LoginObject = DomainAuthentication.GetCurrentLogin(true, LoginId, UserName);
            ActionHelpers.VersionUpdateToEntity(InstanceId, CurrentActionTaskInstance.WfActionInstanceId);
            Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(InstanceId, CurrentActionTaskInstance, LoginObject, CurrentWFContext, AssignedUser, true, Comment);
            FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

            if (ActionToLoginId > 0)
            {
                FLogin ActionTo = WFRepository<FLogin>.GetEntity(ActionToLoginId);
                CurrentWFContext.Parameters.AddOrChangeItem("ActionTo", WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(ActionTo.LoginId));
                //toList.Add(flogin); // talep sahibi
            }
            foreach (var item in SenderObj)
            {
                CurrentWFContext.Parameters.AddOrChangeItem(item.Key, item.Value);
            }
            CurrentWFContext.Parameters.AddOrChangeItem("Action", Action);
            CurrentWFContext.Parameters.AddOrChangeItem("ActionEng", ActionEng);

            CurrentWFContext.Parameters.AddOrChangeItem("ActionDate", DateTime.Now.ToString());
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(LoginId));
            //if(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)!=null)CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WorkFlowHelpers.WfDataHelpers.GetCommentsLoginString(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)));
            FWfWorkflowDef WfDef = WFRepository<FWfWorkflowDef>.GetEntity(CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            CurrentWFContext.Parameters.AddOrChangeItem("WorkflowName", WfDef.Name);
            CurrentWFContext.Parameters.AddOrChangeItem("WfOwner", WfDataHelpers.GetLoginNameSurname(CurrentWfIns.OwnerLogin.LoginId));
            /// Burda Sadece Yes mi set edicek Ya Reddedilirse nolucak????
            if (!CurrentWFContext.Parameters.ContainsKey("Onay")) CurrentWFContext.Parameters.AddOrChangeItem("Onay", "Yes");
            if (!CurrentWFContext.Parameters.ContainsKey("LastUpdatedBy")) CurrentWFContext.Parameters.AddOrChangeItem("LastUpdatedBy", AssignedUser);
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentLoginType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
            //FWfActionTaskInstance ActionTaskIns = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentActionTaskInstance.WfActionInstanceId);
            //Dictionary<string, string> MyDic = new Dictionary<string, string>();
            if (CurrentActionTaskInstance == null)
            {
                //FWfActionTaskInstance
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);

                Digiturk.Workflow.Engine.MailHelper.SendMail(MailTemplateId, ActionInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
            }
            else
            {
                Digiturk.Workflow.Engine.MailHelper.SendMail(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(CurrentActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);
            }

            //MailHelper.SendEmailDirect(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext,MyDic,new List<string>(),new List<string>());
            //MailHelper.SendEmailDirect(
            if (IsSave) CurrentWFContext.Save();
            SenderObj.Clear();
            ToList.Clear();
        }


        // Akışı Yönlendirme Fonksiyonu
        public static void ForwardWorkFlow(DTOWorkflowProcesses dTOWorkflowProcesses, string UserName)
        {
            AuthenticationResult result = IdentityHelper.GetUserInformation(dTOWorkflowProcesses.LoginId, UserName, dTOWorkflowProcesses.WorkflowInstanceId, 0);
            long LoginId = dTOWorkflowProcesses.LoginId;
            long WorkflowInstanceId = dTOWorkflowProcesses.WorkflowInstanceId;
            string Comment = dTOWorkflowProcesses.Comment;
            long ForwardLoginId = dTOWorkflowProcesses.ForwardLoginId;
            List<FLogin> ToList = new List<FLogin>();
            FWfActionTaskInstance CurrentActionTaskInstance = null;
            FWfWorkflowInstance CurrentWfIns = null;
            WFContext CurrentWFContext = null;
            FLogin AssignedUser = null;
            FLogin LoginObject = null;
            ContextObject SenderObj = null;
            long AssignUserId = 0;
            long ActionUserId = result.LoginObject.LoginId;
            var assignLoginIdlist = Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(WorkflowInstanceId);
            FlowAdminOprObject FlowAdminOprs;

            if (ForwardLoginId == 0)
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yönlendirilecek Kullanıcıyı Seçiniz");
            }
            if (string.IsNullOrEmpty(Comment))
            {
                // throw new Exception("Yorum alanı boş bırakılamaz.");
                throw ExceptionHelper.EmptyCommentValidationException();
            }
            if (AssignToLoginIdCheck(WorkflowInstanceId, result.LoginObject.LoginId))
            {
                AssignUserId = result.LoginObject.LoginId;
            }
            else
            {
                AssignUserId = AssignToId(WorkflowInstanceId, result.LoginObject.LoginId);
            }
            if (ForwardLoginId == null || ForwardLoginId == 0)
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yönlendireceğiniz kullanıcıyı seçiniz");
            }
            
            using (UnitOfWork.Start())
            {
                CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
                CurrentWFContext = new WFContext(CurrentWfIns);
                AssignedUser = FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                LoginObject = DomainAuthentication.GetCurrentLogin(true, LoginId, UserName);
                ActionHelpers.VersionUpdateToEntity(WorkflowInstanceId, CurrentActionTaskInstance.WfActionInstanceId);
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region Forward İşlemi Yapılıyor

                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ForwardWorkFlow(WorkflowInstanceId, CurrentActionTaskInstance, result.LoginObject, ForwardLoginId);

                #endregion Forward İşlemi Yapılıyor

                #region Yönlendirilen Kişiye Mail Atılıyor

                var FwLogin = WFRepository<FLogin>.GetEntity(ForwardLoginId);
                ToList.Add(FwLogin);
                SenderObj.Add("Action", "yönlendirildi");
                SenderObj.Add("ActionEng", "Forwarded");
                SenderObj.Add("ActionDescription", Comment);
                //SenderObj.Add("ActionOwner",WfDataHelpers.GetCommentsLoginString(WfDataHelpers.GetLastComment(CurrentWfIns)));
                SendMail(ForwardLoginId, 9, "yönlendirildi", "Forwarded", false, result.LoginObject.LoginId, WorkflowInstanceId, Comment, UserName);

                #endregion Yönlendirilen Kişiye Mail Atılıyor

                #region Formu Oluşturan Kişiye Mail Atılıyor

                SenderObj.Add("DelegateTo", "");
                SenderObj.Add("Delegated", "");
                SenderObj.Add("WfOwner", WfDataHelpers.GetLoginNameSurname(CurrentWfIns.OwnerLogin.LoginId));
                FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                ToList.Add(ToListItem);
                SenderObj.Add("ActionDescription", Comment);
                //SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                SenderObj.Add("ActionTo", String.Format("({0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                //CurrentWFContext.Parameters["ActionTo"] = CurrentWFContext.Parameters["ActionTo"].ToString().Replace("(ilgili kişi:", "").Replace(")", "");
                CurrentWFContext.Parameters["ActionTo"] = CurrentWFContext.Parameters["ActionTo"].ToString().Replace("(", "").Replace(")", "");
                CurrentWFContext.Parameters.AddOrChangeItem("ActionToPersonel", WfDataHelpers.GetLoginNameSurname(FwLogin));
                SendMail(0, 997, "yönlendirildi", "Forwarded", false, result.LoginObject.LoginId, WorkflowInstanceId, Comment, UserName);
                CurrentWFContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(FwLogin));
                CurrentWFContext.Save();

                #endregion Formu Oluşturan Kişiye Mail Atılıyor

                #region History Kaydı Oluşturuluyor

                WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, ActionUserId, AssignUserId, UserActionHistoryActionType.FORWARD, Comment);
                WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, AssignToId(WorkflowInstanceId,LoginId), AssignToId(WorkflowInstanceId,LoginId), UserActionHistoryActionType.ASSIGN, "");

                #endregion History Kaydı Oluşturuluyor
            }
            //FlowAdminOperationChecking(FlowAdminOprs);
            //DisabledControl();
        }


        // Akışı Yoruma Gönderme Fonksiyonu
        public static void SendCommentWorkFlow(DTOWorkflowProcesses dTOWorkflowProcesses, string UserName)
        {
            long LoginId = dTOWorkflowProcesses.LoginId;
            long WorkflowInstanceId = dTOWorkflowProcesses.WorkflowInstanceId;
            string Comment = dTOWorkflowProcesses.Comment;
            long SendCommentLoginId = dTOWorkflowProcesses.SendCommentLoginId;
            AuthenticationResult result = IdentityHelper.GetUserInformation(LoginId, UserName, dTOWorkflowProcesses.WorkflowInstanceId, 0);
            FWfActionTaskInstance CurrentActionTaskInstance = null;
            FWfWorkflowInstance CurrentWfIns = null;
            WFContext CurrentWFContext = null;
            FLogin AssignedUser = null;
            FLogin LoginObject = null;
            ContextObject SenderObj = null;
            List<FLogin> ToList = new List<FLogin>();
            var assignLoginIdlist = Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(WorkflowInstanceId);
            FlowAdminOprObject FlowAdminOprs;

            if (string.IsNullOrEmpty(Comment))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            if (SendCommentLoginId == null || SendCommentLoginId == 0)
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yoruma Gönderilecek Kullanıcıyı Seçiniz");
            }
            
            using (UnitOfWork.Start())
            {
                CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);            
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
                CurrentWFContext = new WFContext(CurrentWfIns);
                AssignedUser = FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                LoginObject = DomainAuthentication.GetCurrentLogin(true, LoginId, UserName);
                ActionHelpers.VersionUpdateToEntity(WorkflowInstanceId, CurrentActionTaskInstance.WfActionInstanceId);
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region Yoruma Gönderme İşlemi Yapılır

                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.SendtoCommendWorkFlow(WorkflowInstanceId, CurrentActionTaskInstance, SendCommentLoginId, result.LoginObject.LoginId);

                #endregion Yoruma Gönderme İşlemi Yapılır

                #region Yoruma Gönderilen Kişiye Mail Atılıyor

                var FwLogin = WFRepository<FLogin>.GetEntity(SendCommentLoginId);
                ToList.Add(FwLogin);
                SenderObj.Add("Action", "yoruma gönderildi");
                SenderObj.Add("ActionEng", "Forwarded for remarks");
                SenderObj.Add("ActionDescription", Comment);
                SendMail(SendCommentLoginId, 9, "yoruma gönderildi", "Forwarded for remarks", false, result.LoginObject.LoginId, WorkflowInstanceId, Comment, UserName);

                #endregion Yoruma Gönderilen Kişiye Mail Atılıyor

                #region Formu Oluşturan Kişiye Bilgilendirme Maili Atılıyor

                if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                {
                    return;
                }

                ToList.Add(CurrentWfIns.OwnerLogin);
                SenderObj.Add("ActionDescription", Comment);
                //SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                SenderObj.Add("ActionTo", String.Format("({0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                SendMail(0, 997, "yoruma gönderildi", "Forwarded for remarks", false, result.LoginObject.LoginId, WorkflowInstanceId, Comment, UserName);

                #endregion Formu Oluşturan Kişiye Bilgilendirme Maili Atılıyor

                #region History Kaydı Oluşturuluyor

                WfHistoryExec(UserActionHistoryActionType.SENDTOCOMMENT, WorkflowInstanceId, result.LoginObject.LoginId, Comment);

                #endregion History Kaydı Oluşturuluyor
            }
            // FlowAdminOperationChecking(FlowAdminOprs);
        }


        // Akışı Yorumlayan Fonksiyon
        public static void CommentedWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses, string UserName)
        {
            long LoginId = dTOWorkflowProcesses.LoginId;
            long WorkflowInstanceId = dTOWorkflowProcesses.WorkflowInstanceId;
            string Comment = dTOWorkflowProcesses.Comment;
            AuthenticationResult result = IdentityHelper.GetUserInformation(LoginId, UserName, dTOWorkflowProcesses.WorkflowInstanceId, 0);
            FWfActionTaskInstance CurrentActionTaskInstance = null;
            FWfWorkflowInstance CurrentWfIns = null;
            WFContext CurrentWFContext = null;
            FLogin AssignedUser = null;
            FLogin LoginObject = null;
            ContextObject SenderObj = null;
            List<FLogin> ToList = new List<FLogin>();
            var assignLoginIdlist = Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(WorkflowInstanceId);
            FlowAdminOprObject FlowAdminOprs;

            if (string.IsNullOrEmpty(Comment))
            {
                throw new Exception("Yorum alanı boş bırakılamaz.");
            }

            using (UnitOfWork.Start())
            {
                #region Son ActionTaskInstanceÜretilir

                CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                CurrentActionTaskInstance = WfDataHelpers.GetLastActionTaskInstance(CurrentWfIns.WfCurrentState.WfStateInstanceFWfActionInstanceList.ToList().OrderBy(t => t.WfActionInstanceId));
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #endregion Son ActionTaskInstanceÜretilir

                #region Yorumu Cevaplama İşlemi Yapılır

                //ActionHelpers.SendRequestToCommentWorkFlow(WfIns, taskIns, ( (AuthenticationResult)Session[SessionUserVariable]).LoginObject, CurrentPageMode, Commend);

                #endregion Yorumu Cevaplama İşlemi Yapılır

                #region History Kaydı Oluşturuluyor

                //if (ActionTaskWorker.GetCommentInboxList(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId).Contains(CurrentActionTaskInstance))
                if (Digiflow.WorkFlowHelpers.FormInformationHelper.IsWFCommentInbox(LoginObject.LoginId, WorkflowInstanceId) || AssignToLoginIdCheck(WorkflowInstanceId, LoginObject.LoginId))
                {
                    WfHistoryExec(UserActionHistoryActionType.COMMENTED, WorkflowInstanceId, LoginId, Comment);
                    if (Digiflow.WorkFlowHelpers.FormInformationHelper.IsWFCommentInbox(LoginObject.LoginId, WorkflowInstanceId))
                    {
                        #region Yoruma Gönderen Kişiye Yorumunu Giriyor.

                        long SendToCommentLoginId = FormInformationHelper.SendToCommentLogin(LoginObject.LoginId, WorkflowInstanceId);
                        var FwLogin = WFRepository<FLogin>.GetEntity(SendToCommentLoginId);
                        ToList.Add(FwLogin);
                        SenderObj.Add("Action", "Yorum Talebi Cevaplandı");
                        SenderObj.Add("ActionEng", "Comment Request Answered");
                        SenderObj.Add("ActionDescription", Comment);
                        SendMail(SendToCommentLoginId, 9, "Yorum Talebi Cevaplandı", "Comment Request Answered", false, LoginId, WorkflowInstanceId, Comment, UserName);

                        #endregion Yoruma Gönderen Kişiye Yorumunu Giriyor.
                    }
                }
                else
                {
                    WfHistoryExec(UserActionHistoryActionType.ADDTOCOMMEND, WorkflowInstanceId, LoginId, Comment);

                    //yorumu ekle adımında gönder 
                    long YorumGrup = ConvertionHelper.ConvertValue<int>(LogicalGroupHelper.LogicalGroupIDBul("Satinalma_YorumGrup"));
                    DataTable DtYorum = LogicalGroupHelper.GetLoginPersonelList(YorumGrup);
                    FLogin FLogin = WFRepository<FLogin>.GetEntity(LoginObject.LoginId);

                    Digiturk.Workflow.DigiFlow.Framework.Action.ContextList ContextList = new Digiturk.Workflow.DigiFlow.Framework.Action.ContextList();
                    ContextList.AddOrChangeItems("Yorum", Comment);
                    ContextList.AddOrChangeItems("WorkFlowInsId", CurrentWfIns.WfWorkflowInstanceId.ToString());
                    ContextList.AddOrChangeItems("OnayDurum", "");
                    ContextList.AddOrChangeItems("IslemYapan", WfDataHelpers.GetLoginNameSurname(LoginObject.LoginId));


                    Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAllAcceptLoginList(FLogin, CurrentWfIns.WfWorkflowInstanceId, DtYorum, 1007, ContextList);
                    DtYorum = null;
                }

                #endregion History Kaydı Oluşturuluyor
            }
            // FlowAdminOperationChecking(FlowAdminOprs);
        }


        // Akışı Askıya Alma Fonksiyonu
        public static void SuspendWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses, string UserName)
        {
            long LoginId = dTOWorkflowProcesses.LoginId;
            long WorkflowInstanceId = dTOWorkflowProcesses.WorkflowInstanceId;
            string Comment = dTOWorkflowProcesses.Comment;
            DateTime SuspendDate = dTOWorkflowProcesses.SuspendDate;
            AuthenticationResult result = IdentityHelper.GetUserInformation(LoginId, UserName, dTOWorkflowProcesses.WorkflowInstanceId, 0);
            FWfActionTaskInstance CurrentActionTaskInstance = null;
            FWfWorkflowInstance CurrentWfIns = null;
            WFContext CurrentWFContext = null;
            FLogin AssignedUser = null;
            FLogin LoginObject = null;
            ContextObject SenderObj = null;
            List<FLogin> ToList = new List<FLogin>();
            var assignLoginIdlist = Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(WorkflowInstanceId);
            FlowAdminOprObject FlowAdminOprs;

            if (string.IsNullOrEmpty(Comment))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            using (UnitOfWork.Start())
            {
                CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
                CurrentWFContext = new WFContext(CurrentWfIns);
                AssignedUser = FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                LoginObject = DomainAuthentication.GetCurrentLogin(true, LoginId, UserName);
                ActionHelpers.VersionUpdateToEntity(WorkflowInstanceId, CurrentActionTaskInstance.WfActionInstanceId);
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region Form Askıya Alınıyor.

                DateTime delegationStartTime = DateTime.MinValue;
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.SuspendRequest(CurrentWfIns, SuspendDate, CurrentActionTaskInstance, LoginObject, Comment);

                #endregion Form Askıya Alınıyor.

                #region Form Askıya Alındıktan Sonra Bilgilendirme Yapılıyor.

                if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                {
                    return;
                }
                FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                ToList.Add(ToListItem);
                SenderObj.Add("ActionDescription", Comment);
                SenderObj.Add("ActionTo", "");
                SenderObj.Add("SuspendDate", CoreHelpers.DateTimeHelper.TarihFormatla(SuspendDate) + " 00:00:00");
                SendMail(0, 996, "askıya alındı", "Suspended", false, LoginId, WorkflowInstanceId, Comment, UserName);

                #endregion Form Askıya Alındıktan Sonra Bilgilendirme Yapılıyor.

                #region History Kaydı Oluşturuluyor

                WfHistoryExec(UserActionHistoryActionType.SUSPEND, WorkflowInstanceId, LoginId, Comment);

                #endregion History Kaydı Oluşturuluyor
            }
            // FlowAdminOperationChecking(FlowAdminOprs);
        }


        // Askıda Bekleyen Akışı Devam Ettiren Fonksiyon
        public static void ResumeWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses, string UserName)
        {
            long LoginId = dTOWorkflowProcesses.LoginId;
            long WorkflowInstanceId = dTOWorkflowProcesses.WorkflowInstanceId;
            string Comment = dTOWorkflowProcesses.Comment;
            AuthenticationResult result = IdentityHelper.GetUserInformation(LoginId, UserName, dTOWorkflowProcesses.WorkflowInstanceId, 0);
            FWfActionTaskInstance CurrentActionTaskInstance = null;
            FWfWorkflowInstance CurrentWfIns = null;
            WFContext CurrentWFContext = null;
            FLogin AssignedUser = null;
            FLogin LoginObject = null;
            ContextObject SenderObj = null;
            List<FLogin> ToList = new List<FLogin>();
            var assignLoginIdlist = Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(WorkflowInstanceId);
            FlowAdminOprObject FlowAdminOprs;

            if (string.IsNullOrEmpty(Comment))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }

            using (UnitOfWork.Start())
            {
                CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
                CurrentWFContext = new WFContext(CurrentWfIns);
                AssignedUser = FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                LoginObject = DomainAuthentication.GetCurrentLogin(true, LoginId, UserName);
                ActionHelpers.VersionUpdateToEntity(WorkflowInstanceId, CurrentActionTaskInstance.WfActionInstanceId);
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ResumeRequest(CurrentWfIns, CurrentActionTaskInstance, LoginObject, Comment);
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region Resume İşlemi Yapılıyor

                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ResumeRequest(CurrentWfIns, CurrentActionTaskInstance, LoginObject, Comment);

                #endregion Resume İşlemi Yapılıyor

                #region Resume İşleminden sonra bilgilendirme yapılıyor.

                if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                {
                    return;
                }
                FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                ToList.Add(ToListItem);
                SenderObj.Add("DelegateTo", "");
                SenderObj.Add("Delegated", "");
                SenderObj.Add("ActionDescription", Comment);
                SenderObj.Add("ActionTo", "");
                SendMail(0, 8, "devam ettirildi", "Resumed", false, LoginId, WorkflowInstanceId, Comment, UserName);

                #endregion Resume İşleminden sonra bilgilendirme yapılıyor.

                #region History Kaydı Oluşturuluyor

                WfHistoryExec(UserActionHistoryActionType.RESUME, WorkflowInstanceId, LoginId, Comment);

                #endregion History Kaydı Oluşturuluyor
            }
            // FlowAdminOperationChecking(FlowAdminOprs);
        }


        // Akışı İptal Eden Fonksiyon
        public static void CancelWorkflow(DTOWorkflowProcesses dTOWorkflowProcesses, string UserName)
        {
            long LoginId = dTOWorkflowProcesses.LoginId;
            long WorkflowInstanceId = dTOWorkflowProcesses.WorkflowInstanceId;
            string Comment = dTOWorkflowProcesses.Comment;
            AuthenticationResult result = IdentityHelper.GetUserInformation(LoginId, UserName, dTOWorkflowProcesses.WorkflowInstanceId, 0);
            FWfActionTaskInstance CurrentActionTaskInstance = null;
            FWfWorkflowInstance CurrentWfIns = null;
            WFContext CurrentWFContext = null;
            FLogin AssignedUser = null;
            FLogin LoginObject = null;
            ContextObject SenderObj = null;
            List<FLogin> ToList = new List<FLogin>();
            var assignLoginIdlist = Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(WorkflowInstanceId);
            FlowAdminOprObject FlowAdminOprs;

            if (string.IsNullOrEmpty(Comment))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }

            using (UnitOfWork.Start())
            {
                CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                CurrentActionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
                CurrentWFContext = new WFContext(CurrentWfIns);
                AssignedUser = FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                LoginObject = DomainAuthentication.GetCurrentLogin(true, LoginId, UserName);
                ActionHelpers.VersionUpdateToEntity(WorkflowInstanceId, CurrentActionTaskInstance.WfActionInstanceId);
                FlowAdminOprs = new FlowAdminOprObject(WorkflowInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);


                #region İptal Öncesi Atanan kişilere yapılan görüntüleme yetkilerini iptal et

                for (int i = 0; i < AssignToIdListLong(WorkflowInstanceId).Count; i++)
                {
                    Digiturk.Workflow.Entities.FWfAssignment asgnmt = new Digiturk.Workflow.Entities.FWfAssignment();
                    asgnmt.WfAssignmentId = ConvertionHelper.ConvertValue<long>(Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.GetAssignmentId(AssignToIdListLong(WorkflowInstanceId)[i].ToString(), WorkflowInstanceId.ToString(), "WFVIEW", false));
                    if (asgnmt.WfAssignmentId != 0)
                    {
                        Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.DeleteAssignment(asgnmt.WfAssignmentId);
                    }
                }

                #endregion İptal Öncesi Atanan kişilere yapılan görüntüleme yetkilerini iptal et

                if (CurrentWfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd == "COMPLETED")
                {
                    var actionHelper = new ActionHelper();
                    actionHelper.CancelEndFlow();
                    // CancelEndFlow();
                }

                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.CancelWorkflow(CurrentWfIns, LoginObject, Comment);

                #region Form İptal edildikten sonra gerekli mailing yapılıyor

                bool isLogical = Digiturk.Workflow.Digiflow.WorkFlowHelpers.LogicalGroupHelper.IsDefExistLogicalGroup(logDefGroupIdAction, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, "0");

                if (!isLogical)
                {
                    if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                    {
                        return;
                    }
                }
                FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                ToList.Add(ToListItem);
                SenderObj.Add("DelegateTo", "");
                SenderObj.Add("Delegated", "");
                SenderObj.Add("ActionDescription", Comment);
                SenderObj.Add("ActionTo", "");
                SendMail(0, 8, "iptal edildi", "Cancelled", false, result.LoginDtoObject.LoginId, WorkflowInstanceId, Comment, UserName);

                #endregion Form İptal edildikten sonra gerekli mailing yapılıyor

                #region Akış Reddedildiğinde Haberdar Edilecek Kullanıcıların listesi

                List<long> AcceptUserList = Digiflow.DataAccessLayer.CheckingWorker.GetLastAcceptLoginList(WorkflowInstanceId);
                for (int i = 0; i < AcceptUserList.Count; i++)
                {
                    FLogin ToListItemRed = WFRepository<FLogin>.GetEntity(AcceptUserList[i]);
                    ToList.Add(ToListItemRed);
                    SenderObj.Add("DelegateTo", "");
                    SenderObj.Add("Delegated", "");
                    SenderObj.Add("ActionDescription", Comment);
                    SenderObj.Add("ActionTo", "");
                    SenderObj.Add("WfAssigner", Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(AcceptUserList[i]));
                    SendMail(0, 995, "iptal edildi", "Cancelled", false, result.LoginDtoObject.LoginId, WorkflowInstanceId, Comment, UserName);
                }

                #endregion Akış Reddedildiğinde Haberdar Edilecek Kullanıcıların listesi

                #region History Kaydı Oluşturuluyor

                WfHistoryExec(UserActionHistoryActionType.CANCEL, WorkflowInstanceId, LoginId, Comment);

                #endregion History Kaydı Oluşturuluyor
            }
            // FlowAdminOperationChecking(FlowAdminOprs);
        }

    }
}
