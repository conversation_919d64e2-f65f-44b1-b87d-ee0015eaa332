﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Digiturk.Workflow.Digiflow.Api.Common
{
    #region Olması Gereken Nesneler
    //{
    //    "FieldName": "FLOWNAME",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "FLOWDESC",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "ATANAN",
    //    "FieldType": "System.Decimal"
    //},
    //{
    //"FieldName": "STATENAME",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "STATEDESC",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "WFINSID",
    //    "FieldType": "System.Decimal"
    //},
    //{
    //"FieldName": "WFINSTANCEDEF",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "WFOWNER",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "BOLUM",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "WF_WORKFLOW_DEF_ID",
    //    "FieldType": "System.Decimal"
    //},
    //{
    //"FieldName": "LASTLOGINID",
    //    "FieldType": "System.Decimal"
    //},
    //{
    //"FieldName": "WFLASTMODIFIEDBY",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "WFLASTMODIFIEDBY_NOM",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "DETAIL",
    //    "FieldType": "System.String"
    //},

    //{
    //"FieldName": "AMOUNT",
    //    "FieldType": "System.Decimal"
    //},
    //{
    //"FieldName": "CURRENCY",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "AMOUNTTL",
    //    "FieldType": "System.Decimal"
    //},
    //{
    //"FieldName": "WFINSTANCELINK",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "MWFINSTANCELINK",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "WFDATE",
    //    "FieldType": "System.DateTime"
    //},
    //{
    //"FieldName": "TOPLUONAYDURUM",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "WF_WORKFLOW_ENTITY",
    //    "FieldType": "System.String"
    //},
    //{
    //"FieldName": "WF_WORKFLOW_ENTITY_VALUE",
    //    "FieldType": "System.String"
    //}
    #endregion

    public class DTOInbox
    {
        public string FlowName { get; set; }

        public string FlowDesc { get; set; }

        public decimal Atanan { get; set; }

        public string StateName { get; set; }

        public string StateDesc { get; set; }

        public decimal WfInsId { get; set; }

        public string WfInstanceDef { get; set; }

        public string WfOwner { get; set; }

        public string Bolum { get; set; }

        public decimal WfWorkflowDefId { get; set; }

        public decimal LastLoginId { get; set; }

        public string WfLastModifiedBy { get; set; }

        public string WfLastModifiedByNom { get; set; }

        public string Detail { get; set; }

        public decimal Amount { get; set; }

        public string Currency { get; set; }

        public decimal Amounttl { get; set; }

        public string WfInstanceLink { get; set; }

        public string MWfInstanceLink { get; set; }

        public string WfDate { get; set; } // Date Time

        public string TopluOnayDurum { get; set; }

        public string WfWorkflowEntity { get; set; }

        public string WfWorkflowEntityValue { get; set; }

    }

}
