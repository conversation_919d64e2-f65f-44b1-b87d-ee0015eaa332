.dxGridView_gvCollapsedButton_Office2010Blue,
.dxGridView_gvCollapsedButtonRtl_Office2010Blue,
.dxGridView_gvExpandedButton_Office2010Blue,
.dxGridView_gvExpandedButtonRtl_Office2010Blue,
.dxGridView_gvDetailCollapsedButton_Office2010Blue,
.dxGridView_gvDetailCollapsedButtonRtl_Office2010Blue,
.dxGridView_gvDetailExpandedButton_Office2010Blue,
.dxGridView_gvDetailExpandedButtonRtl_Office2010Blue,
.dxGridView_gvFilterRowButton_Office2010Blue,
.dxGridView_gvHeaderFilter_Office2010Blue,
.dxGridView_gvHeaderFilterActive_Office2010Blue,
.dxGridView_gvHeaderSortDown_Office2010Blue,
.dxGridView_gvHeaderSortUp_Office2010Blue,
.dxGridView_gvDragAndDropArrowDown_Office2010Blue,
.dxGridView_gvDragAndDropArrowUp_Office2010Blue,
.dxGridView_gvDragAndDropHideColumn_Office2010Blue,
.dxGridView_gvParentGroupRows_Office2010Blue,
.dxGridView_WindowResizer_Office2010Blue,
.dxGridView_WindowResizerRtl_Office2010Blue {
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.GridView.sprite.png")%>');
    -background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.GridView.sprite.gif")%>'); /* for IE6 */
    background-repeat: no-repeat;
    background-color: transparent;
}

.dxGridView_gvCollapsedButton_Office2010Blue {
    background-position: -36px 0px;
    width: 13px;
    height: 13px;
}
.dxGridView_gvCollapsedButtonRtl_Office2010Blue {
    background-position: -94px 0px;
    width: 13px;
    height: 13px;
}

.dxGridView_gvExpandedButton_Office2010Blue {
    background-position: -36px -17px;
    width: 13px;
    height: 13px;
}

.dxGridView_gvExpandedButtonRtl_Office2010Blue {
    background-position: -94px -17px;
    width: 13px;
    height: 13px;
}

.dxGridView_gvDetailCollapsedButton_Office2010Blue {
    background-position: -53px 0px;
    width: 13px;
    height: 13px;
}

.dxGridView_gvDetailCollapsedButtonRtl_Office2010Blue {
    background-position: -111px 0px;
    width: 13px;
    height: 13px;
}

.dxGridView_gvDetailExpandedButton_Office2010Blue {
    background-position: -53px -17px;
    width: 13px;
    height: 13px;
}

.dxGridView_gvDetailExpandedButtonRtl_Office2010Blue {
    background-position: -111px -17px;
    width: 13px;
    height: 13px;
}

.dxGridView_gvFilterRowButton_Office2010Blue {
    background-position: 0px -54px;
    width: 13px;
    height: 13px;
}

.dxGridView_gvHeaderFilter_Office2010Blue {
    background-position: 0px -36px;
    width: 15px;
    height: 15px;
}

.dxGridView_gvHeaderFilterActive_Office2010Blue {
    background-position: -23px -36px;
    width: 15px;
    height: 15px;
}

.dxGridView_gvHeaderSortDown_Office2010Blue {
    background-position: -19px 0px;
    width: 7px;
    height: 5px;
}

.dxGridView_gvHeaderSortUp_Office2010Blue {
    background-position: -19px -17px;
    width: 7px;
    height: 5px;
}

.dxGridView_gvDragAndDropArrowDown_Office2010Blue {
    background-position: 0px 0px;
    width: 11px;
    height: 9px;
}

.dxGridView_gvDragAndDropArrowUp_Office2010Blue {
    background-position: 0px -17px;
    width: 11px;
    height: 9px;
}

.dxGridView_gvDragAndDropHideColumn_Office2010Blue {
    background-position: -70px 0px;
    width: 22px;
    height: 22px;
}

.dxGridView_gvParentGroupRows_Office2010Blue {
    background-position: -70px -36px;
    width: 18px;
    height: 13px;
}

.dxGridView_WindowResizer_Office2010Blue {
    background-position: -46px -36px;
    width: 13px;
    height: 13px;
}

.dxGridView_WindowResizerRtl_Office2010Blue {
    background-position: -46px -54px;
    width: 13px;
    height: 13px;
}