import * as React from 'react'
import {WDatePicker, WGrid, WSelect, WTextField, WRadioGroup, WRadio, WCheckbox, WButton, WContainer} from '@wface/components'
import { useTranslation } from "react-i18next"
import {useState} from "react";
import i18next from "i18next";
import HeaderComponent from "../../../../components/HeaderComponent";
import FooterComponent from "../../../../components/FooterComponent";
import Api from "../../../../services/http-service";

const paymentType = [
    {
        code: 'pesin',
        name: '<PERSON><PERSON><PERSON><PERSON>',
    },
    {
        code: 'vade<PERSON>',
        name: '<PERSON><PERSON><PERSON>',
    },
]

const contractType = [
    {
        code: 'yurtici',
        name: 'Yurt<PERSON><PERSON><PERSON>',
    },
    {
        code: 'yurtdisi',
        name: 'Yurtdışı',
    },
]


function ContractConfirmation() {
    const { t } = useTranslation()
    const [startDate, setStartDate] = useState(null);
    const [finishDate, setFinishDate] = useState(null);

    return (
        <WGrid style={{margin: 20,}}>
            <HeaderComponent />
            <h4 style={{textAlign: "center"}}>USER NAME SURNAME</h4>
            <p style={{textAlign: "center"}}>CUSTOMER CARE AND SALES SOLUTIONS / ENTERPRISE SOLUTIONS DEVELOPMENT / EVP OF TECHNOLOGY</p>

            <WGrid container alignItems='center' direction='row' style={{marginTop: 20, marginBottom: 20,}}>
                <WGrid item md={6} xs={12}>
                    <WSelect
                        id=""
                        label="Taraflar"
                        style={{marginTop: 15}}
                        options={[
                            {label: 'Adana', value: '1'},
                            {label: 'Gaziantep', value: '27'},
                            {label: 'İstanbul', value: '34'},
                            {label: 'Şanlıurfa', value: '63'}
                        ]}
                        isMulti
                    />
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WSelect
                        id=""
                        label="Anlaşma Yapılan Firma"
                        style={{marginTop: 15}}
                        options={[
                            {label: 'Menkar', value: '1'},
                            {label: 'Kule', value: '27'},
                            {label: 'Nilaccra', value: '34'},
                            {label: 'Polaris', value: '63'}
                        ]}
                    />
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WTextField label="Karşı Taraf Vergi Numarası" style={{marginTop: 15}} fullWidth={true}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WTextField label="Karşı Taraf Vergi Dairesi" style={{marginTop: 15}} fullWidth={true}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WDatePicker id="" label={t('starting_date')} value={startDate} style={{marginTop: 15}} onChange={value=>setStartDate(value)}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WDatePicker id="" label={t('end_date')} value={finishDate} onChange={value=>setFinishDate(value)} style={{marginTop:15}}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WTextField label="Ürün - Servis" style={{marginTop:15}} fullWidth={true}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WTextField label="Konu" style={{marginTop:15}} fullWidth={true}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WTextField label="Diğer" style={{marginTop:15}} fullWidth={true}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WSelect
                        id=""
                        label="Sözleşme Kategorisi"
                        style={{marginTop: 13,}}
                        options={[
                            {label: 'Bakım Sözleşmesi', value: '1'},
                            {label: 'Bayilik Sözleşmeleri', value: '27'},
                            {label: 'Reklam Sözleşmeleri', value: '34'},
                            {label: 'Sponsorluk Sözleşmesi', value: '63'}
                        ]}
                    />
                </WGrid>

                <WGrid item md={12} xs={12}>
                    <h3 style={{textAlign: "center", marginTop: 50,}}>Ödeme Bilgileri</h3>
                </WGrid>
                <WGrid container alignItems='center' direction='row' >
                    <WGrid item md={4} xs={12}>
                        <WTextField label="Ödeme Tutarı" fullWidth={true} style={{marginTop: 10}}/>
                    </WGrid>
                    <WGrid item md={4} xs={12}>
                        <WSelect
                            id=""
                            label="Para Birimi"
                            style={{marginTop: 8}}
                            options={[
                                {label: 'Türk Lirası', value: 'tl'},
                                {label: 'Dolar', value: 'dolar'},
                                {label: 'Euro', value: 'euro'},
                                {label: 'GBP', value: 'gbp'},
                            ]}
                        />
                    </WGrid>
                    <WGrid item md={4} xs={12} style={{marginTop: 0,}}>
                        <WGrid style={{marginTop: 15,}}>
                            <WRadioGroup label="Ödeme Şekli: ">
                                {paymentType.map(({ code, name, }) => (
                                    <WRadio value={code} label={name} onClick={() => null}/>
                                ))}
                            </WRadioGroup>
                        </WGrid>
                    </WGrid>
                </WGrid>
                <WTextField label={t('description')} fullWidth={true} style={{marginTop: 15}}/>

                <WGrid item md={12} xs={12}>
                    <h3 style={{textAlign: "center", marginTop: 50,}}>Sözleşme Feshi</h3>
                </WGrid>
                <WGrid container>
                    <WGrid item md={3} xs={12}>
                        <WCheckbox id="" label="Sadece Digiturk" style={{color: "black"}} />
                    </WGrid>
                    <WGrid item md={3} xs={12}>
                        <WCheckbox id="" label="Çift Taraflı" style={{color: "black"}}/>
                    </WGrid>
                    <WGrid item md={3} xs={12} direction="row">
                        <WGrid item md={6} xs={12}>
                            <WCheckbox id="" label="İhlal Ortadan Kaldırma Süresi" style={{color: "black"}}/>
                            <WTextField label="Gün" fullWidth={true}/>
                        </WGrid>
                    </WGrid>
                    <WGrid item md={3} xs={12}>
                        <WCheckbox id="" label="Diğer" style={{color: "black"}}/>
                    </WGrid>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WTextField label="Gizlilik" style={{marginTop: 15}} fullWidth={true}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WTextField label="Cezai Şartlar" style={{marginTop: 15}} fullWidth={true}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WDatePicker id="" label="Sözleşme Kontrol Formu Giriş Tarihi" value={startDate} style={{marginTop: 15}} onChange={value=>setStartDate(value)}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WTextField label="Dosya Yükle" style={{marginTop: 15}} fullWidth={true}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WGrid style={{marginTop: 15,}}>
                        <WRadioGroup label="Sözleşme Tipi: ">
                            {contractType.map(({ code, name, }) => (
                                <WRadio value={code} label={name} onClick={() => null}/>
                            ))}
                        </WRadioGroup>
                    </WGrid>
                </WGrid>

                <WButton id="" style={{marginTop: 25, color: "black", background: "#d3d3d3"}} variant="outlined" fullWidth={true}>{t('create_request')}</WButton>

                <WGrid container alignItems='center' direction='column'
                       style={{marginTop: 50, marginBottom: 20, background: "#dc0836"}}>
                    <h3 style={{textAlign: "center", color: "white", fontSize: 16}}>{t('history')}</h3>
                    <WGrid container alignItems='center' direction='column' style={{background: "white", height: 400}}>
                    </WGrid>
                </WGrid>

                <FooterComponent />
            </WGrid>
        </WGrid>
    )
}

export default ContractConfirmation
