.dxEditors_edtError_Office2010Silver,
.dxEditors_edtCalendarPrevYear_Office2010Silver,
.dxEditors_edtCalendarPrevYearDisabled_Office2010Silver,
.dxEditors_edtCalendarPrevMonth_Office2010Silver,
.dxEditors_edtCalendarPrevMonthDisabled_Office2010Silver,
.dxEditors_edtCalendarNextMonth_Office2010Silver,
.dxEditors_edtCalendarNextMonthDisabled_Office2010Silver,
.dxEditors_edtCalendarNextYear_Office2010Silver,
.dxEditors_edtCalendarNextYearDisabled_Office2010Silver,
.dxEditors_edtCalendarFNPrevYear_Office2010Silver,
.dxEditors_edtCalendarFNNextYear_Office2010Silver,
.dxEditors_edtCheckBoxOn_Office2010Silver,
.dxEditors_edtCheckBoxOff_Office2010Silver,
.dxEditors_edtCheckBoxUndefined_Office2010Silver,
.dxEditors_edtRadioButtonOn_Office2010Silver,
.dxEditors_edtRadioButtonOff_Office2010Silver,
.dxEditors_edtRadioButtonUndefined_Office2010Silver,
.dxEditors_edtEllipsis_Office2010Silver,
.dxEditors_edtEllipsisDisabled_Office2010Silver,
.dxEditors_edtDropDown_Office2010Silver,
.dxEditors_edtDropDownDisabled_Office2010Silver,
.dxEditors_edtSpinEditIncrementImage_Office2010Silver,
.dxEditors_edtSpinEditIncrementImageDisabled_Office2010Silver,
.dxEditors_edtSpinEditDecrementImage_Office2010Silver,
.dxEditors_edtSpinEditDecrementImageDisabled_Office2010Silver,
.dxEditors_edtSpinEditLargeIncImage_Office2010Silver,
.dxEditors_edtSpinEditLargeIncImageDisabled_Office2010Silver,
.dxEditors_edtSpinEditLargeDecImage_Office2010Silver,
.dxEditors_edtSpinEditLargeDecImageDisabled_Office2010Silver,
.dxEditors_fcadd_Office2010Silver,
.dxEditors_fcaddhot_Office2010Silver,
.dxEditors_fcremove_Office2010Silver,
.dxEditors_fcremovehot_Office2010Silver,
.dxEditors_fcgroupaddcondition_Office2010Silver,
.dxEditors_fcgroupaddgroup_Office2010Silver,
.dxEditors_fcgroupremove_Office2010Silver,
.dxEditors_fcopany_Office2010Silver,
.dxEditors_fcopbegin_Office2010Silver,
.dxEditors_fcopbetween_Office2010Silver,
.dxEditors_fcopcontain_Office2010Silver,
.dxEditors_fcopnotcontain_Office2010Silver,
.dxEditors_fcopnotequal_Office2010Silver,
.dxEditors_fcopend_Office2010Silver,
.dxEditors_fcopequal_Office2010Silver,
.dxEditors_fcopgreater_Office2010Silver,
.dxEditors_fcopgreaterorequal_Office2010Silver,
.dxEditors_fcopnotblank_Office2010Silver,
.dxEditors_fcopblank_Office2010Silver,
.dxEditors_fcopless_Office2010Silver,
.dxEditors_fcoplessorequal_Office2010Silver,
.dxEditors_fcoplike_Office2010Silver,
.dxEditors_fcopnotany_Office2010Silver,
.dxEditors_fcopnotbetween_Office2010Silver,
.dxEditors_fcopnotlike_Office2010Silver,
.dxEditors_fcgroupand_Office2010Silver,
.dxEditors_fcgroupor_Office2010Silver,
.dxEditors_fcgroupnotand_Office2010Silver,
.dxEditors_fcgroupnotor_Office2010Silver,
.dxEditors_caRefresh_Office2010Silver
{
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.sprite.png")%>');
    -background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.Editors.sprite.gif")%>'); /* for IE6 */
    background-repeat: no-repeat;
    background-color: transparent;
}

.dxEditors_edtError_Office2010Silver {
    background-position: -126px -34px;
    width: 14px;
    height: 14px;
}

.dxEditors_edtCalendarPrevYear_Office2010Silver {
    background-position: -100px 0px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCalendarPrevYearDisabled_Office2010Silver {
    background-position: -100px -25px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCalendarPrevMonth_Office2010Silver {
    background-position: -75px 0px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCalendarPrevMonthDisabled_Office2010Silver {
    background-position: -75px -25px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCalendarNextMonth_Office2010Silver {
    background-position: -25px 0px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCalendarNextMonthDisabled_Office2010Silver {
    background-position: -25px -25px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCalendarNextYear_Office2010Silver {
    background-position: -50px 0px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCalendarNextYearDisabled_Office2010Silver {
    background-position: -50px -25px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCalendarFNPrevYear_Office2010Silver {
    background-position: 0px 0px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCalendarFNNextYear_Office2010Silver {
    background-position: 0px -25px;
    width: 23px;
    height: 23px;
}

.dxEditors_edtCheckBoxOn_Office2010Silver {
    background-position: -23px -51px;
    width: 15px;
    height: 15px;
}

.dxEditors_edtCheckBoxOff_Office2010Silver {
    background-position: 0px -51px;
    width: 15px;
    height: 15px;
}

.dxEditors_edtCheckBoxUndefined_Office2010Silver {
    background-position: -46px -51px;
    width: 15px;
    height: 15px;
}

.dxEditors_edtRadioButtonOn_Office2010Silver {
    background-position: -91px -51px;
    width: 14px;
    height: 14px;
}

.dxEditors_edtRadioButtonOff_Office2010Silver {
    background-position: -69px -51px;
    width: 14px;
    height: 14px;
}

.dxEditors_edtRadioButtonUndefined_Office2010Silver {
    background-position: -113px -51px;
    width: 14px;
    height: 14px;
}

.dxEditors_edtEllipsis_Office2010Silver {
    background-position: -139px 0px;
    width: 11px;
    height: 13px;
}

.dxEditors_edtEllipsisDisabled_Office2010Silver {
    background-position: -139px -16px;
    width: 11px;
    height: 13px;
}

.dxEditors_edtDropDown_Office2010Silver {
    background-position: -126px 0px;
    width: 10px;
    height: 13px;
}

.dxEditors_edtDropDownDisabled_Office2010Silver {
    background-position: -126px -16px;
    width: 10px;
    height: 13px;
}

.dxEditors_edtSpinEditIncrementImage_Office2010Silver {
    background-position: -162px 0px;
    width: 7px;
    height: 5px;
}

.dxEditors_edtSpinEditIncrementImageDisabled_Office2010Silver {
    background-position: -162px -8px;
    width: 7px;
    height: 5px;
}

.dxEditors_edtSpinEditDecrementImage_Office2010Silver {
    background-position: -153px 0px;
    width: 7px;
    height: 5px;
}

.dxEditors_edtSpinEditDecrementImageDisabled_Office2010Silver {
    background-position: -153px -8px;
    width: 7px;
    height: 5px;
}

.dxEditors_edtSpinEditLargeIncImage_Office2010Silver {
    background-position: -179px 0px;
    width: 5px;
    height: 7px;
}

.dxEditors_edtSpinEditLargeIncImageDisabled_Office2010Silver {
    background-position: -179px -8px;
    width: 5px;
    height: 7px;
}

.dxEditors_edtSpinEditLargeDecImage_Office2010Silver {
    background-position: -172px 0px;
    width: 5px;
    height: 7px;
}

.dxEditors_edtSpinEditLargeDecImageDisabled_Office2010Silver {
    background-position: -172px -8px;
    width: 5px;
    height: 7px;
}

.dxEditors_fcadd_Office2010Silver {
    background-position: 0px -74px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcaddhot_Office2010Silver {
    background-position: -21px -74px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcremove_Office2010Silver {
    background-position: -42px -74px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcremovehot_Office2010Silver {
    background-position: -63px -74px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcgroupaddcondition_Office2010Silver {
    background-position: -168px -53px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcgroupaddgroup_Office2010Silver {
    background-position: -147px -53px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcgroupremove_Office2010Silver {
    background-position: -126px -74px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopany_Office2010Silver {
    background-position: 0px -95px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopbegin_Office2010Silver {
    background-position: -42px -95px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopbetween_Office2010Silver {
    background-position: -84px -95px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopcontain_Office2010Silver {
    background-position: -105px -116px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopnotcontain_Office2010Silver {
    background-position: -147px -116px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopnotequal_Office2010Silver {
    background-position: -168px -95px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopend_Office2010Silver {
    background-position: -63px -95px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopequal_Office2010Silver {
    background-position: 0px -116px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopgreater_Office2010Silver {
    background-position: -21px -116px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopgreaterorequal_Office2010Silver {
    background-position: -42px -116px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopnotblank_Office2010Silver {
    background-position: -105px -95px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopblank_Office2010Silver {
    background-position: -126px -95px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopless_Office2010Silver {
    background-position: -63px -116px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcoplessorequal_Office2010Silver {
    background-position: -84px -116px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcoplike_Office2010Silver {
    background-position: -126px -116px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopnotany_Office2010Silver {
    background-position: -21px -95px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopnotbetween_Office2010Silver {
    background-position: -147px -95px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcopnotlike_Office2010Silver {
    background-position: -168px -116px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcgroupand_Office2010Silver {
    background-position: -84px -74px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcgroupor_Office2010Silver {
    background-position: -168px -74px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcgroupnotand_Office2010Silver {
    background-position: -105px -74px;
    width: 13px;
    height: 13px;
}

.dxEditors_fcgroupnotor_Office2010Silver {
    background-position: -147px -74px;
    width: 13px;
    height: 13px;
}

.dxEditors_caRefresh_Office2010Silver {
    background-position: 0px -132px;
    width: 25px;
    height: 25px;
}