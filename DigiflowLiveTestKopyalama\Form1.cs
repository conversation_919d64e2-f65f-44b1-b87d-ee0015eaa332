﻿using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;

using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Workflow.Activities.Rules;
using System.Workflow.ComponentModel.Serialization;
using System.Xml;

namespace WindowsFormsApplication6
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        public static string BuildAllFieldsSQL(DataTable table)
        {
            string sql = "";
            foreach (DataColumn column in table.Columns)
            {
                if (sql.Length > 0)
                    sql += ", ";
                sql += column.ColumnName;
            }
            return sql;
        }

        public static string BuildInsertSQL(DataTable table)
        {
            StringBuilder sql = new StringBuilder("INSERT INTO " + table.TableName + " (");
            StringBuilder values = new StringBuilder("VALUES (");
            bool bFirst = true;
            bool bIdentity = false;
            string identityType = null;

            foreach (DataColumn column in table.Columns)
            {
                if (column.AutoIncrement)
                {
                    bIdentity = true;

                    switch (column.DataType.Name)
                    {
                        case "Int16":
                            identityType = "smallint";
                            break;

                        case "SByte":
                            identityType = "tinyint";
                            break;

                        case "Int64":
                            identityType = "bigint";
                            break;

                        case "Decimal":
                            identityType = "decimal";
                            break;

                        default:
                            identityType = "int";
                            break;
                    }
                }
                else
                {
                    if (bFirst)
                        bFirst = false;
                    else
                    {
                        sql.Append(", ");
                        values.Append(", ");
                    }

                    sql.Append(column.ColumnName);

                    if (column.DataType.Name == "DateTime")
                    {
                        //values.Append(" TO_DATE(:" + column.ColumnName + ",'DD.MM.YYYY HH24:MI:SS')");
                        values.Append(" SYSDATE");
                    }
                    else
                    {
                        values.Append(":" + column.ColumnName);
                    }
                }
            }
            sql.Append(") ");
            sql.Append(values.ToString());
            sql.Append(")");

            if (bIdentity)
            {
                sql.Append("; SELECT CAST(scope_identity() AS ");
                sql.Append(identityType);
                sql.Append(")");
            }

            return sql.ToString(); ;
        }

        public static OracleCommand CreateInsertCommand(DataRow row, List<string> EskiAlan, List<string> YeniDeger)
        {
            DataTable table = row.Table;
            string sql = BuildInsertSQL(table);
            OracleCommand command = new OracleCommand(sql);
            command.CommandType = System.Data.CommandType.Text;

            foreach (DataColumn column in table.Columns)
            {
                if (!column.AutoIncrement)
                {
                    string parameterName = ":" + column.ColumnName;
                    string deger = row[column.ColumnName].ToString();
                    if (EskiAlan.IndexOf(column.ColumnName) != -1)
                    {
                        deger = YeniDeger[EskiAlan.IndexOf(column.ColumnName)];
                    }
                    if (column.DataType.Name.ToString() == "DateTime")
                    {
                        // DateTime dateValue = DateTime.ParseExact(deger, "d.M.yyyy H:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                        //  deger = dateValue.ToString("dd.MM.yyyy HH:mm:ss");
                    }
                    InsertParameter(command, parameterName,
                                      column.ColumnName,
                                     deger, EskiAlan, YeniDeger, column.DataType.Name.ToString());
                }
            }
            return command;
        }

        public static object InsertDataRow(DataRow row, string connectionString, List<string> EskiAlan, List<string> YeniDeger)
        {
            OracleCommand command = CreateInsertCommand(row, EskiAlan, YeniDeger);

            using (OracleConnection connection = new OracleConnection(connectionString))
            {
                command.Connection = connection;
                command.CommandType = System.Data.CommandType.Text;
                connection.Open();
                return command.ExecuteScalar();
            }
        }

        public static void InsertParameter(OracleCommand command,
                                               string parameterName,
                                               string sourceColumn,
                                               object value, List<string> EskiAlan, List<string> YeniDeger, string Tip)
        {
            OracleParameter parameter = null;
            if (Tip == "Decimal" || Tip == "String" || Tip == "Int16")
            {
                parameter = new OracleParameter(parameterName, value);
            }
            else if (Tip == "DateTime")
            {
                parameter = new OracleParameter(parameterName, OracleDbType.Date);
                parameter.Value = value;
            }
            else
            {
                string kerem = "";
            }
            parameter.Direction = ParameterDirection.Input;
            parameter.ParameterName = parameterName;
            parameter.SourceColumn = sourceColumn;
            parameter.SourceVersion = DataRowVersion.Current;

            command.Parameters.Add(parameter);
        }

        private static RuleExpressionCondition DeserializeCondition(string conditionStr)
        {
            if (string.IsNullOrEmpty(conditionStr)) throw new ArgumentNullException("conditionStr");
            using (StringReader strReader = new StringReader(conditionStr))
            {
                XmlTextReader xmlReader = new XmlTextReader(strReader);
                WorkflowMarkupSerializer serializer = new WorkflowMarkupSerializer();
                return serializer.Deserialize(xmlReader) as RuleExpressionCondition;
            }
        }

        private void btnStateGetir_Click(object sender, EventArgs e)
        {
            string Hedef = "";
            if (radioButton1.Checked)
            {
                Hedef = "TestConnection";
            }
            else
            {
                Hedef = "LiveConnection";
            }
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle(Hedef, "Select * from Framework.f_wf_state_def where WF_WORKFLOW_DEF_ID=" + cmbAkisListesi.SelectedValue.ToString() + " order by name");
            lstState.DataSource = null;
            lstState.DisplayMember = "Name";
            lstState.ValueMember = "WF_STATE_DEF_ID";
            lstState.DataSource = dt;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            DialogResult dr = MessageBox.Show("İşlem başlayacak emin misiniz ? ", "Soru", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Information);
            if (dr == DialogResult.Yes)
            {
                cmbAkisListesi.Enabled = false;
                textBox1.Enabled = false;
                Tasi(cmbAkisListesi.SelectedValue.ToString());
                cmbAkisListesi.Enabled = true;
                textBox1.Enabled = true;
                MessageBox.Show("Tamamlandı");
            }
            else if (dr == DialogResult.Cancel || dr == DialogResult.No)
            {
                MessageBox.Show("İşlem İptal edildi");
            }
        }

        private void button1_Click_2(object sender, EventArgs e)
        {
            textBox1.Text = cmbAkisListesi.Text;
        }

        private void button2_Click(object sender, EventArgs e)
        {
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDialog1 = new SaveFileDialog();

            saveFileDialog1.Filter = "txt files (*.txt)|*.txt";
            saveFileDialog1.FilterIndex = 2;
            saveFileDialog1.RestoreDirectory = true;
            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                btnStateGetir.PerformClick();
                string Hedef = "";
                string icerik = "", baslik = "", dosya = "";
                if (radioButton1.Checked)
                {
                    Hedef = "TestConnection";
                }
                else
                {
                    Hedef = "LiveConnection";
                }

                dosya = saveFileDialog1.FileName;
                if (File.Exists(dosya))
                {
                    File.Delete(dosya);
                }
                using (System.IO.StreamWriter file = new System.IO.StreamWriter(dosya))
                {
                    file.WriteLine(icerik);
                    for (int i = 0; i < lstState.Items.Count; i++)
                    {
                        lstState.SelectedIndex = i;

                        baslik = lstState.Text; //+ "-" + lstState.SelectedValue.ToString();
                        file.WriteLine(baslik);
                        foreach (DataGridViewRow row in dataGridView1.Rows)
                        {
                            icerik = row.Cells["TOS"].Value + "--" + row.Cells["KURAL"].Value;
                            file.WriteLine(icerik);
                        }
                    }
                }
                MessageBox.Show("Kayıt Edildi!");
            }
            else
            {
                MessageBox.Show("Kayıt işlemi iptal edildi !");
            }
        }

        private void cmbAkisListesi_SelectedIndexChanged(object sender, EventArgs e)
        {
            textBox1.Text = cmbAkisListesi.Text + DateTime.Now.ToShortTimeString().Replace(":", ""); ;
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", "Select * from Framework.f_wf_workflow_def order by name");
            cmbAkisListesi.DisplayMember = "Name";

            cmbAkisListesi.ValueMember = "WF_WORKFLOW_DEF_ID";
            cmbAkisListesi.DataSource = dt;
            label2.Text = "";
        }

        private void Liste_Olustur(ref IDictionary<string, string> icerik, OracleDataReader refRd, string ilkDeger, string SonDeger)
        {
            int sayac = -1;
            if (refRd.HasRows)
            {
                while (refRd.Read())
                {
                    sayac++;
                    icerik.Add(refRd[ilkDeger].ToString(), "");
                }
            }
        }

        private void Liste_Update(ref IDictionary<string, string> icerik, string ilkDeger, string SonDeger)
        {
            icerik[ilkDeger] = SonDeger;
        }

        private void lstState_SelectedIndexChanged(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();

            if (cmbAkisListesi.SelectedValue == "" || lstState.SelectedValue == null)
            {
                return;
            }
            string Hedef = "";
            if (radioButton1.Checked)
            {
                Hedef = "TestConnection";
            }
            else
            {
                Hedef = "LiveConnection";
            }
            DataTable dt = DataAccessLayer.DAL.GetDataTable_Oracle(Hedef, QueryClass.WF_TRANS_DEF_QUERY_STATE.Replace(":Deger1", cmbAkisListesi.SelectedValue.ToString()).Replace(":Deger2", lstState.SelectedValue.ToString()));
            dataGridView1.Rows.Clear();
            if (dataGridView1.Columns.Count != 2)
            {
                dataGridView1.Columns.Add("TOS", "TOS 2");
                dataGridView1.Columns.Add("KURAL", "KURAL 2");
                dataGridView1.Columns[1].Width = 900;
            }

            int sayac = -1;
            foreach (DataRow item in dt.Rows)
            {
                DataTable dt2 = DataAccessLayer.DAL.GetDataTable_Oracle(Hedef, QueryClass.WF_TRANS_DEF_RULE_QUERY_.Replace(":Deger1", item["WF_TRANSITION_DEF_ID"].ToString()));
                if (dt2 != null)
                {
                    sayac++;
                    RuleExpressionCondition condition;
                    if (dt2.Rows[0]["CONDITION"].ToString() != "")
                    {
                        condition = DeserializeCondition(dt2.Rows[0]["CONDITION"].ToString());
                        dataGridView1.Rows.Add();

                        dataGridView1.Rows[sayac].Cells[0].Value = dt2.Rows[0]["TOS"].ToString();
                        dataGridView1.Rows[sayac].Cells[1].Value = condition.ToString();
                    }

                    condition = null;
                }
            }
        }

        private void lstTransition_SelectedIndexChanged(object sender, EventArgs e)
        {
            string Hedef = "";
            if (radioButton1.Checked)
            {
                Hedef = "TestConnection";
            }
            else
            {
                Hedef = "LiveConnection";
            }
        }

        private void Tablo_insert(string query, DataRow dr, List<string> EskiAlan, List<string> YeniDeger, string hedef)
        {
            OracleCommand cmd = CreateInsertCommand(dr, EskiAlan, YeniDeger);
            using (OracleConnection cn2 = new OracleConnection(ConfigurationManager.ConnectionStrings[hedef].ToString()))
            {
                if (cn2.State != ConnectionState.Open)
                {
                    cn2.Open();
                }
                cmd.Connection = cn2;
                DataAccessLayer.DAL.ExecuteNonQuery_Oracle(cmd);
            }
        }

        private void Tasi(string AkisId)
        {
            string Hedef = "";
            if (radioButton1.Checked)
            {
                Hedef = "TestConnection";
            }
            else
            {
                Hedef = "LiveConnection";
            }

            #region tanimlamalar

            IDictionary<string, string> WF_DEF = new Dictionary<string, string>();
            IDictionary<string, string> WF_STATE_DEF = new Dictionary<string, string>();
            IDictionary<string, string> WF_ACTION_DEF = new Dictionary<string, string>();
            IDictionary<string, string> YYS_LOGICAL_GROUP = new Dictionary<string, string>();

            List<string> kolonlistesi = new List<string>();
            List<string> degerlistesi = new List<string>();

            string N_WF_STATE_ID, N_WF_DEF_ID, N_WF_ACT_ID, N_TRANS_ID, N_LOGICAL_ID, N_ASSIGNMENT_ID;
            DataRow dr;
            DataTable dt;
            string BaslangicState, komut;

            #endregion tanimlamalar

            #region FRAMEWORK_TARAFI

            label2.Text = "Workflow kopyalanıyor";

            #region WF_DEF

            OracleDataReader rd = DataAccessLayer.DAL.GetDataReader_Oracle("TestConnection", QueryClass.WF_DEF_QUERY.Replace(":Deger", AkisId));
            Liste_Olustur(ref WF_DEF, rd, "WF_WORKFLOW_DEF_ID", "");
            N_WF_DEF_ID = DataAccessLayer.DAL.GetQueryResult_Oracle(Hedef, QueryClass.WF_DEF_MAX_QUERY);
            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.WF_DEF_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "FRAMEWORK.f_wf_workflow_def";
            dr = dt.Rows[0];
            BaslangicState = dr["INITIAL_STATE_ID"].ToString();
            kolonlistesi.Add("WF_WORKFLOW_DEF_ID");
            degerlistesi.Add(N_WF_DEF_ID);
            kolonlistesi.Add("NAME");
            degerlistesi.Add(textBox1.Text);
            kolonlistesi.Add("INITIAL_STATE_ID");
            degerlistesi.Add("");

            Tablo_insert(QueryClass.WF_DEF_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
            kolonlistesi.Clear();
            degerlistesi.Clear();

            Liste_Update(ref WF_DEF, AkisId, N_WF_DEF_ID);

            #endregion WF_DEF

            label2.Text = "Stateler kopyalanıyor";

            #region WF_STATE_DEF

            OracleDataReader rd2 = DataAccessLayer.DAL.GetDataReader_Oracle("TestConnection", QueryClass.WF_STATE_QUERY.Replace(":Deger", AkisId));
            Liste_Olustur(ref WF_STATE_DEF, rd2, "WF_STATE_DEF_ID", "");
            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.WF_STATE_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "FRAMEWORK.F_WF_STATE_DEF";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                N_WF_STATE_ID = DataAccessLayer.DAL.GetQueryResult_Oracle(Hedef, QueryClass.WF_STATE_QUERY_MAX);
                dr = dt.Rows[i];
                kolonlistesi.Clear();
                degerlistesi.Clear();
                kolonlistesi.Add("WF_WORKFLOW_DEF_ID");
                degerlistesi.Add(N_WF_DEF_ID);
                kolonlistesi.Add("WF_STATE_DEF_ID");
                degerlistesi.Add(N_WF_STATE_ID);
                Tablo_insert(QueryClass.WF_STATE_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
                Liste_Update(ref WF_STATE_DEF, dr["WF_STATE_DEF_ID"].ToString(), N_WF_STATE_ID);
            }

            #endregion WF_STATE_DEF

            label2.Text = "Actionlar kopyalanıyor";

            #region WF_ACTION_DEF

            rd2 = DataAccessLayer.DAL.GetDataReader_Oracle("TestConnection", QueryClass.WF_ACT_QUERY.Replace(":Deger", AkisId));
            Liste_Olustur(ref WF_ACTION_DEF, rd2, "WF_ACTION_DEF_ID", "");
            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.WF_ACT_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "FRAMEWORK.F_WF_ACTION_DEF";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                N_WF_ACT_ID = DataAccessLayer.DAL.GetQueryResult_Oracle(Hedef, QueryClass.WF_ACT_QUERY_MAX);
                dr = dt.Rows[i];
                kolonlistesi.Clear();
                degerlistesi.Clear();
                kolonlistesi.Add("WF_ACTION_DEF_ID");
                degerlistesi.Add(N_WF_ACT_ID);

                kolonlistesi.Add("WF_STATE_DEF_ID");
                degerlistesi.Add(WF_STATE_DEF[dr["WF_STATE_DEF_ID"].ToString()]);
                Tablo_insert(QueryClass.WF_ACT_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
                Liste_Update(ref WF_ACTION_DEF, dr["WF_ACTION_DEF_ID"].ToString(), N_WF_ACT_ID);
            }

            #endregion WF_ACTION_DEF

            label2.Text = "Action Task Defler kopyalanıyor";

            #region WF_ACTION_TASK_DEF

            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.WF_ACT_TASK_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "FRAMEWORK.F_WF_ACTION_TASK_DEF";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                dr = dt.Rows[i];
                kolonlistesi.Clear();
                degerlistesi.Clear();
                kolonlistesi.Add("WF_ACTION_TASK_DEF_ID");
                degerlistesi.Add(WF_ACTION_DEF[dr["WF_ACTION_TASK_DEF_ID"].ToString()]);
                Tablo_insert(QueryClass.WF_ACT_TASK_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
            }

            #endregion WF_ACTION_TASK_DEF

            label2.Text = "Action Class Defler kopyalanıyor";

            #region WF_ACTION_CLASS_DEF

            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.WF_ACT_CLASS_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "FRAMEWORK.F_WF_ACTION_CLASS_DEF";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                dr = dt.Rows[i];
                kolonlistesi.Clear();
                degerlistesi.Clear();
                kolonlistesi.Add("WF_ACTION_CLASS_DEF_ID");
                degerlistesi.Add(WF_ACTION_DEF[dr["WF_ACTION_CLASS_DEF_ID"].ToString()]);
                Tablo_insert(QueryClass.WF_ACT_CLASS_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
            }

            #endregion WF_ACTION_CLASS_DEF

            label2.Text = "Transitionlar kopyalanıyor";

            #region WF_TRANSITION_DEF

            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.WF_TRANS_DEF_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "FRAMEWORK.F_WF_TRANSITION_DEF";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                N_TRANS_ID = DataAccessLayer.DAL.GetQueryResult_Oracle(Hedef, QueryClass.WF_TRANS_DEF_QUERY_MAX);
                dr = dt.Rows[i];
                kolonlistesi.Clear();
                degerlistesi.Clear();
                kolonlistesi.Add("WF_TRANSITION_DEF_ID");
                degerlistesi.Add(N_TRANS_ID);
                kolonlistesi.Add("TO_STATE_DEF_ID");
                degerlistesi.Add(WF_STATE_DEF[dr["TO_STATE_DEF_ID"].ToString()]);
                kolonlistesi.Add("FROM_STATE_DEF_ID");
                degerlistesi.Add(WF_STATE_DEF[dr["FROM_STATE_DEF_ID"].ToString()]);
                Tablo_insert(QueryClass.WF_TRANS_DEF_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
            }

            #endregion WF_TRANSITION_DEF

            label2.Text = "Başlangıç State düzeltiliyor";

            #region start_state_update

            komut = "update framework.f_wf_workflow_def set INITIAL_STATE_ID=" + WF_STATE_DEF[BaslangicState].ToString() + " where  WF_WORKFLOW_DEF_ID=" + N_WF_DEF_ID;
            DataAccessLayer.DAL.ExecuteNonQuery_Oracle(Hedef, komut);

            #endregion start_state_update

            #endregion FRAMEWORK_TARAFI

            #region DT_WORKFLOW TARAFI

            label2.Text = "WF Adminler kopyalanıyor";

            #region WF_adminleri

            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.YYS_ADMIN_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                dr = dt.Rows[i];
                kolonlistesi.Clear();
                degerlistesi.Clear();
                kolonlistesi.Add("WF_DEF_ID");
                degerlistesi.Add(N_WF_DEF_ID);
                Tablo_insert(QueryClass.YYS_ADMIN_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
                N_ASSIGNMENT_ID = DataAccessLayer.DAL.GetQueryResult_Oracle(Hedef, QueryClass.WF_ASSINGMENT_QUERY_MAX);
                komut = "Insert into FRAMEWORK.F_WF_ASSIGNMENT (WF_ASSIGNMENT_ID, WF_ASSIGNMENT_TYPE_CD, WF_ASSIGNED_TYPE_CD, ASSIGNMENT_OWNER_REF_ID, ASSIGNED_OWNER_REF_ID, IS_DEF_ASSIGNMENT, DENY) Values (" + N_ASSIGNMENT_ID + ", 'WFMODIFY', 'LOGIN', " + N_WF_DEF_ID + ", " + dr["ADMIN_ID"].ToString() + ", 1, 0)";
                DataAccessLayer.DAL.ExecuteNonQuery_Oracle(Hedef, komut);
            }

            #endregion WF_adminleri

            label2.Text = "Mantıksal Gruplar Kopyalanıyor";

            #region logical_groups

            rd2 = DataAccessLayer.DAL.GetDataReader_Oracle("TestConnection", QueryClass.YYS_LOGICAL_GROUP_QUERY.Replace(":Deger", AkisId));
            Liste_Olustur(ref YYS_LOGICAL_GROUP, rd2, "LOGICAL_GROUP_ID", "");

            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.YYS_LOGICAL_GROUP_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "DT_WORKFLOW.YYS_LOGICAL_GROUPS";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                dr = dt.Rows[i];
                kolonlistesi.Clear();
                degerlistesi.Clear();
                kolonlistesi.Add("WF_DEF_ID");
                degerlistesi.Add(N_WF_DEF_ID);
                Tablo_insert(QueryClass.YYS_LOGICAL_GROUP_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
                N_LOGICAL_ID = DataAccessLayer.DAL.GetQueryResult_Oracle(Hedef, QueryClass.YYS_LOGICAL_GROUP_QUERY_MAX);
                Liste_Update(ref YYS_LOGICAL_GROUP, dr["LOGICAL_GROUP_ID"].ToString(), N_LOGICAL_ID);
            }

            #endregion logical_groups

            label2.Text = "Mantıksal Grup Üyeleri Kopyalanıyor";

            #region logical_group_members

            foreach (var item in YYS_LOGICAL_GROUP)
            {
                dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.YYS_LOGICAL_MEMBER_QUERY.Replace(":Deger", item.Key));
                dt.TableName = "DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS";
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dr = dt.Rows[i];
                    kolonlistesi.Clear();
                    degerlistesi.Clear();
                    kolonlistesi.Add("LOGICAL_GROUP_ID");
                    degerlistesi.Add(YYS_LOGICAL_GROUP[item.Key]);
                    Tablo_insert(QueryClass.YYS_LOGICAL_GROUP_QUERY.Replace(":Deger", item.Key), dr, kolonlistesi, degerlistesi, Hedef);
                }
            }

            #endregion logical_group_members

            label2.Text = "Statik Atamalar kopyalanıyor";

            #region Statik_Atamalar

            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.YYS_STATIK_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "DT_WORKFLOW.YYS_STATE_AUTHORIZATION";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                dr = dt.Rows[i];
                kolonlistesi.Clear();
                degerlistesi.Clear();
                kolonlistesi.Add("STATE_DEF_ID");
                degerlistesi.Add(WF_STATE_DEF[dr["STATE_DEF_ID"].ToString()]);
                kolonlistesi.Add("WF_DEF_ID");
                degerlistesi.Add(N_WF_DEF_ID);
                kolonlistesi.Add("ASSIGMENT_OWNER_ID");
                degerlistesi.Add(YYS_LOGICAL_GROUP[dr["ASSIGMENT_OWNER_ID"].ToString()]);

                Tablo_insert(QueryClass.YYS_STATIK_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
            }

            #endregion Statik_Atamalar

            label2.Text = "Adım bazlı Kurallar Kopyalanıyor";

            #region Adım_Bazlı_Kurallar

            dt = DataAccessLayer.DAL.GetDataTable_Oracle("TestConnection", QueryClass.YYS_ACTION_QUERY.Replace(":Deger", AkisId));
            dt.TableName = "DT_WORKFLOW.YYS_ACTION_AUTHORIZATION";
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                dr = dt.Rows[i];
                kolonlistesi.Clear();
                degerlistesi.Clear();
                kolonlistesi.Add("STATE_DEF_ID");
                degerlistesi.Add(WF_STATE_DEF[dr["STATE_DEF_ID"].ToString()]);
                kolonlistesi.Add("WF_DEF_ID");
                degerlistesi.Add(N_WF_DEF_ID);
                if (dr["TO_GROUP_ID"].ToString() != "0")
                {
                    kolonlistesi.Add("TO_GROUP_ID");
                    degerlistesi.Add(YYS_LOGICAL_GROUP[dr["TO_GROUP_ID"].ToString()]);
                }
                kolonlistesi.Add("SOURCE_ID");
                degerlistesi.Add(YYS_LOGICAL_GROUP[dr["SOURCE_ID"].ToString()]);
                Tablo_insert(QueryClass.YYS_ACTION_QUERY.Replace(":Deger", AkisId), dr, kolonlistesi, degerlistesi, Hedef);
            }

            #endregion Adım_Bazlı_Kurallar

            #endregion DT_WORKFLOW TARAFI

            label2.Text = N_WF_DEF_ID + " olarak kopyalandı !";
            MessageBox.Show("İşlem Tamamlandı");
        }
    }
}