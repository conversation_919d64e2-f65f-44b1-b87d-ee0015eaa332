﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true"
    CodeFile="LogicalGroups.aspx.cs" Inherits="LogicalGroups" %>

<%@ Register Assembly="DevExpress.Web.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxDataView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" width="70%" cellspacing="0" cellpadding="0" align="center">
        <tr>
            <td style="padding: 5px; font-weight: 700;" align="left" width="100%" valign="top">Akış Listesi
            </td>
        </tr>
        <tr>
            <td style="padding: 5px" align="left" width="100%" valign="top">
                <dx:ASPxComboBox ID="WorkFlowCombobox" runat="server" AutoPostBack="True" OnSelectedIndexChanged="WorkFlowCombobox_SelectedIndexChanged"
                    Width="250px" ValueType="System.Int64">
                </dx:ASPxComboBox>
            </td>
        </tr>
        <tr>
            <td style="padding: 5px; font-weight: 700;" align="left" width="100%" valign="top">Mantıksal Gruplar
            </td>
        </tr>
        <tr>
            <td style="padding: 5px" align="left" width="100%" valign="top">
                <dx:ASPxGridView ID="LogicalGroupsGridView" runat="server" AutoGenerateColumns="False"
                    Width="100%"
                    OnPageIndexChanged="LogicalGroupsGridViewPageIndexChanging"
                    OnBeforeColumnSortingGrouping="LogicalGroupsGridViewPageIndexChanging"
                    OnCustomColumnGroup="LogicalGroupsGridViewPageIndexChanging"
                    OnCustomGroupDisplayText="LogicalGroupsGridViewPageIndexChanging"
                    OnFilterControlCustomValueDisplayText="LogicalGroupsGridViewPageIndexChanging"
                    OnFilterControlParseValue="LogicalGroupsGridViewPageIndexChanging"
                    OnFilterControlOperationVisibility="LogicalGroupsGridViewPageIndexChanging"
                    OnHtmlDataCellPrepared="LogicalGroupsGridView_HtmlDataCellPrepared"
                    KeyFieldName="RequestId"
                    OnHtmlRowCreated="LogicalGroupsGridView_HtmlRowCreated"
                    OnRowCommand="LogicalGroupsGridView_RowCommand">
                    <Columns>
                        <dx:GridViewDataTextColumn Caption="İsim" FieldName="Name" ShowInCustomizationForm="True"
                            VisibleIndex="20">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Açıklama" FieldName="Description" ShowInCustomizationForm="True"
                            VisibleIndex="30">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Silinebilir Bir Grup Mu?" FieldName="CanBeDeleted"
                            VisibleIndex="40">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Tek Kişilik Grup Mu?" FieldName="IsOnePersonGroup"
                            VisibleIndex="50">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="WfDefId" FieldName="WfDefId" Visible="False"
                            VisibleIndex="60">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataHyperLinkColumn Caption="Mantıksal Grup Düzenle" FieldName="RequestId"
                            VisibleIndex="70">
                            <PropertiesHyperLinkEdit NavigateUrlFormatString="AddUpdateLogicalGroup.aspx?LogicalGroupId={0}"
                                Text="Mantıksal Grup Düzenle">
                            </PropertiesHyperLinkEdit>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataHyperLinkColumn>
                        <dx:GridViewDataHyperLinkColumn Caption="Mantıksal Grup Üye Düzenle" FieldName="RequestId"
                            VisibleIndex="80">
                            <PropertiesHyperLinkEdit NavigateUrlFormatString="AddUpdateLogicalGroupMember.aspx?LogicalGroupId={0}"
                                Text="Mantıksal Grup Üye Düzenle">
                            </PropertiesHyperLinkEdit>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataHyperLinkColumn>
                        <%--   <dx:GridViewCommandColumn Caption="EskiSil" ShowInCustomizationForm="True" VisibleIndex="7"
                            Name="Sil" Visible="False">
                            <DeleteButton Text="Sil" Visible="True">
                            </DeleteButton>
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewCommandColumn>--%>
                        <%-- <dx:GridViewDataButtonEditColumn Caption="Sil" VisibleIndex="90">
                            <DataItemTemplate>
                                <dx:ASPxButton ID="DeleteButton" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                    <ClientSideEvents Click="function(s, e) {
	 e.processOnServer = confirm('Mantıksal grubu silmek istediğinize emin misiniz?');}" />
                                </dx:ASPxButton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn>--%>
                        <dx:GridViewDataButtonEditColumn Caption="Sil" VisibleIndex="90">
                            <DataItemTemplate>
                                <dx:ASPxButton ID="DeleteButton" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                    <ClientSideEvents Click="function(s, e) {
	 e.processOnServer = confirm('Mantıksal grubu silmek istediğinize emin misiniz?');}" />
                                </dx:ASPxButton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn>
                        <dx:GridViewDataTextColumn Caption="Sıra No" VisibleIndex="0" Width="40px">
                            <DataItemTemplate>
                                <%# Container.ItemIndex + 1 %>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" />
                    <Settings ShowGroupPanel="True" ShowFilterRowMenu="True" ShowFilterBar="Auto" ShowFilterRow="true" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz sütunları buraya sürükleyin."
                        FilterBarClear="Temizle" ConfirmDelete="Mantıksal grubu silmek istediğinize emin misiniz?"
                        EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" />
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
            </td>
        </tr>
        <tr>
            <td style="padding: 5px" align="left" width="100%" valign="top">
                <dx:ASPxLabel ID="ASPxLabelHata" runat="server" ForeColor="#CC0000">
                </dx:ASPxLabel>
            </td>
        </tr>
    </table>
</asp:Content>