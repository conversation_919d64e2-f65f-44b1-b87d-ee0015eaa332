﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.DataAccessLayer;
using Digiturk.Workflow.Digiflow.GenericMailHelper;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System.Collections.Generic;
using System.Data;

namespace Digiturk.Workflow.Digiflow.AssignmentBase
{
    public class AssignmentBase
    {
        private List<long> _AssigmentList;

        public List<long> AssigmentList
        {
            get
            {
                if (_AssigmentList == null)
                {
                    _AssigmentList = new List<long>();
                }
                return _AssigmentList;
            }
            set
            {
                if (_AssigmentList == null)
                {
                    _AssigmentList = new List<long>();
                }
                _AssigmentList = value;
            }
        }

        public virtual bool Execute(WFContext wfContext, FWfActionInstance actionInstance)
        {
            #region Akışın İçerisindeki Aksiyonu belirlemek için gerekli parametreler set edilir

            ActionType CurrentActionType = ActionType.ONWFSTARTASSIGNER;
            ActionType CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
            string assigmentType = "TASKINBOX";
            string loginType = "LOGIN";
            long WorkflowInstanceId = actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
            long AssignToLoginId = CheckingWorker.GetAssignToLoginId(WorkflowInstanceId);
            long LastActionToLoginId = CheckingWorker.GetLastActionToLoginId(WorkflowInstanceId);
            if (AssignToLoginId < 2)
            {
                CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
            }
            else
            {
                CurrentActionType = ActionType.ONWFAPPROVEASSIGNER;
                CurrentActionOwnerType = ActionType.ONWFAPPROVEOWNER;
            }

            #endregion Akışın İçerisindeki Aksiyonu belirlemek için gerekli parametreler set edilir

            #region Önce Atamalar Yapılır

            foreach (long item in AssigmentList)
            {
                Assign(item, loginType, actionInstance.WfActionInstanceId, assigmentType);
                var viewList = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetViewList(item.ToString());
                if (viewList.Count == 0 || viewList.IndexOf(actionInstance.WfStateInstance.WfWorkflowInstance.ToString()) < 0)
                {
                    Assign(item, loginType, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId, "WFVIEW");
                }
            }

            #endregion Önce Atamalar Yapılır

            System.Threading.Thread.Sleep(1000); // History loglama fix, daemon tarafında olduğundan problem çıkarmayacaktır.

            #region Atamalardan Sonra History Kayıtları oluşturulur

            foreach (long item in AssigmentList)
            {
                //Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.Execute
                //    (
                //    actionInstance,
                //    item,
                //    item,
                //    WorkflowHistoryActionType.ASSIGN,
                //    "");
            }

            #endregion Atamalardan Sonra History Kayıtları oluşturulur

            #region Email Action İçin Instance Parametreleri Ekleniyor

            new Actions.AssigmentClass.CommonAssignment().EmailActionsAddition(wfContext, actionInstance, false);

            #endregion Email Action İçin Instance Parametreleri Ekleniyor

            #region Gerekli Mail Atma işlemleri yapılır

            foreach (long item in AssigmentList)
            {
                GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionType, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId, LastActionToLoginId, item, wfContext); // Atanana
            }
            GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionOwnerType, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId, LastActionToLoginId, 0, wfContext, AssigmentList); // Açana

            #endregion Gerekli Mail Atma işlemleri yapılır

            AssigmentList.Clear();

            #region Object Disopsing

            assigmentType = null;
            loginType = null;

            #endregion Object Disopsing

            return true;
        }

        /// <summary>
        /// Tum Assign işlemleri buradan yapılır.//OWNER_LOGIN_ID eklendi zülfiye...kontrol edilmeli
        /// </summary>
        /// <param name="assignedId"></param>
        /// <param name="assignedType"></param>
        /// <param name="assignmentOwnerId"></param>
        /// <param name="assignmentType"></param>
        public static void Assign(long assignedId, string assignedType, long assignmentOwnerId, string assignmentType)
        {
            using (UnitOfWork.Start())
            {
                System.Threading.Thread.Sleep(1000); // History loglama fix, daemon tarafında olduğundan problem çıkarmayacaktır.
                AssignmentHelper.Assign(assignedId, assignedType, assignmentOwnerId, assignmentType);

                #region akış ve kişi bazlı görüntüleme yetkisi mevcut ise

                if (assignmentType == "WFVIEW" && assignedType == "LOGIN")
                {
                    try
                    {
                        FWfWorkflowInstance workflowInstance = WFRepository<FWfWorkflowInstance>.GetEntity(assignmentOwnerId);
                        string MonitoringSql = CustomSQLManager.GetMonitoringViewSql();
                        List<Digiturk.Workflow.Digiflow.DataAccessLayer.CustomParameterList> CustomList = new List<Digiturk.Workflow.Digiflow.DataAccessLayer.CustomParameterList>();
                        CustomParameterList ct = new CustomParameterList("PERSONELID", workflowInstance.OwnerLogin.LoginId.ToString());
                        CustomParameterList ct2 = new CustomParameterList("WFDEFID", workflowInstance.WfWorkflowDef.WfWorkflowDefId.ToString());
                        CustomList.Add(ct);
                        CustomList.Add(ct2);
                        DataTable MonitoringDt = Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable2("FrameworkConnection", MonitoringSql, CustomList);
                        foreach (DataRow dr in MonitoringDt.Rows)
                        {
                            if (dr["OWNER_LOGIN_ID"] != null)
                            {
                                AssignmentHelper.Assign(ConvertionHelper.ConvertValue<long>(dr["OWNER_LOGIN_ID"]), "LOGIN", assignmentOwnerId, "WFVIEW");
                            }

                            AssignmentHelper.Assign(ConvertionHelper.ConvertValue<long>(dr["CREATED_BY"]), "LOGIN", assignmentOwnerId, "WFVIEW");
                        }

                        #region objectdispose

                        MonitoringDt = null;
                        workflowInstance = null;
                        MonitoringSql = null;
                        ct = null;
                        ct2 = null;
                        CustomList = null;

                        #endregion objectdispose
                    }
                    catch (System.Exception ex)
                    {
                    }
                }

                #endregion akış ve kişi bazlı görüntüleme yetkisi mevcut ise

                try
                {
                    if ((assignmentType == "WFVIEW" || assignmentType == "TASKINBOX") && assignedType == "LOGIN")
                    {
                        Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WFRepository<FWfWorkflowInstance>.GetEntity(assignmentOwnerId).WfWorkflowInstanceId);
                    }
                }
                catch (System.Exception)
                {
                }
            }
        }
    }
}