/* Common */
body {
    background-color: #78002B;
    color: black;

    font-size: 9pt;
	padding: 0px;
	margin: 0px;
}
a {
    color: #8A0A37;
    text-decoration: underline;
}
a:hover {
    color: #BE458B;
}
a:visited {
    color: #928489;
    text-decoration: underline;
}
div.PageContent {
    padding: 0px 0px 0px 0px;
}
/* Action Container Panel */
.ACPanel
{
	background: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.RedWine.Xaf.opacity50.png")%>') repeat;

	padding: 2px 5px 2px 20px;
	height: 25px;
}
/* Main table */
.Main {
	background: white;
}
h1 {
	color: Black;

	font-size: 200%;
	font-family: Tahoma;
	font-weight: normal;
	margin: 0px 0px 0px 0px;
	padding: 0px;
}
/* Footer */
.FooterCopyright {
	font-family: Tahoma, Arial, Helvetica, sans-serif;
	font-size: 9pt;
	text-align: right;

	color: #C696A7;
}
/* Dialog */
body.Dialog {
	background-color: White !important;
}
div.DialogPageContent {
	margin: 0px;
}
div.Header {
	width: 100%;
	height: 80px;
	margin: 0px;
}
.DialogContent {
	background-color: White;

	height: 300px;
}
/* Error */
.Error {
	color: Black;
}
/* Links on dark background */
.Footer .menuLinks_RedWine a,
.Footer .menuLinks_RedWine a.dx,
.Security .menuLinks_RedWine a,
.Security .menuLinks_RedWine a.dx,
.TabsContainer .menuLinks_RedWine a,
.TabsContainer .menuLinks_RedWine a.dx,
.Footer .menuLinks_RedWine a:visited,
.Footer .menuLinks_RedWine a.dx:visited,
.Security .menuLinks_RedWine a:visited,
.Security .menuLinks_RedWine a.dx:visited,
.TabsContainer .menuLinks_RedWine a:visited,
.TabsContainer .menuLinks_RedWine a.dx:visited
{
	color: White !important;
}
/* Controls customization */
.dxnbControl_RedWine,
.dxnbLite_RedWine {
	width: 100% !important;
}
.dxgvControl_RedWine {
	width: 100%;
}
.dxgvHeader_RedWine td {
    white-space:normal;
}
.dxtcControl_RedWine,
.dxtcLite_RedWine {
    width: 100% !important;
}
.NavigationTabsActionContainer .dxtcControl_RedWine,
.NavigationTabsActionContainer .dxtcLite_RedWine {
background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.RedWine.Xaf.opacity50.png")%>');
background-repeat: repeat-x;
}
.NavigationTabsActionContainer .dxtcPageContent_RedWine,
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-content {
    padding-left: 18px;
    border: none !important;
}
.NavigationTabsActionContainer .dxtcLeftIndentCell_RedWine div,
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-leftIndent {
	width: 15px !important;
}
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-tab,
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-activeTab,
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-leftIndent,
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-spacer,
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-rightIndent,
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-sbWrapper,
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-sbIndent,
.NavigationTabsActionContainer .dxtcLite_RedWine .dxtc-sbSpacer
{
	height: 25px;
}
.ACV .dxmVerticalMenuItemWithImage_RedWine,
.ACV .dxmLite_RedWine .menuLinks_RedWine .dxm-content,
.ACV .dxmLite_RedWine .menuLinks_RedWine .dxm-hovered .dxm-content,
.ACV .dxmLite_RedWine .menuLinks_RedWine .dxm-disabled .dxm-content
{
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}
/* NavigationHistory (NavigationHistoryActionContainer) */
.NavigationHistoryLinks {
	color: #86888D;
	padding: 0px 0px 0px 2px;
}
.NavigationHistoryLinks a:hover {
	text-decoration: underline;
    color: #BE458B;
}
.NavigationHistoryLinks a, .NavigationHistoryLinks a:visited {
	text-decoration: underline;
    color: #8A0A37;
}
.NavigationHistoryLinks a.Current, .NavigationHistoryLinks a.Current:hover, .NavigationHistoryLinks a.Current:visited {
	text-decoration: none;
	color: #86888D;
}
.Security .dxmMenuSeparator_RedWine {
	padding: 0px 4px 0px 11px;
}
.TabsContainer .dxmMenuSeparator_RedWine {
	padding: 0px 10px 0px 16px;
}
.LayoutTabContainerWithNestedFrame > .Item:first-child > .NestedFrame > .ToolBar .dxmMenu_RedWine,
.LayoutTabContainerWithNestedFrame > .Item:first-child > .NestedFrame > .ToolBar .dxmLite_RedWine .dxm-main {
	border-top: 0px;
}
.LayoutTabContainer > .Item > .NestedFrame > .ToolBar .dxmMenu_RedWine,
.LayoutTabContainer > .Item > .NestedFrame > .ToolBar .dxmLite_RedWine .dxm-main {
	border-bottom-width: 0px;
}
.LayoutTabContainerWithNestedFrame > .Item > .NestedFrame > .ToolBar .dxmMenu_RedWine,
.LayoutTabContainerWithNestedFrame > .Item > .NestedFrame > .ToolBar .dxmLite_RedWine .dxm-main
{
	border-left: 0px;
	border-right: 0px;
	border-bottom-width: 1px !important;
}
.HorizontalTemplateHeader,
.VerticalTemplateHeader,
.Header {
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.RedWine.Xaf.background.jpg")%>');
    background-position: top right;
    background-repeat: no-repeat;
	background-color: #78002B;
}
.RecordsNavigationContainer .menuLinks_RedWine {
	padding-left: 8px;
}
.RecordsNavigationContainer .menuLinks_RedWine .dxmMenuItemWithImage_RedWine {
	padding-left: 3px;
}