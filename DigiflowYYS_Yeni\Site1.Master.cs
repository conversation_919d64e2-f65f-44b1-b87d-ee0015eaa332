﻿using Digiturk.Workflow.Digiflow.WebCore.MasterPage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace DigiflowYYS_Yeni
{
    public partial class Site1 : YYSSecureMasterPage
    {
        #region private fields & properties

        /// <summary>
        /// Sayfanın Title Bilgisi
        /// </summary>
        public string PageTitle
        {
            get { return PageTitleLabel.Text; }
            set
            {
                if (PageTitleLabel != null)
                {
                    PageTitleLabel.Text = value;
                }
            }
        }

        public long AdminVariable;

        #endregion private fields & properties

        #region Page Events

        /// <summary>
        /// Sayfanın Load İşlemi
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!Page.IsPostBack)
            {
                string ApplicatioName = System.Configuration.ConfigurationManager.AppSettings["ApplicationName"];
                Response.Cache.SetCacheability(HttpCacheability.NoCache);
            }
        }

        #endregion Page Events

        #region MasterPage Fonksiyonları

        /// <summary>
        /// Menü elemanlarının gelen değerlere göre görünüp görünmemesini sağlar
        /// </summary>
        /// <param name="SystemAdmin">Sistem yöneticisi elemanının görünüp görünmemesini sağlayan değer</param>
        /// <param name="RuleOperation">İş Akış kuralları elemanının görünüp görünmemesini sağlayan değer</param>
        /// <param name="Logicalgroup">Mantıksal Grup elemanının görünüp görünmemesini sağlayan değer</param>
        public override void BuildMenu(bool SystemAdmin, bool RuleOperation, bool Logicalgroup)
        {
            TopMenuPanel.Visible = true;
            ASPxMenu1.Items[1].Visible = SystemAdmin;
            ASPxMenu1.Items[2].Visible = Logicalgroup;
            ASPxMenu1.Items[3].Visible = RuleOperation;
            FullNameLabel.Text = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(UserInformation.LoginObject.LoginId) + " - " + UserInformation.LoginObject.DomainUserName;
            if (SystemAdmin == true && RuleOperation == true && Logicalgroup == true)
            {
                AdminTypeLabel.Text = "Sistem ve Akış Yöneticisi";
                AdminVariable = 1;
            }
            else if (SystemAdmin == true)
            {
                AdminTypeLabel.Text = "Sistem Yöneticisi";
                AdminVariable = 1;
            }
            else if (RuleOperation == true && Logicalgroup == true)
            {
                AdminTypeLabel.Text = "Akış Yöneticisi";
                AdminVariable = 0;
            }
            else
            {
                AdminTypeLabel.Text = "";
                AdminVariable = 0;
            }
        }

        /// <summary>
        /// Üst menü panelini gizlemek ya da görünür hale getirmek için kullanılır.
        /// </summary>
        /// <param name="display">Menü panelinin görünüp görünmeyeceğini belirten boolean değer</param>
        public void ShowMenu(bool display)
        {
            TopMenuPanel.Visible = display;
        }

        /// <summary>
        /// Sayfada popup mesaj görüntülemek için kullanılır
        /// </summary>
        /// <param name="hyperlinkVisible"></param>
        /// <param name="title"></param>
        /// <param name="message"></param>
        /// <param name="isError"></param>
        /// <param name="trace"></param>
        public void ShowPopup(bool hyperlinkVisible, string title, string message, bool isError, string trace)
        {
            HyperLink1.Visible = hyperlinkVisible;
            if (isError)
            {
                if (string.IsNullOrEmpty(trace))
                {
                    Literal1.Text = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.ShowError(title, message);
                }
                else
                {
                    Literal1.Text = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.ShowError(title, message, trace);
                }
            }
            else
            {
                Literal1.Text = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.ShowInformation(title, message);
            }
            Literal1.Visible = true;
            Modal1.Show();
        }

        /// <summary>
        /// Displays an error at master page
        /// </summary>
        /// <param name="title">Title of the message</param>
        /// <param name="message">Error message</param>
        public void ShowError(string title, string message)
        {
            StatusLiteral.Text = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.ShowError(title, message);
        }

        /// <summary>
        /// Displays an error at master page
        /// </summary>
        /// <param name="title">Title of the message</param>
        /// <param name="details">Details of the message</param>
        /// <param name="message">Error message</param>
        public void ShowError(string title, string message, string details)
        {
            StatusLiteral.Text = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.ShowError(title, message, details);
        }

        /// <summary>
        /// Displays an information at master page
        /// </summary>
        /// <param name="title">Title of the message</param>
        /// <param name="message">Error message</param>
        public void ShowInformation(string title, string message)
        {
            StatusLiteral.Text = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.ShowInformation(title, message);
        }

        /// <summary>
        /// Displays an information at master page
        /// </summary>
        /// <param name="title">Title of the message</param>
        /// <param name="message">Error message</param>
        /// <param name="appendHomePageLink">Appends return to home page link to the information message</param>
        public void ShowInformation(string title, string message, bool appendHomePageLink)
        {
            if (appendHomePageLink)
            {
                message += "<p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><a href='Default.aspx'><b><- Anasayfaya Dön</b></a>";
            }

            StatusLiteral.Text = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.ShowInformation(title, message);
        }

        #endregion MasterPage Fonksiyonları

        public void ScriptManagerRegisterAsyncPostBackControl(Control Controls)
        {
            this.ScriptManager1.RegisterAsyncPostBackControl(Controls);
        }
    }
}