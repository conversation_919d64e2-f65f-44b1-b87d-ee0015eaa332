﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers
{
    public class ActionProcessHelper
    {
        ExchangeHelper exchangeHelper;
        public ActionProcessHelper()
        {
            NHibernate.ISession session = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FSessionHelper.GetDTWorkflowSession();
            exchangeHelper = new ExchangeHelper();
        }
        public void ProcessWaitingEmails(List<EmailInfo> listWaitingEmails)
        {
            if (listWaitingEmails.Count > 0)
            {
                using (UnitOfWork.Start())
                {
                    foreach (var emailInfo in listWaitingEmails)
                    {
                        ProcessTheMail(emailInfo);
                    }
                }
            }
        }
        public void EmailMainError(Exception ex)
        {
            try
            {
                MailMessage message = new MailMessage();
                message = DebugMailListDonustur(message, false);
                message.IsBodyHtml = true;
                message.Subject = ConfigurationManager.AppSettings["ProjectName"] + " Ana Hata";


                message.Body = "<!DOCTYPE html PUBLIC \" -//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">";
                message.Body += "<html xmlns=\"http://www.w3.org/1999/xhtml\">";
                message.Body += "<head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" /><title></title></head>";
                message.Body += "<body style=\"background: url('[cid:kurumsal_back.jpg]')\">";
                message.Body += "<table style=\"padding:60px 60px 60px 60px;font-family:tahoma;font-size:14px;color:#000000;\"><tr><td>";
                message.Body += "<div><img src=\"[cid:logo.png]\" width=\"102\" height=\"37\" vspace=\"5\" /><table width=\"100%\" border=\"0\" cellspacing =\"0\" cellpadding =\"0\" ><tr><td height=\"2\" bgcolor=\"#E51937\" ></td></tr></table></div>";
                message.Body += "<div><br/><br/>" + (ConfigurationManager.AppSettings["ProjectName"] + " ana metot içinde hata meydana geldi.<br/><br/>" + ex.Message) + "</div><br/><br/><br/><br/>";
                message.Body += "</td></tr></table>";
                message.Body += "</body></html> ";
                Digiturk.Workflow.Engine.MailHelper.SendEmailDirect(message, "1");
                message.Dispose();
            }
            catch
            {
            }
        }
        private static MailMessage DebugMailListDonustur(MailMessage mail, bool AddToCC)
        {
            mail.To.Clear();
            mail.CC.Clear();
            WorkflowMailerSection section = (WorkflowMailerSection)ConfigurationManager.GetSection("debugMailList");
            if (section != null)
            {
                foreach (WorkflowMailerAddress address in section.MailAddresses)
                {
                    if (!String.IsNullOrEmpty(address.DisplayName))
                    {
                        if (!AddToCC)
                            mail.To.Add(new MailAddress(address.Address, address.DisplayName));
                        else
                            mail.CC.Add(new MailAddress(address.Address, address.DisplayName));
                    }
                    else
                    {
                        if (!AddToCC)
                            mail.To.Add(new MailAddress(address.Address));
                        else
                            mail.CC.Add(new MailAddress(address.Address));
                    }
                }
            }
            return mail;
        }

        private void ProcessTheMail(EmailInfo emailInfo)
        {
            string errorUserMsg_En = string.Empty, errorUserMsg_Tr = string.Empty, subjectInfo = string.Empty;
            bool showAdminMsg = false;
            try
            {
                if (emailInfo.ActionInfo != null)
                {
                    FWfWorkflowInstance actionInstance = WFRepository<FWfWorkflowInstance>.GetEntity(emailInfo.ActionInfo.WfInstanceId);
                    subjectInfo = actionInstance.WfWorkflowDef.Name + " / " + actionInstance.WfWorkflowDef.Description + " - " + actionInstance.WfWorkflowInstanceId.ToString();

                    WFContext CurrentWFContext = new WFContext(actionInstance);
                    if (!string.IsNullOrEmpty(emailInfo.SenderUserName))
                    {
                        if (!string.IsNullOrEmpty(emailInfo.Language))
                        {
                            if (emailInfo.NoteFetched)
                            {
                                if (actionInstance.WfWorkflowStatusType.WfWorkflowStatusTypeCd.ToLower() != "completed")
                                {
                                    long senderFLoginId = FormInformationHelper.GetLoginId(emailInfo.SenderUserName);
                                    List<long> listAssingnLogins = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(emailInfo.ActionInfo.WfInstanceId);
                                    List<long> listDelegateAssingnLogins = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetDelegeAssignLoginList(actionInstance.WfWorkflowDef.WfWorkflowDefId, senderFLoginId);
                                    if (emailInfo.ActionInfo.WfStateInstanceId == actionInstance.WfCurrentState.WfStateInstanceId || actionInstance.WfCurrentState.WfStateDef.WfStateDefId == 1401)
                                    {
                                        FWfActionTaskInstance taskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(actionInstance.WfCurrentState.WfCurrentActionInstanceId.Value);
                                        switch (emailInfo.ActionInfo.ActionType)
                                        {
                                            case MailActionType.Approve:
                                                {
                                                    if (MyWorkflowHelpers.ApproveAuthorizationControl(senderFLoginId, emailInfo.ActionInfo.WfInstanceId, actionInstance.WfWorkflowDef.WfWorkflowDefId, listAssingnLogins, listDelegateAssingnLogins))
                                                    {
                                                        showAdminMsg = true;
                                                        MyWorkflowHelpers.ApproveButton_Click(actionInstance, senderFLoginId, emailInfo.ActionInfo.WfInstanceId, listAssingnLogins, taskInstance, actionInstance.WfWorkflowDef.WfWorkflowDefId, CurrentWFContext, emailInfo.Note, ref errorUserMsg_Tr, ref errorUserMsg_En);
                                                    }
                                                    else
                                                    {
                                                        errorUserMsg_Tr = string.Format("Onay için yetkiniz bulunmadı.FLoginId={0} , WfInstanceId={1} , WfStateInstanceId={2} , WfWorkflowDefId={3}", senderFLoginId, emailInfo.ActionInfo.WfInstanceId, emailInfo.ActionInfo.WfStateInstanceId, actionInstance.WfWorkflowDef.WfWorkflowDefId);
                                                        errorUserMsg_En = string.Format("You don't have authorization required for approval .FLoginId={0} , WfInstanceId={1} , WfStateInstanceId={2} , WfWorkflowDefId={3}", senderFLoginId, emailInfo.ActionInfo.WfInstanceId, emailInfo.ActionInfo.WfStateInstanceId, actionInstance.WfWorkflowDef.WfWorkflowDefId);
                                                        throw new Exception();
                                                    }
                                                }
                                                break;
                                            case MailActionType.Reject:
                                                if (MyWorkflowHelpers.RejectAuthorizationControl(senderFLoginId, emailInfo.ActionInfo.WfInstanceId, actionInstance.WfWorkflowDef.WfWorkflowDefId, listAssingnLogins, listDelegateAssingnLogins))
                                                {
                                                    showAdminMsg = true;
                                                    MyWorkflowHelpers.RejectButton_Click(actionInstance, senderFLoginId, emailInfo.ActionInfo.WfInstanceId, listAssingnLogins, taskInstance, actionInstance.WfWorkflowDef.WfWorkflowDefId, CurrentWFContext, emailInfo.Note, ref errorUserMsg_Tr, ref errorUserMsg_En);
                                                }
                                                else
                                                {
                                                    errorUserMsg_Tr = string.Format("Ret için yetkiniz bulunmadı.FLoginId={0} , WfInstanceId={1} , WfStateInstanceId={2} , WfWorkflowDefId={3}", senderFLoginId, emailInfo.ActionInfo.WfInstanceId, emailInfo.ActionInfo.WfStateInstanceId, actionInstance.WfWorkflowDef.WfWorkflowDefId);
                                                    errorUserMsg_En = string.Format("You don't have authorization required for rejection.FLoginId={0} , WfInstanceId={1} , WfStateInstanceId={2} , WfWorkflowDefId={3}", senderFLoginId, emailInfo.ActionInfo.WfInstanceId, emailInfo.ActionInfo.WfStateInstanceId, actionInstance.WfWorkflowDef.WfWorkflowDefId);
                                                    throw new Exception();
                                                }
                                                break;
                                            case MailActionType.Cancel:
                                                if (MyWorkflowHelpers.CancelAuthorizationControl(senderFLoginId, actionInstance.OwnerLogin.LoginId, actionInstance.WfWorkflowDef.WfWorkflowDefId))
                                                {
                                                    showAdminMsg = true;
                                                    MyWorkflowHelpers.CancelButton_Click(actionInstance, CurrentWFContext, senderFLoginId, emailInfo.ActionInfo.WfInstanceId, emailInfo.Note, taskInstance, listAssingnLogins);
                                                }
                                                else
                                                {
                                                    errorUserMsg_Tr = string.Format("İptal için yetkiniz bulunmadı.FLoginId={0} , WfInstanceId={1} , WfStateInstanceId={2} , WfWorkflowDefId={3}", senderFLoginId, emailInfo.ActionInfo.WfInstanceId, emailInfo.ActionInfo.WfStateInstanceId, actionInstance.WfWorkflowDef.WfWorkflowDefId);
                                                    errorUserMsg_En = string.Format("You don't have authorization required for cancellation.FLoginId={0} , WfInstanceId={1} , WfStateInstanceId={2} , WfWorkflowDefId={3}", senderFLoginId, emailInfo.ActionInfo.WfInstanceId, emailInfo.ActionInfo.WfStateInstanceId, actionInstance.WfWorkflowDef.WfWorkflowDefId);
                                                    throw new Exception();
                                                }
                                                break;
                                        }
                                        Digiturk.Workflow.Digiflow.WorkFlowHelpers.MailActionsHelper.InsertMailActionHistoryLog(emailInfo.EmailId, emailInfo.MailActionId, true, string.Empty, emailInfo.Language);
                                    }
                                    else
                                    {
                                        showAdminMsg = true;
                                        errorUserMsg_Tr = "Akışın mevcut durumu ile eposta ile yapılması istenen aksiyonun kayıtlı durumu uyuşmuyor.Akış eposta aksiyon isteği sonrasında devam ettirilmiş.";
                                        errorUserMsg_En = "The current state of related workflow does not match the state of workflow recorded when email action requested and registered for automatic process.The workflow has been transacted after email action requested.";
                                        throw new Exception(string.Format("F_WF_MAIL_ACTIONS tablosunda MailActionId={0} satırındaki stateInstanceID={1} ile şu anki stateInstanceID={2} uyuşmuyor.", emailInfo.MailActionId, emailInfo.ActionInfo.WfStateInstanceId, actionInstance.WfCurrentState.WfStateInstanceId));
                                    }
                                }
                                else
                                {
                                    showAdminMsg = true;
                                    errorUserMsg_Tr = "Akış tamamlanmış durumda olduğu için işlem gerçekleştirilemedi.";
                                    errorUserMsg_En = "Since the workflow has been completed , related action could not been fulfilled.";
                                    throw new Exception(string.Format("MailActionId={0} , stateInstanceID={1} olan işlem talebi akış tamamlanmış olduğu için gerçekleştirilemedi.", emailInfo.MailActionId, emailInfo.ActionInfo.WfStateInstanceId));
                                }
                            }
                            else
                            {
                                errorUserMsg_Tr = string.Format("E-Posta body kısmından not alınamadı");
                                errorUserMsg_En = string.Format("Note part could not been obtained from email body");
                                throw new Exception();
                            }
                        }
                        else
                        {
                            errorUserMsg_Tr = string.Format("E-Posta body kısmından dil alınamadı");
                            errorUserMsg_En = string.Format("Language part could not been obtained from email body");
                            throw new Exception();
                        }
                    }
                    else
                    {
                        errorUserMsg_Tr = string.Format("AD kullanıcı adı alınamadı");
                        errorUserMsg_En = string.Format("AD username could not been obtained");
                        throw new Exception();
                    }
                }
                else
                {
                    if (!emailInfo.MailActionId.HasValue)
                    {
                        errorUserMsg_Tr = string.Format("E-Posta body kısmından MailActionId (GuId) alınamadı");
                        errorUserMsg_En = string.Format("MailActionId could not been obtained from email body");
                        throw new Exception();
                    }
                    else
                    {
                        showAdminMsg = true;
                        errorUserMsg_Tr = string.Format("Veri tabanında eposta içinde belirtilen MailActionID ye ait kayıt bulunmadı.");
                        errorUserMsg_En = string.Format("MailActionId record does not exists in database's related table.");
                        throw new Exception(string.Format("F_WF_MAIL_ACTIONS tablosunda kayıt completed=0 olan kayıt bulunmadı.MailActionId={0}", emailInfo.MailActionId.ToString()));
                    }
                }
            }
            catch (Exception ex)
            {
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.MailActionsHelper.InsertMailActionHistoryLog(emailInfo.EmailId, emailInfo.MailActionId, false, ex.Message, emailInfo.Language);
                ProcessInterceptionEmail(
                    errorUserMsg_Tr,
                    errorUserMsg_En,
                    showAdminMsg ? ex.Message : string.Empty,
                    ConfigurationManager.AppSettings["ProjectName"] + " İşlem Sonuç " + subjectInfo,
                    emailInfo.SenderEmail.Address,
                    null
                    );
            }
            finally
            {
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.MailActionsHelper.CompleteMailAction(emailInfo.MailActionId.Value);
                exchangeHelper.SetEmailFlagComplete(emailInfo.EmailId);
            }
        }

        private void ProcessInterceptionEmail(string msgTr, string msgEn, string msgAdmin, string subject, string To, string CC)
        {
            try
            {
                MailMessage message = new MailMessage();
                message = DebugMailListDonustur(message, true);
                message.To.Add(To);
                string msgTotal = string.Empty;
                if (!string.IsNullOrEmpty(msgTr))
                    msgTotal += "<div>" + msgTr + "</div>";
                if (!string.IsNullOrEmpty(msgEn))
                    msgTotal += "<div>" + (msgTotal != string.Empty ? "<br/><br/><hr/><br/><br/>" : "") + msgEn + "</div>";
                if (!string.IsNullOrEmpty(msgAdmin))
                    msgTotal += "<div>" + (msgTotal != string.Empty ? "<br/><br/><hr/><br/><br/>Yönetici Hata Açıklaması :<br/><br/>" : "") + msgAdmin + "</div>";

                message.Body = "<!DOCTYPE html PUBLIC \" -//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">";
                message.Body += "<html xmlns=\"http://www.w3.org/1999/xhtml\">";
                message.Body += "<head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" /><title></title></head>";
                message.Body += "<body style=\"background: url('[cid:kurumsal_back.jpg]')\">";
                message.Body += "<table style=\"padding:60px 60px 60px 60px;font-family:tahoma;font-size:14px;color:#000000;\"><tr><td>";
                message.Body += "<div><img src=\"[cid:logo.png]\" width=\"102\" height=\"37\" vspace=\"5\" /><table width=\"100%\" border=\"0\" cellspacing =\"0\" cellpadding =\"0\" ><tr><td height=\"2\" bgcolor=\"#E51937\" ></td></tr></table></div>";
                message.Body += "<div><br/><br/>" + msgTotal + "</div><br/><br/><br/><br/>";
                message.Body += "</td></tr></table>";
                message.Body += "</body></html> ";

                message.Subject = subject;
                message.IsBodyHtml = true;

                if (!string.IsNullOrEmpty(CC))
                    message.CC.Add(CC);
                Digiturk.Workflow.Engine.MailHelper.SendEmailDirect(message, "1");
                message.Dispose();
            }
            catch
            {
            }
        }
    }
}