﻿using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class YillikIzinHelper
    {
        internal static void CancelEndFlow(FWfWorkflowInstance CurrentWfIns)
        {
            DataTable DtbDetail = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveRequestDetailInformation(CurrentWfIns.EntityRefId.Value);
            string Sicil = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetSicilName(CurrentWfIns.OwnerLogin.LoginId);
            Sicil = Sicil.PadLeft(5, '0');
            foreach (DataRow item in DtbDetail.Rows)
            {
                DateTime baslangic_tarihi = (DateTime)item["StartDate"];
                DateTime bitis_tarihi = (DateTime)item["EndDate"];
                string izin_tur_kodu = item["leaveCode"].ToString();
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.VacationInformationHelper.DtbLeaveInformationCancelWFCorrection(
                    Sicil,
                    baslangic_tarihi.Year,
                    baslangic_tarihi,
                    bitis_tarihi,
                    izin_tur_kodu);
            }
        }
    }
}
