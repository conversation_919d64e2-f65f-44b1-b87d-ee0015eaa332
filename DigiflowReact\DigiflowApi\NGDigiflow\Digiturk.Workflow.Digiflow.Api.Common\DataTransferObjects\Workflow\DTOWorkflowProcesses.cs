﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Digiturk.Workflow.Digiflow.Api.Common.DataTransferObjects.Workflow
{
    public class DTOWorkflowProcesses
    {
        public long LoginId { get; set; }

        public long WorkflowInstanceId { get; set; }

        public string Comment { get; set; }

        public long ForwardLoginId { get; set; }

        public long SendCommentLoginId { get; set; }

        public DateTime SuspendDate { get; set; }
    }
}
