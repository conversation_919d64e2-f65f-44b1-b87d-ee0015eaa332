﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{73966DA1-FBA1-4EA1-A249-2C6E9492F7EF}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>NGDigiflowApi</RootNamespace>
    <AssemblyName>NGDigiflowApi</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44375</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Digiturk.Workflow.Digiflow.Authentication">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Digiflow.Authentication.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authorization">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Digiflow.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Entities">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Digiflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.ExceptionEntites">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Digiflow.ExceptionEntites.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.DigiFlow.Framework">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.DigiFlow.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.GenericMailHelper">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Digiflow.GenericMailHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowServicesHelper">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Engine">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Engine.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Repository">
      <HintPath>..\..\..\..\TFS\Deployment\Digiturk.Workflow.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.7\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Areas\HelpPage\ApiDescriptionExtensions.cs" />
    <Compile Include="Areas\HelpPage\App_Start\HelpPageConfig.cs" />
    <Compile Include="Areas\HelpPage\Controllers\HelpController.cs" />
    <Compile Include="Areas\HelpPage\HelpPageAreaRegistration.cs" />
    <Compile Include="Areas\HelpPage\HelpPageConfigurationExtensions.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\CollectionModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ComplexTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\DictionaryModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumValueDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\IModelDocumentationProvider.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\KeyValuePairModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescriptionGenerator.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameAttribute.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameHelper.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterAnnotation.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\SimpleTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\Models\HelpPageApiModel.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleKey.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ImageSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\InvalidSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ObjectGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\SampleDirection.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\TextSample.cs" />
    <Compile Include="Areas\HelpPage\XmlDocumentationProvider.cs" />
    <Compile Include="Controllers\EntitySaveController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\ValuesController.cs" />
    <Compile Include="Controllers\WorkflowController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Areas\HelpPage\HelpPage.css" />
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Global.asax" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Areas\HelpPage\Views\Web.config" />
    <Content Include="Areas\HelpPage\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\ResourceModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Index.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\TextSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\SimpleTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Samples.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Parameters.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ModelDescriptionLink.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\KeyValuePairModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\InvalidSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ImageSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\HelpPageApiModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\EnumTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\DictionaryModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ComplexTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\CollectionModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ApiGroup.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Api.cshtml" />
    <None Include="Properties\PublishProfiles\DigiFlowWebApiPublish.pubxml" />
    <None Include="Scripts\jquery-3.4.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.4.1.js" />
    <Content Include="Scripts\jquery-3.4.1.min.js" />
    <Content Include="Scripts\jquery-3.4.1.slim.js" />
    <Content Include="Scripts\jquery-3.4.1.slim.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Web.config" />
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Areas\HelpPage\Views\_ViewStart.cshtml" />
    <Content Include="Content\Site.css" />
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Models\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap.min.css.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap.css.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap-theme.min.css.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap-theme.css.map" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
    <Content Include="Scripts\jquery-3.4.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.4.1.min.map" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>1989</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44375/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>