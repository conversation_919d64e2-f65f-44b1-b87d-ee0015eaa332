﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class PurchaseForPaymentRequestHelper
    {
        internal static void InsertThePurchaseLogicalGroup(Digiturk.Workflow.Entities.FWfWorkflowInstance CurrentWfIns,long LoginId)
        {
            PurchaseForPaymentRequest RequestObject = WFRepository<Digiturk.Workflow.Digiflow.Entities.PurchaseForPaymentRequest>.GetEntity(CurrentWfIns.EntityRefId);
            string yetkiID = CurrentWfIns.OwnerLogin.LoginId.ToString();     // talep yapanı ilk önce ata yetkiID alanına
            long LogicalGroupId = 302; // kişi bazlı yetkilendirme

            //kişi seçilmiş ise
            if (RequestObject.PersonnelId != 0)
            {
                LogicalGroupId = 302; // kişi
                yetkiID = RequestObject.PersonnelId.ToString(); // seçilen kullanıcı
            }
            else
            {
                if (RequestObject.PurchaseId != 0)
                {
                    LogicalGroupId = 303; // satınalma id bazlı yetkilendirme
                    yetkiID = RequestObject.PurchaseId.ToString();//seçilen satınalmaID
                }
            }

            //yetkilendirme onaylandı ise
            Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.PurchaseForPaymentHelper.InsertThePurchaseLogicalGroup(yetkiID, CurrentWfIns.OwnerLogin.LoginId.ToString(), LogicalGroupId, LoginId);
        }
    }
}
