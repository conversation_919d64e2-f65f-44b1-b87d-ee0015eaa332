﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.Results;

namespace Digiturk.Workflow.Digiflow.Api.Controllers
{
    public class EntityController : ApiController
    {

        [HttpGet]
        [Route("api/GetEntity")]
        public string GetEntity(long InstanceId, long UserId)
        {
            string jsondata = string.Empty;
            FWfWorkflowInstance CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
            if(Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDefinitionHelper.GetNodeAttributeName("NDelegation.aspx", "value")==CurrentWfIns.WfWorkflowDef.WfWorkflowDefId.ToString())
            {
                var request = WFRepository<Digiturk.Workflow.Digiflow.Entities.DelegationRequest>.GetEntity(CurrentWfIns.EntityRefId);
                jsondata= JsonConvert.SerializeObject(request);
            }
            else if (Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDefinitionHelper.GetNodeAttributeName("ContratRequest.aspx", "value") == CurrentWfIns.WfWorkflowDef.WfWorkflowDefId.ToString())
            {
                //T types = Digiturk.Workflow.Digiflow.Entities.ContractsRequest;
                var request = WFRepository<Digiturk.Workflow.Digiflow.Entities.ContractsRequest>.GetEntity(CurrentWfIns.EntityRefId);
                jsondata = JsonConvert.SerializeObject(request);
            }
            return jsondata;
        }

        [HttpGet]
        [Route("api/GetEntityWithJson")]
        public System.Web.Http.IHttpActionResult GetEntityWithJson(long InstanceId, long UserId)
        {
            FWfWorkflowInstance CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
            if (Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDefinitionHelper.GetNodeAttributeName("NDelegation.aspx", "value") == CurrentWfIns.WfWorkflowDef.WfWorkflowDefId.ToString())
            {
                var request = WFRepository<Digiturk.Workflow.Digiflow.Entities.DelegationRequest>.GetEntity(CurrentWfIns.EntityRefId);
                return Json<Digiturk.Workflow.Digiflow.Entities.DelegationRequest>(request);
            }
            else if (Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDefinitionHelper.GetNodeAttributeName("ContratRequest.aspx", "value") == CurrentWfIns.WfWorkflowDef.WfWorkflowDefId.ToString())
            {
                var request = WFRepository<Digiturk.Workflow.Digiflow.Entities.ContractsRequest>.GetEntity(CurrentWfIns.EntityRefId);
                return Json<Digiturk.Workflow.Digiflow.Entities.ContractsRequest>(request);
            }
            return null;
        }

        [HttpPost]
        [Route("api/SaveDelegationEntity")]
        public long SaveDelegationEntity()
        {
            return 0;
        }

        
    }
}
