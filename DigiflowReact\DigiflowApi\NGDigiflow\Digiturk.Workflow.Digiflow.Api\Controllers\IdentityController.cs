﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Api.Common.DataTransferObjects.Inbox;
using Digiturk.Workflow.Digiflow.Authentication;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Http;
using Digiturk.Workflow.Digiflow.Authorization;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Api.Controllers
{

    public class SaveInsteadOfDto
    {
        public long LoginId { get; set; }
        public long InstLoginId { get; set; }
    }
    public class IdentityController : ApiController
    {
        [HttpGet]
        [Route("api/GetUserId")]
        public long GetUserId()
        {
            FLogin fLogin = DomainAuthentication.GetLoginBySID(UserName);
            if(fLogin==null)
            {
                return 0;
            }
            return fLogin.LoginId;
        }

        [HttpPost]
        [Route("api/SaveInsteadOfLoginId")]
        public long SaveInsteadOfLoginId(SaveInsteadOfDto dto)
        {
            string SQL = string.Format("Select WF_DF_INSTAED_OF_USER_ID from DT_WORKFLOW.WF_DF_INSTAED_OF_USER where LOGIN_ID={0}",dto.LoginId);
            long KeyId=Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetOnlyColumnSQL<long>("ReportConnection",SQL,new List<DataAccessLayer.CustomParameterList>());
            InsteadOfUser Instuser;
            if (KeyId == 0)
            {
                Instuser = new InsteadOfUser();
            }
            else
            {
                Instuser = WFRepository<InsteadOfUser>.GetEntity(KeyId);
            }
            if(Instuser==null)
            {
                Instuser = new InsteadOfUser();
            }
            Instuser.InsteadOfLoginId = dto.InstLoginId;
            Instuser.LoginId = dto.LoginId;
            Instuser.Created = DateTime.Now;
            Instuser.CreatedBy = dto.LoginId;
            ActionHelpers.EntitySave(Instuser);
            return 0;
        }

        [HttpGet]
        [Route("api/GetInsteadOfLoginId")]
        public long GetInsteadOfUserId(long UserId)
        {
            string SQL = string.Format("Select INSTAED_OF_LOGIN_ID from DT_WORKFLOW.WF_DF_INSTAED_OF_USER where LOGIN_ID={0}", UserId);
            long KeyId = Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetOnlyColumnSQL<long>("ReportConnection", SQL, new List<DataAccessLayer.CustomParameterList>());
            if(KeyId==0)
            {
                return UserId;
            }
            else
            {
                return KeyId;
            }
        }

        [HttpGet]
        [Route("api/GetUserName")]
        public string GetCurrentUserName()
        {
            return UserName;
        }

        [HttpGet]
        [Route("api/GetClientUserName")]
        public string GetClientUserName()
        {
            return UserName;
        }

        public bool DebugMode { 
            get { 
                return bool.Parse(System.Configuration.ConfigurationManager.AppSettings["DebugMode"].ToString());
            } 
        }

        public string[] SystemUserList
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["IsSystemAdminUsers"].Split(';');
            }
        }

        public string UserName 
        { 
            get
            {
                if (HttpContext.Current.User.Identity.IsAuthenticated)
                {
                    return HttpContext.Current.User.Identity.Name.Replace(@"DIGITURK\", "").ToUpper();
                }
                else
                {
                    return HttpContext.Current.Request.LogonUserIdentity.Name.Replace(@"DIGITURK\", "").ToUpper();
                }
            } 
        }

        [HttpGet]
        [Route("api/IsCurrentUserSystemAdmin")]
        public bool IsCurrentUserSystemAdmin()
        {
            return SystemUserList.Contains(UserName) && DebugMode;
        }

        [HttpGet]
        [Route("api/GetUserList")]
        public IEnumerable<DTOUserItem> GetUserList()
        {
            List<DTOUserItem> UserList = new List<DTOUserItem>();

            if (IsCurrentUserSystemAdmin())
            {
                string SQL = @"Select 0 as LOGIN_ID, ' ---Seçiniz---' as NAME_SURNAME from dual Union Select  LOGIN_ID,NAME_SURNAME  from DT_WORKFLOW.VW_USER_INFORMATION Order By NAME_SURNAME ASC";
                DataTable dtUserItem = Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", SQL);

                for (int i = 0; i < dtUserItem.Rows.Count; i++)
                {
                    DTOUserItem dto = new DTOUserItem();
                    dto.userId = dtUserItem.Rows[i]["LOGIN_ID"].ToString();
                    dto.userNameSurname = dtUserItem.Rows[i]["NAME_SURNAME"].ToString();
                    UserList.Add(dto);
                }
            }

            return UserList;
        }


        [HttpGet]
        [Route("api/GetUserInformation")]
        public AuthenticationResult GetUserInformation(long LoginId,long InstanceId,long wfDefId)
        {
            long WorkflowDefinitionId = 0;
            long StateDefinitionId = 0;
            if(InstanceId>0)
            {
                using (UnitOfWork.Start())
                {
                    FWfWorkflowInstance wfinstance = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                    WorkflowDefinitionId = wfinstance.WfWorkflowDef.WfWorkflowDefId;
                    StateDefinitionId = wfinstance.WfCurrentState.WfStateDef.WfStateDefId;
                }
            }
            if(wfDefId>0)
            {
                using (UnitOfWork.Start())
                {
                    WorkflowDefinitionId = wfDefId;
                    FWfWorkflowDef fWfWorkflowDef = WFRepository<FWfWorkflowDef>.GetEntity(wfDefId);
                    StateDefinitionId = fWfWorkflowDef.InitialState.WfStateDefId;
                }
            }
            AuthenticationResult authenticationResult = new AuthenticationResult(DebugMode, LoginId, UserName, WorkflowDefinitionId, StateDefinitionId);
            if(authenticationResult.AuthoList==null)
            {
                authenticationResult.AuthoList = new AuthorizationList(LoginId, WorkflowDefinitionId, StateDefinitionId);
            }
            return authenticationResult;
        }

        
        [HttpGet]
        [Route("api/GetAuthoList")]
        public bool GetAuthoList()
        {
            return true;
        }
        
    }
}