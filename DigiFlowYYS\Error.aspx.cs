﻿using Digiturk.Workflow.Digiflow.WebCore;
using System;
using System.Web.UI;

public partial class Error : BasePage
{
    /// <summary>
    /// Her<PERSON>i bir <PERSON> hata aldığında yönlendirilecek ekran
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            if (Session[SessionErrorTitleVariable] != null)
            {
                lblTitle.Text = Session[SessionErrorTitleVariable].ToString();
                lblError.Text = Session[SessionErrorMessageVariable].ToString();
                if (Session[SessionExceptionInstanceVariable] != null)
                {
                    lblContent.Text = Session[SessionExceptionInstanceVariable].ToString();
                }
                else
                {
                    lblContent.Text = "";
                }
            }
            else
            {
                Response.Write(Server.GetLastError().ToString());
                Response.End();
            }
        }
    }
}