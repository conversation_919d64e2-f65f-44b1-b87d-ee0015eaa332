﻿using System;
using System.DirectoryServices;
using System.Security.Principal;
using System.Web;

public partial class SID : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        string icerik = GetSid(this.Page.User.Identity.Name);
    }

    public string GetSid(string strLogin)
    {
        string str = string.Empty;
        //Parse the string to check if domain name is present.
        int idx = strLogin.IndexOf('\\');
        if (idx == -1)
        {
            idx = strLogin.IndexOf('@');
        }
        string strDomain;
        string strName;
        if (idx != -1)
        {
            strDomain = strLogin.Substring(0, idx);
            strName = strLogin.Substring(idx + 1);
        }
        else
        {
            strDomain = Environment.MachineName;
            strName = strLogin;
        }
        try
        {
            Response.Write(strName);
            Response.Write("<br>");
            Response.Write(strDomain);
            DirectoryEntry usr = new DirectoryEntry();
            Response.Write("<br>");
            //Response.Write("Geldi-1");
            usr = GetDirectoryEntry("domainuser", strName, strDomain, "LOCAL");
            Response.Write("<br>");
            //Response.Write("Geldi-2");
            string sid = new SecurityIdentifier((byte[])usr.Properties["objectSid"][0], 0).Value;
            Response.Write("<br>");
            //Response.Write("Geldi-3");
            Response.Write(sid);
            str = sid;
        }
        catch (Exception ex)
        {
            throw ex;
            ////str = string.Empty;
            //str = ex.Message;
        }
        return str;
    }

    public SecurityIdentifier SIDX
    {
        get
        {
            WindowsIdentity identity = null;
            if (HttpContext.Current == null)
            {
                identity = WindowsIdentity.GetCurrent();
            }
            else
            {
                identity = HttpContext.Current.User.Identity as WindowsIdentity;
            }
            return identity.User;
        }
    }

    public DirectoryEntry GetDirectoryEntry(string objectClass, string objectName, String DC1, String DC2)
    {
        DirectoryEntry entry = new DirectoryEntry("LDAP://DC=" + DC1 + ", DC=" + DC2);
        //Response.Write("Geldi-1.1");
        DirectorySearcher searcher = new DirectorySearcher(entry);
        switch (objectClass)
        {
            case "user":
                //Response.Write("Geldi-1.2");
                searcher.Filter = "(&(objectClass=user)(|(cn=" + objectName + ")(displayName=" + objectName + ")(sAMAccountName=" + objectName + ")))";
                break;

            case "group":
                //Response.Write("Geldi-1.3");
                searcher.Filter = "(&(objectClass=group)(|(cn=" + objectName + ")(dn=" + objectName + ")))";
                break;

            case "computer":
                //Response.Write("Geldi-1.4");
                searcher.Filter = "(&(objectClass=computer)(|(cn=" + objectName + ")))";
                break;

            case "domainuser":
                //Response.Write("Geldi-1.5");
                searcher.Filter = "(&(objectClass=user)(|(sAMAccountName=" + objectName + ")))";
                break;
        }
        //Response.Write("Geldi-1.6");
        SearchResult result = searcher.FindOne();
        //Response.Write("Geldi-1.7");
        if (result != null)
        {
            //Response.Write("Geldi-1.8");
            DirectoryEntry directoryObject = result.GetDirectoryEntry();
            //Response.Write("Geldi-1.9");
            entry.Close();
            //Response.Write("Geldi-1.10");
            entry.Dispose();
            //Response.Write("Geldi-1.11");
            searcher.Dispose();
            //Response.Write("Geldi-1.12");
            return directoryObject;
            //Response.Write("Geldi-1.13");
        }
        else
            //Response.Write("Geldi-1.14");
            throw new NullReferenceException(objectClass + " şemasında belirtilen kayıt bulunamadı");
    }
}