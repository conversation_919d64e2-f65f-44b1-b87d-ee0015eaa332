﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site1.Master" AutoEventWireup="true" CodeBehind="WorkflowRules.aspx.cs" Inherits="DigiflowYYS_Yeni.WorkflowRules" %>
<%@ MasterType VirtualPath="~/Site1.Master" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td align="left" style="padding: 5px" valign="middle" width="100%">
                <strong>Akış Listesi</strong>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="middle" width="100%">
                
                <dx:ASPxComboBox ID="WorkfowListASPxComboBox" runat="server" OnSelectedIndexChanged="WorkfowListASPxComboBox_SelectedIndexChanged"
                    Width="250px" AutoPostBack="True">
                </dx:ASPxComboBox>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="middle" width="100%">&nbsp;
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="middle" width="100%">
                <dx:ASPxGridView ID="WorkFlowRulesGridView" runat="server" AutoGenerateColumns="False"
                    Width="100%" KeyFieldName="Id" OnRowDeleting="WorkFlowRulesGridView_RowDeleting"
                    OnRowCommand="WorkFlowRulesGridView_RowCommand">
                    <Columns>
                        <dx:gridviewdatatextcolumn Caption="Adım" VisibleIndex="10" FieldName="State">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Kim" VisibleIndex="20" FieldName="Source">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="İşlem" VisibleIndex="30" FieldName="Action">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Hedef" VisibleIndex="40" FieldName="ToGroup">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Aktif" VisibleIndex="50" FieldName="Active">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:GridViewDataHyperLinkColumn Caption="Düzenle" FieldName="Id" VisibleIndex="60">
                            <PropertiesHyperLinkEdit NavigateUrlFormatString="AddUpdateWorkflowRule.aspx?RuleId={0}"
                                TextFormatString="Düzenle" Text="Düzenle">
                            </PropertiesHyperLinkEdit>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataHyperLinkColumn>
                        <%--    <dx:GridViewCommandColumn Caption="Sil" VisibleIndex="8">
                            <DeleteButton Text="Sil" Visible="True">
                            </DeleteButton>
                            <ClearFilterButton Visible="True">
                            </ClearFilterButton>
                              <CellStyle HorizontalAlign="Center">
                              </CellStyle>
                        </dx:GridViewCommandColumn>--%>
                        <dx:GridViewDataButtonEditColumn  Caption="Sil" VisibleIndex="70">
                            <DataItemTemplate>
                                <dx:aspxbutton ID="DeleteButton" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                    <ClientSideEvents Click="function(s, e) {e.processOnServer = confirm('Kuralı silmek istediğinize emin misiniz?');}" />
                                </dx:aspxbutton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn >
                        <dx:gridviewdatatextcolumn Caption="Sıra No" VisibleIndex="0" Width="40px">
                            <DataItemTemplate>
                                <%# Container.ItemIndex + 1 %>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterRowMenu="True" ShowFilterBar="Auto" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Kuralı silmek istediğinize emin misiniz?" />
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
            </td>
        </tr>
    </table>
</asp:Content>