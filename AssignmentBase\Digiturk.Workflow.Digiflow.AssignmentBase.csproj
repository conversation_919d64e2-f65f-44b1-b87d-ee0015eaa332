﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{458CFA6A-3061-4451-BFF2-71248EE9BD5A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Digiturk.Workflow.Digiflow.AssignmentBase</RootNamespace>
    <AssemblyName>Digiturk.Workflow.Digiflow.AssignmentBase</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Digiturk.Workflow.Common">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Common.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Actions">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Actions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Digiturk.Workflow.DigiFlow.Framework, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.DigiFlow.Framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.GenericMailHelper">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.GenericMailHelper.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Digiturk.Workflow.Engine">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Engine.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Repository">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Repository.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AssignmentBase.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>COPY /Y "$(TargetPath)" \\dtl1iis3\Deployment</PostBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PreBuildEvent>if exist "$(TargetPath).locked" del "$(TargetPath).locked" if exist "$(TargetPath)" if not exist "$(TargetPath).locked" move "$(TargetPath)" "$(TargetPath).locked"
attrib -r c:\TFS\DigiflowPM\*.* /s</PreBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>