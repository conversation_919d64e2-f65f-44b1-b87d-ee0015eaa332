﻿using Digiturk.Workflow.Digiflow.DataAccessLayer;
using System.Collections.Generic;
using System.Data;

/// <summary>
/// Summary description for MonitoringWorkflowAdminHelper
/// </summary>
public class MonitoringWorkflowAdminHelper
{
    public MonitoringWorkflowAdminHelper()
    {
        //
        // TODO: Add constructor logic here
        //

        /*	insert into framework.f_wf_assignment a
         *   (WF_ASSIGNMENT_ID,WF_ASSIGNMENT_TYPE_CD,WF_ASSIGNED_TYPE_CD,ASSIGNMENT_OWNER_REF_ID,ASSIGNED_OWNER_REF_ID,IS_DEF_ASSIGNMENT,DENY)
            •	values
            •	(443351,'WFVIEW','LOGIN',46134,1519725,0,0);  */
        /*
           1 - <PERSON>ş akışı ID girildiğinde;
         * 2 - insert cümlesi oluşturulaiblir
         * 3 - WF_ASSIGNMENT_ID yi sequence ten almak (select SQ_F_WF_ASSIGNMENT.nextval from dual;)
         * 4 - WF_ASSIGNMENT_TYPE_CD sabit alan = WFVIEW
         * 5 - WF_ASSIGNED_TYPE_CD = LOGIN
         * 6 - ASSIGNMENT_OWNER_REF_ID = wf instance id (görüntülemek istenen akışın id si) (formdan alıyoruz)
         * 7 - ASSIGNED_OWNER_REF_ID  = kişinin loginid si (formdan alıyoruz)
         * 8-  IS_DEF_ASSIGNMENT = 0
         * 9 - DENY = 0
        */
    }

    /// <summary>
    /// veritabanına insert işlemi gerçekleştirilir.
    /// </summary>
    /// <param name="ASSIGNMENT_OWNER_REF_ID"></param>
    /// <param name="ASSIGNED_OWNER_REF_ID"></param>
    public static void InsertTable(long ASSIGNMENT_OWNER_REF_ID, long ASSIGNED_OWNER_REF_ID)
    {
        string query = @"INSERT INTO FRAMEWORK.F_WF_ASSIGNMENT A(
          A.WF_ASSIGNMENT_ID,
          A.WF_ASSIGNMENT_TYPE_CD,
          A.WF_ASSIGNED_TYPE_CD,
          A.ASSIGNMENT_OWNER_REF_ID,
          A.ASSIGNED_OWNER_REF_ID,
          A.IS_DEF_ASSIGNMENT,
          A. DENY
              )
          VALUES
             (
          SQ_F_WF_ASSIGNMENT.nextval,
          'WFVIEW',
          'LOGIN',
           :V_ASSIGNMENT_OWNER_REF_ID,
           :V_ASSIGNED_OWNER_REF_ID,
           0,
           0)";

        List<CustomParameterList> CustomList = new List<CustomParameterList>();
        CustomList.Add(new CustomParameterList("V_ASSIGNMENT_OWNER_REF_ID", ASSIGNMENT_OWNER_REF_ID));
        CustomList.Add(new CustomParameterList("V_ASSIGNED_OWNER_REF_ID", ASSIGNED_OWNER_REF_ID));
        Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.ExecuteQuery2("FrameworkConnection", query, CustomList);
        //query = null;
        //CustomList = null;
    }

    /// <summary>
    /// Seçilen kişiye ve iş akışına ait(started veya completed olup rejected haricindeki) ilgili tüm kayıtlar getirilir
    /// </summary>
    /// <param name="WORKFLOW_DEF_ID"></param>
    /// <param name="PERSONELLOGINID"></param>
    /// <returns></returns>
    public static DataTable GetWorkflowInsanceId(long WORKFLOW_DEF_ID, long PERSONELLOGINID)
    {
        string query = @"SELECT WF_WORKFLOW_INSTANCE_ID FROM DT_WORKFLOW.VW_REPORT_MASTER WHERE
                      WF_WORKFLOW_DEF_ID =:WF_WORKFLOW_DEF_ID
                      AND PERSONELLOGINID =:PERSONELLOGINID
                      AND
                      (WF_WORKFLOW_STATUS_TYPE_CD = 'STARTED'
                      OR
                      (WF_WORKFLOW_STATUS_TYPE_CD = 'COMPLETED' AND
                      WF_WORKFLOW_HISTORY_TYPE_CD <> 'REJECTED'))";

        List<CustomParameterList> CustomList = new List<CustomParameterList>();
        CustomList.Add(new CustomParameterList("WF_WORKFLOW_DEF_ID", WORKFLOW_DEF_ID));
        CustomList.Add(new CustomParameterList("PERSONELLOGINID", PERSONELLOGINID));
        return Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable2("FrameworkConnection", query, CustomList);
    }
}