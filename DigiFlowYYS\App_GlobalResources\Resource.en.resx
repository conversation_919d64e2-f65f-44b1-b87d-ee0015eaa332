﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="izin_admin" xml:space="preserve">
    <value>Leave Request flow admins are , CİHAN BAYRAKTAR,NUR BAŞEĞMEZER,ŞENİZ ÖZYAPAR</value>
  </data>
  <data name="izin_bakiye" xml:space="preserve">
    <value>Total leave days available:</value>
  </data>
  <data name="izin_basla" xml:space="preserve">
    <value>Leave date</value>
  </data>
  <data name="izin_bitis" xml:space="preserve">
    <value>Leave Return Date</value>
  </data>
  <data name="izin_delegasyon" xml:space="preserve">
    <value>Permit process that requires my approval as I do not want to delegate any flow</value>
  </data>
  <data name="izin_delege" xml:space="preserve">
    <value />
  </data>
  <data name="izin_delege_form" xml:space="preserve">
    <value>Click Here</value>
  </data>
  <data name="izin_delege_uyari" xml:space="preserve">
    <value>If you do not choose any person , there will be disruptions in business processes</value>
  </data>
  <data name="izin_gun" xml:space="preserve">
    <value>Day</value>
  </data>
  <data name="izin_nedeni" xml:space="preserve">
    <value>Leave reason</value>
  </data>
  <data name="izin_sure" xml:space="preserve">
    <value>Requested leave days</value>
  </data>
  <data name="izin_turu" xml:space="preserve">
    <value>Leave type</value>
  </data>
  <data name="izin_yarim_gun" xml:space="preserve">
    <value>Half Day</value>
  </data>
  <data name="Main_aciklama" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Main_aciklama_duzeltme" xml:space="preserve">
    <value>Edit Description</value>
  </data>
  <data name="Main_Aciklama_Giriniz" xml:space="preserve">
    <value>Please input description</value>
  </data>
  <data name="Main_aciklama_oneri" xml:space="preserve">
    <value>Description&lt;br&gt;Proposal</value>
  </data>
  <data name="Main_askiya_al_aciklama" xml:space="preserve">
    <value>suspended until</value>
  </data>
  <data name="Main_Askıya_al" xml:space="preserve">
    <value>Suspend</value>
  </data>
  <data name="Main_Askıya_al_devam" xml:space="preserve">
    <value>Suspend / Resume</value>
  </data>
  <data name="Main_Delegasyon_Iptal" xml:space="preserve">
    <value>Cancel Delegation</value>
  </data>
  <data name="Main_Devam_Et" xml:space="preserve">
    <value>Resume</value>
  </data>
  <data name="Main_Geri_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to send back workflow ?','vG30')</value>
  </data>
  <data name="Main_Görüntüleme_Iptal" xml:space="preserve">
    <value>Cancel Monitoring</value>
  </data>
  <data name="Main_History" xml:space="preserve">
    <value>History</value>
    <comment>Ana Sayfada menülerdeki İşlem Geçmişi</comment>
  </data>
  <data name="Main_Inbox" xml:space="preserve">
    <value>Inbox</value>
    <comment>Ana Sayfada Menülerdeki Üzerimdeki İşlemler</comment>
  </data>
  <data name="Main_Iptal" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Main_iptal_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to cancel workflow ?','vG4')</value>
  </data>
  <data name="Main_islem" xml:space="preserve">
    <value>Operation</value>
  </data>
  <data name="Main_islem_listesi" xml:space="preserve">
    <value>Command List</value>
  </data>
  <data name="Main_Lutfen_Bekleyiniz" xml:space="preserve">
    <value>Please Wait</value>
  </data>
  <data name="Main_Onayla" xml:space="preserve">
    <value>Accept</value>
  </data>
  <data name="Main_Onay_Red" xml:space="preserve">
    <value>Accept / Reject</value>
  </data>
  <data name="Main_Onay_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to accept workflow ?','vG30')</value>
  </data>
  <data name="Main_Reddet" xml:space="preserve">
    <value>Reject</value>
  </data>
  <data name="Main_Red_Onay" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to reject workflow ?','vG31')</value>
  </data>
  <data name="Main_Seciniz" xml:space="preserve">
    <value>Choose</value>
  </data>
  <data name="Main_state" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="Main_Suspended" xml:space="preserve">
    <value>Suspended Inbox</value>
    <comment>Ana Sayfada Menülerdei Askıda Bekleyenler</comment>
  </data>
  <data name="Main_Talep_Olustur" xml:space="preserve">
    <value>Create Request</value>
  </data>
  <data name="Main_Talep_Olustur_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to create workflow ?','vG0')</value>
  </data>
  <data name="Main_tarih" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Main_user" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="Main_WF_Admin" xml:space="preserve">
    <value>Workflow Administration</value>
  </data>
  <data name="Main_Yonlendir" xml:space="preserve">
    <value>Forward</value>
  </data>
  <data name="Main_yonlendir_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to forward workflow ?','vG1')</value>
  </data>
  <data name="Main_yonlendir_soru_mobile" xml:space="preserve">
    <value>Are you sure want to forward workflow ?</value>
  </data>
  <data name="Main_Yoruma_Gonder" xml:space="preserve">
    <value>Post Comment</value>
  </data>
  <data name="main_yoruma_gonder_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to send a comment workflow ?','vG2')</value>
  </data>
  <data name="main_yorum_duzeltme" xml:space="preserve">
    <value>Edit Comment</value>
  </data>
  <data name="Main_Yorum_Ekle" xml:space="preserve">
    <value>Add Comment</value>
  </data>
  <data name="Main_yorum_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure  add comment on workflow ?','vG5')</value>
  </data>
  <data name="Alt_Birim1" xml:space="preserve">
    <value>Sub Team 1</value>
  </data>
  <data name="Alt_Birim2" xml:space="preserve">
    <value>Sub Team 2</value>
  </data>
  <data name="Alt_Birim3" xml:space="preserve">
    <value>Sub Team 3</value>
  </data>
  <data name="Alt_Birim4" xml:space="preserve">
    <value>Sub Team 4</value>
  </data>
  <data name="Alt_Birim5" xml:space="preserve">
    <value>Sub Team 5</value>
  </data>
  <data name="Main_Geri_al_soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to rollback workflow ?','vG6')</value>
  </data>
  <data name="Main_devam_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to resume workflow','vG1')</value>
  </data>
  <data name="Main_Suspend_Soru" xml:space="preserve">
    <value>javascript:return ConfirmSubmitValGroup('Are you sure want to suspend workflow?','vG3')</value>
  </data>
  <data name="izin_basla_uyari" xml:space="preserve">
    <value>Please Input Start Date</value>
  </data>
  <data name="izin_bitis_uyari" xml:space="preserve">
    <value>Please Input End Date</value>
  </data>
  <data name="izin_prosedur" xml:space="preserve">
    <value>Leave Procedure</value>
  </data>
  <data name="Main_Gorev_Personeli" xml:space="preserve">
    <value>Personnel assigned</value>
  </data>
  <data name="Main_tarih_format" xml:space="preserve">
    <value>Date format must be dd.mm.yyyy</value>
  </data>
  <data name="Main_tarih_format_aralik" xml:space="preserve">
    <value>The date must be greater than today's date.</value>
  </data>
  <data name="Main_Activity_Type" xml:space="preserve">
    <value>Activity Type</value>
  </data>
  <data name="Mobil_Talep_Sahibi" xml:space="preserve">
    <value>Request Owner</value>
  </data>
  <data name="izin_delegasyon_uyari" xml:space="preserve">
    <value>You have to have a delegation between specified dates in order to make this leave request. If you want to take care of all cases yourself on your return, please pick the red box below.</value>
  </data>
  <data name="main_grid_all" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="main_grid_back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="main_grid_first" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="main_grid_flow_aciklama" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="main_grid_flow_birim" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="main_grid_flow_forwarder" xml:space="preserve">
    <value>Forwarder</value>
  </data>
  <data name="main_grid_flow_name" xml:space="preserve">
    <value>Worfklow Name</value>
  </data>
  <data name="main_arama_sozcugu" xml:space="preserve">
    <value>Type keyword here...</value>
  </data>
  <data name="main_filtre" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="main_grid_flow_owner" xml:space="preserve">
    <value>Owner</value>
  </data>
  <data name="main_grid_flow_state" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="main_grid_flow_tarih" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="main_grid_flow_tutar" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="main_grid_group" xml:space="preserve">
    <value>Drag a column header here to group by that column</value>
  </data>
  <data name="main_grid_last" xml:space="preserve">
    <value>Last</value>
  </data>
  <data name="main_grid_next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="main_akislari_onayla" xml:space="preserve">
    <value>Approve Workflow(s)</value>
  </data>
  <data name="main_toplu_onay_bilgi" xml:space="preserve">
    <value />
  </data>
  <data name="main_comment_inbox" xml:space="preserve">
    <value>Comment Inbox</value>
  </data>
  <data name="main_delege_inbox" xml:space="preserve">
    <value>Delegate Inbox</value>
  </data>
  <data name="main_grid_clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="main_grid_devami" xml:space="preserve">
    <value>Continued on next page</value>
  </data>
  <data name="main_grid_no_row" xml:space="preserve">
    <value>There are no rows</value>
  </data>
  <data name="main_inbox_header" xml:space="preserve">
    <value>My Inbox</value>
  </data>
  <data name="main_grid_last_action" xml:space="preserve">
    <value>Last Action</value>
  </data>
  <data name="main_grid_last_modified" xml:space="preserve">
    <value>Last Modified By</value>
  </data>
  <data name="main_grid_atanan" xml:space="preserve">
    <value>Assigned</value>
  </data>
  <data name="main_grid_atlat" xml:space="preserve">
    <value>Bypass workflow</value>
  </data>
  <data name="main_grid_hatirlat" xml:space="preserve">
    <value>Remind</value>
  </data>
  <data name="main_grid_kopyala" xml:space="preserve">
    <value>Copy workflow</value>
  </data>
  <data name="main_grid_flow_action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="main_akis_bitir" xml:space="preserve">
    <value>End the flow</value>
  </data>
  <data name="main_akis_yoneticisi_bildirim" xml:space="preserve">
    <value>You are making process as a workflow manager</value>
  </data>
  <data name="main_askiya_alma_tarihiri_gir" xml:space="preserve">
    <value>Please enter the date of suspension.</value>
  </data>
  <data name="main_askiya_alma_uyari" xml:space="preserve">
    <value>Date of suspension for today can not be entered</value>
  </data>
  <data name="main_comment_bilgi" xml:space="preserve">
    <value>This demand has been sent for comment.</value>
  </data>
  <data name="main_emir_var_uyari" xml:space="preserve">
    <value>The system has pending orders for this flow. Please try again later for making process.</value>
  </data>
  <data name="main_emir_var_uyari1" xml:space="preserve">
    <value>The system has pending orders for this flow. Please refresh the screen (F5).</value>
  </data>
  <data name="main_finalize_bilgi" xml:space="preserve">
    <value>This demand has been terminated.</value>
  </data>
  <data name="main_forward_bilgi" xml:space="preserve">
    <value>This demand has been directed.</value>
  </data>
  <data name="main_gecerli_tarih_formati" xml:space="preserve">
    <value>Please enter available date format (dd.mm.yyyy).</value>
  </data>
  <data name="main_gecmis_askiya_alma" xml:space="preserve">
    <value>Date of suspension for the past can not be done</value>
  </data>
  <data name="main_geri_alindi_bilgi" xml:space="preserve">
    <value>Work is successfully revoked.</value>
  </data>
  <data name="main_IE_uyari" xml:space="preserve">
    <value>Please make process with Internet Explorer</value>
  </data>
  <data name="main_iptal_bilgi" xml:space="preserve">
    <value>This demand has been canceled successfully.</value>
  </data>
  <data name="main_iptal_bilgi1" xml:space="preserve">
    <value>This demand has been canceled.</value>
  </data>
  <data name="main_islem_hata_bilgi" xml:space="preserve">
    <value>An error was encountered. Please try again.</value>
  </data>
  <data name="main_lutfen_yorum_gririniz" xml:space="preserve">
    <value>Error - Please enter comment.</value>
  </data>
  <data name="main_reddet_bilgi" xml:space="preserve">
    <value>This demand has been rejected.</value>
  </data>
  <data name="main_red_karakter_siniri" xml:space="preserve">
    <value>Maximum 200 characters can be entered in description area for decline.</value>
  </data>
  <data name="main_red_soru" xml:space="preserve">
    <value>Are you sure for refusing workflow?</value>
  </data>
  <data name="main_resume_bilgi" xml:space="preserve">
    <value>Workflow is successfully resumed.</value>
  </data>
  <data name="main_resume_bilgi1" xml:space="preserve">
    <value>This demand has been continiued.</value>
  </data>
  <data name="main_sendback_bilgi" xml:space="preserve">
    <value>This demand has been withdrawn</value>
  </data>
  <data name="main_sendowner_bilgi" xml:space="preserve">
    <value>This demand has been sent back the person who request the workflow.</value>
  </data>
  <data name="main_state_uyusmuyor_uyari" xml:space="preserve">
    <value>This flow is no longer in step you take. Please refresh the screen (F5).</value>
  </data>
  <data name="main_suspend_bilgi" xml:space="preserve">
    <value>This demand has been put on standby mode.</value>
  </data>
  <data name="main_talepolustu_bilgi" xml:space="preserve">
    <value>Your demand is successfully created.</value>
  </data>
  <data name="main_Tarihformat_bilgi" xml:space="preserve">
    <value>Date format must be like this format: dd.mm.yyyy</value>
  </data>
  <data name="main_toplam_saniye_bilgi" xml:space="preserve">
    <value>This operation took total xxx seconds</value>
  </data>
  <data name="main_user_seciniz" xml:space="preserve">
    <value>Error - Please select the user who the flow will be redirected.</value>
  </data>
  <data name="main_yazdir_kapat" xml:space="preserve">
    <value>Print and Close</value>
  </data>
  <data name="main_yorumekle_bilgi" xml:space="preserve">
    <value>The comment has been added to the demand.</value>
  </data>
  <data name="main_yorum_eklendi_bilgi" xml:space="preserve">
    <value>Your comment has been successfully saved.</value>
  </data>
  <data name="main_bekle_bilgi" xml:space="preserve">
    <value>This demand has been put on standby mode successfully.</value>
  </data>
  <data name="main_bilgi" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="main_comment_islem" xml:space="preserve">
    <value>This demand has been sent for comment.</value>
  </data>
  <data name="main_hata" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="inbox_akislar_onaylandi" xml:space="preserve">
    <value>Workflows accepted</value>
  </data>
  <data name="inbox_akis_yok" xml:space="preserve">
    <value>There is no checked workflows.</value>
  </data>
  <data name="inbox_hata_olustu" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="topluonaysoru" xml:space="preserve">
    <value>javascript:return confirm('Are you sure want to accept workflow(s) ?');</value>
  </data>
  <data name="Main_Geri_al" xml:space="preserve">
    <value>Rollback</value>
  </data>
  <data name="izin_kayit_bulunamadi" xml:space="preserve">
    <value>There is no annual leave information data, please contact Human Resources Department</value>
  </data>
  <data name="main_akis_yoneticisi" xml:space="preserve">
    <value>Workflow Admin</value>
  </data>
  <data name="main_guncelle_don" xml:space="preserve">
    <value>Back to Update Page</value>
  </data>
  <data name="main_kapat" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="main_basla_tarih" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="main_bitis_tarih" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="main_combo_aktif_delegasyonlar" xml:space="preserve">
    <value>Active Delegations</value>
  </data>
  <data name="main_combo_devam_eden" xml:space="preserve">
    <value>Continued workflows</value>
  </data>
  <data name="main_combo_durdurulan" xml:space="preserve">
    <value>Suspended Workflows</value>
  </data>
  <data name="main_combo_gecmis_delegasyonlar" xml:space="preserve">
    <value>Completed Delegations</value>
  </data>
  <data name="main_combo_iptal" xml:space="preserve">
    <value>Cancelled Workflows</value>
  </data>
  <data name="main_combo_onaylanan" xml:space="preserve">
    <value>Accepted Workflows</value>
  </data>
  <data name="main_combo_reddedilen" xml:space="preserve">
    <value>Rejected Workflows</value>
  </data>
  <data name="main_combo_tamamlanan" xml:space="preserve">
    <value>Completed Workflows</value>
  </data>
  <data name="main_combo_tum" xml:space="preserve">
    <value>All workflows</value>
  </data>
  <data name="main_target" xml:space="preserve">
    <value>Target User</value>
  </data>
  <data name="Main_edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Main_goruntule" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="main_goruntuleme_turu" xml:space="preserve">
    <value>Monitoring Type</value>
  </data>
  <data name="main_grid_flow_no" xml:space="preserve">
    <value>Workflow ID</value>
  </data>
  <data name="Main_sonlandir_delegasyon" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="main_4000_sinir" xml:space="preserve">
    <value>Maximum 4000 characters can be entered in description area</value>
  </data>
  <data name="main_delege_uyari" xml:space="preserve">
    <value>You can not operate on this flow because your authority is delegated</value>
  </data>
  <data name="HistoryState_1FinansOnayıYurtDışı" xml:space="preserve">
    <value>1. Finans Onayı Yurt Dışı</value>
  </data>
  <data name="HistoryState_1FinansOnayıYurtiçi" xml:space="preserve">
    <value>1. Finans Onayı Yurtiçi</value>
  </data>
  <data name="HistoryState_1KontratYönetimiOnayı" xml:space="preserve">
    <value>1. Kontrat Yönetimi Onayı</value>
  </data>
  <data name="HistoryState_2FinansOnayıYurtDışı" xml:space="preserve">
    <value>2. Finans Onayı Yurt Dışı</value>
  </data>
  <data name="HistoryState_2FinansOnayıYurtİçi" xml:space="preserve">
    <value>2. Finans Onayı Yurt İçi</value>
  </data>
  <data name="HistoryState_2GirişAdımı" xml:space="preserve">
    <value>2.Giriş Adımı</value>
  </data>
  <data name="HistoryState_2KontratYönetimiOnayı" xml:space="preserve">
    <value>2. Kontrat Yönetimi Onayı</value>
  </data>
  <data name="HistoryState_2ÜstYöneticisiOnayı" xml:space="preserve">
    <value>2. Üst Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_3KontratYönetimiOnayı" xml:space="preserve">
    <value>3. Kontrat Yönetimi Onayı</value>
  </data>
  <data name="HistoryState_AccOluşturma" xml:space="preserve">
    <value>Acc Oluşturma</value>
  </data>
  <data name="HistoryState_AkışAtlatmaTalebi" xml:space="preserve">
    <value>Akış Atlatma Talebi</value>
  </data>
  <data name="HistoryState_AnalizAşaması" xml:space="preserve">
    <value>Analiz Aşaması</value>
  </data>
  <data name="HistoryState_AraDepartmanOnayı" xml:space="preserve">
    <value>Ara Departman Onayı</value>
  </data>
  <data name="HistoryState_AraDepartmanYöneticiOnayı" xml:space="preserve">
    <value>Ara Departman Yönetici Onayı</value>
  </data>
  <data name="HistoryState_AraDepartmanYöneticisi" xml:space="preserve">
    <value>Ara Departman Yöneticisi</value>
  </data>
  <data name="HistoryState_AraDepartmanYöneticisiOnayı" xml:space="preserve">
    <value>Ara Departman Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_AraOnay" xml:space="preserve">
    <value>Ara Onay</value>
  </data>
  <data name="HistoryState_AraOnay1" xml:space="preserve">
    <value>Ara Onay 1</value>
  </data>
  <data name="HistoryState_AraOnay2" xml:space="preserve">
    <value>Ara Onay 2</value>
  </data>
  <data name="HistoryState_AraState" xml:space="preserve">
    <value>Ara State</value>
  </data>
  <data name="HistoryState_AraçAdımı" xml:space="preserve">
    <value>Araç Adımı</value>
  </data>
  <data name="HistoryState_AvansKRMYetkiliOnayı" xml:space="preserve">
    <value>Avans KRM Yetkili Onayı</value>
  </data>
  <data name="HistoryState_AvansYetkiliOnayı" xml:space="preserve">
    <value>Avans Yetkili Onayı</value>
  </data>
  <data name="HistoryState_AvansYetkiOnayı" xml:space="preserve">
    <value>Avans Yetki Onayı</value>
  </data>
  <data name="HistoryState_BarterYetkiliOnayı" xml:space="preserve">
    <value>Barter Yetkili Onayı</value>
  </data>
  <data name="HistoryState_BatıUlusalBölgeMüdürüOnayı" xml:space="preserve">
    <value>Batı Ulusal Bölge Müdürü Onayı</value>
  </data>
  <data name="HistoryState_BatıUlusalBölgeSatış" xml:space="preserve">
    <value>Batı Ulusal Bölge Satış</value>
  </data>
  <data name="HistoryState_BirimMüdürüOnayı" xml:space="preserve">
    <value>Birim Müdürü Onayı</value>
  </data>
  <data name="HistoryState_BirimOnayı" xml:space="preserve">
    <value>Birim Onayı</value>
  </data>
  <data name="HistoryState_BirimYöneticiOnayı" xml:space="preserve">
    <value>Birim Yönetici Onayı</value>
  </data>
  <data name="HistoryState_BirimYöneticisi" xml:space="preserve">
    <value>Birim Yöneticisi</value>
  </data>
  <data name="HistoryState_BirimYöneticisiOnay" xml:space="preserve">
    <value>Birim Yöneticisi Onay</value>
  </data>
  <data name="HistoryState_BirimYöneticisiOnayı" xml:space="preserve">
    <value>Birim Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_BroadcastSoftwareTanımlamaveOnayı" xml:space="preserve">
    <value>Broadcast Software Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_BölgeMüdürüOnayı" xml:space="preserve">
    <value>Bölge Müdürü Onayı</value>
  </data>
  <data name="HistoryState_BölgeYöneticisiOnayı" xml:space="preserve">
    <value>Bölge Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_BölümYöneticiOnayı" xml:space="preserve">
    <value>Bölüm Yönetici Onayı</value>
  </data>
  <data name="HistoryState_BölümYöneticisi" xml:space="preserve">
    <value>Bölüm Yöneticisi</value>
  </data>
  <data name="HistoryState_BölümYöneticisiOnayı" xml:space="preserve">
    <value>Bölüm Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_BütçeYöneticisi" xml:space="preserve">
    <value>Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_ConditionalAccessTanımlamaveOnayı" xml:space="preserve">
    <value>Conditional Access Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_CRMGrubuOnayı" xml:space="preserve">
    <value>CRM Grubu Onayı</value>
  </data>
  <data name="HistoryState_DelegasyonTalebiCevap" xml:space="preserve">
    <value>Delegasyon Talebi Cevap</value>
  </data>
  <data name="HistoryState_DelegasyonTalep" xml:space="preserve">
    <value>Delegasyon Talep</value>
  </data>
  <data name="HistoryState_DepartmanOnayı" xml:space="preserve">
    <value>Departman Onayı</value>
  </data>
  <data name="HistoryState_DepartmanYöneticiOnayı" xml:space="preserve">
    <value>Departman Yönetici Onayı</value>
  </data>
  <data name="HistoryState_DepartmanYöneticisi" xml:space="preserve">
    <value>Departman Yöneticisi</value>
  </data>
  <data name="HistoryState_DepartmanYöneticisiOnayı" xml:space="preserve">
    <value>Departman Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_DeğerlendirmeTamamlandı" xml:space="preserve">
    <value>Değerlendirme Tamamlandı</value>
  </data>
  <data name="HistoryState_DigiturkFinansOnayı" xml:space="preserve">
    <value>Digiturk Finans Onayı</value>
  </data>
  <data name="HistoryState_DiğerBilgiToplama" xml:space="preserve">
    <value>Diğer Bilgi Toplama</value>
  </data>
  <data name="HistoryState_DoğuUlusalBölgeMüdürüOnayı" xml:space="preserve">
    <value>Doğu Ulusal Bölge Müdürü Onayı</value>
  </data>
  <data name="HistoryState_DoğuUlusalBölgeSatış" xml:space="preserve">
    <value>Doğu Ulusal Bölge Satış</value>
  </data>
  <data name="HistoryState_DtsOnayı" xml:space="preserve">
    <value>Dts Onayı</value>
  </data>
  <data name="HistoryState_DTSReadyOnayı" xml:space="preserve">
    <value>DTS Ready Onayı</value>
  </data>
  <data name="HistoryState_DublajArşivTanımlamveOnayı" xml:space="preserve">
    <value>Dublaj Arşiv Tanımlam ve Onayı</value>
  </data>
  <data name="HistoryState_Düzeltme" xml:space="preserve">
    <value>Düzeltme</value>
  </data>
  <data name="HistoryState_EgitimSonrasıDeğerlendirmeSüreci" xml:space="preserve">
    <value>Egitim Sonrası Değerlendirme Süreci</value>
  </data>
  <data name="HistoryState_EkipYöneticisiOnayı" xml:space="preserve">
    <value>Ekip Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_EkSatınalmaSahibiOnayı" xml:space="preserve">
    <value>Ek Satınalma Sahibi Onayı</value>
  </data>
  <data name="HistoryState_EkİhtiyaçlarınDüzenlenmesi" xml:space="preserve">
    <value>Ek İhtiyaçların Düzenlenmesi</value>
  </data>
  <data name="HistoryState_FaturaDüzenleme" xml:space="preserve">
    <value>Fatura Düzenleme</value>
  </data>
  <data name="HistoryState_FaturaDüzenlemeJüpiter" xml:space="preserve">
    <value>Fatura Düzenleme(Jüpiter)</value>
  </data>
  <data name="HistoryState_FaturaDüzenlemePluton" xml:space="preserve">
    <value>Fatura Düzenleme(Pluton)</value>
  </data>
  <data name="HistoryState_FaturaDüzenlendi" xml:space="preserve">
    <value>Fatura Düzenlendi</value>
  </data>
  <data name="HistoryState_FinansBütçe" xml:space="preserve">
    <value>Finans Bütçe</value>
  </data>
  <data name="HistoryState_FinansBütçeYöneticisi" xml:space="preserve">
    <value>Finans Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_FinansGenelMüdürYardımcısıOnayı" xml:space="preserve">
    <value>Finans Genel Müdür Yardımcısı Onayı</value>
  </data>
  <data name="HistoryState_FinansGMY" xml:space="preserve">
    <value>Finans GMY</value>
  </data>
  <data name="HistoryState_FinansGMYOnayı" xml:space="preserve">
    <value>Finans GMY Onayı</value>
  </data>
  <data name="HistoryState_FinansKontrolOnayı" xml:space="preserve">
    <value>Finans Kontrol Onayı</value>
  </data>
  <data name="HistoryState_FinansKontrolüOnayı" xml:space="preserve">
    <value>Finans Kontrolü Onayı</value>
  </data>
  <data name="HistoryState_FinansYetkiliOnayı" xml:space="preserve">
    <value>Finans Yetkili Onayı</value>
  </data>
  <data name="HistoryState_FirmaBeklemeOnay" xml:space="preserve">
    <value>Firma Bekleme Onay</value>
  </data>
  <data name="HistoryState_FirmaOnayı" xml:space="preserve">
    <value>Firma Onayı</value>
  </data>
  <data name="HistoryState_FormKontrolveOnayı" xml:space="preserve">
    <value>Form Kontrol ve Onayı</value>
  </data>
  <data name="HistoryState_GeliştirmeAşaması" xml:space="preserve">
    <value>Geliştirme Aşaması</value>
  </data>
  <data name="HistoryState_GenelMüdürOnayı" xml:space="preserve">
    <value>Genel Müdür Onayı</value>
  </data>
  <data name="HistoryState_GenelMüdürYardımcısıOnayı" xml:space="preserve">
    <value>Genel Müdür Yardımcısı Onayı</value>
  </data>
  <data name="HistoryState_GMOnayı" xml:space="preserve">
    <value>GM Onayı</value>
  </data>
  <data name="HistoryState_GMonayı1" xml:space="preserve">
    <value>GM onayı</value>
  </data>
  <data name="HistoryState_GMYOnayı" xml:space="preserve">
    <value>GMY Onayı</value>
  </data>
  <data name="HistoryState_GSMTalebiTalepOluşturma" xml:space="preserve">
    <value>GSM Talebi- Talep Oluşturma</value>
  </data>
  <data name="HistoryState_GörüntülemeTalebi" xml:space="preserve">
    <value>Görüntüleme Talebi</value>
  </data>
  <data name="HistoryState_HazineOnayı" xml:space="preserve">
    <value>Hazine Onayı</value>
  </data>
  <data name="HistoryState_Hazineonayı1" xml:space="preserve">
    <value>Hazine onayı</value>
  </data>
  <data name="HistoryState_HazineOnayı2" xml:space="preserve">
    <value>Hazine Onayı 2</value>
  </data>
  <data name="HistoryState_HazineÜstOnayı" xml:space="preserve">
    <value>Hazine Üst Onayı</value>
  </data>
  <data name="HistoryState_HeadEndBütçeDepartmanYöneticisi" xml:space="preserve">
    <value>Head End Bütçe Departman Yöneticisi</value>
  </data>
  <data name="HistoryState_HeadEndBütçeGMY" xml:space="preserve">
    <value>Head End Bütçe GMY</value>
  </data>
  <data name="HistoryState_HeadEndBütçeYöneticisi" xml:space="preserve">
    <value>Head End Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_HukukDepartmanıHukukuÇalışanı" xml:space="preserve">
    <value>Hukuk Departmanı - Hukuku Çalışanı</value>
  </data>
  <data name="HistoryState_HukukGMYOnayı" xml:space="preserve">
    <value>Hukuk GMY Onayı</value>
  </data>
  <data name="HistoryState_HukukMüdürüOnayı" xml:space="preserve">
    <value>Hukuk Müdürü Onayı</value>
  </data>
  <data name="HistoryState_HukukMüdürüYönlendirme" xml:space="preserve">
    <value>Hukuk Müdürü Yönlendirme</value>
  </data>
  <data name="HistoryState_IKBölümYöneticisiOnayı" xml:space="preserve">
    <value>IK Bölüm Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_IKOnayı" xml:space="preserve">
    <value>IK Onayı</value>
  </data>
  <data name="HistoryState_IKOperasyonOnayı" xml:space="preserve">
    <value>IK Operasyon Onayı</value>
  </data>
  <data name="HistoryState_IKOperasyonOnayı1" xml:space="preserve">
    <value>IK-Operasyon Onayı</value>
  </data>
  <data name="HistoryState_IKYöneticisiOnayı" xml:space="preserve">
    <value>IK Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_IlgiliOnayı" xml:space="preserve">
    <value>Ilgili Onayı</value>
  </data>
  <data name="HistoryState_IlkOnayGrubuOnayi" xml:space="preserve">
    <value>Ilk Onay Grubu Onayi</value>
  </data>
  <data name="HistoryState_ISOnayı" xml:space="preserve">
    <value>IS Onayı</value>
  </data>
  <data name="HistoryState_KanalTipiSporise" xml:space="preserve">
    <value>Kanal Tipi Spor ise</value>
  </data>
  <data name="HistoryState_KanalTipiUlusalise" xml:space="preserve">
    <value>Kanal Tipi Ulusal ise</value>
  </data>
  <data name="HistoryState_KartvizitBasımAdımı" xml:space="preserve">
    <value>Kartvizit Basım Adımı</value>
  </data>
  <data name="HistoryState_KontrolGrubuOnayı" xml:space="preserve">
    <value>Kontrol Grubu Onayı</value>
  </data>
  <data name="HistoryState_KontrolOnayı" xml:space="preserve">
    <value>Kontrol Onayı</value>
  </data>
  <data name="HistoryState_KRMOperasyonOnayı" xml:space="preserve">
    <value>KRM Operasyon Onayı</value>
  </data>
  <data name="HistoryState_KırtasiyeYetkilisi" xml:space="preserve">
    <value>Kırtasiye Yetkilisi</value>
  </data>
  <data name="HistoryState_LIGTVBUTCE1" xml:space="preserve">
    <value>LIG TV BUTCE-1</value>
  </data>
  <data name="HistoryState_LIGTVBUTCE2" xml:space="preserve">
    <value>LIG TV BUTCE-2</value>
  </data>
  <data name="HistoryState_LigTVOperasyonOnayı" xml:space="preserve">
    <value>Lig TV Operasyon Onayı</value>
  </data>
  <data name="HistoryState_LigTVYöneticisiOnayı" xml:space="preserve">
    <value>Lig TV Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_MaintenanceTanımlamaveOnayı" xml:space="preserve">
    <value>Maintenance Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_MaliyetveKıymetliEvraklarMuhasebeOnayı" xml:space="preserve">
    <value>Maliyet ve Kıymetli Evraklar Muhasebe Onayı</value>
  </data>
  <data name="HistoryState_MCRTanımlamaveOnayı" xml:space="preserve">
    <value>MCR Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_MuhasebeOnayı" xml:space="preserve">
    <value>Muhasebe Onayı</value>
  </data>
  <data name="HistoryState_OnayKontrol" xml:space="preserve">
    <value>Onay Kontrol</value>
  </data>
  <data name="HistoryState_OrganizasyonelGelişimOnayı" xml:space="preserve">
    <value>Organizasyonel Gelişim Onayı</value>
  </data>
  <data name="HistoryState_OrganizasyonelGelişimOnayı2" xml:space="preserve">
    <value>Organizasyonel Gelişim Onayı 2</value>
  </data>
  <data name="HistoryState_OrganizasyonelGelişimOnayı3" xml:space="preserve">
    <value>Organizasyonel Gelişim Onayı 3</value>
  </data>
  <data name="HistoryState_OrganizasyonelGelişimOnayı4" xml:space="preserve">
    <value>Organizasyonel Gelişim Onayı 4</value>
  </data>
  <data name="HistoryState_OtomatikOnay" xml:space="preserve">
    <value>Otomatik Onay</value>
  </data>
  <data name="HistoryState_PazarlamaDeğerlendirme" xml:space="preserve">
    <value>Pazarlama Değerlendirme</value>
  </data>
  <data name="HistoryState_PazarlamaGMYOnayı" xml:space="preserve">
    <value>Pazarlama GMY Onayı</value>
  </data>
  <data name="HistoryState_PersonelDüzeltme" xml:space="preserve">
    <value>Personel Düzeltme</value>
  </data>
  <data name="HistoryState_RaporKontrolAdımı" xml:space="preserve">
    <value>Rapor Kontrol Adımı</value>
  </data>
  <data name="HistoryState_RegülasyonOnayi" xml:space="preserve">
    <value>Regülasyon Onayi</value>
  </data>
  <data name="HistoryState_SatınalmaSahibiOnayı" xml:space="preserve">
    <value>Satınalma Sahibi Onayı</value>
  </data>
  <data name="HistoryState_SatışBütçeYöneticisi" xml:space="preserve">
    <value>Satış Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_SatışGenelMüdürOnayı" xml:space="preserve">
    <value>Satış Genel Müdür Onayı</value>
  </data>
  <data name="HistoryState_SatışYetkiliOnayı" xml:space="preserve">
    <value>Satış Yetkili Onayı</value>
  </data>
  <data name="HistoryState_SiparişAşaması" xml:space="preserve">
    <value>Sipariş Aşaması</value>
  </data>
  <data name="HistoryState_SistemOnayı" xml:space="preserve">
    <value>Sistem Onayı</value>
  </data>
  <data name="HistoryState_SRBCYetkilisiOnayı" xml:space="preserve">
    <value>SRBC Yetkilisi Onayı</value>
  </data>
  <data name="HistoryState_TakımYöneticiOnayı" xml:space="preserve">
    <value>Takım Yönetici Onayı</value>
  </data>
  <data name="HistoryState_TakımYöneticisiOnayı" xml:space="preserve">
    <value>Takım Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_TalepBaşlat" xml:space="preserve">
    <value>Talep Başlat</value>
  </data>
  <data name="HistoryState_TalepBaşlatma" xml:space="preserve">
    <value>Talep Başlatma</value>
  </data>
  <data name="HistoryState_TalepOluştur" xml:space="preserve">
    <value>Talep Oluştur</value>
  </data>
  <data name="HistoryState_TalepOluşturanaDüzeltme" xml:space="preserve">
    <value>Talep Oluşturana Düzeltme</value>
  </data>
  <data name="HistoryState_TalepOluşturanDüzeltme" xml:space="preserve">
    <value>Talep Oluşturan Düzeltme</value>
  </data>
  <data name="HistoryState_TalepOluşturma" xml:space="preserve">
    <value>Talep Oluşturma</value>
  </data>
  <data name="HistoryState_TalepOnay" xml:space="preserve">
    <value>Talep Onay</value>
  </data>
  <data name="HistoryState_TalepOnaylandı" xml:space="preserve">
    <value>Talep Onaylandı</value>
  </data>
  <data name="HistoryState_TalepOnaylandıXXX" xml:space="preserve">
    <value>Talep OnaylandıXXX</value>
  </data>
  <data name="HistoryState_TalepOnaySatınAlındı" xml:space="preserve">
    <value>Talep Onay - Satın Alındı</value>
  </data>
  <data name="HistoryState_TalepOtomatikOnay" xml:space="preserve">
    <value>Talep Otomatik Onay</value>
  </data>
  <data name="HistoryState_TalepRed" xml:space="preserve">
    <value>Talep Red</value>
  </data>
  <data name="HistoryState_TalepReddedildi" xml:space="preserve">
    <value>Talep Reddedildi</value>
  </data>
  <data name="HistoryState_TalepSahibiDüzeltme" xml:space="preserve">
    <value>Talep Sahibi Düzeltme</value>
  </data>
  <data name="HistoryState_TalepSahibiGirişOnayı" xml:space="preserve">
    <value>Talep Sahibi Giriş Onayı</value>
  </data>
  <data name="HistoryState_TalepSahibiGmyOnayı" xml:space="preserve">
    <value>Talep Sahibi Gmy Onayı</value>
  </data>
  <data name="HistoryState_TalepSahibiGMYOnayı1" xml:space="preserve">
    <value>Talep Sahibi GMY Onayı</value>
  </data>
  <data name="HistoryState_TalepSahibiOnayı" xml:space="preserve">
    <value>Talep Sahibi Onayı</value>
  </data>
  <data name="HistoryState_TeklifAşaması" xml:space="preserve">
    <value>Teklif Aşaması</value>
  </data>
  <data name="HistoryState_TeknikServisYöneticisiOnayı" xml:space="preserve">
    <value>Teknik Servis Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_TeknikServisYönlendirmeOnayı" xml:space="preserve">
    <value>Teknik Servis Yönlendirme Onayı</value>
  </data>
  <data name="HistoryState_TestAşaması" xml:space="preserve">
    <value>Test Aşaması</value>
  </data>
  <data name="HistoryState_TicariSatışOnayı" xml:space="preserve">
    <value>Ticari Satış Onayı</value>
  </data>
  <data name="HistoryState_TransmissionTanımlamaveOnayı1" xml:space="preserve">
    <value>Transmission Tanımlama ve Onayı -1-</value>
  </data>
  <data name="HistoryState_TransmissionTanımlamaveOnayı2" xml:space="preserve">
    <value>Transmission Tanımlama ve Onayı -2-</value>
  </data>
  <data name="HistoryState_TurkmaxBütçeYöneticisi" xml:space="preserve">
    <value>Turkmax Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_TURKMAXFinansOnayı" xml:space="preserve">
    <value>TURKMAX Finans Onayı</value>
  </data>
  <data name="HistoryState_TURKMAXOperasyonOnayı" xml:space="preserve">
    <value>TURKMAX Operasyon Onayı</value>
  </data>
  <data name="HistoryState_UlusalBölgeMüdürüOnayı" xml:space="preserve">
    <value>Ulusal Bölge Müdürü Onayı</value>
  </data>
  <data name="HistoryState_UlusalSatışGMYOnayı" xml:space="preserve">
    <value>Ulusal Satış GMY Onayı</value>
  </data>
  <data name="HistoryState_UlusalSatışYöneticiOnayı" xml:space="preserve">
    <value>Ulusal Satış Yönetici Onayı</value>
  </data>
  <data name="HistoryState_YardımMasası" xml:space="preserve">
    <value>Yardım Masası</value>
  </data>
  <data name="HistoryState_YardımMasası1Adım" xml:space="preserve">
    <value>Yardım Masası 1. Adım</value>
  </data>
  <data name="HistoryState_YardımMasası1AdımOnayı" xml:space="preserve">
    <value>Yardım Masası 1.Adım Onayı</value>
  </data>
  <data name="HistoryState_YardımMasası2Adım" xml:space="preserve">
    <value>Yardım Masası 2. Adım</value>
  </data>
  <data name="HistoryState_YetkiliGrubuOnayı" xml:space="preserve">
    <value>Yetkili Grubu Onayı</value>
  </data>
  <data name="HistoryState_YetkiliOnayı" xml:space="preserve">
    <value>Yetkili Onayı</value>
  </data>
  <data name="HistoryState_YetkiliOnayı1" xml:space="preserve">
    <value>Yetkili Onayı 1</value>
  </data>
  <data name="HistoryState_YetkiliOnayıKullanilmiyor" xml:space="preserve">
    <value>Yetkili Onayı (Kullanilmiyor)</value>
  </data>
  <data name="HistoryState_YetkiliOnayıYurtDışı" xml:space="preserve">
    <value>Yetkili Onayı Yurt Dışı</value>
  </data>
  <data name="HistoryState_YetkiliOnayıYurtİçi" xml:space="preserve">
    <value>Yetkili Onayı Yurt İçi</value>
  </data>
  <data name="HistoryState_YetkiliyöneticiGirişOnayı" xml:space="preserve">
    <value>Yetkili yönetici Giriş Onayı</value>
  </data>
  <data name="HistoryState_YetkiliYöneticiOnayı" xml:space="preserve">
    <value>Yetkili Yönetici Onayı</value>
  </data>
  <data name="HistoryState_YetkiliYöneticiOnayı2" xml:space="preserve">
    <value>Yetkili Yönetici Onayı 2</value>
  </data>
  <data name="HistoryState_YetkiliYöneticiOnayı3" xml:space="preserve">
    <value>Yetkili Yönetici Onayı 3</value>
  </data>
  <data name="HistoryState_YetkiOnay" xml:space="preserve">
    <value>Yetki Onay</value>
  </data>
  <data name="HistoryState_YoneticiGrubu" xml:space="preserve">
    <value>Yonetici Grubu</value>
  </data>
  <data name="HistoryState_YTSTalepKapatmaİşlemi" xml:space="preserve">
    <value>YTS Talep Kapatma İşlemi</value>
  </data>
  <data name="HistoryState_YurtdışıFinansOnayı" xml:space="preserve">
    <value>Yurtdışı Finans Onayı</value>
  </data>
  <data name="HistoryState_YurtdışıSatışYöneticisiOnayı" xml:space="preserve">
    <value>Yurtdışı Satış Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_YöneticiDeğerlendirme" xml:space="preserve">
    <value>Yönetici Değerlendirme</value>
  </data>
  <data name="HistoryState_YöneticiOnayı" xml:space="preserve">
    <value>Yönetici Onayı</value>
  </data>
  <data name="HistoryState_ZincirMağazaYetkili" xml:space="preserve">
    <value>Zincir Mağaza Yetkili</value>
  </data>
  <data name="HistoryState_ÇalışanOnayı" xml:space="preserve">
    <value>Çalışan Onayı</value>
  </data>
  <data name="HistoryState_ÖzellikleriTanımlamaveOnayı" xml:space="preserve">
    <value>Özellikleri Tanımlama ve Onayı</value>
  </data>
  <data name="HistoryState_ÜrünGeliştirmeVeOperGMYOnayı" xml:space="preserve">
    <value>Ürün Geliştirme Ve Oper. GMY Onayı</value>
  </data>
  <data name="HistoryState_ÜrünGeliştirmeVeOperGMYOnayı1" xml:space="preserve">
    <value>Ürün Geliştirme Ve Oper.GMY Onayı</value>
  </data>
  <data name="HistoryState_ÜstYöneticisiOnay" xml:space="preserve">
    <value>Üst Yöneticisi Onay</value>
  </data>
  <data name="HistoryState_ÜstYöneticisiOnayı" xml:space="preserve">
    <value>Üst Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_İnsanKaynaklarıAdımı" xml:space="preserve">
    <value>İnsan Kaynakları Adımı</value>
  </data>
  <data name="HistoryState_İnsanKaynaklarıYetkilsi" xml:space="preserve">
    <value>İnsan Kaynakları Yetkilsi</value>
  </data>
  <data name="HistoryState_İçerikBütçeYöneticisi" xml:space="preserve">
    <value>İçerik Bütçe Yöneticisi</value>
  </data>
  <data name="HistoryState_İçerikBütçeYöneticisi2" xml:space="preserve">
    <value>İçerik Bütçe Yöneticisi-2</value>
  </data>
  <data name="HistoryState_İçHizmetler2YöneticiOnayı" xml:space="preserve">
    <value>İç Hizmetler 2. Yönetici Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerBirimYöneticisiOnayı" xml:space="preserve">
    <value>İç Hizmetler Birim Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerBölümYöneticisiOnayı" xml:space="preserve">
    <value>İç Hizmetler Bölüm Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerGenelMüdürYardımcısıOnayı" xml:space="preserve">
    <value>İç Hizmetler Genel Müdür Yardımcısı Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerGMYOnayı" xml:space="preserve">
    <value>İç Hizmetler GMY Onayı</value>
  </data>
  <data name="HistoryState_İçHizmetlerYöneticisiOnayı" xml:space="preserve">
    <value>İç Hizmetler Yöneticisi Onayı</value>
  </data>
  <data name="HistoryState_İşeGirişİşlemleri" xml:space="preserve">
    <value>İşe Giriş İşlemleri</value>
  </data>
  <data name="HistoryState_İşGeliştirmeveStratejiGMYOnayı" xml:space="preserve">
    <value>İş Geliştirme ve Strateji GMY Onayı</value>
  </data>
  <data name="inbox_akislar_toplu_onay" xml:space="preserve">
    <value>Multiple Workflow is Approved</value>
  </data>
  <data name="main_aciklama_degistir" xml:space="preserve">
    <value>Change Description</value>
  </data>
  <data name="main_duzeltme_talebi" xml:space="preserve">
    <value>Request of Revision</value>
  </data>
  <data name="main_onay" xml:space="preserve">
    <value>Approval</value>
  </data>
  <data name="main_sartli_onay" xml:space="preserve">
    <value>Conditional Approval</value>
  </data>
  <data name="main_yetki_uyari" xml:space="preserve">
    <value>You don't have permission to open this workflow.</value>
  </data>
  <data name="Main_Onay_Uyari" xml:space="preserve">
    <value>You have not choose to one of the Conditional Approval / Approval options, Please check and try again.</value>
  </data>
  <data name="Main_Dokuman_Uyari" xml:space="preserve">
    <value>File field is mandatory. Please add a file.</value>
  </data>
  <data name="Main_Uyari" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="Uc_Birim" xml:space="preserve">
    <value>Birim</value>
  </data>
  <data name="Uc_Bolum" xml:space="preserve">
    <value>Bölüm</value>
  </data>
  <data name="Uc_Departman" xml:space="preserve">
    <value>Departman</value>
    <comment>User Control Departman</comment>
  </data>
  <data name="Uc_Kullanici" xml:space="preserve">
    <value>Kullanıcı</value>
  </data>
  <data name="Uc_KullaniciHata" xml:space="preserve">
    <value>Kullanıcı alanı boş bırakılamaz.</value>
  </data>
  <data name="Uc_Takim" xml:space="preserve">
    <value>Takım</value>
  </data>
  <data name="Uc_Yonetici" xml:space="preserve">
    <value>Yönetici</value>
  </data>
</root>