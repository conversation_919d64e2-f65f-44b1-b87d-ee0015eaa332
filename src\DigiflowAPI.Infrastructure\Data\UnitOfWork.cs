using DigiflowAPI.Domain.Interfaces;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Infrastructure.Data.Repositories;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Infrastructure.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using AutoMapper;
using System.Data;

namespace DigiflowAPI.Infrastructure.Data;

/// <summary>
/// Implementation of the Unit of Work pattern to manage database transactions and coordinate repositories.
/// Adapted for Oracle-based data access architecture.
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    private readonly IOracleDataAccessRepositoryFactory _repositoryFactory;
    private readonly IGlobalHelpers _globalHelpers;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IOracleConnectionManager _connectionManager;
    private readonly IConfiguration _configuration;
    private readonly IMapper _mapper;
    private readonly IHttpService _httpService;
    private readonly IUserService _userService;
    private readonly IPermissionProcessService _permissionProcessService;
    private readonly WorkflowTypeHelper _workflowTypeHelper;
    private readonly Func<IGenericMailService> _genericMailServiceFactory;
    private bool _disposed;

    // Repository instances
    private IUserRepository _userRepository;
    private IWorkflowRepository _workflowRepository;
    private IHistoryRepository _historyRepository;
    private IInboxRepository _inboxRepository;
    private IOrganizationRepository _organizationRepository;
    private IPermissionRepository _permissionRepository;
    private IEmailTemplateRepository _emailTemplateRepository;
    private ISlideRepository _slideRepository;
    private ILogicalGroupRepository _logicalGroupRepository;
    private ISharePointRepository _sharePointRepository;
    private IRepository _repository;

    public UnitOfWork(
        IOracleDataAccessRepositoryFactory repositoryFactory,
        IGlobalHelpers globalHelpers,
        IHttpContextAccessor httpContextAccessor,
        IOracleConnectionManager connectionManager,
        IConfiguration configuration,
        IMapper mapper,
        IHttpService httpService,
        IUserService userService,
        IPermissionProcessService permissionProcessService,
        WorkflowTypeHelper workflowTypeHelper,
        Func<IGenericMailService> genericMailServiceFactory)
    {
        _repositoryFactory = repositoryFactory ?? throw new ArgumentNullException(nameof(repositoryFactory));
        _globalHelpers = globalHelpers ?? throw new ArgumentNullException(nameof(globalHelpers));
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _httpService = httpService ?? throw new ArgumentNullException(nameof(httpService));
        _userService = userService ?? throw new ArgumentNullException(nameof(userService));
        _permissionProcessService = permissionProcessService ?? throw new ArgumentNullException(nameof(permissionProcessService));
        _workflowTypeHelper = workflowTypeHelper ?? throw new ArgumentNullException(nameof(workflowTypeHelper));
        _genericMailServiceFactory = genericMailServiceFactory ?? throw new ArgumentNullException(nameof(genericMailServiceFactory));
    }

    // Repository properties with lazy initialization
    public IUserRepository Users =>
        _userRepository ??= new UserRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);

    public IWorkflowRepository Workflows =>
        _workflowRepository ??= new WorkflowRepository(_repositoryFactory, _workflowTypeHelper, _genericMailServiceFactory, _globalHelpers, _userService, _configuration);

    public IHistoryRepository Histories =>
        _historyRepository ??= new HistoryRepository(_repositoryFactory, _httpService, _configuration, _globalHelpers, _httpContextAccessor, _userService, _mapper);

    public IInboxRepository Inboxes =>
        _inboxRepository ??= new InboxRepository(_repositoryFactory, _httpContextAccessor, _mapper);

    public IOrganizationRepository Organizations =>
        _organizationRepository ??= new OrganizationRepository(_repositoryFactory, _httpContextAccessor, _userService);

    public IPermissionRepository Permissions =>
        _permissionRepository ??= new PermissionRepository(_permissionProcessService);

    public IEmailTemplateRepository EmailTemplates =>
        _emailTemplateRepository ??= new EmailTemplateRepository(_repositoryFactory);

    public ISlideRepository Slides =>
        _slideRepository ??= new SlideRepository(_repositoryFactory);

    public ILogicalGroupRepository LogicalGroups =>
        _logicalGroupRepository ??= new LogicalGroupRepository(_repositoryFactory);

    public ISharePointRepository SharePoint =>
        _sharePointRepository ??= new SharePointRepository(_repositoryFactory, _globalHelpers, _httpContextAccessor);

    public IRepository Repository =>
        _repository ??= new OracleGenericRepository(_repositoryFactory);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // In Oracle-based architecture, changes are typically committed at the transaction level
        // Individual repository operations may auto-commit based on configuration
        return await Task.FromResult(0);
    }

    public int SaveChanges()
    {
        // In Oracle-based architecture, changes are typically committed at the transaction level
        // Individual repository operations may auto-commit based on configuration
        return 0;
    }

    public async Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        // For Oracle, we need to implement transaction management differently
        // This is a placeholder - actual implementation would depend on Oracle connection management
        throw new NotImplementedException("Oracle transaction management needs to be implemented based on the existing connection manager.");
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        // Placeholder for Oracle transaction commit
        throw new NotImplementedException("Oracle transaction management needs to be implemented based on the existing connection manager.");
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        // Placeholder for Oracle transaction rollback
        throw new NotImplementedException("Oracle transaction management needs to be implemented based on the existing connection manager.");
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // Dispose managed resources if any
            }

            _disposed = true;
        }
    }
}

/// <summary>
/// Oracle-specific implementation of IDbContextTransaction
/// </summary>
public class OracleDbTransaction : IDbContextTransaction
{
    private readonly IDbTransaction _transaction;

    public OracleDbTransaction(IDbTransaction transaction)
    {
        _transaction = transaction ?? throw new ArgumentNullException(nameof(transaction));
    }

    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        _transaction.Commit();
        await Task.CompletedTask;
    }

    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        _transaction.Rollback();
        await Task.CompletedTask;
    }

    public void Dispose()
    {
        _transaction?.Dispose();
    }
}