﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace NGDigiflowApi.Controllers
{
    public class WorkflowController : ApiController
    {
        [HttpPost]
        public IHttpActionResult CreateWorkflow([FromBody] string value)
        {

            return Ok();
        }

        [HttpPost]
        public IHttpActionResult ApproveWorkflow([FromBody] string value)
        {
            return Ok();
        }

        [HttpPost]
        public IHttpActionResult RejectWorkFlow([FromBody] string value)
        {
            return Ok();
        }

        [HttpPost]
        public IHttpActionResult ForwardWorkFlow([FromBody] string value)
        {
            return Ok();
        }

        [HttpPost]
        public IHttpActionResult SendToCommendWorkFlow([FromBody] string value)
        {
            return Ok();
        }

        [HttpPost]
        public IHttpActionResult CommendedWorkFlow([FromBody] string value)
        {
            return Ok();
        }
    }
}
