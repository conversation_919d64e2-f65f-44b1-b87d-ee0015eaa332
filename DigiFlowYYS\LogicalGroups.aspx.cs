﻿using DevExpress.Web.ASPxGridView;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Web.UI;

public partial class LogicalGroups : YYSSecurePage
{
    /// <summary>
    /// Mantıksal Grupların görüntülendiği ekran
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        #region İş akışları combobox ı doldurulur

        ((MasterPage)Master).PageTitle = "Mantıksal Gruplar";
        ((MasterPage)Master).ShowMenu(true);

        try
        {
            if (!Page.IsPostBack)
            {
                long IsAdmin = 0;

                if (Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAndFlowAdmin.ToString() || Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAdmin.ToString())
                {
                    IsAdmin = 1;
                }

                WorkFlowCombobox = Common.FillComboboxWorkFlow("Name", "WorkflowDefId", WorkFlowCombobox, WorkflowHelper.GetWorkflowsOfAdmin(UserInformation.LoginObject.LoginId, IsAdmin), "İş Akışı Seçiniz", "0");

                if (Request.QueryString["WfDefId"] != null)
                {
                    Session["WfDefId"] = Request.QueryString["WfDefId"];
                    WorkFlowCombobox.Value = Convert.ToInt64(Request.QueryString["WfDefId"]);
                    WorkFlowCombobox_SelectedIndexChanged(null, null);
                }
            }
            if (Page.IsCallback)
            {
                FillDataGrid(ConvertionHelper.ConvertValue<long>(Session["WfDefId"]));
            }
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowError("Hata", "Sayfa yüklenirken beklenmeyen bir hata ile karşılaşıldı", ex.Message);
        }

        #endregion İş akışları combobox ı doldurulur
    }

    /// <summary>
    /// Seçilen iş akışında bulunan mantıksal grupları getirmeyi sağlar
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowCombobox_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            if (WorkFlowCombobox.SelectedIndex != 0)
            {
                //O akışa ait mantıksal gruplar grid e doldurulur
                Session["WfDefId"] = WorkFlowCombobox.SelectedItem.Value;
                FillDataGrid(ConvertionHelper.ConvertValue<long>(Session["WfDefId"]));
            }
        }
        catch
        {
        }
    }

    /// <summary>
    /// DataGrid i seçilen iş akışına göre doldurmaya yarar
    /// </summary>
    /// <param name="WorkFlowId">İş Akış Id değeri</param>
    private void FillDataGrid(long WorkFlowId)
    {
        List<Digiturk.Workflow.Digiflow.Entities.LogicalGroup> lg = LogicalGroupHelper.GetAllByWorkflowId(WorkFlowId);
        LogicalGroupsGridView.DataSource = lg;
        LogicalGroupsGridView.DataBind();
        Session["LogicalGroupsGridView"] = lg;
    }

    /// <summary>
    /// Veritabanından gelen değerlerin karşılıklarını bulup yazmamızı sağlar
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void LogicalGroupsGridView_HtmlDataCellPrepared(object sender, DevExpress.Web.ASPxGridView.ASPxGridViewTableDataCellEventArgs e)
    {
        #region Gridview deki sayısal olan Silinebilir mi ve Tek kişilik grup mu değerlerini Evet veya Hayır a çevirir

        if (WorkFlowCombobox.SelectedIndex != 0)
        {
            if (e.DataColumn.FieldName == "CanBeDeleted" || e.DataColumn.FieldName == "IsOnePersonGroup")
            {
                if (e.CellValue.ToString() == "1")
                {
                    e.Cell.Text = "Evet";
                }
                else
                {
                    e.Cell.Text = "Hayır";
                }
            }
        }

        #endregion Gridview deki sayısal olan Silinebilir mi ve Tek kişilik grup mu değerlerini Evet veya Hayır a çevirir
    }

    /// <summary>
    /// Silme işlemini yapar, eğer silinebilir olarak işaretlendi ise silinir, eğer bir iş kuralına bağlı ise silinemez
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void LogicalGroupsGridView_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
    {
        #region Mantıksal Grup Silme İşlemi

        //long LogicalGroupId = ConvertionHelper.ConvertValue<long>(e.Values[5]);
        //ASPxGridView gridView = (ASPxGridView)sender;
        ////Bu mantıksal grubun ActionAuthorization tablosunda bağlı olduğu bir kural var mı kontrolü yapılır
        //if (!LogicalGroupHelper.IsExistActionAuthorizationRule(LogicalGroupId))
        //{
        //    //Grubun silinebilir olup olmadığı kontrolü yapılır. Silinebilir ise silme işlemine devam edilir
        //    if (e.Values[2].ToString() == "1")
        //    {
        //        LogicalGroupHelper.DeleteLogicalGroup(LogicalGroupId);
        //        ((MasterPage)Master).ShowPopup(false, "Mantıksal Grup Silme", "İstenilen grup silindi!", false, "");
        //        FillDataGrid(ConvertionHelper.ConvertValue<long>(WorkFlowCombobox.SelectedItem.Value));
        //        gridView.CancelEdit();
        //        e.Cancel = true;
        //    }
        //    else
        //    {
        //        ((MasterPage)Master).ShowPopup(false, "Mantıksal Grup Silme", "Bu mantıksal grup silinemez olarak tanımlanmış!", true, "");
        //        gridView.CancelEdit();
        //        e.Cancel = true;
        //    }

        //}
        //else
        //{
        //    ((MasterPage)Master).ShowPopup(false, "Mantıksal Grup Silme", "Bu mantıksal grubun bağlı bulunduğu kural bulunduğu için silinemedi!", true, "");
        //    FillDataGrid(ConvertionHelper.ConvertValue<long>(WorkFlowCombobox.SelectedItem.Value));
        //    gridView.CancelEdit();
        //    e.Cancel = true;
        //    //ClientScript.RegisterStartupScript(typeof(Page), "test", "<script>alert('Hello');return false;</script>");
        //    //Response.Write("<script language=javascript>alert('HEY!');</script>");
        //    //DevExpress.XtraEditors.XtraMessageBox.Show("Hi!");
        //}

        #endregion Mantıksal Grup Silme İşlemi
    }

    /// <summary>
    /// Sil butonunun görünürlüğünü ayarlar, Silinebilir grup ise görünür, yoksa görünmez
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void LogicalGroupsGridView_HtmlRowCreated(object sender, ASPxGridViewTableRowEventArgs e)
    {
        //if (LogicalGroupsGridView.VisibleRowCount > 0)
        //{
        //    object value = e.GetValue("CanBeDeleted");
        //    if (value != null)
        //    {
        //        if (value.ToString() == "0" || value.ToString() == "Hayır")
        //        {
        //           // e.Row.Cells[7].Enabled = false;
        //            GridViewDataColumn gvdc =(GridViewDataColumn) LogicalGroupsGridView.Columns[7];
        //            ASPxButton btn = (ASPxButton) LogicalGroupsGridView.FindRowCellTemplateControl(e.VisibleIndex,gvdc, "DeleteButton");
        //            btn.Enabled = false;
        //        }
        //        else
        //        {
        //            GridViewDataColumn gvdc = (GridViewDataColumn)LogicalGroupsGridView.Columns[7];
        //            ASPxButton btn = (ASPxButton)LogicalGroupsGridView.FindRowCellTemplateControl(e.VisibleIndex, gvdc, "DeleteButton");
        //            btn.Enabled = true;
        //            //e.Row.Cells[7].Enabled = true;
        //        }
        //    }

        //}

        if (LogicalGroupsGridView.VisibleRowCount > 0)
        {
            object value = e.GetValue("CanBeDeleted");
            if (value != null)
            {
                try
                {
                    if (value.ToString() == "0" || value.ToString() == "Hayır")
                    {
                        e.Row.Cells[7].Enabled = false;
                        e.Row.Cells[7].Text = "&nbsp;";
                    }
                    else
                    {
                        e.Row.Cells[7].Enabled = true;
                    }
                }
                catch
                {
                }
            }
        }
        FillDataGrid(ConvertionHelper.ConvertValue<long>(WorkFlowCombobox.SelectedItem.Value));
    }

    /// <summary>
    /// Silme işlemi burada yapılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DeleteButton_Click(object sender, EventArgs e)
    {
        #region Mantıksal Grup Silme İşlemi

        long LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["DeletingLogicalGroupId"]);
        Digiturk.Workflow.Digiflow.Entities.LogicalGroup lg = LogicalGroupHelper.Get(LogicalGroupId);

        //Bu mantıksal grubun ActionAuthorization tablosunda bağlı olduğu bir kural var mı kontrolü yapılır
        if (!LogicalGroupHelper.IsExistActionAuthorizationRule(LogicalGroupId))
        {
            //Grubun silinebilir olup olmadığı kontrolü yapılır. Silinebilir ise silme işlemine devam edilir
            if (lg.CanBeDeleted == 1)//silinebilir ise
            {
                LogicalGroupHelper.DeleteLogicalGroup(LogicalGroupId);
                ((MasterPage)Master).ShowPopup(false, "Mantıksal Grup Silme", "İstenilen grup silindi!", false, "");
                FillDataGrid(ConvertionHelper.ConvertValue<long>(WorkFlowCombobox.SelectedItem.Value));
            }
            else
            {
                ((MasterPage)Master).ShowPopup(false, "Mantıksal Grup Silme", "İstenilen grup silinebilir bir grup değil!", true, "");
            }
        }
        else
        {
            ((MasterPage)Master).ShowPopup(false, "Mantıksal Grup Silme", "Bu mantıksal grubun bağlı bulunduğu kural bulunduğu için silinemedi!", true, "");
            FillDataGrid(ConvertionHelper.ConvertValue<long>(WorkFlowCombobox.SelectedItem.Value));
        }

        #endregion Mantıksal Grup Silme İşlemi
    }

    /// <summary>
    /// Silmek için grubun Id si buradan alınır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void LogicalGroupsGridView_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
    {
        Session["DeletingLogicalGroupId"] = e.KeyValue.ToString();
    }

    /// <summary>
    /// Sayfalamada datayı daha hızlı getirmek için kullanılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void LogicalGroupsGridViewPageIndexChanging(object sender, EventArgs e)
    {
        // DataTable dtInboxGridView = new DataTable();
        List<Digiturk.Workflow.Digiflow.Entities.LogicalGroup> lg = new List<Digiturk.Workflow.Digiflow.Entities.LogicalGroup>();
        if (Session["LogicalGroupsGridView"] != null)
        {
            lg = (List<Digiturk.Workflow.Digiflow.Entities.LogicalGroup>)Session["LogicalGroupsGridView"];
        }
        else
        {
            lg = LogicalGroupHelper.GetAllByWorkflowId(ConvertionHelper.ConvertValue<long>(Session["WfDefId"]));

            Session["LogicalGroupsGridView"] = lg;
        }
        LogicalGroupsGridView.DataSource = lg;
        LogicalGroupsGridView.DataBind();
        lg = null;
    }
}