﻿<%@ Control Language="C#" AutoEventWireup="true" CodeFile="OrganizationTreeWebUserControl2.ascx.cs" Inherits="UserControls_OrganizationTreeWebUserControl2" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>

<asp:UpdatePanel ID="OrganizationUpdatePanel" runat="server"
    UpdateMode="Conditional">
    <ContentTemplate>
        <asp:UpdateProgress ID="UP1" AssociatedUpdatePanelID="OrganizationUpdatePanel" runat="server">
            <ProgressTemplate>
                <%--<img src="images/ajax-loader.gif" />--%>
            </ProgressTemplate>
        </asp:UpdateProgress>

        <asp:Panel ID="OrganizationTreePanel" runat="server">
            <asp:Label ID="TitleLabel" runat="server" Text="" Font-Bold="true"></asp:Label>

            <asp:Panel ID="DepartmentPanel" runat="server">
                <span class="detayaciklama mLeft00">
                    <asp:Literal ID="ltDept" runat="server" Text="<%$Resources:Resource, Uc_Departman %>"></asp:Literal></span>
                <asp:DropDownList ID="DepartmentDropDownList" runat="server"
                    AutoPostBack="True" CssClass="talepicerikTextBox mLeft00 DepartmentDropDownList" Enabled="False"
                    OnSelectedIndexChanged="DepartmentDropDownList_SelectedIndexChanged">
                </asp:DropDownList>
            </asp:Panel>

            <asp:Panel ID="DivisionPanel" runat="server">
                <span class="detayaciklama mLeft00">
                    <asp:Literal ID="Literal1" runat="server" Text="<%$Resources:Resource, Uc_Bolum%>"></asp:Literal>
                </span>
                <asp:DropDownList
                    ID="DivisionDropDownList"
                    runat="server"
                    AutoPostBack="True"
                    CssClass="talepicerikTextBox mLeft00 DivisionDropDownList"
                    Enabled="False"
                    OnSelectedIndexChanged="DivisionDropDownList_SelectedIndexChanged">
                </asp:DropDownList>
            </asp:Panel>

            <asp:Panel ID="UnitPanel" runat="server">
                <span class="detayaciklama mLeft00">
                    <asp:Literal ID="Literal2" runat="server" Text="<%$Resources:Resource, Uc_Birim%>"></asp:Literal>
                </span>
                <asp:DropDownList ID="UnitDropDownList" runat="server" AutoPostBack="True"
                    CssClass="talepicerikTextBox mLeft00 UnitDropDownList" Enabled="False"
                    OnSelectedIndexChanged="UnitDropDownList_SelectedIndexChanged">
                </asp:DropDownList>

            </asp:Panel>

            <asp:Panel ID="TeamPanel" runat="server">
                <span class="detayaciklama mLeft00">
                    <asp:Literal ID="Literal3" runat="server" Text="<%$Resources:Resource, Uc_Takim%>"></asp:Literal>
                </span>
                <asp:DropDownList ID="TeamDropDownList" runat="server" AutoPostBack="True"
                    CssClass="talepicerikTextBox mLeft00 TeamDropDownList" Enabled="False"
                    OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                </asp:DropDownList>

            </asp:Panel>

            <asp:Panel ID="SubTeamPanel1" runat="server">
                <span class="detayaciklama mLeft00">
                    <asp:Literal ID="Literal6" runat="server" Text="<%$Resources:Resource, Alt_Birim1%>"></asp:Literal>
                </span>
                <asp:DropDownList Width="350" ID="SubTeamDropDownList1" runat="server" AutoPostBack="True"
                    CssClass="talepicerikTextBox mLeft00 TeamDropDownList" Enabled="False"
                    OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                </asp:DropDownList>
            </asp:Panel>


            <asp:Panel ID="SubTeamPanel2" runat="server">
             <span class="detayaciklama mLeft00">   <asp:Literal ID="Literal7" runat="server" Text="<%$Resources:Resource, Alt_Birim2%>"></asp:Literal>
             </span>   <asp:DropDownList Width="350" ID="SubTeamDropDownList2" runat="server" AutoPostBack="True"
                    CssClass="talepicerikTextBox mLeft00 TeamDropDownList" Enabled="False"
                    OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                </asp:DropDownList>
            </asp:Panel>


            <asp:Panel ID="SubTeamPanel3" runat="server">
                <span class="detayaciklama mLeft00"><asp:Literal ID="Literal8" runat="server" Text="<%$Resources:Resource, Alt_Birim3%>"></asp:Literal>
              </span>  <asp:DropDownList Width="350" ID="SubTeamDropDownList3" runat="server" AutoPostBack="True"
                     CssClass="talepicerikTextBox mLeft00 TeamDropDownList" Enabled="False"
                    OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                </asp:DropDownList>
            </asp:Panel>


            <asp:Panel ID="SubTeamPanel4" runat="server">
              <span class="detayaciklama mLeft00">  <asp:Literal ID="Literal9" runat="server" Text="<%$Resources:Resource, Alt_Birim4%>"></asp:Literal>
               </span> <asp:DropDownList Width="350" ID="SubTeamDropDownList4" runat="server" AutoPostBack="True"
                     CssClass="talepicerikTextBox mLeft00 TeamDropDownList" Enabled="False"
                    OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                </asp:DropDownList>
            </asp:Panel>


            <asp:Panel ID="SubTeamPanel5" runat="server">
              <span class="detayaciklama mLeft00">  <asp:Literal ID="Literal10" runat="server" Text="<%$Resources:Resource, Alt_Birim5%>"></asp:Literal>
               </span> <asp:DropDownList Width="350" ID="SubTeamDropDownList5" runat="server" AutoPostBack="True"
                     CssClass="talepicerikTextBox mLeft00 TeamDropDownList" Enabled="False"
                    OnSelectedIndexChanged="TeamDropDownList_SelectedIndexChanged">
                </asp:DropDownList>
            </asp:Panel>


            <asp:Panel ID="UserPanel" runat="server">
                <span class="detayaciklama mLeft00">
                    <asp:Literal ID="Literal4" runat="server" Text="<%$Resources:Resource, Uc_Kullanici%>"></asp:Literal>
                </span>
                <asp:DropDownList ID="UserDropDownList" runat="server" AutoPostBack="True"
                    CssClass="talepicerikTextBox mLeft00 UserDropDownList" Enabled="False" OnSelectedIndexChanged="UserDropDownList_SelectedIndexChanged">
                </asp:DropDownList>
                <asp:RequiredFieldValidator ID="UserDropdownRequiredFieldValidator" runat="server" ErrorMessage="<%$Resources:Resource, Uc_KullaniciHata%>" ControlToValidate="UserDropDownList" Display="None" ValidationGroup="vG0" Enabled="False"></asp:RequiredFieldValidator>
                </td>
                </tr>
            </asp:Panel>

            <asp:Panel ID="ManagerPanel" runat="server" Visible="false">
                <span class="detayaciklama mLeft00">
                    <asp:Literal ID="Literal5" runat="server" Text="<%$Resources:Resource, Uc_Yonetici%>"></asp:Literal>
                </span>
                <asp:Label ID="ManagerLabel" runat="server" CssClass="talepicerikLabel"></asp:Label>

            </asp:Panel>

        </asp:Panel>

    </ContentTemplate>
    <Triggers>
        <asp:AsyncPostBackTrigger ControlID="UnitDropDownList" EventName="SelectedIndexChanged" />
        <asp:AsyncPostBackTrigger ControlID="DivisionDropDownList" EventName="SelectedIndexChanged" />
        <asp:AsyncPostBackTrigger ControlID="DepartmentDropDownList" EventName="SelectedIndexChanged" />
    </Triggers>
</asp:UpdatePanel>
