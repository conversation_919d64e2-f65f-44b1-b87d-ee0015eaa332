﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Actions.AssigmentClass;
using Digiturk.Workflow.Digiflow.Authorization;
using Digiturk.Workflow.Digiflow.DataAccessLayer;
using Digiturk.Workflow.Digiflow.Entities.Enums;
using Digiturk.Workflow.Digiflow.GenericMailHelper;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;
using System.Data;

namespace Digiturk.Workflow.Digiflow.Actions
{
    /// <summary>
    /// Son İşlem yapan kullanıcının departman yöneticisini bularak departman yöneticisine Assigment yapar
    /// </summary>
    public class DynamicAssigmentDivisionManager : CommonAssignment, IActionClass, IWorkflowDiagramAssignmentHelper
    {
        private List<string> ListNameSurname = new List<string>();
        /// <summary>
        /// Son İşlem yapan kullanıcının departman yöneticisini bularak departman yöneticisine Assigment yapar
        /// </summary>
        /// <param name="wfContext"></param>
        /// <param name="actionInstance"></param>
        /// <returns></returns>
        public bool Execute(WFContext wfContext, FWfActionInstance actionInstance)
        {
            #region EskiKod
            /*
             #region Assigment için Gerekli Bilgiler Toplanır

            WorkflowLoginHelper _wfHelper = new WorkflowLoginHelper();
            ActionType CurrentActionType = ActionType.ONWFSTARTASSIGNER;
            ActionType CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
            string assigmentType = "TASKINBOX";
            string loginType = "LOGIN";

            #endregion Assigment için Gerekli Bilgiler Toplanır

            #region Akış ile ilgili Kullanıcı Bilgileri Toplanıyor

            long WorkflowInstanceId = actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
            long AssignToLoginId = actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
            long LastActionToLoginId = CheckingWorker.GetLastActionToLoginId(WorkflowInstanceId);

            #endregion Akış ile ilgili Kullanıcı Bilgileri Toplanıyor

            #region Akışı açan Kullanıcının Departman Yöneticisi bulunuyor

            CurrentActionType = ActionType.ONWFSTARTASSIGNER;
            CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
            //TODO:Böyle bir kontrole ihtiyaç kalmadı bir sonraki release ile silinebilir
            
            //if (AssignToLoginId == 0 || AssignToLoginId == 1)
            //{
            //    AssignToLoginId = actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
            //    CurrentActionType = ActionType.ONWFSTARTASSIGNER;
            //    CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
            //}
            //else
            //{
            //    CurrentActionType = ActionType.ONWFAPPROVEASSIGNER;
            //    CurrentActionOwnerType = ActionType.ONWFAPPROVEOWNER;
            //}
            long NewAssignLoginId = AssignToLoginId;

            LoginDto dto = new LoginDto();
            dto = WflowDataHelpers.GetDetailsOfLogin(actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId);

            long deptId = 0;

            //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("OwnerLoginId=" + actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId.ToString(), "department:" + dto.DepartmentId + " division:" + dto.DivisionId + " unit:" + dto.UnitId + "team: " + dto.TeamId, "EYK");
            GetManager(out NewAssignLoginId, dto, out deptId);

            // Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("NewAssignLoginId", "NewAssignLoginId:" + NewAssignLoginId.ToString(), "EYK");
            string yonlendirmeHataStr = "";
            yonlendirmeHataStr = "AssignToLoginId=" + AssignToLoginId.ToString() + "LastActionToLoginId=" + LastActionToLoginId.ToString();
            long MaxDinamicAssignLoginId = WorkFlowHelpers.WorkFlowInformationHelper.GetMaxDinamicAssignLoginId(actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);

            if (MaxDinamicAssignLoginId == 0)
            {
                CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
                AssignToLoginId = actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                //NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId); Murat Kasapoğlu değiştirdi.06.01.2025 alternatif yönetici
                NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId);
            }
            else
            {
                AssignToLoginId = MaxDinamicAssignLoginId;

                yonlendirmeHataStr += "MaxDinamicAssignLoginId" + MaxDinamicAssignLoginId;

                if (AssignToLoginId == 0 || AssignToLoginId == 1 || AssignToLoginId == actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId)
                {
                    CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                    CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
                }
                else
                {
                    CurrentActionType = ActionType.ONWFAPPROVEASSIGNER;
                    CurrentActionOwnerType = ActionType.ONWFAPPROVEOWNER;
                }

                //NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId); Murat Kasapoğlu değiştirdi.06.01.2025 alternatif yönetici
                NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId);
            }

            #endregion Akışı açan Kullanıcının Departman Yöneticisi bulunuyor

            #region Assigment işlemleri yapılarak Departman Yöneticisine Atanıyor

            Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(NewAssignLoginId, loginType, actionInstance.WfActionInstanceId, assigmentType);
            var viewList = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetViewList(NewAssignLoginId.ToString());
            if (viewList.Count == 0 || viewList.IndexOf(actionInstance.WfStateInstance.WfWorkflowInstance.ToString()) < 0)
            {
                Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(NewAssignLoginId, loginType, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId, "WFVIEW");
            }

            #region Email Action İçin Instance Parametreleri Ekleniyor
            base.EmailActionsAddition(wfContext, actionInstance, false);
            #endregion

            Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionType, WorkflowInstanceId, LastActionToLoginId, NewAssignLoginId, wfContext);
            Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionOwnerType, WorkflowInstanceId, LastActionToLoginId, NewAssignLoginId, wfContext);
            Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.Execute(actionInstance, NewAssignLoginId, NewAssignLoginId, WorkflowHistoryActionType.ASSIGN, "");

            #endregion Assigment işlemleri yapılarak Departman Yöneticisine Atanıyor

            #region 2 kere onaylama kontrolleri

            //Kerem Yeni Delegasyonu olana atama !
            //Yeni atanacak kişinin bu akışa ait delegasyonları kontrol edilir
            //Senaryo 1 Eğer  son onaylayan kişinin delegelerinden biri daha önce onayladıysa akış bir sonraki adıma geçilir
            //Senaryo 2 Eğer atanan kişi daha önce onayladıysa ve akış geri gönderilmemişse bir sonraki adıma geçilir
            //Performans Değerlendirme akışı hariç tutuldu- 1852-zülfiye-26.12.2014
            //zzstring yonlendirmeHataStr = "";
            string YorumStr = "";
            string Senaryo = "";
            bool DelegasyonKontrolu = false;
            //Sözleşme akışı, Ödeme, Macro, Rota ve Ad Hoc da çalışır..

            #region defId setleme

            long sozlesmeId = 1338;
            long odemeId = 1336;
            long AdHocID = 2174;
            long macroID = 1333;
            long rotaID = 1337;

            if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
            {
                sozlesmeId = 1338;
                odemeId = 1336;
                AdHocID = 2174;
                macroID = 1333;
                rotaID = 1337;
            }
            else
            {
                sozlesmeId = 1338;
                odemeId = 1336;
                AdHocID = 2174;
                macroID = 1333;
                rotaID = 1337;
            }

            #endregion defId setleme

            if (actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == sozlesmeId || actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == odemeId || actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == AdHocID || actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == macroID || actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == rotaID)
            {
                #region Otomatik onaylama Senaryo 1 - üzerine atanan kişinin delegesi daha önce onaylamışsa

                DataTable dt = new DataTable();
                dt = DigiFlowDelegationManager.GetDelegationsOfUser(NewAssignLoginId, "3", actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId);
                List<long> AcceptUserList = Digiflow.DataAccessLayer.CheckingWorker.GetLastAcceptLoginList(WorkflowInstanceId);
                long DelegeId = 0;
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Senaryo 1 kontrolü", "Dynamic Assignment");
                foreach (DataRow dr in dt.Rows)
                {
                    DelegeId = Digiturk.Workflow.Digiflow.CoreHelpers.ConvertionHelper.ConvertValue<long>(dr["DELEGATE_REF_ID"]);
                    if (AcceptUserList.Contains(DelegeId))
                    {
                        bool delegeSonuc = false;
                        if (wfContext.Parameters.ContainsKey("Onay"))
                        {
                            if (wfContext.Parameters["Onay"].ToString() == "SendBack")
                            {
                                delegeSonuc = false;
                            }
                            else
                            {
                                delegeSonuc = CheckingWorker.IsBeforeAccept2(WorkflowInstanceId, DelegeId);
                            }
                        }
                        else
                        {
                            delegeSonuc = CheckingWorker.IsBeforeAccept2(WorkflowInstanceId, DelegeId);
                        }
                        if (delegeSonuc)
                        {
                            DelegasyonKontrolu = true;
                            Senaryo = "1";
                            //YorumStr = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(DelegeId) + ", " + Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId) + " delegesi olduğundan sistem tarafından onaylanmıştır";
                            YorumStr = "This form is automatically approved since " + Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(DelegeId) + " is " + Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId) + "'s delegate";

                            break;
                        }
                    }
                }

                #endregion Otomatik onaylama Senaryo 1 - üzerine atanan kişinin delegesi daha önce onaylamışsa

                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Senaryo 1 kontrolü sonu = " + DelegasyonKontrolu.ToString(), "Dynamic Assignment");
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Senaryo 2 kontrolü", "Dynamic Assignment");

                #region Otomatik Onaylama Senaryo 2 - Üzerine atanan kişi daha önce onaylamış ve akış send back yapılmamışsa

                if (DelegasyonKontrolu == false)
                {
                    bool sonuc = false;
                    if (wfContext.Parameters.ContainsKey("Onay"))
                    {
                        if (wfContext.Parameters["Onay"].ToString() == "SendBack")
                        {
                            sonuc = false;
                        }
                        else
                        {
                            sonuc = CheckingWorker.IsBeforeAccept2(WorkflowInstanceId, NewAssignLoginId);
                        }
                    }
                    else
                    {
                        sonuc = CheckingWorker.IsBeforeAccept2(WorkflowInstanceId, NewAssignLoginId);
                    }
                    //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", sonuc.ToString() + ", sonuc ( " + WorkflowInstanceId + "," + NewAssignLoginId, "Dynamic Assignment K");
                    if (sonuc)
                    {
                        //IResourceReader reader = new ResourceReader("Resource.resources");
                        //IDictionaryEnumerator en = reader.GetEnumerator();
                        //string keyValue = "deneme";
                        //if (en.Key == "Main_user")
                        //{
                        //    keyValue = en.Value.ToString();
                        //}

                        // Üzerine atanan kişi bu akışı daha önce onaylamış ve akış daha önce SendBack yapılmamış
                        DelegasyonKontrolu = true;
                        Senaryo = "2";
                        //YorumStr = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId) + ", bu akışı daha önce onayladığından sistem tarafından onaylanmıştır";
                        YorumStr = "This flow has been approved by the system, because previously approved by " + Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId);
                        //YorumStr = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId) + keyValue;
                    }
                }

                #endregion Otomatik Onaylama Senaryo 2 - Üzerine atanan kişi daha önce onaylamış ve akış send back yapılmamışsa

                #region Görev atamalı statelerde otomatik onaylama önüne engel koyulması

                if (DelegasyonKontrolu)
                {
                    List<CustomParameterList> Param = new List<CustomParameterList>();
                    Param.Add(new CustomParameterList(":STATE_DEF_ID", actionInstance.WfStateInstance.WfStateDef.WfStateDefId));
                    string stateKontrol = "";
                    stateKontrol = DataAccessLayer.ModelWorking.GetOnlyColumnSQL<string>("DefaultConnection", "select WF_DEF_ID from DT_WORKFLOW.YYS_ACTION_AUTHORIZATION where ACTION_ID = 16 and STATE_DEF_ID = :STATE_DEF_ID", Param);
                    if (stateKontrol != "0")
                    {
                        DelegasyonKontrolu = false;
                        YorumStr = "";
                    }
                    else
                    {
                    }
                }

                #endregion Görev atamalı statelerde otomatik onaylama önüne engel koyulması

                #region delegasyonu var ise otomatik onaylama fonksiyonalitesi

                if (DelegasyonKontrolu)
                {
                    System.Threading.Thread.Sleep(2000);
                    using (UnitOfWork.Start())
                    {
                        yonlendirmeHataStr += "Geldi 14,";
                        wfContext.Parameters.AddOrChangeItem("yonlendirmeHataStr", yonlendirmeHataStr);
                        wfContext.Save();

                        FLogin flog, flog2;
                        if (Senaryo == "1") //1.senaryoda  onaylayan delegesi oluyor
                        {
                            flog = WFRepository<FLogin>.GetEntity(DelegeId);
                        }
                        else  //2.senaryoda atanan ve otomatik onaylayan aynı kişi oluyor
                        {
                            flog = WFRepository<FLogin>.GetEntity(NewAssignLoginId);
                        }
                        flog2 = WFRepository<FLogin>.GetEntity(NewAssignLoginId);
                        FWfWorkflowInstance NewWFInstance = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                        actionInstance = WFRepository<FWfActionInstance>.GetEntity(NewWFInstance.WfCurrentState.WfCurrentActionInstanceId);
                        Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(WorkflowInstanceId, WFRepository<FWfActionTaskInstance>.GetEntity(actionInstance.WfStateInstance.WfWorkflowInstance.WfCurrentState.WfCurrentActionInstanceId), flog, wfContext, flog2, true, YorumStr);
                        flog = null;
                        flog2 = null;
                        NewWFInstance = null;
                        actionInstance = null;
                    }
                }

                #endregion delegasyonu var ise otomatik onaylama fonksiyonalitesi

                AcceptUserList = null;

                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Senaryo 2 kontrolü sonu  = " + DelegasyonKontrolu.ToString(), "Dynamic Assignment");
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Delegasyon Kontrolü (" + WorkflowInstanceId + "), Senaryo =" + Senaryo + ", Delegasyon Kontrolu = " + DelegasyonKontrolu.ToString() + ", Atanacak Kişi =  " + NewAssignLoginId + ",  Son İşlem Yapan Kişi = " + LastActionToLoginId + ", WFDEF ID =  " + actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId, "Dynamic Assignment");
            }

            #region DigiflowAdmin'e düşen bir akış varsa otomatik onaylama

            if (NewAssignLoginId == 123456789)
            {
                if (wfContext.Parameters.ContainsKey("OtoOnay") == false)  // akışta bir hata varsa loopa girmemesi için
                {
                    yonlendirmeHataStr += "Geldi 15,";
                    wfContext.Parameters.AddOrChangeItem("yonlendirmeHataStr", yonlendirmeHataStr);
                    wfContext.Save();
                    System.Threading.Thread.Sleep(2000);
                    using (UnitOfWork.Start())
                    {
                        FLogin flog, flog2;
                        flog = WFRepository<FLogin>.GetEntity(NewAssignLoginId);
                        FWfWorkflowInstance NewWFInstance = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                        actionInstance = WFRepository<FWfActionInstance>.GetEntity(NewWFInstance.WfCurrentState.WfCurrentActionInstanceId);
                        //YorumStr = "Sistem tarafından onaylanmıştır (Admin)";
                        YorumStr = " This flow has been approved by the system (Admin)";
                        Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(WorkflowInstanceId, WFRepository<FWfActionTaskInstance>.GetEntity(actionInstance.WfStateInstance.WfWorkflowInstance.WfCurrentState.WfCurrentActionInstanceId), flog, wfContext, flog, true, YorumStr);
                        wfContext.Parameters.AddOrChangeItem("OtoOnay", "1");
                        flog = null;
                        flog2 = null;
                        NewWFInstance = null;
                        actionInstance = null;
                        ActionHelpers.deleteMultipleParameter(WorkflowInstanceId);
                    }
                }
                else
                {
                    yonlendirmeHataStr += "2. admin onayına geldi.oto onay yapılmadı.";
                    wfContext.Parameters.AddOrChangeItem("yonlendirmeHataStr", yonlendirmeHataStr);
                    wfContext.Save();
                }
            }

            #endregion DigiflowAdmin'e düşen bir akış varsa otomatik onaylama

            wfContext.Parameters.AddOrChangeItem("yonlendirmeHataStr", yonlendirmeHataStr);
            wfContext.Save();

            #endregion 2 kere onaylama kontrolleri

            #region Object disposing

            assigmentType = null;
            loginType = null;

            #endregion Object disposing

            #region Talebi açan kişinin yöneticisine gitmesi için eklenen parametrenin kaldırılması

            if (wfContext.Parameters.ContainsKey("OwnerYoneticiOnay"))
            {
                wfContext.Parameters.Remove("OwnerYoneticiOnay");
                wfContext.Save();
            }

            #endregion Talebi açan kişinin yöneticisine gitmesi için eklenen parametrenin kaldırılması

            return true;
            */
            #endregion
            return DoExecution(wfContext, actionInstance, null, true);
        }
        private bool DoExecution(WFContext wfContext, FWfActionInstance actionInstance, FWfWorkflowInstance wfInstance, bool doExecution)
        {
            if (doExecution)
            {
                #region Assigment için Gerekli Bilgiler Toplanır

                WorkflowLoginHelper _wfHelper = new WorkflowLoginHelper();
                ActionType CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                ActionType CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
                string assigmentType = "TASKINBOX";
                string loginType = "LOGIN";

                #endregion Assigment için Gerekli Bilgiler Toplanır

                #region Akış ile ilgili Kullanıcı Bilgileri Toplanıyor

                long WorkflowInstanceId = actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                long AssignToLoginId = actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                long LastActionToLoginId = CheckingWorker.GetLastActionToLoginId(WorkflowInstanceId);

                #endregion Akış ile ilgili Kullanıcı Bilgileri Toplanıyor

                #region Akışı açan Kullanıcının Departman Yöneticisi bulunuyor

                CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
                //TODO:Böyle bir kontrole ihtiyaç kalmadı bir sonraki release ile silinebilir

                //if (AssignToLoginId == 0 || AssignToLoginId == 1)
                //{
                //    AssignToLoginId = actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                //    CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                //    CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
                //}
                //else
                //{
                //    CurrentActionType = ActionType.ONWFAPPROVEASSIGNER;
                //    CurrentActionOwnerType = ActionType.ONWFAPPROVEOWNER;
                //}
                long NewAssignLoginId = AssignToLoginId;

                LoginDto dto = new LoginDto();
                dto = WflowDataHelpers.GetDetailsOfLogin(actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId);

                long deptId = 0;

                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("OwnerLoginId=" + actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId.ToString(), "department:" + dto.DepartmentId + " division:" + dto.DivisionId + " unit:" + dto.UnitId + "team: " + dto.TeamId, "EYK");
                GetManager(out NewAssignLoginId, dto, out deptId);

                // Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("NewAssignLoginId", "NewAssignLoginId:" + NewAssignLoginId.ToString(), "EYK");
                string yonlendirmeHataStr = "";
                yonlendirmeHataStr = "AssignToLoginId=" + AssignToLoginId.ToString() + "LastActionToLoginId=" + LastActionToLoginId.ToString();
                long MaxDinamicAssignLoginId = WorkFlowHelpers.WorkFlowInformationHelper.GetMaxDinamicAssignLoginId(actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);

                if (MaxDinamicAssignLoginId == 0)
                {
                    CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                    CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
                    AssignToLoginId = actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId;
                    //NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId); Murat Kasapoğlu değiştirdi.06.01.2025 alternatif yönetici
                    NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId);
                }
                else
                {
                    AssignToLoginId = MaxDinamicAssignLoginId;

                    yonlendirmeHataStr += "MaxDinamicAssignLoginId" + MaxDinamicAssignLoginId;

                    if (AssignToLoginId == 0 || AssignToLoginId == 1 || AssignToLoginId == actionInstance.WfStateInstance.WfWorkflowInstance.OwnerLogin.LoginId)
                    {
                        CurrentActionType = ActionType.ONWFSTARTASSIGNER;
                        CurrentActionOwnerType = ActionType.ONWFSTARTOWNER;
                    }
                    else
                    {
                        CurrentActionType = ActionType.ONWFAPPROVEASSIGNER;
                        CurrentActionOwnerType = ActionType.ONWFAPPROVEOWNER;
                    }

                    //NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId); Murat Kasapoğlu değiştirdi.06.01.2025 alternatif yönetici
                    NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId);
                }

                #endregion Akışı açan Kullanıcının Departman Yöneticisi bulunuyor

                #region Assigment işlemleri yapılarak Departman Yöneticisine Atanıyor

                Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(NewAssignLoginId, loginType, actionInstance.WfActionInstanceId, assigmentType);
                var viewList = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetViewList(NewAssignLoginId.ToString());
                if (viewList.Count == 0 || viewList.IndexOf(actionInstance.WfStateInstance.WfWorkflowInstance.ToString()) < 0)
                {
                    Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(NewAssignLoginId, loginType, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId, "WFVIEW");
                }

                #region Email Action İçin Instance Parametreleri Ekleniyor
                base.EmailActionsAddition(wfContext, actionInstance, false);
                #endregion

                Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionType, WorkflowInstanceId, LastActionToLoginId, NewAssignLoginId, wfContext);
                Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmail(CurrentActionOwnerType, WorkflowInstanceId, LastActionToLoginId, NewAssignLoginId, wfContext);
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.Execute(actionInstance, NewAssignLoginId, NewAssignLoginId, WorkflowHistoryActionType.ASSIGN, "");

                #endregion Assigment işlemleri yapılarak Departman Yöneticisine Atanıyor

                #region 2 kere onaylama kontrolleri

                //Kerem Yeni Delegasyonu olana atama !
                //Yeni atanacak kişinin bu akışa ait delegasyonları kontrol edilir
                //Senaryo 1 Eğer  son onaylayan kişinin delegelerinden biri daha önce onayladıysa akış bir sonraki adıma geçilir
                //Senaryo 2 Eğer atanan kişi daha önce onayladıysa ve akış geri gönderilmemişse bir sonraki adıma geçilir
                //Performans Değerlendirme akışı hariç tutuldu- 1852-zülfiye-26.12.2014
                //zzstring yonlendirmeHataStr = "";
                string YorumStr = "";
                string Senaryo = "";
                bool DelegasyonKontrolu = false;
                //Sözleşme akışı, Ödeme, Macro, Rota ve Ad Hoc da çalışır..

                #region defId setleme

                long sozlesmeId = 1338;
                long odemeId = 1336;
                long AdHocID = 2174;
                long macroID = 1333;
                long rotaID = 1337;

                if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
                {
                    sozlesmeId = 1338;
                    odemeId = 1336;
                    AdHocID = 2174;
                    macroID = 1333;
                    rotaID = 1337;
                }
                else
                {
                    sozlesmeId = 1338;
                    odemeId = 1336;
                    AdHocID = 2174;
                    macroID = 1333;
                    rotaID = 1337;
                }

                #endregion defId setleme

                if (actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == sozlesmeId || actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == odemeId || actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == AdHocID || actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == macroID || actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId == rotaID)
                {
                    #region Otomatik onaylama Senaryo 1 - üzerine atanan kişinin delegesi daha önce onaylamışsa

                    DataTable dt = new DataTable();
                    dt = DigiFlowDelegationManager.GetDelegationsOfUser(NewAssignLoginId, "3", actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId);
                    List<long> AcceptUserList = Digiflow.DataAccessLayer.CheckingWorker.GetLastAcceptLoginList(WorkflowInstanceId);
                    long DelegeId = 0;
                    //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Senaryo 1 kontrolü", "Dynamic Assignment");
                    foreach (DataRow dr in dt.Rows)
                    {
                        DelegeId = Digiturk.Workflow.Digiflow.CoreHelpers.ConvertionHelper.ConvertValue<long>(dr["DELEGATE_REF_ID"]);
                        if (AcceptUserList.Contains(DelegeId))
                        {
                            bool delegeSonuc = false;
                            if (wfContext.Parameters.ContainsKey("Onay"))
                            {
                                if (wfContext.Parameters["Onay"].ToString() == "SendBack")
                                {
                                    delegeSonuc = false;
                                }
                                else
                                {
                                    delegeSonuc = CheckingWorker.IsBeforeAccept2(WorkflowInstanceId, DelegeId);
                                }
                            }
                            else
                            {
                                delegeSonuc = CheckingWorker.IsBeforeAccept2(WorkflowInstanceId, DelegeId);
                            }
                            if (delegeSonuc)
                            {
                                DelegasyonKontrolu = true;
                                Senaryo = "1";
                                //YorumStr = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(DelegeId) + ", " + Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId) + " delegesi olduğundan sistem tarafından onaylanmıştır";
                                YorumStr = "This form is automatically approved since " + Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(DelegeId) + " is " + Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId) + "'s delegate";

                                break;
                            }
                        }
                    }

                    #endregion Otomatik onaylama Senaryo 1 - üzerine atanan kişinin delegesi daha önce onaylamışsa

                    //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Senaryo 1 kontrolü sonu = " + DelegasyonKontrolu.ToString(), "Dynamic Assignment");
                    //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Senaryo 2 kontrolü", "Dynamic Assignment");

                    #region Otomatik Onaylama Senaryo 2 - Üzerine atanan kişi daha önce onaylamış ve akış send back yapılmamışsa

                    if (DelegasyonKontrolu == false)
                    {
                        bool sonuc = false;
                        if (wfContext.Parameters.ContainsKey("Onay"))
                        {
                            if (wfContext.Parameters["Onay"].ToString() == "SendBack")
                            {
                                sonuc = false;
                            }
                            else
                            {
                                sonuc = CheckingWorker.IsBeforeAccept2(WorkflowInstanceId, NewAssignLoginId);
                            }
                        }
                        else
                        {
                            sonuc = CheckingWorker.IsBeforeAccept2(WorkflowInstanceId, NewAssignLoginId);
                        }
                        //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", sonuc.ToString() + ", sonuc ( " + WorkflowInstanceId + "," + NewAssignLoginId, "Dynamic Assignment K");
                        if (sonuc)
                        {
                            //IResourceReader reader = new ResourceReader("Resource.resources");
                            //IDictionaryEnumerator en = reader.GetEnumerator();
                            //string keyValue = "deneme";
                            //if (en.Key == "Main_user")
                            //{
                            //    keyValue = en.Value.ToString();
                            //}

                            // Üzerine atanan kişi bu akışı daha önce onaylamış ve akış daha önce SendBack yapılmamış
                            DelegasyonKontrolu = true;
                            Senaryo = "2";
                            //YorumStr = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId) + ", bu akışı daha önce onayladığından sistem tarafından onaylanmıştır";
                            YorumStr = "This flow has been approved by the system, because previously approved by " + Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId);
                            //YorumStr = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(NewAssignLoginId) + keyValue;
                        }
                    }

                    #endregion Otomatik Onaylama Senaryo 2 - Üzerine atanan kişi daha önce onaylamış ve akış send back yapılmamışsa

                    #region Görev atamalı statelerde otomatik onaylama önüne engel koyulması

                    if (DelegasyonKontrolu)
                    {
                        List<CustomParameterList> Param = new List<CustomParameterList>();
                        Param.Add(new CustomParameterList(":STATE_DEF_ID", actionInstance.WfStateInstance.WfStateDef.WfStateDefId));
                        string stateKontrol = "";
                        stateKontrol = DataAccessLayer.ModelWorking.GetOnlyColumnSQL<string>("DefaultConnection", "select WF_DEF_ID from DT_WORKFLOW.YYS_ACTION_AUTHORIZATION where ACTION_ID = 16 and STATE_DEF_ID = :STATE_DEF_ID", Param);
                        if (stateKontrol != "0")
                        {
                            DelegasyonKontrolu = false;
                            YorumStr = "";
                        }
                        else
                        {
                        }
                    }

                    #endregion Görev atamalı statelerde otomatik onaylama önüne engel koyulması

                    #region delegasyonu var ise otomatik onaylama fonksiyonalitesi

                    if (DelegasyonKontrolu)
                    {
                        System.Threading.Thread.Sleep(2000);
                        using (UnitOfWork.Start())
                        {
                            yonlendirmeHataStr += "Geldi 14,";
                            wfContext.Parameters.AddOrChangeItem("yonlendirmeHataStr", yonlendirmeHataStr);
                            wfContext.Save();

                            FLogin flog, flog2;
                            if (Senaryo == "1") //1.senaryoda  onaylayan delegesi oluyor
                            {
                                flog = WFRepository<FLogin>.GetEntity(DelegeId);
                            }
                            else  //2.senaryoda atanan ve otomatik onaylayan aynı kişi oluyor
                            {
                                flog = WFRepository<FLogin>.GetEntity(NewAssignLoginId);
                            }
                            flog2 = WFRepository<FLogin>.GetEntity(NewAssignLoginId);
                            FWfWorkflowInstance NewWFInstance = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                            actionInstance = WFRepository<FWfActionInstance>.GetEntity(NewWFInstance.WfCurrentState.WfCurrentActionInstanceId);
                            Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(WorkflowInstanceId, WFRepository<FWfActionTaskInstance>.GetEntity(actionInstance.WfStateInstance.WfWorkflowInstance.WfCurrentState.WfCurrentActionInstanceId), flog, wfContext, flog2, true, YorumStr);
                            flog = null;
                            flog2 = null;
                            NewWFInstance = null;
                            actionInstance = null;
                        }
                    }

                    #endregion delegasyonu var ise otomatik onaylama fonksiyonalitesi

                    AcceptUserList = null;

                    //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Senaryo 2 kontrolü sonu  = " + DelegasyonKontrolu.ToString(), "Dynamic Assignment");
                    //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Delegasyon Kontrolü (" + WorkflowInstanceId + "), Senaryo =" + Senaryo + ", Delegasyon Kontrolu = " + DelegasyonKontrolu.ToString() + ", Atanacak Kişi =  " + NewAssignLoginId + ",  Son İşlem Yapan Kişi = " + LastActionToLoginId + ", WFDEF ID =  " + actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId, "Dynamic Assignment");
                }

                #region DigiflowAdmin'e düşen bir akış varsa otomatik onaylama

                if (NewAssignLoginId == 123456789)
                {
                    if (wfContext.Parameters.ContainsKey("OtoOnay") == false)  // akışta bir hata varsa loopa girmemesi için
                    {
                        yonlendirmeHataStr += "Geldi 15,";
                        wfContext.Parameters.AddOrChangeItem("yonlendirmeHataStr", yonlendirmeHataStr);
                        wfContext.Save();
                        System.Threading.Thread.Sleep(2000);
                        using (UnitOfWork.Start())
                        {
                            FLogin flog, flog2;
                            flog = WFRepository<FLogin>.GetEntity(NewAssignLoginId);
                            FWfWorkflowInstance NewWFInstance = WFRepository<FWfWorkflowInstance>.GetEntity(WorkflowInstanceId);
                            actionInstance = WFRepository<FWfActionInstance>.GetEntity(NewWFInstance.WfCurrentState.WfCurrentActionInstanceId);
                            //YorumStr = "Sistem tarafından onaylanmıştır (Admin)";
                            YorumStr = " This flow has been approved by the system (Admin)";
                            Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(WorkflowInstanceId, WFRepository<FWfActionTaskInstance>.GetEntity(actionInstance.WfStateInstance.WfWorkflowInstance.WfCurrentState.WfCurrentActionInstanceId), flog, wfContext, flog, true, YorumStr);
                            wfContext.Parameters.AddOrChangeItem("OtoOnay", "1");
                            flog = null;
                            flog2 = null;
                            NewWFInstance = null;
                            actionInstance = null;
                            ActionHelpers.deleteMultipleParameter(WorkflowInstanceId);
                        }
                    }
                    else
                    {
                        yonlendirmeHataStr += "2. admin onayına geldi.oto onay yapılmadı.";
                        wfContext.Parameters.AddOrChangeItem("yonlendirmeHataStr", yonlendirmeHataStr);
                        wfContext.Save();
                    }
                }

                #endregion DigiflowAdmin'e düşen bir akış varsa otomatik onaylama

                wfContext.Parameters.AddOrChangeItem("yonlendirmeHataStr", yonlendirmeHataStr);
                wfContext.Save();

                #endregion 2 kere onaylama kontrolleri

                #region Object disposing

                assigmentType = null;
                loginType = null;

                #endregion Object disposing

                #region Talebi açan kişinin yöneticisine gitmesi için eklenen parametrenin kaldırılması

                if (wfContext.Parameters.ContainsKey("OwnerYoneticiOnay"))
                {
                    wfContext.Parameters.Remove("OwnerYoneticiOnay");
                    wfContext.Save();
                }

                #endregion Talebi açan kişinin yöneticisine gitmesi için eklenen parametrenin kaldırılması
            }
            else
            {
                long deptId = 0;
                LoginDto dto = new LoginDto();

                long AssignToLoginId = wfInstance.OwnerLogin.LoginId;
                long NewAssignLoginId = AssignToLoginId;
                GetManager(out NewAssignLoginId, dto, out deptId);

                long MaxDinamicAssignLoginId = WorkFlowHelpers.WorkFlowInformationHelper.GetMaxDinamicAssignLoginId(wfInstance.WfWorkflowInstanceId);
                if (MaxDinamicAssignLoginId == 0)
                {
                    //NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId); Murat Kasapoğlu değiştirdi.06.01.2025 alternatif yönetici
                    NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId, wfInstance.WfWorkflowDef.WfWorkflowDefId);
                }
                else
                {
                    AssignToLoginId = MaxDinamicAssignLoginId;

                    //NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId); Murat Kasapoğlu değiştirdi.06.01.2025 alternatif yönetici
                    NewAssignLoginId = CheckingWorker.GetManager(AssignToLoginId, wfInstance.WfWorkflowDef.WfWorkflowDefId);
                }

                if (NewAssignLoginId > 0)
                {
                    string nameSurname = Digiflow.Authorization.WflowDataHelpers.GetLoginNameSurname(NewAssignLoginId);
                    ListNameSurname.Add(nameSurname);
                }
                ListNameSurname.Sort();
            }
            return true;
        }

        private enum ManagertType
        {
            Division = 1,
            Unit = 2,
            Team = 3,
            SubTeam_1 = 4,
            SubTeam_2 = 5,
            SubTeam_3 = 6,
            SubTeam_4 = 7,
            Department = 8
        }

        private static void GetManager(out long NewAssignLoginId, LoginDto dto, out long deptId, int managerType = 1)
        {
            if (managerType > 8) throw new Exception("Üst Yönetici Bulunamadı" + " # department:" + dto.DepartmentId + " division:" + dto.DivisionId + " unit:" + dto.UnitId + "team: " + dto.TeamId + " managerType: " + managerType);
            long DepartmentId = 0, divisionId = 0, UnitId = 0, TeamId = 0, SubTeam_1ID = 0, SubTeam_2ID = 0, SubTeam_3ID = 0, SubTeam_4ID = 0, Subteam_5ID = 0;
            NewAssignLoginId = 0;

            deptId = dto.DivisionId;
            //Kullanıcının en alt seviye yöneticisinden en üst seviye yöneticisine kadar gezilerek departman yöneticisine en yakın yöneticisi bulunuyor

            Subteam_5ID = dto.SubTeam_5_Id;
            SubTeam_4ID = dto.SubTeam_4_Id;
            SubTeam_3ID = dto.SubTeam_3_Id;
            SubTeam_2ID = dto.SubTeam_2_Id;
            SubTeam_1ID = dto.SubTeam_1_Id;
            TeamId = dto.TeamId;
            UnitId = dto.UnitId;
            divisionId = dto.DivisionId;
            DepartmentId = dto.DepartmentId;
            /*
            if (dto.SubTeam_4_Id < 1)
            {
                deptId = Subteam_5ID;
            }
            if (dto.SubTeam_3_Id < 1)
            {
                deptId = SubTeam_4ID;
            }
            if (dto.SubTeam_2_Id < 1)
            {
                deptId = SubTeam_3ID;
            }
            if (dto.SubTeam_1_Id < 1)
            {
                deptId = SubTeam_2ID;
            }
            if (dto.TeamId < 1)
            {
                deptId = SubTeam_1ID;
            }
            if ((dto.UnitId < 1) && (dto.TeamId > 0))
            {
                deptId = TeamId;
            }
            if ((dto.DivisionId < 1) && (dto.UnitId > 0))
            {
                deptId = UnitId;
            }
            if (dto.DivisionId > 0) //divisionId var ve managerType=
            {
                deptId = divisionId;
            }

            if (dto.DepartmentId > 0) //divisionId var ve managerType=
            {
                DepartmentId = dto.DepartmentId;
            }
            */
            //Aranan Yönetici tipi ne ise deptID ona setleniyor
            switch ((ManagertType)managerType)
            {
                case ManagertType.Unit:
                    deptId = UnitId;
                    break;

                case ManagertType.Team:
                    deptId = TeamId;
                    break;

                case ManagertType.SubTeam_1:
                    deptId = SubTeam_1ID;
                    break;

                case ManagertType.SubTeam_2:
                    deptId = SubTeam_2ID;
                    break;

                case ManagertType.SubTeam_3:
                    deptId = SubTeam_3ID;
                    break;

                case ManagertType.SubTeam_4:
                    deptId = SubTeam_4ID;
                    break;

                case ManagertType.Department:
                    deptId = DepartmentId;
                    break;

                default:
                    break;
            }

            try
            {
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Getmanager", "NewAssignLoginId:" + NewAssignLoginId + " managerType:" + managerType + " deptId:" + deptId, "EYK");
                NewAssignLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetManagerOf(deptId);
                // Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("GetManagerOf", "deptId:" + deptId + " NewAssignLoginId:" + NewAssignLoginId + " managerType:" + managerType, "EYK");
            }
            catch (Exception)
            {
            }
            //Organizasyon şemasında üst birimi olup o üst birimin yöneticisinin olmadığı durumlar oluyor
            //sistem division,unit,team, sub teamlara bakıyor hala daha arada yönetici bulamamışsa departman yöneticisini döndürüyor
            if (NewAssignLoginId == null || NewAssignLoginId < 1)
            {
                int newMngTypeId = managerType + 1;
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("NoManagerOf", "deptId:" + deptId + " NewAssignLoginId:" + NewAssignLoginId + " managerType:" + managerType + " newMngTypeId:" + newMngTypeId, "EYK");
                GetManager(out NewAssignLoginId, dto, out deptId, newMngTypeId);
            }
        }

        public List<string> GetAssignmentListNames(WFContext wfContext, FWfWorkflowInstance wfInstance, long WfStateDefId)
        {
            DoExecution(wfContext, null, wfInstance, false);
            return ListNameSurname;
        }
    }
}