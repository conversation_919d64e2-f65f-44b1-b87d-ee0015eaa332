﻿using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Domain.Entities.History;
using DigiflowAPI.Domain.Entities.Workflow;
using DigiflowAPI.Domain.Enums;

namespace DigiflowAPI.Domain.Interfaces.Repositories;

public interface IWorkflowRepository
{
        Task<IEnumerable<FWfWorkflowDef>> GetAllWorkflowsAsync();
        Task<IEnumerable<FWfWorkflowDef>> GetAllAdminWorkflowsAsync();
        Task<long> GetActiveDelegateWithRecursiveDateValidation(long ownerLoginID, long delegationLoginId, long workflowDefID, DateTime delegationStartDate, DateTime delegationEndDate);
        Task<long> GetActiveDelegateWithRecursive(long loginID, long workflowDefID);
        Task<bool> CheckForDelegationOwn(long LoginId, long WfDefinitionId, DateTime StartDate, DateTime EndDate);
        Task<long> GetLastActionToLoginId(long WfInsId);
        Task<IList<string>?> GetLastActionToLoginIdList(long WfInsId);
        Task<IList<long>> GetAssignedUser(long WfInsId);
        Task<IList<FLogin>> GetAssignedList(long WfActionInsId, long? WfDefId = null, string assignmentTypeCd = "TASKINBOX");

        Task<long> AssignToId(long wfInsId, long loginId);
        Task<IList<FLogin>> AssignToIdList(long WfInsId);
        Task<IList<long>> AssignToIdListLong(long wfInsId);
        Task<bool> DelegateCheck(long InstanceId, long LoginId);
        Task<FWfWorkflowInstance?> GetWorkflowInstance(long wfInstanceId);
        Task<string> GetWorkflowAdmins(long WfWorkflowDefId);
        Task<string> GetWorkflowAdmins(string PageName);
        Task<IEnumerable<VwWorkflowHistory>> GetWorkflowHistory(long WfInstanceId);
        Task<VwWorkflowHistory?> GetHistory(long WfHistoryId);
        Task UpdateHistoryCommentAsync(long wfHistoryId, string NewComment, List<string> uploadedFiles = null);
        Task<bool> GetDaemonStatus(long wfInstanceId);
        Task<int> GetStateDefId(string stateDefAdi);
        // TODO: Replace WorkflowHistoryDto with domain entity
        // Task<IEnumerable<WorkflowHistoryDto>> GetWorkflowInstanceHistoryMigrateDelegation(long wfdefId, long WfInstanceId, IEnumerable<WorkflowHistoryDto> historyData);
        bool IsInFlowManagerList(string val, string AccessLoginIdList);
        Task<long> GetMaxDinamicAssignLoginId(long WorkFlowInstanceId);
        Task<bool?> IsWorkflowInstanceExists(int wfInstanceId);
        Task<bool> DelegationCheck(long InstanceId, long AssignedLoginId, long ActionLoginId, DateTime DelegationCheckDate);
        Task<bool> IsActionFlow(long instanceId, long loginId);
        Task<YYSAdminType> GetFlowAdminType(long LoginId);
        Task<IEnumerable<FWfWorkflowDef>> GetLiveWorkFlows();
        Task<IEnumerable<FWfWorkflowDef>> GetWorkflowsOfAdmin(long adminId, bool isAdmin);
        Task<bool> IsStateStatik(long WorkFlowInstanceId, long WorkFlowStateId);
        Task<bool> IsFlowAdmin(long instanceId, long userId);
        Task<long> CommendToId(long instanceId);
        Task<IList<long>> CommendToIdLongList(long instanceId);
        Task<long> DelegationUserId(long loginId, long workflowDefId);
        Task<IList<FLogin>> GetCommendToLoginList(long instanceId);
        Task<bool> IsWfAdmin(long wfDefId);
        Task<bool> IsViewUser(long UserId, long WfInstanceId, long wfDefinitionId);
        Task<bool> IsReportAdmin(long WfDefId, long loginId);
        Task<bool> IsRolledBack(long instanceId);
        Task<FWfWorkflowInstance> GetCurrentWfIns(long instanceId);
        Task<FWfActionTaskInstance> GetCurrentActionTaskInstance(long instanceId);

        Task<List<DelegationWorkflowEntity>> GetDelegationsOfUserAsync(long loginId, string delegationType);
        Task<FWfDelegation?> GetDelegationInfoAsync(long delegationId);
        Task<int> UpdateDelegationRequestAsync(long delegationId, DateTime startDate, DateTime endDate);
        Task<List<DelegationWorkflowEntity>> GetAllDelegationsAsync(long adminId, string delegationType);
        Task<bool> EndDelegationAsync(long[] delegationIds);

        Task<IEnumerable<MonitoringRequestEntity>> GetAllMonitoringsAsync(long adminId);
        Task<IEnumerable<MonitoringRequestEntity>> GetActiveMonitoringsAsync(long loginId);
        Task<bool> EndMonitoringAsync(long[] monitoringIds);
        Task<bool> UpdateEntity(long entityId, string entityType, Dictionary<string, object> updates);
        // Task<bool> UpdateEntity(UpdateEntityRequestDto request, string workflowName);

        Task<IEnumerable<long>> GetCommendToLoginDelegeIDList(long WfInsId);

}
