.dxeLoadingDiv_Office2010Blue
{
	background: White;
    opacity: 0.85;
    filter: alpha(opacity=85);
    cursor: wait;
}
.dxeLoadingDivWithContent_Office2010Blue
{
	background: White;
    opacity: 0.01;
    filter: alpha(opacity=1);
}

.dxeLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxeLoadingPanelWithContent_Office2010Blue
{
    font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}

.dxeLoadingPanel_Office2010Blue td.dx,
.dxeLoadingPanelWithContent_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}

.dxeReadOnly_Office2010Blue
{
}
.dxeBase_Office2010Blue
{
    font-family: Verdana;
    font-size: 8pt;
}
/* -- ErrorFrame -- */
.dxeErrorCell_Office2010Blue,
.dxeErrorCell_Office2010Blue td
{
    font-family: Verdana;
    font-size: 8pt;
	color: Red;
}
.dxeErrorCell_Office2010Blue
{
	padding-left: 4px;
	padding-right: 5px;
}
.dxeErrorFrameWithoutError_Office2010Blue {
    border: 1px solid Red;
}
.dxeErrorFrameWithoutError_Office2010Blue .dxeControlsCell_Office2010Blue {
    padding: 2px;
}

.dxeEditArea_Office2010Blue
{
	font-family: Verdana;
	font-size: 8pt;
	border: 1px solid #8ba0bc;
}
.dxeMemoEditArea_Office2010Blue,
input.dxeEditArea_Office2010Blue,
.dxeBase_Office2010Blue input
{
    outline: none;
}

/* -- Buttons -- */
.dxeButtonEditButton_Office2010Blue,
.dxeCalendarButton_Office2010Blue,
.dxeButtonEditButton_Office2010Blue td.dx,
.dxeCalendarButton_Office2010Blue td.dx,
.dxeSpinIncButton_Office2010Blue,
.dxeSpinDecButton_Office2010Blue,
.dxeSpinLargeIncButton_Office2010Blue,
.dxeSpinLargeDecButton_Office2010Blue,
.dxeSpinIncButton_Office2010Blue td.dx,
.dxeSpinDecButton_Office2010Blue td.dx,
.dxeSpinLargeIncButton_Office2010Blue td.dx,
.dxeSpinLargeDecButton_Office2010Blue td.dx
{
    font-family: Verdana;
    font-size: 8pt;
    font-weight: normal;
	text-align: center;
	white-space: nowrap;
}

.dxeButtonEditButton_Office2010Blue,
.dxeCalendarButton_Office2010Blue,
.dxeSpinIncButton_Office2010Blue,
.dxeSpinDecButton_Office2010Blue,
.dxeSpinLargeIncButton_Office2010Blue,
.dxeSpinLargeDecButton_Office2010Blue
{
	vertical-align: middle;
	cursor: pointer;
}
.dxeButtonEdit_Office2010Blue .dxeSBC,
.dxeCalendarButton_Office2010Blue,
.dxeButtonEditButton_Office2010Blue
{
    border-style: solid;
	border-color: #8ba0bc;
}
.dxeCalendarButton_Office2010Blue
{
    border-width: 1px;
}
.dxeButtonEditButton_Office2010Blue,
.dxeButtonEdit_Office2010Blue .dxeSBC
{
    border-width: 0 0 0 1px;
}
.dxeButtonEdit_Office2010Blue .dxeButtonLeft,
.dxeButtonEdit_Office2010Blue .dxeSBC.dxeButtonLeft
{
    border-width: 0 1px 0 0;
}
.dxeSpinIncButton_Office2010Blue,
.dxeSpinDecButton_Office2010Blue,
.dxeSpinLargeIncButton_Office2010Blue,
.dxeSpinLargeDecButton_Office2010Blue
{
    border-width: 0;
}

.dxeButtonEditButton_Office2010Blue,
.dxeSpinLargeIncButton_Office2010Blue,
.dxeSpinLargeDecButton_Office2010Blue
{
    background: #c6d7e8 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtDropDownBtnBack.png")%>') repeat-x left top;
}
.dxeSpinIncButton_Office2010Blue
{
    background: #d6e4f1 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtSpinIncBtnBack.png")%>') repeat-x left top;
}
.dxeSpinDecButton_Office2010Blue
{
    background: #c5d6e7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtSpinDecBtnBack.png")%>') repeat-x left top;
}

.dxeButtonEditButton_Office2010Blue table.dxbebt
{
    padding: 3px 2px 3px 3px;
}
.dxeSpinIncButton_Office2010Blue table.dxbebt,
.dxeSpinDecButton_Office2010Blue table.dxbebt,
.dxeSpinLargeIncButton_Office2010Blue table.dxbebt,
.dxeSpinLargeDecButton_Office2010Blue table.dxbebt
{
    padding: 0;
}

.dxeSpinIncButton_Office2010Blue
{
    padding: 1px 0 2px;
}
.dxeSpinDecButton_Office2010Blue
{
   padding: 3px 0 2px;
}
.dxeSpinLargeIncButton_Office2010Blue,
.dxeSpinLargeDecButton_Office2010Blue
{
    padding: 6px 0;
}

.dxeButtonEditButton_Office2010Blue table.dxbebt,
.dxeSpinLargeIncButton_Office2010Blue table.dxbebt,
.dxeSpinLargeDecButton_Office2010Blue table.dxbebt
{
	width: 9px;
}
.dxeSpinIncButton_Office2010Blue table.dxbebt,
.dxeSpinDecButton_Office2010Blue table.dxbebt
{
    width: 15px;
}
.dxeCalendarButton_Office2010Blue
{
	font-size: 8pt;
	background: #d1dfee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtBtnBack.png")%>') repeat-x left top;
	padding: 4px 11px;
	padding-top: 3px;
	width: 32px;
}
.dxeCalendarButton_Office2010Blue td.dx
{
	font-size: 8pt;
	text-align: center;
	white-space: nowrap;
}
.dxeCalendarButton_Office2010Blue table.dxbebt
{
	width: 100%;
}

/* -- Pressed -- */
.dxeCalendarButtonPressed_Office2010Blue
{
	border-color: #c2762b;
}
.dxeCalendarButtonPressed_Office2010Blue
{
    background: #fee287 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtBtnPBack.png")%>') repeat-x left top;
}
.dxeButtonEditButtonPressed_Office2010Blue,
.dxeSpinIncButtonPressed_Office2010Blue,
.dxeSpinDecButtonPressed_Office2010Blue,
.dxeSpinLargeIncButtonPressed_Office2010Blue,
.dxeSpinLargeDecButtonPressed_Office2010Blue
{
    background: #f3d984 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtDropDownBtnPBack.png")%>') repeat-x left top;
}
/* -- Hover -- */
.dxeCalendarButtonHover_Office2010Blue
{
	border-color: #eecc53;
}
.dxeCalendarButtonHover_Office2010Blue
{
    background: #fcf8e5 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtBtnHBack.png")%>') repeat-x left top;
}
.dxeButtonEditButtonHover_Office2010Blue,
.dxeSpinLargeIncButtonHover_Office2010Blue,
.dxeSpinLargeDecButtonHover_Office2010Blue
{
    background: #f8e9ac url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtDropDownBtnHBack.png")%>') repeat-x left top;
}
.dxeSpinIncButtonHover_Office2010Blue,
.dxeSpinDecButtonHover_Office2010Blue
{
    background: #f8e18a url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtSpinIncDecBtnHBack.png")%>') repeat-x left top;
}

.dxeButtonEdit_Office2010Blue
{
    background-color: white;
    border: 1px solid #8ba0bc;
    width: 170px;
}
.dxeButtonEdit_Office2010Blue .dxeEditArea_Office2010Blue {
    background-color: white;
}
.dxeButtonEdit_Office2010Blue .dxeEditArea_Office2010Blue,
.dxeButtonEdit_Office2010Blue td.dxic
{
	width: 100%;
}
.dxeButtonEdit_Office2010Blue td.dxic
{
    padding: 2px 4px 1px;
}
.dxeButtonEdit_Office2010Blue .dxeIIC,
.dxeButtonEdit_Office2010Blue .dxeIICR
{
    padding: 1px;
}
.dxeButtonEdit_Office2010Blue .dxeIIC img {
    padding-left: 3px;
}
.dxeButtonEdit_Office2010Blue .dxeIICR img {
    padding-right: 3px;
}

.dxeTextBox_Office2010Blue,
.dxeMemo_Office2010Blue
{
    background-color: white;
    border: 1px solid #8ba0bc;
}
.dxeTextBox_Office2010Blue td.dxic
{
	padding: 2px 4px 1px;
	width: 100%;
}
.dxeTextBox_Office2010Blue .dxeEditArea_Office2010Blue
{
    background-color: white;
}
.dxeRadioButtonList_Office2010Blue
{
    border: 1px solid #8ba0bc;
}
.dxeRadioButtonList_Office2010Blue,
.dxeRadioButtonList_Office2010Blue table
{
    font-family: Verdana;
    font-size: 8pt;
}
.dxeRadioButtonList_Office2010Blue td.dxe
{
    padding: 7px 25px 6px 11px;
}
.dxeRadioButtonList_Office2010Blue label
{
	margin-right: 6px;
}
/* Disabled */
.dxeDisabled_Office2010Blue .dxeButtonEditButton_Office2010Blue,
.dxeDisabled_Office2010Blue .dxeSpinLargeIncButton_Office2010Blue,
.dxeDisabled_Office2010Blue .dxeSpinLargeDecButton_Office2010Blue
{
    background: #ecf2f7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtDropDownBtnDisabledBack.png")%>') repeat-x left top;
}
.dxeDisabled_Office2010Blue .dxeSpinIncButton_Office2010Blue
{
    background: #f2f7fb url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtSpinIncBtnDisabledBack.png")%>') repeat-x left top;
}
.dxeDisabled_Office2010Blue .dxeSpinDecButton_Office2010Blue
{
    background: #ecf2f7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtSpinDecBtnDisabledBack.png")%>') repeat-x left top;
}

/* -- Memo -- */
.dxeMemo_Office2010Blue
{
}
.dxeMemoEditArea_Office2010Blue
{
	background-color: white;
	font-family: Verdana;
	font-size: 8pt;
}
.dxeMemo_Office2010Blue td
{
	padding: 0 0 0 1px;
	width: 100%;
}

/* -- Hyperlink -- */
.dxeHyperlink_Office2010Blue
{
    font-family: Verdana;
    font-size: 8pt;
    font-weight: normal;
    color: #1e395b;
    text-decoration: none;
}
a.dxeHyperlink_Office2010Blue:hover
{
    text-decoration: underline;
}
a.dxeHyperlink_Office2010Blue:visited
{
    color: #8467b2;
}

/* -- ListBox -- */
.dxeListBox_Office2010Blue
{
    font-family: Verdana;
    font-size: 8pt;
	background-color: white;
	border: 1px solid #8ba0bc;
    width: 70px;
    height: 109px;
}
.dxeListBox_Office2010Blue div.dxlbd
{
    padding-top: 1px;
    height: 108px;
}
.dxeListBoxItemRow_Office2010Blue
{
    cursor: default;
}
.dxeListBoxItem_Office2010Blue
{
    font-family: Verdana;
    font-weight: normal;
    font-size: 8pt;
    color: Black;
    padding: 2px 5px;
    white-space: nowrap;
    text-align: left;
    border-style: solid;
    border-color: White;
    border-width: 0 1px 1px 1px;
}
.dxeListBoxItem_Office2010Blue em
{
    background: none repeat scroll 0 0 #d5e8ff;
    color: Black;
    font-weight:bold;
    font-style:normal;
}

.dxeListBox_Office2010Blue td.dxeI,
.dxeListBox_Office2010Blue td.dxeIM,
.dxeListBox_Office2010Blue .dxeHIC,
.dxeListBox_Office2010Blue td.dxeFTM,
.dxeListBox_Office2010Blue td.dxeTM,
.dxeListBox_Office2010Blue td.dxeC,
.dxeListBox_Office2010Blue td.dxeCM,
.dxeListBox_Office2010Blue td.dxeHCC,
.dxeListBox_Office2010Blue td.dxeMI,
.dxeListBox_Office2010Blue td.dxeMIM
{
    border-right: 0!important;
}

.dxeListBox_Office2010Blue td.dxeIR,
.dxeListBox_Office2010Blue td.dxeIMR,
.dxeListBox_Office2010Blue .dxeHICR,
.dxeListBox_Office2010Blue td.dxeFTMR,
.dxeListBox_Office2010Blue td.dxeTMR,
.dxeListBox_Office2010Blue td.dxeCR,
.dxeListBox_Office2010Blue td.dxeCMR,
.dxeListBox_Office2010Blue td.dxeHCCR,
.dxeListBox_Office2010Blue td.dxeMIR,
.dxeListBox_Office2010Blue td.dxeMIMR
{
    border-left: 0!important;
}

.dxeListBox_Office2010Blue td.dxeCM,
.dxeListBox_Office2010Blue td.dxeHCC,
.dxeListBox_Office2010Blue td.dxeCMR,
.dxeListBox_Office2010Blue td.dxeHCCR
{
    width: 25px;
}

.dxeListBox_Office2010Blue td.dxeIM,
.dxeListBox_Office2010Blue td.dxeIMR
{
    width: 0;
}

.dxeListBox_Office2010Blue td.dxeT
{
    width: 100%;
    padding-left: 0!important;
}

.dxeListBox_Office2010Blue td.dxeTR
{
    width: 100%;
    padding-right: 0!important;
}

.dxeListBox_Office2010Blue td.dxeT,
.dxeListBox_Office2010Blue td.dxeMI
{
    border-left: 0!important;
}

.dxeListBox_Office2010Blue td.dxeTR,
.dxeListBox_Office2010Blue td.dxeMIR
{
    border-right: 0!important;
}

.dxeListBox_Office2010Blue td.dxeFTM,
.dxeListBox_Office2010Blue td.dxeTM,
.dxeListBox_Office2010Blue td.dxeLTM,
.dxeListBox_Office2010Blue .dxeHFC,
.dxeListBox_Office2010Blue .dxeHC,
.dxeListBox_Office2010Blue .dxeHLC,
.dxeListBox_Office2010Blue td.dxeFTMR,
.dxeListBox_Office2010Blue td.dxeTMR,
.dxeListBox_Office2010Blue td.dxeLTMR,
.dxeListBox_Office2010Blue .dxeHFCR,
.dxeListBox_Office2010Blue .dxeHCR,
.dxeListBox_Office2010Blue .dxeHLCR
{
    overflow: hidden;
}

.dxeListBox_Office2010Blue td.dxeFTM,
.dxeListBox_Office2010Blue td.dxeTM,
.dxeListBox_Office2010Blue .dxeHFC,
.dxeListBox_Office2010Blue .dxeHC
{
    padding-right: 6px!important;
}

.dxeListBox_Office2010Blue td.dxeFTMR,
.dxeListBox_Office2010Blue td.dxeTMR,
.dxeListBox_Office2010Blue .dxeHFCR,
.dxeListBox_Office2010Blue .dxeHCR
{
    padding-left: 6px!important;
}

.dxeListBox_Office2010Blue td.dxeLTM,
.dxeListBox_Office2010Blue td.dxeTM,
.dxeListBox_Office2010Blue .dxeHC,
.dxeListBox_Office2010Blue .dxeHLC
{
    padding-left: 6px!important;
}

.dxeListBox_Office2010Blue td.dxeLTMR,
.dxeListBox_Office2010Blue td.dxeTMR,
.dxeListBox_Office2010Blue .dxeHCR,
.dxeListBox_Office2010Blue .dxeHLCR
{
    padding-right: 6px!important;
}

.dxeListBox_Office2010Blue .dxeFTM,
.dxeListBox_Office2010Blue .dxeTM,
.dxeListBox_Office2010Blue .dxeHFC,
.dxeListBox_Office2010Blue .dxeHC,
.dxeListBox_Office2010Blue .dxeLTM,
.dxeListBox_Office2010Blue .dxeTM,
.dxeListBox_Office2010Blue .dxeHC,
.dxeListBox_Office2010Blue .dxeHLC,
.dxeListBox_Office2010Blue td.dxeIM,
.dxeListBox_Office2010Blue td.dxeFTM,
.dxeListBox_Office2010Blue td.dxeTM,
.dxeListBox_Office2010Blue td.dxeCM,
.dxeListBox_Office2010Blue td.dxeMIM,
.dxeListBox_Office2010Blue .dxeFTMR,
.dxeListBox_Office2010Blue .dxeTMR,
.dxeListBox_Office2010Blue .dxeHFCR,
.dxeListBox_Office2010Blue .dxeHCR,
.dxeListBox_Office2010Blue .dxeLTMR,
.dxeListBox_Office2010Blue .dxeTMR,
.dxeListBox_Office2010Blue .dxeHCR,
.dxeListBox_Office2010Blue .dxeHLCR,
.dxeListBox_Office2010Blue td.dxeIMR,
.dxeListBox_Office2010Blue td.dxeFTMR,
.dxeListBox_Office2010Blue td.dxeTMR,
.dxeListBox_Office2010Blue td.dxeCMR,
.dxeListBox_Office2010Blue td.dxeMIMR
{
    border-top-width: 0;
    border-bottom-width: 0;
}

/*Grid lines*/

.dxeListBox_Office2010Blue td.dxeLTM,
.dxeListBox_Office2010Blue td.dxeTM,
.dxeListBox_Office2010Blue td.dxeMIM
{
    border-left: 1px solid #cfddee !important;
}

.dxeListBox_Office2010Blue td.dxeLTMR,
.dxeListBox_Office2010Blue td.dxeTMR,
.dxeListBox_Office2010Blue td.dxeMIMR
{
    border-right: 1px solid #cfddee !important;
}

.dxeListBox_Office2010Blue td.dxeIM,
.dxeListBox_Office2010Blue td.dxeFTM,
.dxeListBox_Office2010Blue td.dxeTM,
.dxeListBox_Office2010Blue td.dxeLTM,
.dxeListBox_Office2010Blue td.dxeCM,
.dxeListBox_Office2010Blue td.dxeMIM,
.dxeListBox_Office2010Blue td.dxeIMR,
.dxeListBox_Office2010Blue td.dxeFTMR,
.dxeListBox_Office2010Blue td.dxeTMR,
.dxeListBox_Office2010Blue td.dxeLTMR,
.dxeListBox_Office2010Blue td.dxeCMR,
.dxeListBox_Office2010Blue td.dxeMIMR
{
    border-bottom: solid 1px #cfddee;
}

.dxeListBoxItemSelected_Office2010Blue     /* inherits dxeListBoxItem */
{
    color: Black;
    background: #e2ecf7;
}
.dxeListBoxItemHover_Office2010Blue        /* inherits dxeListBoxItem */
{
    color: Black;
    background: #d5e8ff;
}
.dxeListBoxItemHover_Office2010Blue em,
.dxeListBoxItemSelected_Office2010Blue em
{
    background: none;
}

/*Header*/

.dxeListBox_Office2010Blue .dxeHD
{
    background: #e4effb url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.lbHeaderBack.png")%>') repeat-x left top;
    border-bottom: 1px solid #8ba0bc;
}
.dxeHD .dxeListBoxItem_Office2010Blue
{
    color: #1e395b;
    border-width: 0 1px;
    padding-top: 3px;
    padding-bottom: 3px;
}

.dxeListBox_Office2010Blue .dxeHC,
.dxeListBox_Office2010Blue .dxeHLC,
.dxeListBox_Office2010Blue td.dxeHMIC
{
    border-left: 1px solid #8ba0bc;
}

.dxeListBox_Office2010Blue .dxeHCR,
.dxeListBox_Office2010Blue .dxeHLCR,
.dxeListBox_Office2010Blue td.dxeHMICR
{
    border-right: 1px solid #8ba0bc;
    text-align: right;
}

.dxeListBox_Office2010Blue .dxeHIC,
.dxeListBox_Office2010Blue .dxeHFC,
.dxeListBox_Office2010Blue .dxeHCC
{
    border-left: 1px solid #e8f0fb;
}

.dxeListBox_Office2010Blue .dxeHICR,
.dxeListBox_Office2010Blue .dxeHFCR,
.dxeListBox_Office2010Blue .dxeHCCR
{
    border-right: 1px solid #e8f0fb;
    text-align: right;
}

.dxeListBox_Office2010Blue .dxeHFC,
.dxeListBox_Office2010Blue .dxeHC,
.dxeListBox_Office2010Blue .dxeHMIC
{
    border-right: 0;
}

.dxeListBox_Office2010Blue .dxeHFCR,
.dxeListBox_Office2010Blue .dxeHCR,
.dxeListBox_Office2010Blue .dxeHMICR
{
    border-left: 0;
    text-align: right;
}

.dxeListBox_Office2010Blue .dxeHLC
{
    border-right: 1px solid #cfddee;
}

.dxeListBox_Office2010Blue .dxeHLCR
{
    border-left: 1px solid #cfddee;
    text-align: right;
}

/* -- Calendar -- */
.dxeCalendar_Office2010Blue
{
    font-weight: normal;
    color: #1e395b;
    border: 1px solid #8ba0bc;
    background-color: #cfddee;
    cursor: default;
}
.dxeCalendar_Office2010Blue td.dxMonthGrid
{
    padding: 1px 8px;
}
.dxeCalendar_Office2010Blue td.dxMonthGridWithWeekNumbers
{
    padding: 1px 20px 6px 8px;
}
.dxeCalendar_Office2010Blue td.dxMonthGridWithWeekNumbersRtl
{
    padding: 1px 8px 1px 20px;
}
.dxeCalendarDayHeader_Office2010Blue
{
    font-family: Verdana;
    font-size: 8pt;
    padding: 2px 4px 6px;
    border-bottom: 1px solid #b7c8dd;
}
.dxeCalendarWeekNumber_Office2010Blue
{
    font-family: Verdana;
    font-size: 7pt;
    text-align: right;
    padding: 3px 8px 2px 4px;
    color: #abb7c6;
}
.dxeCalendarDay_Office2010Blue
{
    font-family: Verdana;
    font-size: 8pt;
    padding: 2px 5px 3px;
    text-align: center;
}
.dxeCalendarWeekend_Office2010Blue        /* inherits dxeCalendarDay */
{
    color: #C00000;
}
.dxeCalendarOtherMonth_Office2010Blue     /* inherits dxeCalendarDay */
{
    color: #b6c3d2;
}
.dxeCalendarOutOfRange_Office2010Blue     /* inherits dxeCalendarDay */
{
    color: #b6c3d2;
}
.dxeCalendarSelected_Office2010Blue       /* inherits dxeCalendarDay */
{
    color: #1e395b;
    background-color: #a9c1de;
    border-width: 0;
    padding: 2px 5px 3px;
}
.dxeCalendarToday_Office2010Blue         /* inherits dxeCalendarDay */
{
    padding: 1px 4px 2px;
    border: 1px solid #c95c05;
}
.dxeCalendarHeader_Office2010Blue
{
    border-style: none;
    padding: 4px;
}
.dxeCalendarHeader_Office2010Blue td.dxe
{
    font-family: Verdana;
    font-size: 8pt;
    text-align: center;
	cursor: pointer;
}
.dxeCalendarFooter_Office2010Blue
{
    background-color: #cfddee;
    padding: 10px 0;
    border-top: 1px solid #b7c8dd;
}
.dxeCalendarFastNav_Office2010Blue
{
    color: #1e395b;
    background: #cfddee;
    border: 1px solid #8ba0bc;
    border-bottom-width: 0;
    padding: 5px 8px;
    cursor: default;
}
.dxeCalendarFastNavMonthArea_Office2010Blue
{
    padding: 0px 9px;
}
.dxeCalendarFastNavYearArea_Office2010Blue
{
}
.dxeCalendarFastNavFooter_Office2010Blue
{
    color: #1e395b;
    background: #cfddee;
    padding: 8px 0 17px 0;
    border: 1px solid #8ba0bc;
    border-top-width: 0;
}
.dxeCalendarFastNavMonth_Office2010Blue,
.dxeCalendarFastNavYear_Office2010Blue
{
    font: normal 8pt Verdana;
    color: #1e395b;
    padding: 3px 5px;
    text-align: center;
	cursor: pointer;
}
.dxeCalendarFastNavYear_Office2010Blue
{
    padding: 3px 5px;
}
.dxeCalendarFastNavMonth_Office2010Blue
{
	padding: 6px;
}

.dxeCalendarFastNavMonthHover_Office2010Blue,
.dxeCalendarFastNavYearHover_Office2010Blue
{
    color: #1e395b;
    padding: 2px 4px;
    border: 1px solid #c95c05
}
.dxeCalendarFastNavMonthHover_Office2010Blue
{
	padding: 5px;
}

.dxeCalendarFastNavMonthSelected_Office2010Blue,
.dxeCalendarFastNavYearSelected_Office2010Blue
{
    color: #1e395b;
    background-color: #a9c1de;
    border-width: 0;
}
.dxeCalendarFastNavYearSelected_Office2010Blue
{
    padding: 3px 5px;
}
.dxeCalendarFastNavMonthSelected_Office2010Blue
{
    padding: 6px;
}
/* Disabled */
.dxeDisabled_Office2010Blue,
.dxeDisabled_Office2010Blue td.dxe
{
	color: #b2b7bd;
	cursor: default;
}
a.dxeDisabled_Office2010Blue:hover
{
    color: #b2b7bd;
}
.dxeButtonDisabled_Office2010Blue,
.dxeButtonDisabled_Office2010Blue td.dxe
{
	border-color: #bdcbdf;
	color: #b2b7bd;
	cursor: default;
}
/* -- Button -- */
.dxbButton_Office2010Blue
{
  	color: #1e395b;
  	font-weight:normal;
	font-size: 8pt;
	font-family: Verdana;
	vertical-align: middle;
	border: 1px solid #abbad0;
	background: #d1dfef url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtBtnBack.png")%>') repeat-x left top;
    padding: 1px;
	cursor: pointer;
}
.dxbButtonHover_Office2010Blue
{
  	color: #1e395b;
	background: #fcf8e5 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtBtnHBack.png")%>') repeat-x left top;
	border: solid 1px #eecc53;
}
.dxbButtonChecked_Office2010Blue
{
    color: #1e395b;
	background: #fee287 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtBtnPBack.png")%>') repeat-x left top;
	border: solid 1px #c2762b;
}
.dxbButtonPressed_Office2010Blue
{
  	color: #1e395b;
	background: #fee287 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtBtnPBack.png")%>') repeat-x left top;
	border: solid 1px #c2762b;
}
.dxbButton_Office2010Blue div.dxb
{
    padding: 3px 15px;
	border: 0px;
}
.dxbButton_Office2010Blue div.dxbf
{
    padding: 2px 14px;
	border: dotted 1px black;
}
.dxbButton_Office2010Blue div.dxb table
{
  	color: #1e395b;
	font-size: 8pt;
	font-family: Verdana;
}
.dxbButton_Office2010Blue div.dxb td.dxb
{
    background-color: transparent!important;
    background-image: url('')!important;
    border-width: 0px!important;
    padding: 0px!important;
}
/* Disabled */
.dxbDisabled_Office2010Blue
{
    border-color: #bdcbdf;
	color: #87929f;
	background-image: none;
	background-color: #dae0e6;
	cursor: default;
}
.dxbDisabled_Office2010Blue td.dxb
{
	color: #87929f;
}
/* -- FilterControl -- */
.dxfcTable_Office2010Blue
{
	border-collapse: separate!important;
}
.dxfcTable_Office2010Blue td.dxfc
{
	padding: 0px 0px 0px 3px;
	vertical-align: middle;
	font: 8pt Verdana;
	color: Black;
}
a.dxfcPropertyName_Office2010Blue
{
	white-space: nowrap!important;
	color: Blue!important;
}
a.dxfcGroupType_Office2010Blue
{
	white-space: nowrap!important;
	padding: 0px 3px 0px 3px!important;
	color: Red!important;
}
a.dxfcOperation_Office2010Blue
{
	white-space: nowrap!important;
	color: Green!important;
}
a.dxfcValue_Office2010Blue
{
	white-space: nowrap!important;
	color: Gray!important;
}

.dxfcLoadingDiv_Office2010Blue
{
	background: white;
	opacity: 0.01;
	filter: alpha(opacity=1);
}
.dxfcLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxfcLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}

.dxeMaskHint_Office2010Blue
{
    font: 8pt Verdana;
    color: #1e395b;
	background: #ffffe1;
	border: 1px solid Black;
	padding: 2px 5px 3px;
}

/* -- ProgressBar -- */
.dxeProgressBar_Office2010Blue
{
    background: #f9f9fa url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtProgressBack.png")%>') repeat-x left top;
    border: 1px solid #a5acb5;
}
.dxeProgressBar_Office2010Blue,
.dxeProgressBar_Office2010Blue td.dxe
{
    font-family: Verdana;
    font-size: 8pt;
   	color: #1e395b;
}
.dxeProgressBar_Office2010Blue .dxePBMainCell_Office2010Blue,
.dxeProgressBar_Office2010Blue td.dxe
{
    padding: 0;
}
.dxeProgressBarIndicator_Office2010Blue
{
    background: #dfe6ed url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Editors.edtProgressIndicatorBack.png")%>') repeat-x left top;
}

/* -- DropDownWindow -- */
.dxeDropDownWindow_Office2010Blue
{
    background-color: white;
    border: 1px solid #8ba0bc;
}

/*----------------- ColorTable -----------------*/
.dxeColorIndicator_Office2010Blue
{
    border: 1px solid #8ba0bc;
    width: 15px;
    height: 15px;
    cursor: pointer;
}
.dxeColorTable_Office2010Blue
{
    background-color: White;
    border: 1px solid #8ba0bc;
}
.dxeItemPicker_Office2010Blue
{
    background-color: White;
	border: 1px solid #a7abb0;
}
.dxeColorTable_Office2010Blue td.dx,
.dxeItemPicker_Office2010Blue td.dx
{
    padding: 4px;
}
.dxeColorTableCell_Office2010Blue,
.dxeItemPickerCell_Office2010Blue
{
    padding: 3px;
    cursor: pointer;
}
.dxeColorTableCellDiv_Office2010Blue
{
    border: 1px solid #808080;
    width: 12px;
    height: 12px;
    font-size: 0px;
}
.dxeColorTableCellSelected_Office2010Blue
{
    padding: 2px!important;
    background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemSBack.png")%>') repeat-x left top;
	border: 1px solid #c2762b;
}
.dxeColorTableCellHover_Office2010Blue,
.dxeItemPickerCellHover_Office2010Blue
{
    padding: 2px!important;
    background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemHBack.png")%>') repeat-x left top;
	border: 1px solid #f2ca58;
}

/* -- Invalid Style -- */
.dxeInvalid_Office2010Blue
{
}
.dxeInvalid_Office2010Blue .dxeEditArea_Office2010Blue,
.dxeInvalid_Office2010Blue .dxeMemoEditArea_Office2010Blue {
}

/* -- Focused Style -- */
.dxeFocused_Office2010Blue
{
	border: 1px solid #6c86ad;
}

/* -- Null Text Style -- */
.dxeNullText_Office2010Blue .dxeEditArea_Office2010Blue,
.dxeNullText_Office2010Blue .dxeMemoEditArea_Office2010Blue
{
	color: #b2b7bd;
}

/* -- Captcha -- */
.dxcaRefreshButton_Office2010Blue
{
	font-family: Verdana;
	text-decoration: none;
	font-size: 8pt;
	color: #1e395b;
}

.dxcaDisabledRefreshButton_Office2010Blue
{
	color: #b2b7bd;
}

.dxcaRefreshButtonCell_Office2010Blue
{
	padding-left: 4px;
}

.dxcaRefreshButtonText_Office2010Blue
{
}

.dxcaDisabledRefreshButtonText_Office2010Blue
{
}

.dxcaTextBoxCell_Office2010Blue
{
	font-family: Verdana;
	font-size: 8pt;
}

.dxcaTextBoxCell_Office2010Blue,
.dxcaTextBoxCellNoIndent_Office2010Blue
{
	font-family: Verdana;
	font-size: 8pt;
}

.dxcaTextBoxCell_Office2010Blue .dxeErrorCell_Office2010Blue
{
}

.dxcaTextBoxCellNoIndent_Office2010Blue .dxeErrorCell_Office2010Blue
{
	padding-left: 0px;
	padding-top: 4px;
	color: Red;
}

.dxcaTextBoxLabel_Office2010Blue
{
	padding-bottom: 4px;
	display: block;
}

.dxcaLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}

.dxcaLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}