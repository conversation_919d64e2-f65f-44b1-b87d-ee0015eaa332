﻿/* Common */
.TabsContainer
{
	padding-right: 5px;
	float:right;
}
.TabsContainer td
{
	text-align: left;
}
.Layout .Item .ACH .dxm-ie7,
.TabsContainer .dxm-ie7
{
    height: auto !important;
}
.Layout .Item .ACH .dxm-ie7,
.TabsContainer .dxm-ie7 > div:first-child
{
    height: auto !important;
}
/* Top */
table.Top td.Logo {
	padding: 10px 20px 10px 15px;
}
table.Top td.Security {
	padding: 15px 10px 0px 20px;
	text-align: right;
	vertical-align: top;
}
table.Top td.Security td {
	text-align: left;
}
table.Top td.Security div.Security {
	display: inline;
	float: right;
}
/* Search panel */
table.Search {
	margin: 0px 0px 0px 0px;
}
table.Search td.Left {
	padding: 0px 20px 0px 0px;
}
table.Search td.Right {
	padding: 0px 0px 0px 20px;
}
/* Action Container Panel */
.ACPanel td td
{
	text-align: left;
}
/* Main table */
.Main td.Right, .Main td.Left {
	vertical-align: top;
}
.Main td.Right, .Main td.Left .dxrp .VContainer {
	padding: 0px;
}
.Main td.Left {
	padding: 15px 0px 20px 25px;
}
.Main td.Right {
	padding: 10px 15px 0px 15px;
}
.VerticalTemplate .Main td.Left {
	width: 197px;
}
.HorizontalTemplate .Main td.Right {
	width: 100%;
}
.Main td.Grip {
	vertical-align: middle;
	width: 14px;
}
/* Round panel of VerticalToolsActionContainer */
.ToolsActionContainerPanel
{
	margin: 15px;
}
.ToolsActionContainerPanel .dxrpcontent
{
    width: 100% !important;
    padding-left: 0px !important;
    padding-right: 0px !important;
}
.LeftPane {
	padding-bottom: 15px;
}
/* Main round panel*/
.Empty {
	font-size: 0px;
}
table.MainContent tr.Header td.Header {
	padding: 3px 10px 10px 10px;
}
table.MainContent tr.Content td.Content {
	padding: 10px 15px 2px 15px;
}
table.MainContent td.ViewImage {
	width: 32px;
	padding: 0px 5px 0px 15px;
}
table.MainContent td.ViewHeader {
}
table.ViewHeader {
	margin: 10px 0px 0px 0px;
}
.EditModeActions {
	padding: 5px 15px 5px 10px;
	border-bottom: #b6bdcc 1px solid;
}
.Content .EditModeActions {
	padding: 5px 0px 5px 10px;
	border-bottom: 0px;
}
/* TopToolBar */
.ToolBar {
	margin: 0px 0px 0px 0px;
}
/* Navigation Links*/
.Links {
	padding: 35px 40px 15px 40px !important;
	line-height: 24px;
}
/* Footer */
tr.Footer td.Right {
	padding: 20px 10px 30px 0px;
}
div.Footer {
	padding: 20px 10px 30px;
}
div.Header table {
	margin: 0px 15px 0px 15px;
}
div.Header table td {
	padding: 24px 0px 0px 0px;
}
.DialogContent .ContentCell {
	vertical-align: top;
	padding: 20px 10px;
}
.Dialog .ViewImage img {
	margin: 0px 5px 0px 0px;
}
/* Logon */
.Logon {
	width: 500px;
	text-align: left;
}
.LogonContentCell {
}
.LogonContentCell .Layout .NextColumn div.Item {
	padding-left: 10px;
}
/* Error */
.ErrorDetails {
	font-family: Tahoma, Arial, Helvetica, sans-serif;
	font-size: 90%;
}
.TabsContainer .TemplatedItem {
	padding: 0px 5px 0px 11px;
}
.TabsContainer .TemplatedItem .SingleChoiceActionItemLabel {
	padding-left: 0px;
}
/* NavigationBarActionContainer */
.xafNavigationBarActionContainer
{
    margin-bottom: 3px;
}
/* Navigation Links (QuickAccessNavigationActionContainer) */
.NavigationLinks a, .NavigationLinks a:hover, .NavigationLinks a:visited {
	text-decoration: underline;
	margin: 0px 6px;
}
.NestedFrameViewSite > .Layout {
	padding: 0px;
}
.Layout > .NestedFrameControl > .NestedFrame > .ToolBar + .NestedFrameViewSite > .Item > .GridView:first-child {
	border-top: 0px;
}
.Layout > .NestedFrameControl > .NestedFrame > .ToolBar + .NestedFrameViewSite > .ListViewItem {
	padding: 0px !important;
}
.GroupContent > .NestedFrameControl > .NestedFrame > .ToolBar + .NestedFrameViewSite > .ListViewItem
{
	padding: 0px !important;
}
.GroupContent > .NestedFrameControl > .NestedFrame > .ToolBar + .NestedFrameViewSite > .ListViewItem > .GridView:first-child
{
	border-top: 0px;
}
.LayoutTabContainer
{
	padding: 12px !important;
}
.LayoutTabContainer > .Item > .NestedFrame > .NestedFrameViewSite > .Item {
	padding-top: 0px !important;
}
.LayoutTabContainer > .Item > .NestedFrame > .ToolBar {
	padding: 0px 5px 0px 5px !important;
}
.LayoutTabContainer > .NestedFrameControl {
	padding: 0px !important;
}
.LayoutTabContainerWithNestedFrame {
	padding: 0px !important;
}
.LayoutTabContainerWithNestedFrame > .Item > .NestedFrame > .NestedFrameViewSite > .Item {
	padding: 0px !important;
}
.LayoutTabContainerWithNestedFrame  > .Item > .NestedFrame > .ToolBar {
	padding: 0px !important;
}
.LayoutTabContainerWithNestedFrame > .Item > .NestedFrame > .NestedFrameViewSite > .Item > .GridView {
	border: 0px;
}
.Content > div > .Layout {
	padding-top: 15px;
}
.Content > div > .Layout > .GroupHeader:first-child {
	margin-top: 0px !important;
}
div.LeftPane > .ACV {
	padding-top: 15px;
}
.RecordsNavigationContainer {
	padding-right: 14px;
}
.DialogContent .HContainer {
	display: table;
}