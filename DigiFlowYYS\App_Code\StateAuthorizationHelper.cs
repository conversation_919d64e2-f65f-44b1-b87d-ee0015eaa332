﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System.Collections.Generic;
using System.Data;

/// <summary>
/// State bazlı(Statik Atama) iş akış kuralları için kullanılan ve kullanılabilecek olan fonksiyonları barındırır.
/// </summary>
public class StateAuthorizationHelper
{
    /// <summary>
    /// Secilen WorkflowRuleId bazinda tek bir Workflow Rule dondurur.
    /// </summary>
    /// <param name="workflowRuleId"></param>
    /// <returns></returns>
    public static StateAuthorization GetByWorkflowRuleID(long workflowRuleId)
    {
        string query = "SELECT * FROM DT_WORKFLOW.YYS_STATE_AUTHORIZATION WHERE DT_WORKFLOW.YYS_STATE_AUTHORIZATION.STATE_AUTHORIZATION_ID=:STATE_AUTHORIZATION_ID";
        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("STATE_AUTHORIZATION_ID", workflowRuleId);
        dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

        if (dt != null && dt.Rows.Count == 0)
        {
            return null;
        }

        return ConvertDataRow(dt.Rows[0]);
    }

    /// <summary>
    /// Verilen DataRow parametresini StateAuthorization nesnesine donusturur.
    /// </summary>
    /// <param name="dr">DataRow</param>
    /// <returns></returns>
    public static StateAuthorization ConvertDataRow(DataRow dr)
    {
        StateAuthorization wfr = new StateAuthorization();
        wfr.RequestId = ConvertionHelper.ConvertValue<long>(dr["STATE_AUTHORIZATION_ID"]);
        wfr.WfDefId = ConvertionHelper.ConvertValue<long>(dr["WF_DEF_ID"]);
        wfr.StateDefId = ConvertionHelper.ConvertValue<long>(dr["STATE_DEF_ID"]);
        wfr.IsActive = ConvertionHelper.ConvertValue<long>(dr["IS_ACTIVE"]);
        wfr.AssignmentOwnerId = ConvertionHelper.ConvertValue<long>(dr["ASSIGMENT_OWNER_ID"]);
        wfr.AssignmentTypeId = ConvertionHelper.ConvertValue<long>(dr["ASSIGMENT_TYPE_ID"]);
        return wfr;
    }

    /// <summary>
    /// Secilen WorkflowId bazinda tek bir StateAuthorization dondurur.
    /// </summary>
    /// <param name="workflowId"></param>
    /// <returns></returns>
    public static List<StateAuthorization> GetByWorkflowId(long workflowId)
    {
        string query = @"SELECT * FROM DT_WORKFLOW.YYS_STATE_AUTHORIZATION inner join FRAMEWORK.F_WF_WORKFLOW_DEF on
 DT_WORKFLOW.YYS_STATE_AUTHORIZATION.WF_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
WHERE DT_WORKFLOW.YYS_STATE_AUTHORIZATION.WF_DEF_ID=:WF_DEF_ID order by DT_WORKFLOW.YYS_STATE_AUTHORIZATION.STATE_DEF_ID";
        List<StateAuthorization> ret = new List<StateAuthorization>();
        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("WF_DEF_ID", workflowId);
        dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
        if (dt.Rows.Count > 0)
        {
            foreach (DataRow item in dt.Rows)
            {
                ret.Add(ConvertDataRow(item));
            }
        }

        return ret;
    }

    /// <summary>
    /// Secilen workflowStateRuleId'ye bagli olan StateAuthorization'u siler.
    /// </summary>
    /// <param name="workflowRuleId"></param>
    /// <returns></returns>
    public static long DeleteWorkflowRule(long workflowStateRuleId)
    {
        string query = "DELETE FROM DT_WORKFLOW.YYS_STATE_AUTHORIZATION WHERE DT_WORKFLOW.YYS_STATE_AUTHORIZATION.STATE_AUTHORIZATION_ID=:STATE_AUTHORIZATION_ID";
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("STATE_AUTHORIZATION_ID", workflowStateRuleId);
        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }

    /// <summary>
    /// Yeni bir StateAuthorization kayit islemi yapar.
    /// </summary>
    /// <param name="wfr"></param>
    /// <returns></returns>
    public static void AddNewWorkflowRule(StateAuthorization wfr)
    {
        string query = @"INSERT INTO DT_WORKFLOW.YYS_STATE_AUTHORIZATION (
         WF_DEF_ID, STATE_DEF_ID, ASSIGMENT_OWNER_ID, ASSIGMENT_TYPE_ID, CREATED, LAST_UPDATED, CREATED_BY, LAST_UPDATED_BY, IS_ACTIVE)
VALUES (:WF_DEF_ID,:STATE_DEF_ID,:ASSIGMENT_OWNER_ID,:ASSIGMENT_TYPE_ID,:CREATED,:LAST_UPDATED,:CREATED_BY,:LAST_UPDATED_BY,:IS_ACTIVE)";

        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[9];

        p[0] = new OracleParameter("WF_DEF_ID", wfr.WfDefId);
        p[1] = new OracleParameter("STATE_DEF_ID", wfr.StateDefId);
        p[2] = new OracleParameter("ASSIGMENT_OWNER_ID", wfr.AssignmentOwnerId);
        p[3] = new OracleParameter("ASSIGMENT_TYPE_ID", wfr.AssignmentTypeId);
        p[4] = new OracleParameter("CREATED", wfr.Created);
        p[5] = new OracleParameter("LAST_UPDATED", wfr.LastUpdated);
        p[6] = new OracleParameter("CREATED_BY", wfr.CreatedBy);
        p[7] = new OracleParameter("LAST_UPDATED_BY", wfr.LastUpdatedBy);
        p[8] = new OracleParameter("IS_ACTIVE", wfr.IsActive);

        Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }

    /// <summary>
    /// Bu kuralın tanımlı olup olmadığını kontrol eder
    /// </summary>
    /// <param name="wfr">StateAuthorization nesnesi</param>
    /// <returns></returns>
    public static StateAuthorization IsDefined(StateAuthorization wfr)
    {
        string query = @"SELECT * FROM DT_WORKFLOW.YYS_STATE_AUTHORIZATION WHERE DT_WORKFLOW.YYS_STATE_AUTHORIZATION.WF_DEF_ID=:WF_DEF_ID
AND DT_WORKFLOW.YYS_STATE_AUTHORIZATION.STATE_DEF_ID=:STATE_DEF_ID and DT_WORKFLOW.YYS_STATE_AUTHORIZATION.ASSIGMENT_TYPE_ID=:ASSIGMENT_TYPE_ID";
        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[3];
        p[0] = new OracleParameter("STATE_DEF_ID", wfr.StateDefId);
        p[1] = new OracleParameter("WF_DEF_ID", wfr.WfDefId);
        p[2] = new OracleParameter("ASSIGMENT_TYPE_ID", wfr.AssignmentTypeId);

        dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

        if (dt != null && dt.Rows.Count == 0)
        {
            return null;
        }

        return ConvertDataRow(dt.Rows[0]);
    }

    /// <summary>
    /// StateAuthorization nesnesini update eder
    /// </summary>
    /// <param name="wfr">StateAuthorization nesnesi</param>
    public static int Update(StateAuthorization wfr)
    {
        string query = @"UPDATE DT_WORKFLOW.YYS_STATE_AUTHORIZATION
SET    WF_DEF_ID              = :WF_DEF_ID,
       STATE_DEF_ID           = :STATE_DEF_ID,
       ASSIGMENT_OWNER_ID     = :ASSIGMENT_OWNER_ID,
       ASSIGMENT_TYPE_ID      = :ASSIGMENT_TYPE_ID,
       LAST_UPDATED           = :LAST_UPDATED,
       LAST_UPDATED_BY        = :LAST_UPDATED_BY,
       IS_ACTIVE              = :IS_ACTIVE
WHERE  STATE_AUTHORIZATION_ID = :STATE_AUTHORIZATION_ID";

        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[8];

        p[0] = new OracleParameter("WF_DEF_ID", wfr.WfDefId);
        p[1] = new OracleParameter("STATE_DEF_ID", wfr.StateDefId);
        p[2] = new OracleParameter("ASSIGMENT_OWNER_ID", wfr.AssignmentOwnerId);
        p[3] = new OracleParameter("ASSIGMENT_TYPE_ID", wfr.AssignmentTypeId);
        p[4] = new OracleParameter("LAST_UPDATED", wfr.LastUpdated);
        p[5] = new OracleParameter("LAST_UPDATED_BY", wfr.LastUpdatedBy);
        p[6] = new OracleParameter("IS_ACTIVE", wfr.IsActive);
        p[7] = new OracleParameter("STATE_AUTHORIZATION_ID", wfr.RequestId);

        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }

    /// <summary>
    /// Atama tipine de Execution class name i getirir
    /// </summary>
    /// <param name="AssignmentTypeId"></param>
    /// <returns></returns>
    public static string GetExecutionClass(string AssignmentTypeId)
    {
        return "";
    }
}