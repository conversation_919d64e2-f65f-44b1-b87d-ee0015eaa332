﻿using DevExpress.Web.ASPxGridView;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
//using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Digiflow.YYS.Core;
using System;
using System.Collections.Generic;
using System.Data;
using System.Web.UI;
namespace DigiflowYYS_Yeni
{
    public partial class WorkflowRules : YYSSecurePage
    {
        /// <summary>
        /// Page Load
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
               this.Master.ShowMenu(true);
               this.Master.PageTitle = "Akış Kuralları";

                if (!Page.IsPostBack)
                {
                    //Kullanıcının admin i olduğu iş akışları doldurulur
                    long IsAdmin = 0;

                    if (Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAndFlowAdmin.ToString() || Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAdmin.ToString())
                    {
                        IsAdmin = 1;
                    }

                    WorkfowListASPxComboBox = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillComboboxWorkFlow("Name", "WorkflowDefId", WorkfowListASPxComboBox, WorkflowHelper.GetWorkflowsOfAdmin(UserInformation.LoginObject.LoginId, IsAdmin), "İş Akışı Seçiniz", "0");
                }
                if (Page.IsCallback)
                {
                    #region Kurallar Doldurulur

                    List<ActionAuthorization> items = ActionAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(Session["WfDefId"]));
                    WorkFlowRulesGridView.DataSource = ConvertToDataTable(items);
                    WorkFlowRulesGridView.DataBind();

                    #endregion Kurallar Doldurulur
                }
            }
            catch (Exception ex)
            {
                this.Master.ShowPopup(false, "Hata", "Sayfa yüklenirken beklenmeyen bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.Message);
            }
        }

        /// <summary>
        /// İş akışı değiştiği zaman o iş akışının kurallarını getirir
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void WorkfowListASPxComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                List<ActionAuthorization> items = ActionAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
                WorkFlowRulesGridView.DataSource = ConvertToDataTable(items);
                WorkFlowRulesGridView.DataBind();
                Session["WfDefId"] = WorkfowListASPxComboBox.SelectedItem.Value;
            }
            catch (Exception ex)
            {
                this.Master.ShowPopup(false, "Hata", "Kurallar listelenirken bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.Message);
            }
        }

        #region Inner Functions

        /// <summary>
        /// Grid teki int değerlerin isim karşılıklarını bulup değiştirir
        /// </summary>
        /// <param name="items">Action authorization nesneleri</param>
        /// <returns></returns>
        private DataTable ConvertToDataTable(List<ActionAuthorization> items)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("Id");
            dt.Columns.Add("State");
            dt.Columns.Add("Source");
            dt.Columns.Add("Action");
            dt.Columns.Add("ToGroup");
            dt.Columns.Add("Active");
            dt.AcceptChanges();

            foreach (ActionAuthorization item in items)
            {
                DataRow r = dt.NewRow();
                r["Id"] = item.RequestId;
                r["State"] = WorkflowHelper.GetState(item.StateDefId).Name;
                r["Source"] = ConvertUser(item.SourceId);
                r["Action"] = ConvertActionType(item.ActionId);

                if (item.ToGroupId != 0)
                {
                    r["ToGroup"] = ConvertUser(item.ToGroupId);
                }

                r["Active"] = ConvertIsActive(ConvertionHelper.ConvertValue<Boolean>(item.IsActive));

                dt.Rows.Add(r);
            }

            dt.AcceptChanges();
            return dt;
        }

        /// <summary>
        /// Grid teki Int olan isActive değerini Aktif veya Pasif olarak değiştirir
        /// </summary>
        /// <param name="val"></param>
        /// <returns></returns>
        private string ConvertIsActive(bool val)
        {
            return val ? "Evet" : "Hayır";
        }

        /// <summary>
        /// Grid teki int olan ActionId değerini Action adı olarak değiştirir
        /// </summary>
        /// <param name="val"></param>
        /// <returns></returns>
        private string ConvertActionType(long val)
        {
            string ret = string.Empty;

            var lg = ActionTypeHelper.GetActionTypeName(val);
            ret = lg.ActionTypeName;
            return ret;
        }

        /// <summary>
        /// Grid teki loginId değerinin karşılığı olan isme çevirir
        /// </summary>
        /// <param name="val"></param>
        /// <returns></returns>
        private string ConvertUser(long val)
        {
            string ret = string.Empty;

            var lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(val);
            ret = lg.Name;

            return ret;
        }

        /// <summary>
        /// İş akışına göre grid doldurulur
        /// </summary>
        private void FillGrid()
        {
            List<ActionAuthorization> items = ActionAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
            WorkFlowRulesGridView.DataSource = ConvertToDataTable(items);
            WorkFlowRulesGridView.DataBind();
        }

        /// <summary>
        /// Silme işlemi
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void WorkFlowRulesGridView_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
        {
            //long ruleId = ConvertionHelper.ConvertValue<long>(e.Values[5]);
            //aspxgridview gridView = (aspxgridview)sender;

            //ActionAuthorizationHelper.DeleteWorkflowRule(ruleId);
            //((MasterPage)Master).ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
            //// DeleteButton.Enabled = false;
            //// SaveButton.Enabled = false;
            //FillGrid();
            //gridView.CancelEdit();
            //e.Cancel = true;
        }

        /// <summary>
        /// Silme işlemi burada yapılır
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void DeleteButton_Click(object sender, EventArgs e)
        {
            long ruleId = ConvertionHelper.ConvertValue<long>(Session["DeletingWorkFlowRule"]);
            ActionAuthorizationHelper.DeleteWorkflowRule(ruleId);
            this.Master.ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
            // DeleteButton.Enabled = false;
            // SaveButton.Enabled = false;
            FillGrid();
        }

        /// <summary>
        /// Silmek için grubun Id si buradan alınır
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void WorkFlowRulesGridView_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
        {
            Session["DeletingWorkFlowRule"] = e.KeyValue.ToString();
        }

        #endregion Inner Functions
    }
}