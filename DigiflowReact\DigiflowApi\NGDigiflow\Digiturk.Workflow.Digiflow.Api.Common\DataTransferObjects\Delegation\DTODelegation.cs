﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Digiturk.Workflow.Digiflow.Api.Common.Delegation
{
    public class DTODelegation
    {
        public long LoginId { get; set; }

        public long RequestID { get; set; }

        public string DelegationComment { get; set; }

        public long OwnerLoginId { get; set; }

        public long DelegatedLoginId { get; set; }

        public long WorkflowDefId { get; set; }

        public DateTime StartTime { get; set; }

        public DateTime EndTime { get; set; }

        public DateTime Created { get; set; }

        public long CreatedBy { get; set; }

        public DateTime LastUpdated { get; set; }

        public long LastUpdatedBy { get; set; }

        public long VersionID { get; set; }

        public string WorkflowIds { get; set; }
    }
}
