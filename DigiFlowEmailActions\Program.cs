﻿using DigiFlowEmailActions.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions
{
    class Program
    {
        static void Main(string[] args)
        {
            ExchangeHelper exchangeHelper = new ExchangeHelper();
            ActionProcessHelper processHelper = new ActionProcessHelper();
            try
            {
                List<EmailInfo> listWaitingEmails = exchangeHelper.GetWaitingEmails();
                if (listWaitingEmails.Count > 0)
                    processHelper.ProcessWaitingEmails(listWaitingEmails);
            }
            catch (Exception ex)
            {
                processHelper.EmailMainError(ex);
            }
        }
    }
}
