﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Http;
using System.Web.Http.Results;

namespace Digiturk.Workflow.Digiflow.Api.Core
{
    public class ApiControllerBase: ApiController
    {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        protected override BadRequestResult BadRequest()
        {
            // Commend Yazıldı.
            // Commend yazıldı, Melih..
            return base.BadRequest();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        protected override OkResult Ok()
        {
            return base.Ok();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="content"></param>
        /// <returns></returns>
        protected override OkNegotiatedContentResult<T> Ok<T>(T content)
        {
            return base.Ok<T>(content);
        }
    }
}
