﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class AlternatifYoneticiAkisHelper
    {
        internal static void RejectWorkFlow(Digiturk.Workflow.Common.WFContext currentWFContext)
        {
            currentWFContext.Parameters.AddOrChangeItem("CompleteStatus_Tr", "reddedilip");
            currentWFContext.Parameters.AddOrChangeItem("CompleteStatus_En", "rejected");
            currentWFContext.Save();
        }
        internal static void AkisKontrolveOnay(long entityRefId, Digiturk.Workflow.Common.WFContext currentWFContext, long wfInstanceId, FLogin LoginObject, ref string errorUserMsg_Tr, ref string errorUserMsg_En)
        {
            var requestObject = WFRepository<Digiturk.Workflow.Digiflow.Entities.AlternativeManagerRequest>.GetEntity(entityRefId);
            List<WorkFlowKeyValues> listOfSelectedWorkFlows = new List<WorkFlowKeyValues>();
            foreach (string wfdefId in requestObject.WorkFlowDefIds.Split(','))
            {
                FWfWorkflowDef defEntity = WFRepository<FWfWorkflowDef>.GetEntity(wfdefId);
                listOfSelectedWorkFlows.Add(new WorkFlowKeyValues() { WorkflowId = defEntity.WfWorkflowDefId, WorkflowName_Tr = defEntity.Name, WorkflowName_En = defEntity.Description });
            }
            ControlForAssistantManagerAssignmentBetweenDatesForWorkFlows(requestObject.PersonnelId, requestObject.StartDate, requestObject.EndDate, listOfSelectedWorkFlows, ref errorUserMsg_Tr, ref errorUserMsg_En);

            bool frameworkInsertResult = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.AlternativeManagerRequestHelper.InsertFrameWorkTable(wfInstanceId, entityRefId, requestObject.PersonnelId, requestObject.PersonnelDelegatedNewManagerId, requestObject.StartDate, requestObject.EndDate, listOfSelectedWorkFlows.Select(x => x.WorkflowId).ToList());
            if (frameworkInsertResult)
            {
                currentWFContext.Parameters.AddOrChangeItem("CompleteStatus_Tr", "onaylanıp");
                currentWFContext.Parameters.AddOrChangeItem("CompleteStatus_En", "approved");
                currentWFContext.Save();

                requestObject.LastUpdated = DateTime.Now;
                requestObject.LastUpdatedBy = LoginObject.LoginId;
                ActionHelpers.EntitySave(requestObject);
            }
            else
            {
                errorUserMsg_En = "Framework table insertion failed.";
                errorUserMsg_Tr = "Framework tablo eklenme hatası meydana geldi.";
                throw new Exception();
            }
        }

        private static void ControlForAssistantManagerAssignmentBetweenDatesForWorkFlows(long personnelId, DateTime startDate, DateTime endDate, List<WorkFlowKeyValues> listOfSelectedWorkFlows, ref string errorUserMsg_Tr, ref string errorUserMsg_En)
        {
            DataTable dt = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.AlternativeManagerRequestHelper.GetApprovedAlternativeManagerWorkFlowTableForPersonelAndDates(personnelId, startDate, endDate);
            foreach (var item in listOfSelectedWorkFlows)
            {
                bool alreadyExists = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.AlternativeManagerRequestHelper.IsPersonnelAlreadyBoundToAlternativeManagerForRelatedFlow(dt, item.WorkflowId);
                if (alreadyExists)
                {
                    errorUserMsg_En = "The defined workflow has been already assigned to an alternative manager in selected dates range." + " [" + item.WorkflowName_En + " ] - [" + startDate.ToString("dd.MM.yyyy HH:mm") + " - " + endDate.ToString("dd.MM.yyyy HH:mm") + "]";
                    errorUserMsg_Tr = "Belirtilen iş akışı seçilen tarih aralığında zaten bir alternatif yöneticiye atanmıştır." + " [" + item.WorkflowName_Tr + " ] - [" + startDate.ToString("dd.MM.yyyy HH:mm") + " - " + endDate.ToString("dd.MM.yyyy HH:mm") + "]";
                    throw new Exception();
                }
            }
        }
    }

    class WorkFlowKeyValues
    {
        public long WorkflowId { get; set; }
        public string WorkflowName_En { get; set; }
        public string WorkflowName_Tr { get; set; }
    }
}
