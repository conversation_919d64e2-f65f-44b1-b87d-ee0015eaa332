﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using Microsoft.Exchange.WebServices.Data;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.DirectoryServices.AccountManagement;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers
{
    public class ExchangeHelper
    {
        ExchangeService service;
        string userEmailAddress = string.Empty, userEmailPassword = string.Empty, webServiceUrl = string.Empty;
        int topItems = 0;
        public ExchangeHelper()
        {
            service = new ExchangeService(ExchangeVersion.Exchange2016);
            if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
                userEmailAddress = ConfigurationManager.AppSettings["UserName_Test"];
            else
                userEmailAddress = ConfigurationManager.AppSettings["UserName_Live"];
            userEmailPassword = ConfigurationManager.AppSettings["UserPassword"];
            webServiceUrl = ConfigurationManager.AppSettings["WebServiceUrl"];
            topItems = Convert.ToInt32(ConfigurationManager.AppSettings["EmailsTopCount"]);
            service.Credentials = new WebCredentials(userEmailAddress, userEmailPassword);
            service.Url = new Uri(webServiceUrl);
        }

        public List<EmailInfo> GetWaitingEmails()
        {
            List<EmailInfo> listWaitingEmails = new List<EmailInfo>();
            ItemView view = new ItemView(topItems);
            view.OrderBy.Add(ItemSchema.DateTimeReceived, SortDirection.Ascending);
            ExtendedPropertyDefinition flagStatusProperty = new ExtendedPropertyDefinition(0x1090, MapiPropertyType.Integer);
            SearchFilter filter = new SearchFilter.IsNotEqualTo(flagStatusProperty, 1); // 1 corresponds to "Complete".
            FindItemsResults<Item> findResults = service.FindItems(WellKnownFolderName.Inbox, filter, view);

            foreach (Item item in findResults.Items)
            {
                EmailMessage email = EmailMessage.Bind(service, item.Id);
                EmailInfo emailInfo = null;
                try
                {
                    emailInfo = GetEmailInfo(email);
                    if (emailInfo.ActionInfo == null)
                        throw new Exception("GetMailActionInfo null döndü.");
                    if (string.IsNullOrEmpty(emailInfo.SenderUserName))
                        throw new Exception("SenderUserName alınamadı.");
                    listWaitingEmails.Add(emailInfo);
                }
                catch (Exception ex)
                {
                    if (emailInfo != null && emailInfo.MailActionId.HasValue)
                    {
                        Digiturk.Workflow.Digiflow.WorkFlowHelpers.MailActionsHelper.InsertMailActionHistoryLog(emailInfo.EmailId, emailInfo.MailActionId, false, ex.Message, emailInfo.Language);
                        Digiturk.Workflow.Digiflow.WorkFlowHelpers.MailActionsHelper.CompleteMailAction(emailInfo.MailActionId.Value);
                    }
                    SetEmailFlagComplete(item.Id.ToString());
                }
            }
            return listWaitingEmails;
        }

        public void SetEmailFlagComplete(string emailId)
        {
            try
            {
                ItemView view = new ItemView(1);
                SearchFilter searchFilter = new SearchFilter.IsEqualTo(EmailMessageSchema.Id, emailId);
                FindItemsResults<Item> findResults = service.FindItems(WellKnownFolderName.Inbox, searchFilter, view);

                if (findResults.TotalCount > 0)
                {
                    EmailMessage email = findResults.Items[0] as EmailMessage;
                    if (email != null)
                    {
                        email.Load();
                        email.Flag.FlagStatus = ItemFlagStatus.Complete;
                        email.IsRead = true;
                        email.Update(ConflictResolutionMode.AlwaysOverwrite);
                    }
                }
            }
            catch (Exception ex)
            {
            }
        }

        private EmailInfo GetEmailInfo(EmailMessage email)
        {
            EmailInfo info = new EmailInfo()
            {
                Body = email.Body.Text,
                EmailId = email.Id.UniqueId,
                Subject = email.Subject,
                SenderEmail = email.From,
                Language = GetLanguageFromBody(email.Body),
                MailActionId = GetMailActionIdFromBody(email.Body)
            };
            try
            {
                info.ActionInfo = Digiturk.Workflow.Digiflow.WorkFlowHelpers.MailActionsHelper.GetMailActionInfo(info.MailActionId);
            }
            catch
            {
                info.ActionInfo = null;
            }
            try
            {
                info.SenderUserName = GetADUserName(email.From.Name);
            }
            catch (Exception)
            {
            }

            bool fetched = false;
            info.Note = GetNote(info.Body, info.Language, ref fetched);
            info.NoteFetched = fetched;
            return info;
        }

        private string GetADUserName(string name)
        {
            string donen = string.Empty;
            using (PrincipalContext context = new PrincipalContext(ContextType.Domain))
            {
                UserPrincipal user = UserPrincipal.FindByIdentity(context, IdentityType.Name, name);
                donen = user.SamAccountName;
            }
            return donen;
        }

        private string GetNote(string body, string language, ref bool fetched)
        {
            fetched = false;
            string foundText = string.Empty;
            try
            {
                string key = language.ToLower() == "tr" ? "Not:”" : "Note:”";
                string pattern = language.ToLower() == "tr" ? @"Not:”(.*?)”" : @"Note:”(.*?)”";

                if (!body.Contains(key))
                {
                    key = language.ToLower() == "tr" ? "Not:&quot;" : "Note:&quot;";
                    pattern = language.ToLower() == "tr" ? @"Not:&quot;(.*?)&quot;" : @"Note:&quot;(.*?)&quot;";
                }
                if (!body.Contains(key))
                {
                    key = language.ToLower() == "tr" ? "Not:\"" : "Note:\"";
                    pattern = language.ToLower() == "tr" ? "Not:\"(.*?)\"" : "Note:\"(.*?)\"";
                }
                if (body.Contains(key))
                {
                    Match match = Regex.Match(body, pattern);
                    if (match.Success && match.Groups.Count > 1)
                    {
                        foundText = match.Groups[1].Value;
                        fetched = true;
                    }
                }
            }
            catch
            {
            }
            return foundText;
        }
        private Guid? GetMailActionIdFromBody(string body)
        {
            return body.Contains(": Action=") ? new Guid(body.Split(new string[] { ": Action=" }, StringSplitOptions.RemoveEmptyEntries)[1].Split(',')[0]) : (Guid?)null;
        }

        private string GetLanguageFromBody(string body)
        {
            if (body.Contains(": Action=") && body.Contains(",Lang="))
            {
                body = body.Split(new string[] { ": Action=" }, StringSplitOptions.RemoveEmptyEntries)[1].Split(new string[] { ",Lang=" }, StringSplitOptions.RemoveEmptyEntries)[1];
                body = body.Split(new string[] { "---" }, StringSplitOptions.RemoveEmptyEntries)[0];
                return body;
            }
            else
                return string.Empty;
        }
    }

    public class EmailInfo
    {
        public string EmailId { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public string Note { get; set; }
        public bool NoteFetched { get; set; }
        public string Language { get; set; }
        public Guid? MailActionId { get; set; }
        public EmailAddress SenderEmail { get; set; }
        public MailActionInfo ActionInfo { get; set; }
        public string SenderUserName { get; set; }
    }
}
