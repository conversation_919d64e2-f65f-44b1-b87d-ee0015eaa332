/* -- ASPxSpellChecker -- */
.dxwscLoadingDiv_Office2010Silver
{
	background-color:Gray;
	opacity: 0.01;
	filter:progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=1);
}
.dxwscLoadingPanel_Office2010Silver
{
	font: 8pt Verdana;
    color: #585e68;
    background: White;
	border: 1px solid #80858d;
}
.dxwscLoadingPanel_Office2010Silver td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}

.dxwscCheckedTextContainer_Office2010Silver
{
	background-color: white;
	font-size: 8pt;
	font-family: Verdana;
	border: 1px solid #a5acb5;
	vertical-align: top;
	overflow: auto;
	height: 110px;
	width: 340px;
	padding: 5px;
}
.dxwscErrorWord_Office2010Silver
{
	background: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.SpellChecker.scErrorUnderline.gif")%>') repeat-x left bottom;
	padding-bottom: 3px;
	color: Black;
	font-weight: bold;
}

/*-- Dialog Forms --*/
.leftBottomButton,
.rightBottomButton
{
    border-top: 1px solid #c9cdd2;
}
.leftBottomButton
{
    padding: 12px 0;
}
.rightBottomButton
{
	padding: 10px 12px 10px 10px;
	width: 100px;
}
.footerBackground
{
	background-color: #e9edf1;
}

#dxMainSpellCheckFormTable
{
	width: 480px;
}
#dxMainSpellCheckFormTable .contentSCFormContainer
{
	padding: 9px 12px 0px;
}
#dxSpellCheckForm .buttonsTable
{
	width: 100px;
}
#dxSpellCheckForm .buttonTableContainer
{
	padding-left: 10px;
	padding-top: 5px;
}
#dxSpellCheckForm .checkedDivContainer
{
	overflow: hidden;
	padding-top:5px;
}
#dxSpellCheckForm .changeToText
{
	padding-top: 15px;
}
#dxSpellCheckForm .verticalSeparator
{
	padding-top: 5px;
}
#dxSpellCheckForm .listBoxContainer
{
	padding-top: 5px;
	padding-bottom: 12px;
}
#dxMainSpellCheckOptionsFormTable
{
	width: 400px;
}
#dxMainSpellCheckOptionsFormTable .contentSCOptionsFormContainer
{
	padding: 12px 12px 0px;
}
#dxOptionsForm .languagePanel
{
	padding-top: 10px;
	padding-bottom: 12px;
}