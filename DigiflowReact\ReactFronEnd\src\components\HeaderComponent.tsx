import * as React from 'react'
import { useEffect, useState } from 'react'
import * as WFace from '@wface/components'
import { WGrid } from '@wface/components'
import { useTranslation } from "react-i18next"
import {openScreen} from "../../configs/wface/wface.config";

function HeaderComponent() {
    const { t } = useTranslation()

    function goInboxScreen() {
        openScreen("InboxScreen")
    }

    return (
        <div>
            <WFace.WAppBar
                id=""
                position="static">
                <WGrid container alignItems='center' direction='row'>
                    <WGrid item md={2} xs={12}>
                        <WFace.WButton id="" color="secondary" onClick={goInboxScreen}>
                            <WFace.WIcon style={{ marginRight: 10, color: "red" }}>arrow_forward_ios</WFace.WIcon>
                            {t('inbox')}
                        </WFace.WButton>
                    </WGrid>
                    <WGrid item md={2} xs={12}>
                        <WFace.WButton id="" color="secondary">
                            <WFace.WIcon style={{ marginRight: 10, color: "red" }}>arrow_forward_ios</WFace.WIcon>
                            {t('history')}
                        </WFace.WButton>
                    </WGrid>
                    <WGrid item md={2} xs={12}>
                        <WFace.WButton id="" color="secondary">
                            <WFace.WIcon style={{ marginRight: 10, color: "red" }}>arrow_forward_ios</WFace.WIcon>
                            {t('suspended_inbox')}
                        </WFace.WButton>
                    </WGrid>
                    <WGrid item md={2} xs={12}>
                        <WFace.WButton id="" color="secondary">
                            <WFace.WIcon style={{ marginRight: 10, color: "red" }}>arrow_forward_ios</WFace.WIcon>
                            {t('cancel_delegation')}
                        </WFace.WButton>
                    </WGrid>
                    <WGrid item md={2} xs={12}>
                        <WFace.WButton id="" color="secondary">
                            <WFace.WIcon style={{ marginRight: 10, color: "red" }}>arrow_forward_ios</WFace.WIcon>
                            {t('cancel_monitoring')}
                        </WFace.WButton>
                    </WGrid>
                    <WGrid item md={2} xs={12}>
                        <WFace.WButton
                            id=""
                            style={{ margin: 10, background: 'black', }}
                            variant="text"
                            onClick={() => window.open('http://digiport/default.aspx', '_blank')}>
                            Digiport
                        </WFace.WButton>
                    </WGrid>

                </WGrid>
            </WFace.WAppBar>
        </div>
    )
}

export default HeaderComponent
