﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ASPxGridViewStringId.GroupPanel" xml:space="preserve">
    <value>Drag a column header here to group by that column</value>
  </data>
  <data name="ASPxGridViewStringId.EmptyDataRow" xml:space="preserve">
    <value>Herhangi bir içerik bulunmamaktadır</value>
  </data>
  <data name="ASPxGridViewStringId.EmptyHeaders" xml:space="preserve">
    <value>Buraya sürükleyebilirsiniz</value>
  </data>
  <data name="ASPxGridViewStringId.ConfirmDelete" xml:space="preserve">
    <value>Silmek istediğinizden Emin misiniz?</value>
  </data>
  <data name="ASPxGridViewStringId.GroupContinuedOnNextPage" xml:space="preserve">
    <value>(Continued on the next page)</value>
  </data>
  <data name="ASPxGridViewStringId.CustomizationWindowCaption" xml:space="preserve">
    <value>Field Seçimi</value>
  </data>
  <data name="ASPxGridViewStringId.PopupEditFormCaption" xml:space="preserve">
    <value>Edit Form</value>
  </data>
  <data name="ASPxGridViewStringId.HeaderFilterShowAllItem" xml:space="preserve">
    <value>Tümünü göster</value>
  </data>
  <data name="ASPxGridViewStringId.HeaderFilterShowBlanksItem" xml:space="preserve">
    <value>Boş</value>
  </data>
  <data name="ASPxGridViewStringId.HeaderFilterShowNonBlanksItem" xml:space="preserve">
    <value>Boş değil</value>
  </data>
  <data name="ASPxGridViewStringId.CommandEdit" xml:space="preserve">
    <value>Düzenle</value>
  </data>
  <data name="ASPxGridViewStringId.CommandNew" xml:space="preserve">
    <value>Yeni</value>
  </data>
  <data name="ASPxGridViewStringId.CommandDelete" xml:space="preserve">
    <value>Sil</value>
  </data>
  <data name="ASPxGridViewStringId.CommandSelect" xml:space="preserve">
    <value>Seç</value>
  </data>
  <data name="ASPxGridViewStringId.CommandCancel" xml:space="preserve">
    <value>İptal</value>
  </data>
  <data name="ASPxGridViewStringId.CommandUpdate" xml:space="preserve">
    <value>Güncelle</value>
  </data>
  <data name="ASPxGridViewStringId.CommandClearFilter" xml:space="preserve">
    <value>Temizle</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterBeginsWith" xml:space="preserve">
    <value>İle başlar</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterContains" xml:space="preserve">
    <value>içerir</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterDoesNotContain" xml:space="preserve">
    <value>içermez</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterEndsWith" xml:space="preserve">
    <value>ile biter</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterEquals" xml:space="preserve">
    <value>Eşittir</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterGreater" xml:space="preserve">
    <value>Büyüktür</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterGreaterOrEqual" xml:space="preserve">
    <value>Büyük Eşit</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterLess" xml:space="preserve">
    <value>Küçüktür</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterLessOrEqual" xml:space="preserve">
    <value>Küçük eşit</value>
  </data>
  <data name="ASPxGridViewStringId.AutoFilterNotEqual" xml:space="preserve">
    <value>Eşit değil</value>
  </data>
  <data name="ASPxGridViewStringId.Alt_HeaderFilterButton" xml:space="preserve">
    <value>Filtrele</value>
  </data>
  <data name="ASPxGridViewStringId.Alt_HeaderFilterButtonActive" xml:space="preserve">
    <value>Filtrelendir</value>
  </data>
  <data name="ASPxGridViewStringId.Alt_SortedAscending" xml:space="preserve">
    <value>Küçükten Büyüğe</value>
  </data>
  <data name="ASPxGridViewStringId.Alt_SortedDescending" xml:space="preserve">
    <value>Büyükten küçüge</value>
  </data>
  <data name="ASPxGridViewStringId.Alt_DragAndDropHideColumnIcon" xml:space="preserve">
    <value>Gizle</value>
  </data>
  <data name="ASPxGridViewStringId.Alt_Expand" xml:space="preserve">
    <value>[Expand]</value>
  </data>
  <data name="ASPxGridViewStringId.Alt_Collapse" xml:space="preserve">
    <value>[Collapse]</value>
  </data>
  <data name="ASPxGridViewStringId.Alt_FilterRowButton" xml:space="preserve">
    <value>[Condition]</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_Older" xml:space="preserve">
    <value>Older</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_LastMonth" xml:space="preserve">
    <value>Last Month</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_EarlierThisMonth" xml:space="preserve">
    <value>Earlier this Month</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_ThreeWeeksAgo" xml:space="preserve">
    <value>Three Weeks Ago</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_TwoWeeksAgo" xml:space="preserve">
    <value>İki Hafta Sonra</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_LastWeek" xml:space="preserve">
    <value>Last Week</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_Yesterday" xml:space="preserve">
    <value>Yesterday</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_Today" xml:space="preserve">
    <value>Bugün</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_Tomorrow" xml:space="preserve">
    <value>Yarın</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_NextWeek" xml:space="preserve">
    <value>Next Week</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_TwoWeeksAway" xml:space="preserve">
    <value>Two Weeks Away</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_ThreeWeeksAway" xml:space="preserve">
    <value>Three Weeks Away</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_LaterThisMonth" xml:space="preserve">
    <value>Later this Month</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_NextMonth" xml:space="preserve">
    <value>Next Month</value>
  </data>
  <data name="ASPxGridViewStringId.Outlook_BeyondNextMonth" xml:space="preserve">
    <value>Beyond Next Month</value>
  </data>
  <data name="ASPxperienceStringId.Pager_All" xml:space="preserve">
    <value>Tümü</value>
  </data>
  <data name="ASPxperienceStringId.Pager_First" xml:space="preserve">
    <value>İlk</value>
  </data>
  <data name="ASPxperienceStringId.Pager_Last" xml:space="preserve">
    <value>Son</value>
  </data>
  <data name="ASPxperienceStringId.Pager_Next" xml:space="preserve">
    <value>Sonraki</value>
  </data>
  <data name="ASPxperienceStringId.Pager_Prev" xml:space="preserve">
    <value>Önceki</value>
  </data>
  <data name="ASPxperienceStringId.Pager_SummaryAllPagesFormat" xml:space="preserve">
    <value>Sayfalar {0} - {1} ({2} Sayfa)</value>
  </data>
  <data name="ASPxperienceStringId.Pager_SummaryFormat" xml:space="preserve">
    <value>Sayfalar {0} von {1} ({2} Sayfa)</value>
  </data>
</root>