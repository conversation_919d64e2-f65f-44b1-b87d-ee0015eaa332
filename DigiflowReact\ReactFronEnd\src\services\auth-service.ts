import { IAuthService, IMenuTreeItem } from "@wface/ioc";
import { injectable, inject } from "inversify";
import { UserContext } from "@wface/store";
import { useTranslation } from "react-i18next";

@injectable()
export default class AuthService implements IAuthService {
  @inject("UserContext") userContext: UserContext;

  public login(
    username: string,
    password: string,
    values?: any
  ): Promise<{ displayName: string; token?: string }> {
    return new Promise(resolve =>
      resolve({ displayName: "deneme", token: "deneme" })
    );

    // return new Promise((resolve, reject) => {
    //   if (username === "connection-error") {
    //     setTimeout(() => reject("Connection error"), 1000);
    //   }

    //   if (username === "wrong-password") {
    //     setTimeout(() => reject("Wrong username or password"), 1000);
    //   }

    //   setTimeout(() => resolve({ displayName: 'MockUser', token: 'MockToken' }), 1500);
    // });
  }

  public getMenuTree(): Promise<IMenuTreeItem[]> {
    return new Promise((resolve, reject) => {
      const result: IMenuTreeItem[] = [];
      let id = 0;
      result.push({
        id: (++id).toString(),
        text: "Yönetsel Süreçler",
        screen: "administrativeProcesses",
        icon: "confirmation_number",
        subNodes: [
          {
            id: "DelegationScreen",
            text: "Delegasyon Süreci",
            screen: "DelegationScreen",
            icon: "local_offer",
            hideOnNavigationList: false
          }
        ]
      });
      result.push({
        id: (++id).toString(),
        text: "İnsan Kaynakları Süreçleri",
        screen: "humanResourcesProcesses",
        icon: "groups",
        subNodes: [
          {
            id: "0",
            text: "New Screen",
            icon: "local_offer",
            hideOnNavigationList: true
          }
        ]
      });
      result.push({
        id: (++id).toString(),
        text: "Hukuki Süreçler",
        screen: "legalProcesses",
        icon: "gavel",
        subNodes: [
          {
            id: "0",
            text: "Sözleşme Onay Süreci",
            screen: "ContractConfirmation",
            icon: "sticky_note_2",
            hideOnNavigationList: false
          }
        ]
      });
      result.push({
        id: (++id).toString(),
        text: "Finansal Süreçler",
        screen: "financialProcesses",
        icon: "save",
        subNodes: [
          {
            id: "0",
            text: "new screen",
            icon: "local_offer",
            hideOnNavigationList: true
          },
          {
            id: "1",
            text: "demo index screen",
            screen: "IndexScreen",
            icon: "local_offer",
            hideOnNavigationList: true
          }
        ]
      });
      result.push({
        id: "InboxScreen",
        text: "Inbox",
        screen: "InboxScreen",
        icon: "save",
        isDefaultScreen: true,
        notClosable: true,
        hideOnNavigationList: true
      });
      result.push({
        id: "WorkflowDetailScreen",
        text: "Workflow",
        screen: "WorkflowDetailScreen",
        icon: "save",
        hideOnNavigationList: true
      });
      result.push({
        id: (++id).toString(),
        text: "Profile",
        screen: "ProfileScreen",
        hideOnNavigationList: true
      });
      // for(let i = 3; i < 20; i++) {
      //   result.push({
      //     id: i.toString(),
      //     text: 'DemoScreen' + i,
      //     screen: 'DemoScreen2'
      //   });
      // }
      setTimeout(() => resolve(result), 1000);
    });
  }
}
