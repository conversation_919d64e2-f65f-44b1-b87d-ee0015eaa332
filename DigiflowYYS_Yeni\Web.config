﻿<configuration>
	<configSections>
		<section name="bccMailList" type="Digiturk.Workflow.Common.WorkflowMailerSection, Digiturk.Workflow.Common"/>
		<section name="debugMailList" type="Digiturk.Workflow.Common.WorkflowMailerSection, Digiturk.Workflow.Common"/>
		<sectionGroup name="devExpress">
			<section name="themes" type="DevExpress.Web.ASPxClasses.ThemesConfigurationSection, DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
			<section name="compression" type="DevExpress.Web.ASPxClasses.CompressionConfigurationSection, DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
			<section name="settings" type="DevExpress.Web.ASPxClasses.SettingsConfigurationSection, DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
			<section name="errors" type="DevExpress.Web.ASPxClasses.ErrorsConfigurationSection, DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
		</sectionGroup>
	</configSections>
	<appSettings>
		<add key="DatabaseMode" value="Oracle"/>
		<add key="debugMode" value="true"/>
		<add key="IsSIDControls" value="True"/>
		<add key="PageTitle" value="Digiturk İş Akışları"/>
		<add key="hibernate.use_reflection_optimizer" value="false"/>
		<add key="ServiceName" value="ApplicationServer1"/>
		<add key="IsYYSActive" value="false"/>
		<add key="DefinitionConfigration" value="\\dtl1sp1\Digiturk.Workflow.DigiFlow\WFPages\Definiton.xml"/>
		<add key="SharePointRaporTalepUploadFolder" value="http://belgeler/DigiFlowDocs/RaporTalepDocs/"/>
		<!--email settings-->
		<add key="Workflow.Mail.EmbeddedImagesPath" value="D:\\ITTPDev\\Deployment\\MailImages"/>
		<add key="Workflow.Mail.Params" value="ad9bBOUpHG1st9IlCOvZA9DCTJKj7XTlewXqZpa4xWo/m0f/ZXwzFpTy9cdYK53Hx2MQqWxlyxSVT5lg5waY6LC3p5i77oc4pHAEGgnKFbAuL48SNlMELo9dIiUOo2RmdTprZ/SAkyKF03+gmRGRexw3+qCFnr/iVOx/58S075o="/>
		<add key="Workflow.Mail.Server" value="smtp.digiturk.local"/>
		<add key="Workflow.Mail.FromAddress" value="<EMAIL>"/>
		<add key="Workflow.Mail.IsMailDebugMode" value="True"/>
		<add key="Web.Services.UserName" value="Digiflow_sa"/>
		<add key="Web.Services.Password" value="Digif16up+-"/>
		<add key="ESSBDurum" value="E"/>
		<!--E YADA H-->
		<add key="DBSLIVE_INQUIRY" value="********"/>
		<add key="DBSTEST_INQUIRY" value="INQUIRY"/>
		<add key="ITTPTEST_INQUIRY" value="INQUIRY"/>
		<add key="DBSLIVE_INQUIRY_APPSTR" value="Digiflow"/>
		<add key="DBSLIVE_INQUIRY_UNIQUE" value="4!fedL0w"/>
		<add key="DBSTEST_INQUIRY_APPSTR" value="Digiflow"/>
		<add key="DBSTEST_INQUIRY_UNIQUE" value="4!fedL0w"/>
		<add key="ITTPTEST_INQUIRY_APPSTR" value="Digiflow"/>
		<add key="ITTPTEST_INQUIRY_UNIQUE" value="4!fedL0w"/>
		<!--email settings-->
		<!-- ADPortal Config-->
    <add key="AdPortalServices" value="http://dtl4sptst1:3212/AdPortalServices.asmx"/>
    <add key="AdPortalCCServices" value="http://dtl4sptst1:3212/AdPortalServices.asmx"/>
	</appSettings>
	<connectionStrings>
		<add name="connStrESSB_TEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ITTPTEST)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient"/>
		<add name="connStrESSB_LIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id=ESSB_USR;Password=******" providerName="System.Data.OracleClient"/>
		<add name="DBSLIVE" connectionString="Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = DPSMS2)(PORT = 1522))(CONNECT_DATA = (SID = DBSLIVE)));User Id={0};Password=***" providerName="System.Data.OracleClient"/>
		<add name="DBSTEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=DBSTEST)));User Id={0};Password=***" providerName="System.Data.OracleClient"/>
		<add name="ITTPTEST" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ITTPTEST)));User Id={0};Password=***" providerName="System.Data.OracleClient"/>
		<add name="DBSConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SID=DBSLIVE)));User Id=INQUIRY;Password=********;Self Tuning=false"/>
		<add name="DefaultConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR;Pooling=true;Statement Cache Size=10"/>
		<add name="ReportConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=true;Statement Cache Size=10"/>
		<add name="FrameworkConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=FRAMEWORK;Password=FRAMEWORK;Pooling=true;Statement Cache Size=10"/>
		<!--<add name="DefaultConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVER=dedicated)(SERVICE_NAME=DSSFLOW)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR;Pooling=true;Statement Cache Size=10;Self Tuning=false" />
    <add name="ReportConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVER=dedicated)(SERVICE_NAME=DSSFLOW)));User Id=***********;Password=***********;Pooling=true;Self Tuning=false" />
    <add name="FrameworkConnection" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SERVER=dedicated)(SERVICE_NAME=DSSFLOW)));User Id=FRAMEWORK;Password=FRAMEWORK;Pooling=true;Statement Cache Size=10;Self Tuning=false" />-->
	</connectionStrings>
	<!--
		For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

		The following attributes can be set on the <httpRuntime> tag.
			<system.Web>
				<httpRuntime targetFramework="4.5.2" />
			</system.Web>
	-->
	<system.web>
		<compilation debug="true" targetFramework="4.5.2">
			<assemblies>
				<add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
				<add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
				<add assembly="System.DirectoryServices, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
				<add assembly="DevExpress.Data.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>

				<add assembly="DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="System.Security, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
				<add assembly="System.DirectoryServices, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
				<add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
				<add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
				<add assembly="DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<add assembly="DevExpress.Data.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.Utils.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.Charts.v12.1.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.Data.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.RichEdit.v12.1.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.Utils.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.XtraBars.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.XtraCharts.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.XtraEditors.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.XtraNavBar.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.XtraPivotGrid.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.XtraPrinting.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.XtraReports.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.XtraRichEdit.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.XtraTreeList.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.Web.ASPxEditors.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly=" DevExpress.Web.ASPxGridView.v12.1.Export, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly=" DevExpress.Web.ASPxGridView.v12.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
				<add assembly="DevExpress.Printing.v12.1.Core, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
			</assemblies>
		</compilation>
		<authentication mode="Windows"/>
		<customErrors mode="Off"/>
		<httpModules>
			<add type="DevExpress.Web.ASPxClasses.ASPxHttpHandlerModule, DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
		</httpModules>
		<pages clientIDMode="AutoID" validateRequest="false" controlRenderingCompatibilityVersion="4.0">
			<controls></controls>
		</pages>
		<httpHandlers>
			<remove verb="*" path="*.asmx"/>
			<add verb="*" path="*_AppService.axd" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="false"/>
			<add verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="false"/>
			<add path="*.asmx" verb="*" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="false"/>
			<add type="DevExpress.Web.ASPxClasses.ASPxHttpHandlerModule, DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET" path="DX.ashx" validate="false"/>
		</httpHandlers>
	</system.web>
	<!--
        The system.webServer section is required for running ASP.NET AJAX under Internet
        Information Services 7.0.  It is not necessary for previous version of IIS.
    -->
	<system.webServer>
		<handlers>
			<add type="DevExpress.Web.ASPxClasses.ASPxHttpHandlerModule, DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET" path="DX.ashx" name="ASPxHttpHandlerModule" preCondition="integratedMode"/>
		</handlers>
		<modules>
			<add type="DevExpress.Web.ASPxClasses.ASPxHttpHandlerModule, DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
		</modules>
		<httpProtocol>
			<customHeaders>
				<clear/>
				<add name="X-UA-Compatible" value="IE=7"/>
			</customHeaders>
		</httpProtocol>
		<validation validateIntegratedModeConfiguration="false"/>
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<qualifyAssembly partialName="Oracle.DataAccess" fullName="Oracle.DataAccess, Version=4.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342"/>
		</assemblyBinding>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Oracle.DataAccess" culture="neutral" publicKeyToken="89b483f429c47342" />
				<bindingRedirect oldVersion="0.0.0.0-4.122.19.1" newVersion="4.122.19.1" />
			</dependentAssembly>
		</assemblyBinding>

	</runtime>
	<debugMailList/>
	<bccMailList/>
	<devExpress>
		<themes enableThemesAssembly="true" styleSheetTheme="" theme=""/>
		<compression enableHtmlCompression="false" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="true"/>
		<settings rightToLeft="false"/>
		<errors callbackErrorRedirectUrl=""/>
	</devExpress>
</configuration>
