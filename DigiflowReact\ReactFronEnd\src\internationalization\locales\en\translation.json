{"technology": "TECHNOLOGY", "administrative_processes": "Administrative Processes", "human_resources_processes": "Human Resources Processes", "legal_processes": "Legal Processes", "financial_processes": "Financial Processes", "workflows_system": "Workflows System", "description": "Description", "create_request": "Create Request", "history": "History", "command_list": "Command List", "inbox": "Inbox", "suspended_inbox": "Suspended Inbox", "cancel_delegation": "Cancel Delegation", "cancel_monitoring": "Cancel Monitoring", "my_inbox": "MY INBOX", "delegate_inbox": "DELEGATE INBOX", "comment_inbox": "COMMENT INBOX", "department": "Department", "division": "Division", "unit": "Unit", "team": "Team", "user": "User", "workflow": "Workflow", "starting_date": "Starting Date", "end_date": "End Date", "workflow_name": "Workflow Name", "owner": "Owner", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "state": "State", "forwarder": "Forwarder", "date": "Date", "bring": "Bring", "users": "Users", "accept_or_reject": "Accept/Reject", "suspend_or_resume": "Suspend/Resume", "accept": "Accept", "reject": "Reject", "forward": "Forward", "post_comment": "Post Comment", "suspend": "Suspend", "resume": "Resume", "cancel": "Cancel", "add_comment": "Add Comment", "operation": "Operation", "description_proposal": "Description Proposal", "edit_description": "Edit Description"}