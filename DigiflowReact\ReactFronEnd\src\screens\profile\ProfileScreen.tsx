import * as React from 'react'
import * as WFace from '@wface/components'
import i18next from "i18next"
import { useTranslation } from "react-i18next"
import cookies from 'js-cookie'
import {useEffect, useState} from "react";
import {WRadio, WRadioGroup} from "@wface/components";
import axios from "axios";
import {getUser} from "../../services/wface-helper";

const languages = [
    {
        code: 'en',
        name: 'English',
        country_code: 'gb'
    },
    {
        code: 'tr',
        name: 'Türkçe',
        country_code: 'tr'
    },
]

function ProfileScreen() {
    const currentLanguageCode = cookies.get('i18next') || 'en'
    // const currentLanguage = languages.find(1 => 1.code === currentLanguageCode)
    const { t } = useTranslation()

    const user = getUser()



    return (
        <div className="container">
            <div className="row">
                <div className="col" style={{display: 'flex', alignItems: 'center', justifyContent: 'center',}}>
                    <WFace.WAvatar
                        style={{margin: 10, width: 120, height: 120,}}
                        alt="bein HD1"
                        src="https://www.w3schools.com/howto/img_avatar.png"
                    />
                </div>
            </div>

            <div className="row">
                <div className="col" style={{fontSize: 26, fontWeight: 'bold', textAlign: 'center', margin: 10}}>
                    {user&&user.UserNameSurName}
                </div>
                <div className="col" style={{fontSize: 26, fontWeight: 'bold', textAlign: 'center', margin: 10}}>
                    {user&&user.LoginObject._Email }
                </div>
            </div>

            <div className="row">
                <div className="col" style={{fontSize: 18, textAlign: 'center', margin: 10}}>
                    {t('technology')}
                </div>
            </div>

            <div>
                <WRadioGroup value={currentLanguageCode}>
                    {languages.map(({ code, name, country_code }) => (
                        <WRadio value={code} label={name} onClick={() => i18next.changeLanguage(code)} />
                    ))}
                </WRadioGroup>
            </div>
        </div>
    )
}

export default ProfileScreen
