import { IConfiguration, IHttpService } from "@wface/ioc";
import AuthService from "../../src/services/auth-service";
import AppHooks from "../../src/services/app-hooks";
import { WTheme } from "@wface/components";
import InboxScreen from "../../src/screens/header-options/InboxScreen";
import ProfileScreen from "../../src/screens/profile/ProfileScreen";
import IOC from "@wface/ioc";
import { UserContext, AppContext } from "@wface/store";
import DelegationScreen from "../../src/screens/menu/administrative-processes/delegation/DelegationScreen";
import ContractConfirmation from "../../src/screens/menu/legal-processes/contract-confirmation/ContractConfirmation";
import BasePathRouterContainer from "../../src/components/BasePathRouterContainer";
import WorkflowDetailScreen from "../../src/screens/detail/WorkflowDetailScreen";

const theme = {
  palette: {
    primary: {
      main: "#000"
    },
    secondary: {
      main: "#801318"
    }
  },
  props: {},
  overrides: {
    MuiDrawer: {
      paper: {
        background: "#662e85",
        "& *": {
          color: "#fff"
        }
      },
      hover: "000"
    },
    MuiTab: {
      root: {
        height: 30,
        minHeight: 30
      },
      wrapper: {
        height: 30,
        minHeight: 30,
        display: "inline"
      }
    },
    MuiIconButton: {
      root: {
        padding: 5
      }
    },
    MuiTabs: {
      flexContainer: {
        height: 30,
        overflow: "hidden"
      },
      root: {
        height: 30,
        minHeight: 30
      },
      indicator: {
        backgroundColor: "#fff",
        height: 2,
        bottom: 5
      }
    },
    MuiAppBar: {
      root: {
        background: "#662e85 !important",
        "& *": {
          color: "rgba(255, 255, 255, 1)"
        }
      }
    },
    MuiToolbar: {
      root: {
        height: 30,
        minHeight: "30px !important"
      }
    },
    MuiGrid: {
      container: {
        fontSize: "0.8rem"
      },
      item: {
        fontSize: "0.8rem"
      }
    },
    MuiInput: {
      root: {
        fontSize: "0.875rem !important"
      }
    },
    MuiTypography: {
      root: {
        fontSize: "0.875rem !important"
      }
    }
  }
} as unknown as WTheme;

export const openScreen = (screen: string, initialValues?: any): void => {
  let appContext: AppContext;

  if (IOC.isBound("openScreen") && IOC.isBound("AppContext")) {
    appContext = IOC.get<AppContext>("AppContext");

    let menuTreeItem = appContext.menuTree.find(m => m.screen === screen);

    if (menuTreeItem) {
      let m = { ...menuTreeItem };

      m.initialValues = initialValues;

      IOC.get<any>("openScreen")(m);
    }
  }
};

const config = {
  projectName: "Digiflow",
  screenList: {
    InboxScreen,
    ProfileScreen,
    DelegationScreen,
    ContractConfirmation,
    WorkflowDetailScreen
  },

  authService: AuthService,
  theme: theme,
  useLocalStorage: true,
  hooks: AppHooks,
  search: false,
  rightContextItems: [
    {
      id: "my-profile",
      icon: "account_circle",
      text: "Profilim",
      onClick: () => openScreen("ProfileScreen")
    }
  ],
  components: {
    Container: BasePathRouterContainer
  },
  authRequired: false
} as IConfiguration;

export default config;
