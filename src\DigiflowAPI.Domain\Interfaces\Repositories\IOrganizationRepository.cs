﻿using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Entities.Organization;

namespace DigiflowAPI.Domain.Interfaces.Repositories;

public interface IOrganizationRepository
{
    string GetDepartment(long managerID);
    Task<IEnumerable<DpHrDeps>> GetDepartmentSelectAsync(long? id);
    Task<IEnumerable<DpHrDeps>> GetDepartmentAsync(long? id);
    Task<IEnumerable<DpHrDeps>> GetSubDepartments(long departmentId);
    Task<DpHrUsers?> GetDPHRUserByIdAsync(long? userId = null);
    Task<DepsPath?> GetDPHRUserDepsPathByIdAsync(long? userId = null);
    Task<(DepsPath? depsPath, List<DpHrDeps> departments, List<VwUserInformation> users)> GetOrganizationHierarchy(long? wfInstanceId = null);
}
