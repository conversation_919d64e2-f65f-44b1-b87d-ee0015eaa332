﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Digiturk.Workflow.Common;
using Digiturk.Workflow.Entities;
using NHibernate.Impl;
using NHibernate;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Repository;
using NHibernate.Cfg;
using NHibernate.Criterion;

namespace Digiturk.Workflow.Digiflow.Entities
{
    /// <summary>
    /// YYS içerisinde kullanılan ve kullanılacak olan değişkenleri barındırır.
    /// </summary>
    public static class DBHelper
    {
        private static ISession _session;
      
        public static ISession DBSLiveSession
        {
            get
            {
                if (_session == null || !_session.IsConnected)
                {
                    _session = getSession();
                }
                return _session;
            }
            set
            {
                _session = value;
            }
        }

        private static ISession getSession()
        {
            Configuration cfg = new Configuration();
            cfg.SetProperty("dialect", "NHibernate.Dialect.Oracle10gDialect");
            cfg.SetProperty("connection.driver_class", "NHibernate.Driver.OracleDataClientDriver");
            cfg.SetProperty("connection.connection_string", "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SID=FLOWDEV)));User Id=FRAMEWORK;Password=FRAMEWORK;Pooling=true;Statement Cache Size=10");
            cfg.SetProperty("show_sql", "true");
            cfg.SetProperty("query.substitutions", "true 1, false 0, yes 'Y', no 'N'");
            cfg.SetProperty("proxyfactory.factory_class", "NHibernate.ByteCode.LinFu.ProxyFactoryFactory, NHibernate.ByteCode.LinFu");
            cfg.AddAssembly("Digiturk.Workflow.Digiflow.Entities");
            ISessionFactory factory = cfg.BuildSessionFactory();
            ISession session = factory.OpenSession();
            return session;
        }

        public static IList<Workflow> GetWorkFlows()
        {
            IList<Workflow> wfl = DBSLiveSession.CreateCriteria((typeof(Workflow))).List<Workflow>();
            DBSLiveSession.Close();
            return wfl;
        }

        public static IList<HrUser> GetHrUsers()
        {
            IList<HrUser> wfl = DBSLiveSession.CreateCriteria((typeof(HrUser))).List<HrUser>();
            DBSLiveSession.Close();
            return wfl;
        }
    }
}
