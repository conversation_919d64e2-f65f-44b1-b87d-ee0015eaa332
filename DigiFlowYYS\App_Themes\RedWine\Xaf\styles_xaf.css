/* Button ActionContainer (DefaultActionContainerControlBuilder, removed in v9.3) */
.ActionContainer .Item {
	padding: 0px 10px 0px 10px;
}
.ActionContainer .WrappedItem {
	padding: 4px 10px 4px 10px;
	float: left;
}
.Vertical .Item {
	padding: 4px 0px 4px 0px;
}
/* ParametrizedActionControl */
.ParametrizedActionControl .Label, .ParametrizedActionControl .Editor  {
	vertical-align: middle;
	padding: 0px;
	border-collapse: separate;
}
.ParametrizedActionControl .Editor {
	padding-left: 1px;
	border-collapse: separate;
}
.ParametrizedActionControl .Editor div.dxb {
	padding-top: 1px;
	padding-bottom: 2px;
}
.ParametrizedActionWithImage .Editor div.dxb {
	padding: 0px 8px 1px 9px;
}
.ParametrizedActionControl .Editor div.dxb span {
	white-space: nowrap;
}
.ParametrizedActionControl .dxeTextBox_xaf
{
	width: 130px;
}
/* LookupButton */
.xafLookupButton {
	margin-left: 1px;
}
.Layout {
	padding-left: 32px;
}
.LayoutTabbedGroupContainer {
	padding: 16px 5px 5px 5px;
}
/* Layout Group Header */
.GroupHeader
{
	width: 100%;
	height: 27px;
	margin: 20px 0px 5px 0px;
}
.GroupHeader:first-child
{
	margin: 0px 0px 5px 0px;
}
.GroupHeader td {
}
.NextColumn .GroupHeader td.L div {
	width: 35px;
}
.GroupHeader td.R div {
	width: 5px;
	height: 27px;
}
/* Layout Group Content */
.Layout table.GroupContent {
	/*margin: 13px 0px 22px 0px;*/
	width: 100%;
}
.Layout td.GroupContent {
	vertical-align: top;
}
.Layout .WebEditorCell {
	border-collapse: separate;
	padding: 0px 0px 0px 0px;
}
.Layout .WebEditorCell a, .Layout .WebEditorCell a:hover, .Layout .WebEditorCell a:visited{
	text-decoration: underline;
}
.Layout div.Item {
	padding: 2px 5px 2px 5px;
}
.Layout .NextColumn div.Item {
	padding: 2px 5px 2px 5px;
}
.Layout .LayoutTabs {
	margin: 10px 0px 0px 0px;
}
.Layout .LayoutTabContainer {
	margin: 0px !important;
}
.Layout .HItem {
	vertical-align: top;
}
.StaticText {
}
.Layout .haLeft
{
	text-align: left !important;
}
.Layout .haRight
{
	text-align: right !important;
}
.Layout .haCenter
{
	text-align: center !important;
}

.Layout .vaTop
{
	vertical-align: top !important;
}
.Layout .vaCenter
{
	vertical-align: middle !important;
}
.Layout .vaBottom
{
	vertical-align: bottom !important;
}
.Layout .Item .ACH
{
	margin: 1px 0px;
}
.Layout .Item .ACH .ParametrizedActionControl
{
	margin: 0px !important;
}
/* Grid */
td.ActionCell
{
	text-align: center;
	white-space: nowrap;
}
td.ActionCell *
{
	white-space: nowrap;
}
.xafGridColumnChooser
{
	text-align: right;
	padding: 3px;
}
.xafGridPageSizeChooserLabel
{
	padding: 3px;
	white-space: nowrap;
}
/* Chart */
.xafChartPanel label {
	font-size: 85%;
}
.xafChartPanel .dxeMemoEditArea_xaf label {
	font-size: 100%;
}
/* ActionContainerHolder */
.ACH .TemplatedItem
{
	padding: 0px 10px 0px 10px;
}
.ACH .dxm-item .TemplatedItem
{
	padding: 0px 10px 0px 5px;
}
/* TreeList Node Template:
   NavigationDataCellTemplate and ASPxTreeListDataCellTemplateImageDecorator */
.TextCell
{
	padding-left: 5px;
}
.XafTreeNode .ImageCell
{
	width: 16px;
	vertical-align: top;
}
.XafTreeNode .TextCell
{
	padding: 0px 5px;
	font-size: 90%;
}
/* FileDataEdit */
.XafFileDataAnchor
{
	white-space:nowrap;
	margin-right: 5px;
}
/* DropDownSingleChoiceActionControlHorizontal */
.SingleChoiceActionItemLabel
{
    padding-left: 9px;
    padding-right: 5px;
}
.xafLookupEditor
{
	border-collapse: separate;
}