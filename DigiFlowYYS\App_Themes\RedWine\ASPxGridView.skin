<%@ Register TagPrefix="dx" Namespace="DevExpress.Data" Assembly="DevExpress.Data.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=B88D1754D700E49A" %>
<%@ Register TagPrefix="dxgv" Namespace="DevExpress.Web.ASPxGridView" Assembly="DevExpress.Web.ASPxGridView.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>

<dxgv:ASPxGridView runat="server" CssFilePath="~/App_Themes/RedWine/{0}/styles.css" CssPostfix="RedWine">
    <Images SpriteCssFilePath="~/App_Themes/RedWine/{0}/sprite.css">
        <LoadingPanel Url="~/App_Themes/RedWine/GridView/Loading.gif"></LoadingPanel>
        <LoadingPanelOnStatusBar Url="~/App_Themes/RedWine/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>
    </Images>
    <ImagesFilterControl>
        <LoadingPanel Url="~/App_Themes/RedWine/Editors/Loading.gif"></LoadingPanel>
    </ImagesFilterControl>
    <ImagesEditors>
		<DropDownEditDropDown>
			<SpriteProperties HottrackedCssClass="dxEditors_edtDropDownHover_RedWine" />
		</DropDownEditDropDown>
    </ImagesEditors>
    <SettingsLoadingPanel ImagePosition="Top" />
    <SettingsPager CurrentPageNumberFormat="{0}">
    </SettingsPager>
    <Styles>
        <LoadingPanel ImageSpacing="8px"></LoadingPanel>
    </Styles>
    <StylesEditors>
        <ProgressBar Height="25px">
        </ProgressBar>
        <CalendarHeader Spacing="1px">
        </CalendarHeader>
    </StylesEditors>
</dxgv:ASPxGridView>