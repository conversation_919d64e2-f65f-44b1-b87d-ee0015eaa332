﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using NHibernate.Criterion;
using System;
using System.Linq;

namespace Digiturk.Workflow.Digiflow.Actions
{
    /// <summary>
    /// Akış Admini Delegasyon Yönetimi işlemini Deamon tarafında düzenler
    /// </summary>
    public class DelegatePreviousTask : IActionClass
    {
        /// <summary>
        /// Akış Admini Delegasyon Yönetimi işlemini Deamon tarafında düzenler
        /// </summary>
        /// <param name="wfContext"></param>
        /// <param name="actionInstance"></param>
        /// <returns></returns>
        public bool Execute(WFContext wfContext, FWfActionInstance actionInstance)
        {
            using (UnitOfWork.Start())
            {
                if (wfContext.Parameters.ContainsKey("DelegateTo") && wfContext.Parameters["DelegateTo"] != null && !String.IsNullOrEmpty(wfContext.Parameters["DelegateTo"].ToString()) && wfContext.Parameters.ContainsKey("DelegationStart") && wfContext.Parameters["DelegationStart"] != null && !String.IsNullOrEmpty(wfContext.Parameters["DelegationStart"].ToString()))
                {
                    var prevIns = actionInstance.WfStateInstance.WfStateInstanceFWfActionInstanceList.OrderBy(t => t.WfActionInstanceId).ToList()[actionInstance.WfStateInstance.WfStateInstanceFWfActionInstanceList.Count - 2];
                    if (prevIns != null)
                    {
                        #region Delegasyon Bölme işlemi Öncesinde gerekli kontroller yapılıyor

                        long assigmentOwner = AssignmentHelper.GetAssignmentList(FWfAssignmentTypeValues.TaskInbox, prevIns.WfActionInstanceId, -1).First().AssignedOwnerRefId;
                        var delL = WFRepository<FWfDelegation>.GetEntityList(DetachedCriteria.For<FWfDelegation>().
                        Add(Expression.Lt("DelegationStartDate", prevIns.EndTime.Value.AddMinutes(-1))).
                        Add(Expression.Gt("DelegationEndDate", prevIns.EndTime.Value.AddMinutes(1))).
                        Add(Expression.Eq("DelegationOwnerRefId", assigmentOwner)).
                        Add(Expression.Eq("WorkflowDefId", actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId)));

                        #endregion Delegasyon Bölme işlemi Öncesinde gerekli kontroller yapılıyor

                        #region Delegasyonlar düzenleniyor

                        if (delL.Count > 0)
                        {
                            foreach (var oldDelegation in delL)
                            {
                                WorkflowDelegationHelper.StartDelegation(assigmentOwner, oldDelegation.DelegateRefId, actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId, oldDelegation.DelegationComment, prevIns.EndTime.Value.AddMinutes(1).AddSeconds(1), oldDelegation.DelegationEndDate);
                                oldDelegation.DelegationEndDate = prevIns.StartTime.Value.AddMinutes(-1).AddSeconds(-1);
                            }
                        }

                        #endregion Delegasyonlar düzenleniyor

                        #region Yeni İşlemin delegasyonu Tanımlanıyor

                        WorkflowDelegationHelper.StartDelegation(assigmentOwner, Convert.ToInt64(wfContext.Parameters["DelegateTo"]), actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowDef.WfWorkflowDefId, "", prevIns.EndTime.Value.AddSeconds(-10), prevIns.EndTime.Value.AddMilliseconds(100));

                        #endregion Yeni İşlemin delegasyonu Tanımlanıyor

                        #region Object Disopsing

                        delL = null;
                        prevIns = null;

                        #endregion Object Disopsing
                    }
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }
    }
}