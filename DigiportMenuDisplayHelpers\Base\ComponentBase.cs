﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiportMenuDisplayHelpers.Base
{
    public class ComponentBase
    {
        public EffectType GetEffectType(int effectNo)
        {
            if (effectNo == (int)EffectType.slide)
                return EffectType.slide;
            else if (effectNo == (int)EffectType.fade)
                return EffectType.fade;
            else if (effectNo == (int)EffectType.slideDown)
                return EffectType.slideDown;
            else if (effectNo == (int)EffectType.zoom)
                return EffectType.zoom;
            else
                return EffectType.slide;
        }
        public TransitionEasing GetTransitionEasing(int transitionEasingNo)
        {
            if (transitionEasingNo == (int)TransitionEasing.ease)
                return TransitionEasing.ease;
            else if (transitionEasingNo == (int)TransitionEasing.easeInBack)
                return TransitionEasing.easeInBack;
            else if (transitionEasingNo == (int)TransitionEasing.easeOutElastic)
                return TransitionEasing.easeOutElastic;
            else if (transitionEasingNo == (int)TransitionEasing.ease_in_out)
                return TransitionEasing.ease_in_out;
            else
                return TransitionEasing.ease_in_out;
        }
        public TransitionEasing2 GetTransitionEasing2(int transitionEasingNo)
        {
            if (Enum.IsDefined(typeof(TransitionEasing2), transitionEasingNo))
            {
                return (TransitionEasing2)transitionEasingNo;
            }
            else
            {
                return TransitionEasing2.linear;
            }
        }
        public enum DigiportComponentClickActions
        {
            Olay_Yok = 0,
            Belirtilen_Linki_Yeni_Sayfada_Aç = 1,
            Belirtilen_Linki_Yeni_Pencerede_Aç = 2,
            Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç = 3,
            Belirtilen_Html_İçeriği_Yeni_Pencerede_Aç = 4,
            Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç = 5,
            Belirtilen_Html_İçeriği_Yeni_Digiport_Pencerede_Aç = 6
        }
        public enum DigiportComponentClickActions_En
        {
            No_Event = 0,
            Open_New_Tab_For_Defined_Link = 1,
            Open_New_Window_For_Defined_Link = 2,
            Open_New_Tab_For_Defined_Html_Content = 3,
            Open_New_Window_For_Defined_Html_Content = 4,
            Open_New_Digiport_Tab_For_Defined_Html_Content = 5,
            Open_New_Digiport_Window_For_Defined_Html_Content = 6
        }
        public TitleMode GetTitleMode(int titleModeNo)
        {
            if (titleModeNo == (int)TitleMode.Always)
                return TitleMode.Always;
            else if (titleModeNo == (int)TitleMode.Hover)
                return TitleMode.Hover;
            else if (titleModeNo == (int)TitleMode.None)
                return TitleMode.None;
            else
                return TitleMode.None;
        }
        public string GetComponentClickEvent(string componentType, int componentClickAction, string contentId, string contentTargetLink, string popupWindowWidth, string popupWindowHeight, string digiportContentPageLink)
        {
            string onclickFunc = string.Empty;
            if (componentClickAction == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Sayfada_Aç)
            {
                onclickFunc = "openNewTab('" + contentTargetLink + "')";
            }
            else if (componentClickAction == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Linki_Yeni_Pencerede_Aç)
            {
                onclickFunc = "openNewWindow(" + popupWindowWidth + ", " + popupWindowHeight + ", '" + contentTargetLink + "')";
            }
            else if (componentClickAction == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Sayfada_Aç)
            {
                contentTargetLink = "/Digiport/DisplayContent.aspx?content-id=" + contentId + "&component-type=" + componentType;
                onclickFunc = "openNewTab('" + contentTargetLink + "')";
            }
            else if (componentClickAction == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Pencerede_Aç)
            {
                contentTargetLink = "/Digiport/DisplayContent.aspx?content-id=" + contentId + "&component-type=" + componentType;
                onclickFunc = "openNewWindow(" + popupWindowWidth + ", " + popupWindowHeight + ", '" + contentTargetLink + "')";
            }
            else if (componentClickAction == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Sayfada_Aç)
            {
                contentTargetLink = digiportContentPageLink + "?content-id=" + contentId + "&component-type=" + componentType;
                onclickFunc = "openNewTab('" + contentTargetLink + "')";
            }
            else if (componentClickAction == (int)DigiportMenuDisplayHelpers.Base.ComponentBase.DigiportComponentClickActions.Belirtilen_Html_İçeriği_Yeni_Digiport_Pencerede_Aç)
            {
                contentTargetLink = digiportContentPageLink + "?content-id=" + contentId + "&component-type=" + componentType;
                onclickFunc = "openNewWindow(" + popupWindowWidth + ", " + popupWindowHeight + ", '" + contentTargetLink + "')";
            }
            return onclickFunc;
        }
    }

    public enum EffectType
    {
        slide = 1, fade = 2, zoom = 3, slideDown = 4
    }
    public enum TransitionEasing
    {
        ease_in_out = 1, ease = 2, easeInBack = 3, easeOutElastic = 4
    }
    public enum TransitionEasing2
    {
        easeInOutBack = 1,
        easeInCubic = 2,
        easeOutBounce = 3,
        swing = 4,
        easeInOutQuad = 5,
        linear = 6,
        easeInBounce = 7
    }
    public enum TitleMode
    {
        Always = 1, Hover = 2, None = 0
    }
    public enum TitleMode_Tr
    {
        Her_Zaman = 1, Üstüne_Gelince = 2, Yok = 0
    }
}
