﻿using Digiturk.Workflow.Digiflow.DataAccessLayer;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System.Collections.Generic;
using System.Data;

/// <summary>
/// TopluOnayHelper için veritabanı bağlantıları yapılır.
/// </summary>
public class TopluOnayHelper
{
    public static DataTable GetGrid()
    {
        string strSql = @"select
                        A.LOGIN_ID,
                        A.WF_WORKFLOW_DEF_ID,
                        A.WF_WORKFLOW_ENTITY,
                        A.WF_WORKFLOW_ENTITY_VALUE,
                        D.NAME,
                        U.NAME_SURNAME
                        from
                        DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF A INNER JOIN
                        FRAMEWORK.F_WF_WORKFLOW_DEF D ON A.WF_WORKFLOW_DEF_ID = D.WF_WORKFLOW_DEF_ID
                        INNER JOIN DT_WORKFLOW.VW_USER_INFORMATION U ON U.LOGIN_ID = A.LOGIN_ID order by name_surname, d.name";
        return Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", strSql);
    }

    /// <summary>
    /// Verilen değerleri
    /// WF_DF_MULTIPLE_APPROVAL_DEF tablosuna kaydeder
    /// </summary>
    /// <param name="LOGIN_ID"></param>
    /// <param name="WF_WORKFLOW_DEF_ID"></param>
    /// <param name="WF_WORKFLOW_ENTITY"></param>
    /// <param name="WF_WORKFLOW_ENTITY_VALUE"></param>
    public static void KayitEt(long LOGIN_ID, long WF_WORKFLOW_DEF_ID, string WF_WORKFLOW_ENTITY, string WF_WORKFLOW_ENTITY_VALUE)
    {
        #region WF_DF_MULTIPLE_APPROVAL_DEF Tablosuna insert

        string query = @"INSERT INTO DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF A
                (
                A.LOGIN_ID,
                A.WF_WORKFLOW_DEF_ID,
                A.WF_WORKFLOW_ENTITY,
                A.WF_WORKFLOW_ENTITY_VALUE
                )
                VALUES
                (
                :V_LOGIN_ID,
                :V_DEF_ID,
                :V_ENTITY,
                :V_ENTITY_VALUE
                )";
        List<CustomParameterList> CustomList = new List<CustomParameterList>();
        CustomList.Add(new CustomParameterList("V_LOGIN_ID", LOGIN_ID));
        CustomList.Add(new CustomParameterList("V_DEF_ID", WF_WORKFLOW_DEF_ID));
        CustomList.Add(new CustomParameterList("V_ENTITY", WF_WORKFLOW_ENTITY));
        CustomList.Add(new CustomParameterList("V_ENTITY_VALUE", WF_WORKFLOW_ENTITY_VALUE));

        Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.ExecuteQuery2("DefaultConnection", query, CustomList);

        query = null;
        CustomList = null;

        #endregion WF_DF_MULTIPLE_APPROVAL_DEF Tablosuna insert
    }

    /// <summary>
    /// Verilen değerleri WF_DF_MULTIPLE_APPROVAL_ENTITY
    /// tablosuna kaydeder.
    /// </summary>
    /// <param name="WF_WORKFLOW_DEF_ID"></param>
    /// <param name="WF_WORKFLOW_ENTITY"></param>
    public static void EntityKayitEt(long WF_WORKFLOW_DEF_ID, string WF_WORKFLOW_ENTITY)
    {
        #region WF_DF_MULTIPLE_APPROVAL_ENTITY Tablosuna insert

        List<CustomParameterList> CustomList = new List<CustomParameterList>();
        string query = @"INSERT INTO DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_ENTITY A
                (
                A.WF_WORKFLOW_ENTITY_NAME,
                A.WF_WORKFLOW_DEF_ID,
                A.WF_WORKFLOW_ENTITY_TYPE
                )
                VALUES
                (
                :V_ENTITY_NAME,
                :V_DEF_ID,
                'System.Decimal')";

        CustomList.Add(new CustomParameterList("V_ENTITY_NAME", WF_WORKFLOW_ENTITY));
        CustomList.Add(new CustomParameterList("V_DEF_ID", WF_WORKFLOW_DEF_ID));
        Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.ExecuteQuery2("DefaultConnection", query, CustomList);
        query = null;
        CustomList = null;

        #endregion WF_DF_MULTIPLE_APPROVAL_ENTITY Tablosuna insert
    }

    /// <summary>
    /// Seçilen satır veritabanından silinir
    /// </summary>
    /// <param name="loginId"></param>
    /// <param name="defId"></param>
    /// <returns></returns>
    public static int Delete(long loginId, long defId)
    {
        string query = "DELETE FROM DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF WHERE DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF.LOGIN_ID=:V_LOGIN_ID AND DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_DEF.WF_WORKFLOW_DEF_ID=:V_DEF_ID";
        OracleParameter[] p = new OracleParameter[2];
        p[0] = new OracleParameter("LOGIN_ID", loginId);
        p[1] = new OracleParameter("WF_WORKFLOW_DEF_ID", defId);
        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }

    /// <summary>
    /// WF_DF_MULTIPLE_APPROVAL_ENTITY tablosunu getirir.
    /// </summary>
    /// <returns></returns>
    public static DataTable ControlToEntityTable()
    {
        string query = "select * from  DT_WORKFLOW.WF_DF_MULTIPLE_APPROVAL_ENTITY";

        return Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", query);
    }
}