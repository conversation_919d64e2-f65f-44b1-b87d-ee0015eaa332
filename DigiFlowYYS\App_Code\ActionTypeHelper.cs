﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System.Collections.Generic;
using System.Data;

/// <summary>
/// İş akış kuralı oluştururken kullanılan Action Tiplerinin fonksiyonlarını barındırır.
/// </summary>
public class ActionTypeHelper
{
    /// <summary>
    /// Action tiplerini getirir
    /// </summary>
    /// <returns></returns>
    public static DataTable GetActionTypes(string StateDef)
    {
        string sql = "SELECT * FROM DT_WORKFLOW.YYS_ACTION_TYPES WHERE DT_WORKFLOW.YYS_ACTION_TYPES.STATE_DEF_TYPE IS NOT NULL AND STATE_DEF_TYPE=:STATE_DEF_TYPE order by DT_WORKFLOW.YYS_ACTION_TYPES.ACTION_TYPE_NAME ";
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("STATE_DEF_TYPE", StateDef);
        DataTable dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, sql);
        List<ActionAuthorizationType> ret = new List<ActionAuthorizationType>();
        if (dt.Rows.Count > 0)
        {
            foreach (DataRow item in dt.Rows)
            {
                ret.Add(ConvertDataRow(item));
            }
        }
        return dt;
    }

    /// <summary>
    /// Action tiplerini getirir
    /// </summary>
    /// <returns></returns>
    public static ActionAuthorizationType GetActionTypeName(long ActionId)
    {
        string sql = "SELECT * FROM DT_WORKFLOW.YYS_ACTION_TYPES WHERE DT_WORKFLOW.YYS_ACTION_TYPES.ACTION_TYPE_ID=:ACTION_TYPE_ID";
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("ACTION_TYPE_ID", ActionId);
        DataTable dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, sql);
        ActionAuthorizationType ret = new ActionAuthorizationType();
        if (dt.Rows.Count > 0)
        {
            ret = (ConvertDataRow(dt.Rows[0]));
        }
        return ret;
    }

    /// <summary>
    /// Veritabanından gelen datayı ActionType nesnesine çevirir
    /// </summary>
    /// <param name="dr"></param>
    /// <returns></returns>
    public static ActionAuthorizationType ConvertDataRow(DataRow dr)
    {
        ActionAuthorizationType At = new ActionAuthorizationType();
        At.ActionTypeCd = dr["ACTION_TYPE_CD"].ToString();
        At.ActionTypeColor = dr["ACTION_TYPE_COLOR"].ToString();
        At.ActionTypeId = ConvertionHelper.ConvertValue<int>(dr["ACTION_TYPE_ID"].ToString());
        At.ActionTypeName = dr["ACTION_TYPE_NAME"].ToString();
        At.IsToGroup = ConvertionHelper.ConvertValue<long>(dr["IS_TO_GROUP"].ToString());
        At.StateDefType = dr["STATE_DEF_TYPE"].ToString();
        return At;
    }
}