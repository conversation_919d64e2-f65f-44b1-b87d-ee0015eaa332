{"compilerOptions": {"outDir": "./dist/", "sourceMap": true, "noImplicitAny": false, "module": "commonjs", "target": "es6", "jsx": "react", "lib": ["es5", "es6", "dom", "esnext"], "types": ["reflect-metadata", "node"], "importHelpers": true, "noImplicitReturns": true, "suppressImplicitAnyIndexErrors": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true}, "include": ["./index.tsx", "./src/**/*"]}