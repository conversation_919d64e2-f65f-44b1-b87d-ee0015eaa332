﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.WebCore;
using System;
using System.Data;
using System.Web.UI;

[System.Runtime.InteropServices.GuidAttribute("A1F48856-4B69-4034-9D3D-BD3838587AD4")]
public partial class MonitoringWorkflowAdmin : YYSSecurePage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("tr-TR");
        System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("tr-TR");
        System.Threading.Thread.CurrentThread.CurrentCulture = System.Globalization.CultureInfo.GetCultureInfo("tr-TR");
        System.Threading.Thread.CurrentThread.CurrentUICulture = System.Globalization.CultureInfo.GetCultureInfo("tr-TR");

        try
        {
            if (!Page.IsPostBack)
            {
                if (AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAdmin || AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAndFlowAdmin)
                {
                    DataTable dtbl = WorkflowHelper.GetLiveWorkFlows();
                    DrpWorkFlow = Common.FillDropDownList("NAME", "WF_WORKFLOW_DEF_ID", DrpWorkFlow, dtbl, "İŞ AKIŞI SEÇİNİZ", "0");
                    pnlWorkFlowId.Visible = false;
                    LoadDataBinding();
                }
                else
                {
                    Response.Redirect("Error.aspx", false);
                }
            }
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowError("Hata", "Sayfa yüklenirken beklenmeyen bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", ex.Message);
        }
    }

    /// <summary>
    /// Org tree dolduruluyor.
    /// </summary>
    public void LoadDataBinding()
    {
        ((MasterPage)this.Master).PageTitle = "GÖRÜNTÜLEME YETKİSİ";
        OrgTreePurchaseUser.SetControlStatus(true, true, true, true, true, true, true, true, true, true);
        OrgTreePurchaseOwner.SetControlStatus(true, true, true, true, true, true, true, true, true, true);
        OrgTreePurchaseUser.SetValues(0, 0, 0, 0, "", false, 0, true, true, 0, 0, 0, 0, 0);
        OrgTreePurchaseOwner.SetValues(0, 0, 0, 0, "", false, 0, true, true, 0, 0, 0, 0, 0);

        kullanici_ekle();
    }

    private void kullanici_ekle()
    {
        if (!IsPostBack)
        {
            string SQL = @"Select 0 as LOGIN_ID, ' ---Seçiniz---' as NAME_SURNAME from dual Union Select  LOGIN_ID,NAME_SURNAME  from DT_WORKFLOW.VW_USER_INFORMATION Order By NAME_SURNAME ASC";
            DataTable DtbSonuc = Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", SQL);

            DrpUserName.DataTextField = "NAME_SURNAME";
            DrpUserName.DataValueField = "LOGIN_ID";
            DrpUserName.DataSource = DtbSonuc;
            DrpUserName.DataBind();
        }
    }

    /// <summary>
    /// Panellerin Görüntüleme Talebi Türüne göre görünürlüklerini değiştirir
    /// </summary>
    protected void ChangeVisibilityOfPanels()
    {
        //Akış Numarası seçerek
        if (rdTalepTuru.SelectedValue == "0")
        {
            pnlWorkFlowId.Visible = true;
            PnlPurchaseOwner.Visible = false;
            pnlWorkFlowList.Visible = false;
        }
        //Kullanıcı Seçerek
        else if (rdTalepTuru.SelectedValue == "1")
        {
            pnlWorkFlowId.Visible = false;
            PnlPurchaseOwner.Visible = true;
            pnlWorkFlowList.Visible = true;
        }
    }

    /// <summary>
    /// iş akış tipi seçimine göre aşağıdaki dropdown,textbox kontrollerini açıp kapatmak için kullanılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void RdFlowType_SelectedIndexChanged(object sender, EventArgs e)
    {
        txtWorkflowInstanceId.Text = "";
        ChangeVisibilityOfPanels();
    }

    /// <summary>
    /// kaydetme işlemi burada gerçekleştirilir.
    /// </summary>
    public void Kaydet()
    {
        if (rdTalepTuru.SelectedValue == "0")  //İş akış numarası girerek
        {

                if (OrgTreePurchaseUser.UserId != "0" && OrgTreePurchaseUser.UserId != null)
                {
                    MonitoringWorkflowAdminHelper.InsertTable(Convert.ToInt64(txtWorkflowInstanceId.Text), Convert.ToInt64(OrgTreePurchaseUser.UserId.ToString()));
                    ((MasterPage)Master).ShowPopup(false, "Üye Eklendi", "Belirttiğiniz kayıt eklendi", false, "");
                }
                else
                {
                    ((MasterPage)Master).ShowPopup(false, "Üye Eklemede Hata", "Hata", false, "");
                }
           
        }
        if (rdTalepTuru.SelectedValue == "1") //İş akış türü girerek
        {
            
            if (DrpUserName.SelectedValue != "0")
            {
                DataTable dt = MonitoringWorkflowAdminHelper.GetWorkflowInsanceId(Convert.ToInt64(DrpWorkFlow.SelectedValue.ToString()), Convert.ToInt64(DrpUserName.SelectedValue.ToString()));
                if (dt.Rows.Count == 0)
                {
                    ((MasterPage)Master).ShowPopup(false, "Hata", "Kulanıcının hiç bir iş akışı bulunamadı", false, "");
                    return;
                }
                if (dt.Rows.Count != 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        MonitoringWorkflowAdminHelper.InsertTable(ConvertionHelper.ConvertValue<long>(dt.Rows[i]["WF_WORKFLOW_INSTANCE_ID"].ToString()), ConvertionHelper.ConvertValue<long>(OrgTreePurchaseUser.UserId));
                    }
                    ((MasterPage)Master).ShowPopup(false, "Üye Eklendi", "Belirttiğiniz kayıt eklendi", false, "");
                }
            }
            else
            {
                if (OrgTreePurchaseUser.UserId != "0" && OrgTreePurchaseUser.UserId != null && OrgTreePurchaseOwner.UserId != "0" && OrgTreePurchaseOwner.UserId != null)
                {
                    DataTable dt = MonitoringWorkflowAdminHelper.GetWorkflowInsanceId(Convert.ToInt64(DrpWorkFlow.SelectedValue.ToString()), Convert.ToInt64(OrgTreePurchaseOwner.UserId.ToString()));
                    if (dt.Rows.Count == 0)
                    {
                        ((MasterPage)Master).ShowPopup(false, "Hata", "Kulanıcının hiç bir iş akışı bulunamadı", false, "");
                        return;
                    }
                  
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            MonitoringWorkflowAdminHelper.InsertTable(ConvertionHelper.ConvertValue<long>(dt.Rows[i]["WF_WORKFLOW_INSTANCE_ID"].ToString()), ConvertionHelper.ConvertValue<long>(OrgTreePurchaseUser.UserId));
                        }
                        ((MasterPage)Master).ShowPopup(false, "Üye Eklendi", "Belirttiğiniz kayıt eklendi", false, "");
                
                }
                else
                {
                    {
                        ((MasterPage)Master).ShowPopup(false, "Üye Eklemede Hata", "Hata", false, "");
                    }
                }
            }
          
        }
    }

    /// <summary>
    /// Kaydetme butonuna tıklanınca çalışan blok.
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void btnKaydet_Click(object sender, EventArgs e)
    {
        Kaydet();
    }
}