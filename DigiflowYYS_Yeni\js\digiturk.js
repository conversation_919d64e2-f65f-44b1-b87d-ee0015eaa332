﻿function ConfirmSubmit(val) {
    var v = Page_ClientValidate();
    Page_BlockSubmit = false;

    if (v) {
        return confirm(val);
    }
    return v;
}

function ConfirmSubmitWithoutPageValidation(val) {
    return confirm(val);
}

function ShowHideDiv(val) {
    if (document.getElementById(val).style.visibility == 'hidden') {
        document.getElementById(val).style.visibility = 'visible';
    }
    else {
        document.getElementById(val).style.visibility = 'hidden'
    }
}

//Multiline TextBox ların maxlength kontrolü için
function maxLengthControl(field, maxChars) {
    if (field.value.length >= maxChars) {
        event.returnValue = false;
        alert("Maximum karakter sayısı : " + maxChars + " karakterdir");
        field.value = field.value.substring(0, maxChars);
        return false;
    }
}