.help-page h1,
.help-page .h1,
.help-page h2,
.help-page .h2,
.help-page h3,
.help-page .h3,
#body.help-page,
.help-page-table th,
.help-page-table pre,
.help-page-table p {
    font-family: "Segoe UI Light", <PERSON><PERSON><PERSON>, "Frutiger Linotype", "Dejavu Sans", "Helvetica Neue", Arial, sans-serif;
}

.help-page pre.wrapped {
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    white-space: pre-wrap;
}

.help-page .warning-message-container {
    margin-top: 20px;
    padding: 0 10px;
    color: #525252;
    background: #EFDCA9; 
    border: 1px solid #CCCCCC;
}

.help-page-table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
    margin: 0px 0px 20px 0px;
    border-top: 1px solid #D4D4D4;
}

.help-page-table th {
    text-align: left;
    font-weight: bold;
    border-bottom: 1px solid #D4D4D4;
    padding: 5px 6px 5px 6px;
}

.help-page-table td {
    border-bottom: 1px solid #D4D4D4;
    padding: 10px 8px 10px 8px;
    vertical-align: top;
}

.help-page-table pre,
.help-page-table p {
    margin: 0px;
    padding: 0px;
    font-family: inherit;
    font-size: 100%;
}

.help-page-table tbody tr:hover td {
    background-color: #F3F3F3;
}

.help-page a:hover {
    background-color: transparent;
}

.help-page .sample-header {
    border: 2px solid #D4D4D4;
    background: #00497E;
    color: #FFFFFF;
    padding: 8px 15px;
    border-bottom: none;
    display: inline-block;
    margin: 10px 0px 0px 0px;
}

.help-page .sample-content {
    display: block;
    border-width: 0;
    padding: 15px 20px;
    background: #FFFFFF;
    border: 2px solid #D4D4D4;
    margin: 0px 0px 10px 0px;
}

.help-page .api-name {
    width: 40%;
}

.help-page .api-documentation {
    width: 60%;
}

.help-page .parameter-name {
    width: 20%;
}

.help-page .parameter-documentation {
    width: 40%;
}

.help-page .parameter-type {
    width: 20%;
}

.help-page .parameter-annotations {
    width: 20%;
}

.help-page h1,
.help-page .h1 {
    font-size: 36px;
    line-height: normal;
}

.help-page h2,
.help-page .h2 {
    font-size: 24px;
}

.help-page h3,
.help-page .h3 {
    font-size: 20px;
}

#body.help-page {
    font-size: 14px;
    line-height: 143%;
    color: #333;
}

.help-page a {
    color: #0000EE;
    text-decoration: none;
}
