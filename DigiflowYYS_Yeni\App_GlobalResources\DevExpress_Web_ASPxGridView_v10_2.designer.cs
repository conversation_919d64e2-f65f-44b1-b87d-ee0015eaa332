//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "14.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class DevExpress_Web_ASPxGridView_v10_2 {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal DevExpress_Web_ASPxGridView_v10_2() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.DevExpress_Web_ASPxGridView_v10_2", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [Collapse].
        /// </summary>
        internal static string ASPxGridViewStringId_Alt_Collapse {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Alt_Collapse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gizle.
        /// </summary>
        internal static string ASPxGridViewStringId_Alt_DragAndDropHideColumnIcon {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Alt_DragAndDropHideColumnIcon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [Expand].
        /// </summary>
        internal static string ASPxGridViewStringId_Alt_Expand {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Alt_Expand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [Condition].
        /// </summary>
        internal static string ASPxGridViewStringId_Alt_FilterRowButton {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Alt_FilterRowButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtrele.
        /// </summary>
        internal static string ASPxGridViewStringId_Alt_HeaderFilterButton {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Alt_HeaderFilterButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtrelendir.
        /// </summary>
        internal static string ASPxGridViewStringId_Alt_HeaderFilterButtonActive {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Alt_HeaderFilterButtonActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Küçükten Büyüğe.
        /// </summary>
        internal static string ASPxGridViewStringId_Alt_SortedAscending {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Alt_SortedAscending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Büyükten küçüge.
        /// </summary>
        internal static string ASPxGridViewStringId_Alt_SortedDescending {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Alt_SortedDescending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İle başlar.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterBeginsWith {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterBeginsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to içerir.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterContains {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterContains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to içermez.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterDoesNotContain {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterDoesNotContain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ile biter.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterEndsWith {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterEndsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eşittir.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterEquals {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterEquals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Büyüktür.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterGreater {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterGreater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Büyük Eşit.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterGreaterOrEqual {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterGreaterOrEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Küçüktür.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterLess {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterLess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Küçük eşit.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterLessOrEqual {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterLessOrEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eşit değil.
        /// </summary>
        internal static string ASPxGridViewStringId_AutoFilterNotEqual {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.AutoFilterNotEqual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İptal.
        /// </summary>
        internal static string ASPxGridViewStringId_CommandCancel {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.CommandCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Temizle.
        /// </summary>
        internal static string ASPxGridViewStringId_CommandClearFilter {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.CommandClearFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sil.
        /// </summary>
        internal static string ASPxGridViewStringId_CommandDelete {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.CommandDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzenle.
        /// </summary>
        internal static string ASPxGridViewStringId_CommandEdit {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.CommandEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yeni.
        /// </summary>
        internal static string ASPxGridViewStringId_CommandNew {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.CommandNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seç.
        /// </summary>
        internal static string ASPxGridViewStringId_CommandSelect {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.CommandSelect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Güncelle.
        /// </summary>
        internal static string ASPxGridViewStringId_CommandUpdate {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.CommandUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Silmek istediğinizden Emin misiniz?.
        /// </summary>
        internal static string ASPxGridViewStringId_ConfirmDelete {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.ConfirmDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Field Seçimi.
        /// </summary>
        internal static string ASPxGridViewStringId_CustomizationWindowCaption {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.CustomizationWindowCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Herhangi bir içerik bulunmamaktadır.
        /// </summary>
        internal static string ASPxGridViewStringId_EmptyDataRow {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.EmptyDataRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buraya sürükleyebilirsiniz.
        /// </summary>
        internal static string ASPxGridViewStringId_EmptyHeaders {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.EmptyHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Continued on the next page).
        /// </summary>
        internal static string ASPxGridViewStringId_GroupContinuedOnNextPage {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.GroupContinuedOnNextPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drag a column header here to group by that column.
        /// </summary>
        internal static string ASPxGridViewStringId_GroupPanel {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.GroupPanel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tümünü göster.
        /// </summary>
        internal static string ASPxGridViewStringId_HeaderFilterShowAllItem {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.HeaderFilterShowAllItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boş.
        /// </summary>
        internal static string ASPxGridViewStringId_HeaderFilterShowBlanksItem {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.HeaderFilterShowBlanksItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boş değil.
        /// </summary>
        internal static string ASPxGridViewStringId_HeaderFilterShowNonBlanksItem {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.HeaderFilterShowNonBlanksItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beyond Next Month.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_BeyondNextMonth {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_BeyondNextMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Earlier this Month.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_EarlierThisMonth {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_EarlierThisMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Month.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_LastMonth {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_LastMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Week.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_LastWeek {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_LastWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Later this Month.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_LaterThisMonth {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_LaterThisMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next Month.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_NextMonth {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_NextMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next Week.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_NextWeek {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_NextWeek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Older.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_Older {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_Older", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Three Weeks Ago.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_ThreeWeeksAgo {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_ThreeWeeksAgo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Three Weeks Away.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_ThreeWeeksAway {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_ThreeWeeksAway", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bugün.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_Today {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_Today", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yarın.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_Tomorrow {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_Tomorrow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İki Hafta Sonra.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_TwoWeeksAgo {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_TwoWeeksAgo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Two Weeks Away.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_TwoWeeksAway {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_TwoWeeksAway", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yesterday.
        /// </summary>
        internal static string ASPxGridViewStringId_Outlook_Yesterday {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.Outlook_Yesterday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Form.
        /// </summary>
        internal static string ASPxGridViewStringId_PopupEditFormCaption {
            get {
                return ResourceManager.GetString("ASPxGridViewStringId.PopupEditFormCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tümü.
        /// </summary>
        internal static string ASPxperienceStringId_Pager_All {
            get {
                return ResourceManager.GetString("ASPxperienceStringId.Pager_All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İlk.
        /// </summary>
        internal static string ASPxperienceStringId_Pager_First {
            get {
                return ResourceManager.GetString("ASPxperienceStringId.Pager_First", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Son.
        /// </summary>
        internal static string ASPxperienceStringId_Pager_Last {
            get {
                return ResourceManager.GetString("ASPxperienceStringId.Pager_Last", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sonraki.
        /// </summary>
        internal static string ASPxperienceStringId_Pager_Next {
            get {
                return ResourceManager.GetString("ASPxperienceStringId.Pager_Next", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Önceki.
        /// </summary>
        internal static string ASPxperienceStringId_Pager_Prev {
            get {
                return ResourceManager.GetString("ASPxperienceStringId.Pager_Prev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfalar {0} - {1} ({2} Sayfa).
        /// </summary>
        internal static string ASPxperienceStringId_Pager_SummaryAllPagesFormat {
            get {
                return ResourceManager.GetString("ASPxperienceStringId.Pager_SummaryAllPagesFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sayfalar {0} von {1} ({2} Sayfa).
        /// </summary>
        internal static string ASPxperienceStringId_Pager_SummaryFormat {
            get {
                return ResourceManager.GetString("ASPxperienceStringId.Pager_SummaryFormat", resourceCulture);
            }
        }
    }
}
