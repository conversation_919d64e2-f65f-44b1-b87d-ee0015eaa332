﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site1.Master" AutoEventWireup="true" CodeBehind="AddUpdateWorkflowRule.aspx.cs" Inherits="DigiflowYYS_Yeni.AddUpdateWorkflowRule" %>
<%@ MasterType VirtualPath="~/Site1.Master" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>


<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <%--<script language="JavaScript">

      function confirmSubmit() {
      var agree=confirm("Emin misiniz ?");

      if (agree)

       return true ;

      else

       return false ;

    }
    </script>--%>


    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="js/select2.min.js"></script>
    <link href="css/select2.min.css" rel="stylesheet" />
    <script type="text/javascript">    
        function pageLoad() {
            debugger
            $(".workflowsDrop").select2();
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td align="left" valign="middle" width="100%" style="padding: 5px">
                <strong>Akış Listesi</strong>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle" width="100%" style="padding: 5px">
                
                <%--<dx:ASPxComboBox ID="WorkfowListASPxComboBox" runat="server" OnSelectedIndexChanged="WorkfowListASPxComboBox_SelectedIndexChanged"
                    Width="250px" AutoPostBack="True" ValueType="System.Int64">
                </dx:ASPxComboBox>--%>

                <asp:DropDownList ID="WorkfowListASPxComboBox1" runat="server" OnSelectedIndexChanged="WorkfowListASPxComboBox1_SelectedIndexChanged"
                    Width="250px" AutoPostBack="True" CssClass="workflowsDrop" ValueType="System.Int64">                                        
                </asp:DropDownList>
            </td>
        </tr>
    </table>
    <hr />
    <br />
    <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td align="center" style="border-left: 1px solid #F5F5F5; border-right: 1px solid #F5F5F5; border-top: 1px solid #F5F5F5; border-bottom: 1px solid #C0C0C0; padding: 5px"
                width="18%">
                <b>Adım</b>
            </td>
            <td align="center" style="border-left: 1px solid #F5F5F5; border-right: 1px solid #F5F5F5; border-top: 1px solid #F5F5F5; border-bottom: 1px solid #C0C0C0; padding: 5px"
                width="18%">
                <b>Kim</b>
            </td>
            <td align="center" style="border-left: 1px solid #F5F5F5; border-right: 1px solid #F5F5F5; border-top: 1px solid #F5F5F5; border-bottom: 1px solid #C0C0C0; padding: 5px"
                width="18%">
                <b>İşlem</b>
            </td>
            <td align="center" style="border-left: 1px solid #F5F5F5; border-right: 1px solid #F5F5F5; border-top: 1px solid #F5F5F5; border-bottom: 1px solid #C0C0C0; padding: 5px"
                width="18%">
                <b>Hedef</b>
            </td>
            <td align="center" style="border-left: 1px solid #F5F5F5; border-right: 1px solid #F5F5F5; border-top: 1px solid #F5F5F5; border-bottom: 1px solid #C0C0C0; padding: 5px"
                width="5%">
                <b>Aktif</b>
            </td>
        </tr>
        <tr>
            <td align="center" style="border: 1px solid #F5F5F5; padding: 5px" width="18%">
                <dx:ASPxComboBox ID="StateDropDownList" runat="server" Width="98%" Enabled="False"
                    OnSelectedIndexChanged="StateDropDownList_SelectedIndexChanged" AutoPostBack="True">
                </dx:ASPxComboBox>
            </td>
            <td align="center" style="border: 1px solid #F5F5F5; padding: 5px" width="18%">
                <dx:ASPxComboBox ID="FromDropDownList" runat="server" Width="98%" Enabled="False"
                    FilterMinLength="1">
                </dx:ASPxComboBox>
            </td>
            <td align="center" style="border: 1px solid #F5F5F5; padding: 5px" width="18%">
                <dx:ASPxComboBox ID="ActionTypeDropDownList" runat="server" Width="98%" Enabled="False"
                    ValueType="System.String" OnSelectedIndexChanged="ActionTypeDropDownList_SelectedIndexChanged"
                    AutoPostBack="True">
                </dx:ASPxComboBox>
            </td>
            <td align="center" style="border: 1px solid #F5F5F5; padding: 5px" width="18%">
                <dx:ASPxComboBox ID="TargetDropDownList" runat="server" Width="98%" Enabled="False"
                    FilterMinLength="1">
                </dx:ASPxComboBox>
            </td>
            <td align="center" style="border: 1px solid #F5F5F5; padding: 5px" width="5%">
                <asp:CheckBox ID="IsActiveCheckBox" runat="server" Text=" " Checked="True" />
            </td>
        </tr>
    </table>
    <hr color="#ebebeb" size="1" />
    <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td align="center" width="14%" style="width: 100%">
                <table>
                    <tr>
                        <td>
                            <dx:aspxbutton ID="SaveButton" runat="server" Text="Kuralı Kaydet" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                                CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                                OnClick="SaveButton_Click" Width="150px" />
                        </td>
                        <td>
                            <dx:aspxbutton ID="DeleteButton" runat="server" Text="Kuralı Sil" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                                CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                                OnClick="DeleteButton_Click" Visible="False" Width="100px">
                                <ClientSideEvents Click="function(s, e) {
	 e.processOnServer = confirm('Kuralı silmek istediğinize emin misiniz?');}" />
                            </dx:aspxbutton>
                        </td>
                        <td>
                            <dx:aspxbutton ID="ClearButton" runat="server" Text="Temizle" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                                CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                                OnClick="Clear_Click" Width="150px" />
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <hr size="1" />
    <center>
        <b>Mevcut Kural Listesi</b></center>
    <br />
    <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
            <td>
                <dx:ASPxGridView ID="WorkFlowRulesGridView" runat="server" AutoGenerateColumns="False"
                    Width="100%" KeyFieldName="Id"
                    OnPageIndexChanged="WorkFlowRulesGridViewPageIndexChanging"
                    OnBeforeColumnSortingGrouping="WorkFlowRulesGridViewPageIndexChanging"
                    OnCustomColumnGroup="WorkFlowRulesGridViewPageIndexChanging"
                    OnCustomGroupDisplayText="WorkFlowRulesGridViewPageIndexChanging"
                    OnFilterControlCustomValueDisplayText="WorkFlowRulesGridViewPageIndexChanging"
                    OnFilterControlParseValue="WorkFlowRulesGridViewPageIndexChanging"
                    OnFilterControlOperationVisibility="WorkFlowRulesGridViewPageIndexChanging"
                    OnRowDeleting="WorkFlowRulesGridView_RowDeleting"
                    OnRowCommand="WorkFlowRulesGridView_RowCommand">
                    <Columns>
                        <dx:gridviewdatatextcolumn Caption="Adım" VisibleIndex="1" FieldName="State">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Kim" VisibleIndex="1" FieldName="Source">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="İşlem" VisibleIndex="2" FieldName="Action">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Hedef" VisibleIndex="5" FieldName="ToGroup">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Aktif" VisibleIndex="6" FieldName="Active">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <%-- <dx:GridViewDataHyperLinkColumn Caption="Düzenle" FieldName="Id" VisibleIndex="7">
                            <PropertiesHyperLinkEdit NavigateUrlFormatString="AddUpdateWorkflowRule.aspx?RuleId={0}"
                                TextFormatString="Düzenle" Text="Düzenle">
                            </PropertiesHyperLinkEdit>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataHyperLinkColumn>--%>
                        <%--   <dx:GridViewCommandColumn Caption="Sil" VisibleIndex="8">
                            <DeleteButton Text="Sil" Visible="True">
                            </DeleteButton>
                            <ClearFilterButton Visible="True">
                            </ClearFilterButton>
                              <CellStyle HorizontalAlign="Center">
                              </CellStyle>
                        </dx:GridViewCommandColumn>--%>
                        <dx:gridviewdatatextcolumn FieldName="Id" VisibleIndex="7" Caption="Düzenle">
                            <DataItemTemplate>
                                <asp:LinkButton ID="lbEdit" runat="server" CommandName="Id" Text="Düzenle"></asp:LinkButton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:GridViewDataButtonEditColumn  Caption="Sil" VisibleIndex="8">
                            <DataItemTemplate>
                                <dx:aspxbutton ID="Aspxbutton1" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteRuleButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                    <ClientSideEvents Click="function(s, e) {e.processOnServer = confirm('Kuralı silmek istediğinize emin misiniz?');}" />
                                </dx:aspxbutton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn >
                        <dx:gridviewdatatextcolumn Caption="Sıra No" VisibleIndex="0" Width="40px">
                            <DataItemTemplate>
                                <%# Container.ItemIndex + 1 %>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterBar="Auto" ShowFilterRowMenu="True" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Kuralı silmek istediğinize emin misiniz?" />
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
            </td>
        </tr>
    </table>
</asp:Content>