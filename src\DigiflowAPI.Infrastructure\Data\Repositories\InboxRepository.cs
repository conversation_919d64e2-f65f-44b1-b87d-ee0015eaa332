﻿using DigiflowAPI.Application.DTOs.Inbox;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Domain.Entities;
using Microsoft.AspNetCore.Http;
using AutoMapper;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class InboxRepository(IOracleDataAccessRepositoryFactory repositoryFactory, IHttpContextAccessor httpContextAccessor, IMapper mapper) : IInboxRepository
    {
        public async Task<(IEnumerable<Inbox> inbox, IEnumerable<Inbox> delegated, IEnumerable<Inbox> commented)> GetAllAsync(long userId)
        {
            var loginParam = new Dictionary<string, object>
            {
                {"loginValue", userId },
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var inboxDtos = await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetInboxQuery, loginParam);
            var delegeDtos = await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetDelegeInboxSQL, loginParam);
            var commentDtos = await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetCommentInboxSQL, loginParam);

            // Map DTOs to domain entities
            var inbox = mapper.Map<IEnumerable<Inbox>>(inboxDtos);
            var delegated = mapper.Map<IEnumerable<Inbox>>(delegeDtos);
            var commented = mapper.Map<IEnumerable<Inbox>>(commentDtos);

            return (inbox, delegated, commented);
        }

        public async Task<IEnumerable<Inbox?>> GetAllInboxAsync(long userId)
        {
            var parameters = new Dictionary<string, object>
            {
                {"LoginId", userId },
                {"AssignmentTypeCd", "TASKINBOX"},
                {"language", httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en"},
                {"ordered", "true"}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var inboxDtos = await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetInboxQuery, parameters);
            return mapper.Map<IEnumerable<Inbox?>>(inboxDtos);
        }

        public async Task<IEnumerable<Inbox?>> GetAllDelegatedAsync(long userId)
        {
            var parameters = new Dictionary<string, object>
            {
                {"LoginId", userId },
                {"AssignmentTypeCd", "TASKINBOX"},
                {"language", httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en"},
                {"ordered", "true"}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var delegatedDtos = await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetDelegeInboxSQL, parameters);
            return mapper.Map<IEnumerable<Inbox?>>(delegatedDtos);
        }

        public async Task<IEnumerable<Inbox?>> GetAllCommentedAsync(long userId)
        {
            var parameters = new Dictionary<string, object>
            {
                {"LoginId", userId },
                {"AssignmentTypeCd", "TASKINBOX"},
                {"language", httpContextAccessor.HttpContext.Items["UserLanguage"]?.ToString() ?? "en"},
                {"ordered", "true"}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            var commentedDtos = await repository.ExecuteQueryAsync<InboxDto>(SqlScripts.Inbox.InboxSqlQueries.GetCommentInboxSQL, parameters);
            return mapper.Map<IEnumerable<Inbox?>>(commentedDtos);
        }
    }
}