﻿using DevExpress.Web.ASPxEditors;
using DevExpress.Web.ASPxGridView;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Web.UI;

public partial class AddUpdateStateAuthorization : YYSSecurePage
{
    /// <summary>
    /// Statik atama kurallarının tanımlandığı ekran
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        ((MasterPage)Master).ShowMenu(true);
        ((MasterPage)Master).PageTitle = "Adım bazlı akış kuralları düzenleme ekranı";

        if (!Page.IsPostBack)
        {
            #region İş Akış kuralı tanımlaması için gerekli olan tüm comboboxlar doldurulur

            long IsAdmin = 0;

            if (Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAndFlowAdmin.ToString() || Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAdmin.ToString())
            {
                IsAdmin = 1;
            }

            WorkfowListASPxComboBox = Common.FillComboboxWorkFlow("Name", "WorkflowDefId", WorkfowListASPxComboBox, WorkflowHelper.GetWorkflowsOfAdmin(UserInformation.LoginObject.LoginId, IsAdmin), "İş Akışı Seçiniz", "0");

            if (Request.QueryString["RuleId"] != null)
            {
                #region Sayfaya Update İşlemi için gelinmiş ise kontroller doldurulur

                DeleteButton.Visible = true;
                DeleteButton.Enabled = true;

                string wfId = Request.QueryString["RuleId"];
                Session["UpdatingWorkFlowRule"] = wfId;
                StateAuthorization wfRule = StateAuthorizationHelper.GetByWorkflowRuleID(ConvertionHelper.ConvertValue<long>(wfId));
                // AssignedUserComboBox.Value =Convert.ToInt64( wfRule.AssignmentOwnerId);
                for (int i = 0; i < WorkfowListASPxComboBox.Items.Count; i++)
                {
                    if (WorkfowListASPxComboBox.Items[i].Value.ToString() == wfRule.WfDefId.ToString())
                    {
                        WorkfowListASPxComboBox.Items[i].Selected = true;
                        break;
                    }
                }
                WorkfowListASPxComboBox_SelectedIndexChanged(null, null);
                IsActiveCheckBox.Checked = ConvertionHelper.ConvertValue<Boolean>(wfRule.IsActive);
                // select state
                for (int i = 0; i < StateDropDownList.Items.Count; i++)
                {
                    if (StateDropDownList.Items[i].Value.ToString() == wfRule.StateDefId.ToString())
                    {
                        StateDropDownList.Items[i].Selected = true;
                        break;
                    }
                }

                for (int i = 0; i < AssignedUserComboBox.Items.Count; i++)
                {
                    if (AssignedUserComboBox.Items[i].Value.ToString() == wfRule.AssignmentOwnerId.ToString())
                    {
                        AssignedUserComboBox.Items[i].Selected = true;
                        break;
                    }
                }

                foreach (ListEditItem item in WorkfowListASPxComboBox.Items)
                {
                    if (item.Value.ToString() == wfId)
                    {
                        item.Selected = true;
                        IsActiveCheckBox.Enabled = true;
                        SaveButton.Enabled = true;
                        DeleteButton.Visible = true;
                        DeleteButton.Enabled = true;

                        break;
                    }
                }

                #endregion Sayfaya Update İşlemi için gelinmiş ise kontroller doldurulur
            }

            #endregion İş Akış kuralı tanımlaması için gerekli olan tüm comboboxlar doldurulur
        }
        if (Page.IsCallback)
        {
            if (Session["WfDefId"] != null && Session["WorkFlowRulesGridView"] != null)
            {
                WorkFlowRulesGridView.DataSource = (DataTable)Session["WorkFlowRulesGridView"];
                WorkFlowRulesGridView.DataBind();
            }
            else
            {
                List<StateAuthorization> items = StateAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
                DataTable dtRules = ConvertToDataTable(items);
                WorkFlowRulesGridView.DataSource = dtRules;
                Session["WorkFlowRulesGridView"] = dtRules;
                WorkFlowRulesGridView.DataBind();
            }
        }
    }

    /// <summary>
    /// İş akışına göre state leri, kullanıcıları doldurur
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkfowListASPxComboBox_SelectedIndexChanged(object sender, EventArgs e)
    {
        #region Seçilen iş akışına göre state ler ve kullanıcılar doldurulur

        if (WorkfowListASPxComboBox.SelectedItem == null)
        {
            return;
        }

        bool enable = WorkfowListASPxComboBox.SelectedItem != null;
        StateDropDownList.Enabled = enable;
        IsActiveCheckBox.Enabled = enable;
        SaveButton.Enabled = enable;

        if (enable)
        {
            long WfDefId = ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value);
            List<LogicalGroup> lgList = LogicalGroupHelper.GetAllByWorkflowId(WfDefId);
            AssignedUserComboBox = Common.FillComboboxLogicalGroup("Name", "RequestId", AssignedUserComboBox, lgList, "Mantıksal Grup Seçiniz", "0");
            StateDropDownList = Common.FillComboboxWorkFlowState("Name", "WorkflowStateId", StateDropDownList, WorkflowHelper.GetStates(WfDefId), "Adım Seçiniz", "0");
        }

        #endregion Seçilen iş akışına göre state ler ve kullanıcılar doldurulur

        #region O akışın tüm kuralları doldurulur

        //List<StateAuthorization> items = StateAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
        //WorkFlowRulesGridView.DataSource = ConvertToDataTable(items);
        //WorkFlowRulesGridView.DataBind();
        FillGrid();

        #endregion O akışın tüm kuralları doldurulur
    }

    /// <summary>
    /// İş kuralı kaydetmeyi sağlar
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void SaveButton_Click(object sender, EventArgs e)
    {
        try
        {
            if (WorkfowListASPxComboBox.SelectedIndex == 0)
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Kural kaydetmek için bir akış seçmeniz gerekli.", true, string.Empty);
                return;
            }
            if (StateDropDownList.SelectedIndex == 0)
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Akış aşaması alanı boş bırakılamaz.", true, string.Empty);
                return;
            }
            if (AssignedUserComboBox.SelectedIndex == 0)
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Mantıksal grup seçiniz..", true, string.Empty);
                return;
            }
            StateAuthorization rule;
            if (Session["UpdatingWorkFlowRule"] != null)
            {
                long ruleId = ConvertionHelper.ConvertValue<long>(Session["UpdatingWorkFlowRule"]);
                rule = StateAuthorizationHelper.GetByWorkflowRuleID(ruleId);
                if (rule == null)
                {
                    rule = new StateAuthorization();
                }
            }
            else
            {
                rule = new StateAuthorization();
            }
            rule.Created = DateTime.Now;
            rule.CreatedBy = UserInformation.LoginObject.LoginId;
            rule.LastUpdated = DateTime.Now;
            rule.LastUpdatedBy = UserInformation.LoginObject.LoginId;
            rule.WfDefId = ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value);
            rule.AssignmentOwnerId = ConvertionHelper.ConvertValue<long>(AssignedUserComboBox.SelectedItem.Value);
            rule.StateDefId = ConvertionHelper.ConvertValue<long>(StateDropDownList.SelectedItem.Value);
            rule.IsActive = ConvertionHelper.ConvertValue<long>(IsActiveCheckBox.Checked);
            rule.AssignmentTypeId = 0;
            rule.ExecutionClassName = "";

            if (Session["UpdatingWorkFlowRule"] == null)
            {
                #region Aynı kural tanımlanmış mı kontrolü yapılır

                StateAuthorization ac = StateAuthorizationHelper.IsDefined(rule);
                if (ac == null)
                {
                    StateAuthorizationHelper.AddNewWorkflowRule(rule);
                    ((MasterPage)Master).ShowPopup(false, "Kural Tanımlandı", "Kural başarıyla oluşturuldu.", false, string.Empty);
                }
                else
                {
                    //// rule.RequestId = ac.RequestId;
                    StateAuthorizationHelper.Update(rule);
                    ((MasterPage)Master).ShowPopup(false, "Kural Tanımlanamadı", "Kural tanımlı olduğu için tanımlanamadı!", true, string.Empty);
                }
                // ((MasterPage)Master).ShowPopup(false, "Kural Tanımlandı", "Kural başarıyla oluşturuldu.", false, string.Empty);

                #endregion Aynı kural tanımlanmış mı kontrolü yapılır
            }
            else
            {
                #region Update İşlemi

                rule.RequestId = ConvertionHelper.ConvertValue<long>(Session["UpdatingWorkFlowRule"]);
                StateAuthorizationHelper.Update(rule);
                ((MasterPage)Master).ShowPopup(false, "Kural Güncellendi", "Kural başarıyla güncellendi.", false, string.Empty);
                Session["UpdatingWorkFlowRule"] = null;

                #endregion Update İşlemi
            }
            //Grid tekrar doldurulur
            FillGrid();
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowPopup(false, "Hata", "İşlem sırasında bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.Message);
        }
    }

    /// <summary>
    /// Ekrandaki iş kuralını silmemizi sağlar
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DeleteButton_Click(object sender, EventArgs e)
    {
        #region Ekrandaki kural silinir

        try
        {
            if (Session["UpdatingWorkFlowRule"] != null)
            {
                long ruleId = ConvertionHelper.ConvertValue<long>(Session["UpdatingWorkFlowRule"]);
                StateAuthorizationHelper.DeleteWorkflowRule(ruleId);
                ((MasterPage)Master).ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
                DeleteButton.Visible = false;
                //    SaveButton.Enabled = false;
                Session["UpdatingWorkFlowRule"] = null;
                FillGrid();
                Clear();
            }
            else
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Bu kuralı tanımlı olup olmadığı belirlenemediğinden silinemedi, Listeden silmeyi deneyiniz", true, "");
            }
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowPopup(false, "Hata", "İşlem sırasında bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.Message);
        }

        #endregion Ekrandaki kural silinir
    }

    #region Inner Functions

    /// <summary>
    /// Grid teki int değerlerin isim karşılıklarını bulup değiştirir
    /// </summary>
    /// <param name="items">State authorization nesneleri</param>
    /// <returns></returns>
    private DataTable ConvertToDataTable(List<StateAuthorization> items)
    {
        DataTable dt = new DataTable();
        dt.Columns.Add("RequestId");
        dt.Columns.Add("State");
        dt.Columns.Add("Active");
        dt.Columns.Add("AssignmentTypeId");
        dt.Columns.Add("AssignmentOwnerId");
        dt.AcceptChanges();

        foreach (StateAuthorization item in items)
        {
            DataRow r = dt.NewRow();
            r["RequestId"] = item.RequestId;
            r["State"] = WorkflowHelper.GetState(item.StateDefId).Name;

            r["Active"] = ConvertIsActive(ConvertionHelper.ConvertValue<Boolean>(item.IsActive));
            r["AssignmentTypeId"] = ConvertAssignmentType(item.AssignmentTypeId);
            r["AssignmentOwnerId"] = ConvertUser(item.AssignmentOwnerId);
            dt.Rows.Add(r);
        }

        dt.AcceptChanges();
        return dt;
    }

    /// <summary>
    /// AssignmentType dönüşümü yapılır
    /// </summary>
    /// <param name="AssignmentTypeId"></param>
    /// <returns></returns>
    private string ConvertAssignmentType(long AssignmentTypeId)
    {
        return AssignmentTypeId.ToString();
    }

    /// <summary>
    /// Grid teki Int olan isActive değerini Aktif veya Pasif olarak değiştirir
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private string ConvertIsActive(bool val)
    {
        return val ? "Evet" : "Hayır";
    }

    /// <summary>
    /// Grid teki loginId değerinin karşılığı olan isme çevirir
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private string ConvertUser(long val)
    {
        string ret = string.Empty;

        var lg = LogicalGroupHelper.Get(val);
        ret = lg.Name;

        return ret;
    }

    /// <summary>
    /// İş Akışına göre grid doldurulur
    /// </summary>
    private void FillGrid()
    {
        #region O akışın tüm kuralları doldurulur

        List<StateAuthorization> items = StateAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
        DataTable dtRules = ConvertToDataTable(items);
        WorkFlowRulesGridView.DataSource = dtRules;
        Session["WorkFlowRulesGridView"] = dtRules;
        WorkFlowRulesGridView.DataBind();

        #endregion O akışın tüm kuralları doldurulur
    }

    /// <summary>
    /// Combobox lardaki değerleri ilk değerine getiren fonksiyonu çağırır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Clear_Click(object sender, EventArgs e)
    {
        Clear();
    }

    /// <summary>
    /// Combobox lardaki değerleri ilk değerine getirir
    /// </summary>
    private void Clear()
    {
        StateDropDownList.SelectedIndex = 0;
        AssignedUserComboBox.SelectedIndex = 0;
    }

    #endregion Inner Functions

    /// <summary>
    /// Sil butonuna basıldığı zaman yapılacak işlemleri barındırır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowRulesGridView_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
    {
        //#region Admin in akış yöneticisi olduğu akışı Silme İşlemi
        //long ruleId = ConvertionHelper.ConvertValue<long>(e.Values[3]);
        //ASPxGridView gridView = (ASPxGridView)sender;

        //StateAuthorizationHelper.DeleteWorkflowRule(ruleId);
        //((MasterPage)Master).ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
        ////DeleteButton.Enabled = false;
        ////    SaveButton.Enabled = false;
        //FillGrid();

        //gridView.CancelEdit();
        //e.Cancel = true;
        //#endregion
    }

    /// <summary>
    /// Düzenle butonuna basıldığı zaman üstteki combobox lar o row daki bilgileri doldurur
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowRulesGridView_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
    {
        string ruleId = e.KeyValue.ToString();
        Session["DeletingWorkFlowRule"] = ruleId;
        Session["UpdatingWorkFlowRule"] = ruleId;

        #region Sayfaya Update İşlemi için gelinmiş ise kontroller doldurulur

        DeleteButton.Visible = true;
        DeleteButton.Enabled = true;

        string wfId = ruleId;

        StateAuthorization wfRule = StateAuthorizationHelper.GetByWorkflowRuleID(ConvertionHelper.ConvertValue<long>(wfId));
        // AssignedUserComboBox.Value =Convert.ToInt64( wfRule.AssignmentOwnerId);
        for (int i = 0; i < WorkfowListASPxComboBox.Items.Count; i++)
        {
            if (WorkfowListASPxComboBox.Items[i].Value.ToString() == wfRule.WfDefId.ToString())
            {
                WorkfowListASPxComboBox.Items[i].Selected = true;
                break;
            }
        }
        WorkfowListASPxComboBox_SelectedIndexChanged(null, null);
        IsActiveCheckBox.Checked = ConvertionHelper.ConvertValue<Boolean>(wfRule.IsActive);
        // select state
        for (int i = 0; i < StateDropDownList.Items.Count; i++)
        {
            if (StateDropDownList.Items[i].Value.ToString() == wfRule.StateDefId.ToString())
            {
                StateDropDownList.Items[i].Selected = true;
                break;
            }
        }

        for (int i = 0; i < AssignedUserComboBox.Items.Count; i++)
        {
            if (AssignedUserComboBox.Items[i].Value.ToString() == wfRule.AssignmentOwnerId.ToString())
            {
                AssignedUserComboBox.Items[i].Selected = true;
                break;
            }
        }

        foreach (ListEditItem item in WorkfowListASPxComboBox.Items)
        {
            if (item.Value.ToString() == wfId)
            {
                item.Selected = true;
                IsActiveCheckBox.Enabled = true;
                SaveButton.Enabled = true;
                DeleteButton.Visible = true;
                DeleteButton.Enabled = true;

                break;
            }
        }

        #endregion Sayfaya Update İşlemi için gelinmiş ise kontroller doldurulur
    }

    /// <summary>
    /// Silme işlemi burada yapılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DeleteRuleButton_Click(object sender, EventArgs e)
    {
        #region Admin in akış yöneticisi olduğu akışı Silme İşlemi

        long ruleId = ConvertionHelper.ConvertValue<long>(Session["DeletingWorkFlowRule"]);
        StateAuthorizationHelper.DeleteWorkflowRule(ruleId);
        ((MasterPage)Master).ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
        FillGrid();
        Session["UpdatingWorkFlowRule"] = null;
        DeleteButton.Visible = false;
        Clear();

        #endregion Admin in akış yöneticisi olduğu akışı Silme İşlemi
    }

    /// <summary>
    /// Sayfalamada datayı daha hızlı getirmek için kullanılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowRulesGridViewPageIndexChanging(object sender, EventArgs e)
    {
        DataTable dtInboxGridView = new DataTable();
        if (Session["WorkFlowRulesGridView"] != null)
        {
            dtInboxGridView = (DataTable)Session["WorkFlowRulesGridView"];
        }
        else
        {
            List<StateAuthorization> items = StateAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
            dtInboxGridView = ConvertToDataTable(items);
            Session["WorkFlowRulesGridView"] = dtInboxGridView;
        }
        WorkFlowRulesGridView.DataSource = dtInboxGridView;
        WorkFlowRulesGridView.DataBind();
        dtInboxGridView.Dispose();
        dtInboxGridView = null;
    }
}