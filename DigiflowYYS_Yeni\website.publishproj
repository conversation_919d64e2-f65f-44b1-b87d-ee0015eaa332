﻿<?xml version="1.0" encoding="utf-8"?>
<!--

***********************************************************************************************
website.publishproj

WARNING: DO NOT MODIFY this file, it is used for the web publish process.

Copyright (C) Microsoft Corporation. All rights reserved.

***********************************************************************************************
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.30319</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{6c66704c-6e88-4d0a-9fa2-d0fc15446d5c}</ProjectGuid>
    <SourceWebPhysicalPath>$(MSBuildThisFileDirectory)</SourceWebPhysicalPath>
    <SourceWebVirtualPath>/DigiFlowYYS</SourceWebVirtualPath>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <SourceWebProject>
    </SourceWebProject>
    <SourceWebMetabasePath>
    </SourceWebMetabasePath>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <!-- for VS2010 we need to use 10.5 but for VS2012+ we should use VisualStudioVersion -->
    <WebPublishTargetsVersion Condition=" '$(WebPublishTargetsVersion)' =='' and '$(VisualStudioVersion)' == 10.0 ">10.5</WebPublishTargetsVersion>
    <WebPublishTargetsVersion Condition=" '$(WebPublishTargetsVersion)'=='' ">$(VisualStudioVersion)</WebPublishTargetsVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(WebPublishTargetsVersion)</VSToolsPath>
    <_WebPublishTargetsPath Condition=" '$(_WebPublishTargetsPath)'=='' ">$(VSToolsPath)</_WebPublishTargetsPath>
    <AssemblyFileVersion Condition="'$(AssemblyFileVersion)' == ''">1.0.0.0</AssemblyFileVersion>
    <AssemblyVersion Condition="'$(AssemblyVersion)' == ''">1.0.0.0</AssemblyVersion>
  </PropertyGroup>
  <ItemGroup>
    <AssemblyAttributes Include="AssemblyFileVersion">
      <Value>$(AssemblyFileVersion)</Value>
    </AssemblyAttributes>
    <AssemblyAttributes Include="AssemblyVersion">
      <Value>$(AssemblyVersion)</Value>
    </AssemblyAttributes>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AssignmentBase\Digiturk.Workflow.Digiflow.AssignmentBase.csproj">
      <Project>{458CFA6A-3061-4451-BFF2-71248EE9BD5A}</Project>
      <Name>Digiturk.Workflow.Digiflow.AssignmentBase</Name>
    </ProjectReference>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.Actions\Digiturk.Workflow.Digiflow.Actions.csproj">
      <Project>{1F089233-EB58-4773-A98F-CEE69112FE2D}</Project>
      <Name>Digiturk.Workflow.Digiflow.Actions</Name>
    </ProjectReference>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.Authentication\Digiturk.Workflow.Digiflow.Authentication.csproj">
      <Project>{840C288D-C12A-42AD-AF9F-E4DD16E47599}</Project>
      <Name>Digiturk.Workflow.Digiflow.Authentication</Name>
    </ProjectReference>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.Authorization\Digiturk.Workflow.Digiflow.Authorization.csproj">
      <Project>{3AAB307C-F63E-4BC5-A2BA-4A573898164E}</Project>
      <Name>Digiturk.Workflow.Digiflow.Authorization</Name>
    </ProjectReference>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.DataAccessLayer\Digiturk.Workflow.Digiflow.DataAccessLayer.csproj">
      <Project>{79D2C071-5B92-481E-A281-8E49A5D4E1EB}</Project>
      <Name>Digiturk.Workflow.Digiflow.DataAccessLayer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.Entities\Digiturk.Workflow.Digiflow.Entities.csproj">
      <Project>{2332BA40-BC4C-41DD-83CC-7B2060C089FE}</Project>
      <Name>Digiturk.Workflow.Digiflow.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\Digiturk.Workflow.DigiFlow.Framework\Digiturk.Workflow.DigiFlow.Framework.csproj">
      <Project>{F1F71DAD-7780-4300-BFC2-2FD4E081F0F8}</Project>
      <Name>Digiturk.Workflow.DigiFlow.Framework</Name>
    </ProjectReference>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.GenericMailHelper\Digiturk.Workflow.Digiflow.GenericMailHelper.csproj">
      <Project>{37815664-C4CA-4420-B209-B6745E4601BE}</Project>
      <Name>Digiturk.Workflow.Digiflow.GenericMailHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.WebCore\Digiturk.Workflow.Digiflow.WebCore.csproj">
      <Project>{DF01CF52-A7B1-41B0-B982-42BC2EDA601A}</Project>
      <Name>Digiturk.Workflow.Digiflow.WebCore</Name>
    </ProjectReference>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.csproj">
      <Project>{9FA5D1F9-73FC-44D7-8165-A0F6BBAF7095}</Project>
      <Name>Digiturk.Workflow.Digiflow.WorkFlowServicesHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\ExceptionEntites\Digiturk.Workflow.Digiflow.ExceptionEntites.csproj">
      <Project>{5C7F6611-2648-44C0-A034-C169EC95CE1A}</Project>
      <Name>Digiturk.Workflow.Digiflow.ExceptionEntites</Name>
    </ProjectReference>
    <ProjectReference Include="..\Helpers\Digiturk.Workflow.Digiflow.CoreHelpers.csproj">
      <Project>{5A84C96F-2145-4C71-A1E0-F157D60EADED}</Project>
      <Name>Digiturk.Workflow.Digiflow.CoreHelpers</Name>
    </ProjectReference>
    <ProjectReference Include="..\WorkFlowHelpers\Digiturk.Workflow.Digiflow.WorkFlowHelpers.csproj">
      <Project>{846CDF0B-9B04-4947-A097-7D35B5DB1147}</Project>
      <Name>Digiturk.Workflow.Digiflow.WorkFlowHelpers</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(_WebPublishTargetsPath)\Web\Microsoft.WebSite.Publishing.targets" />
</Project>