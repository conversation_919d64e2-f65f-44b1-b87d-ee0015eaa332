﻿using Digiturk.Workflow.Digiflow.Authentication;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Authorization;

namespace Digiturk.Workflow.Digiflow.Api.WorkflowHelper
{
    public class IdentityHelper
    {
        public static bool DebugMode
        {
            get
            {
                return bool.Parse(System.Configuration.ConfigurationManager.AppSettings["DebugMode"].ToString());
            }
        }

        public static AuthenticationResult GetUserInformation(long LoginId,string UserName, long InstanceId, long wfDefId)
        {
            long WorkflowDefinitionId = 0;
            long StateDefinitionId = 0;
            if (InstanceId > 0)
            {
                using (UnitOfWork.Start())
                {
                    FWfWorkflowInstance wfinstance = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                    WorkflowDefinitionId = wfinstance.WfWorkflowDef.WfWorkflowDefId;
                    StateDefinitionId = wfinstance.WfCurrentState.WfStateDef.WfStateDefId;
                }
            }
            if (wfDefId > 0)
            {
                using (UnitOfWork.Start())
                {
                    WorkflowDefinitionId = wfDefId;
                    FWfWorkflowDef fWfWorkflowDef = WFRepository<FWfWorkflowDef>.GetEntity(wfDefId);
                    StateDefinitionId = fWfWorkflowDef.InitialState.WfStateDefId;
                }
            }
            AuthenticationResult authenticationResult = new AuthenticationResult(DebugMode, LoginId, UserName, WorkflowDefinitionId, StateDefinitionId);
            if (authenticationResult.AuthoList == null)
            {
                authenticationResult.AuthoList = new AuthorizationList(LoginId, WorkflowDefinitionId, StateDefinitionId);
            }
            return authenticationResult;
        }
    }
}
