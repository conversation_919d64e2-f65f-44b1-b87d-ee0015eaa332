.dxgvControl_Office2010Silver,
.dxgvDisabled_Office2010Silver
{
	border: 1px solid #868b91;
	font: 8pt Verdana;
	background-color: #e7eaee;
	color: Black;
	cursor: default;
}
.dxgvDisabled_Office2010Silver
{
	color: #989898;
}
.dxgvControl_Office2010Silver a
{
	color: #5a9ddb;
	text-decoration: none;
}
.dxgvControl_Office2010Silver a:hover
{
    text-decoration: underline;
}
.dxgvDisabled_Office2010Silver a
{
	color: #989898;
}
.dxgvLoadingPanel_Office2010Silver
{
	font: 8pt Verdana;
    color: #585e68;
    background: White;
	border: 1px solid #80858d;
}
.dxgvLoadingPanel_Office2010Silver td
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxgvLoadingPanelStatusBar_Office2010Silver
{
	background-color: Transparent;
	font: 8pt Verdana;
}
.dxgvLoadingPanelStatusBar_Office2010Silver td
{
	white-space: nowrap;
	text-align: center;
	padding: 0 2px;
}
.dxgvFilterPopupWindow_Office2010Silver
{
	color: Black;
	font: 8pt Verdana;
	border: 1px solid #a5acb5;
}
.dxgvFilterPopupItemsArea_Office2010Silver
{
	color: Black;
	background-color: White;
	padding-top: 1px;
}
.dxgvFilterPopupButtonPanel_Office2010Silver
{
	font: 8pt Verdana;
	background-color: #e9edf1;
	border-top: 1px solid #a5acb5;
	color: Black;
}

.dxgvFilterPopupItem_Office2010Silver td.dxgv,
.dxgvFilterPopupActiveItem_Office2010Silver td.dxgv,
.dxgvFilterPopupSelectedItem_Office2010Silver td.dxgv
{
	border-left: 1px solid white;
	border-right: 1px solid white;
	border-bottom: 1px solid white;
	padding: 3px 2px 3px 3px;
	cursor: default;
	white-space: nowrap;
}
.dxgvFilterPopupActiveItem_Office2010Silver
{
	background: #e2ecf7;
	color: Black;
}
.dxgvFilterPopupSelectedItem_Office2010Silver
{
	background: #ecedef;
	color: Black;
}

.dxgvTable_Office2010Silver
{
	background-color: White;
	border: 0;
	border-collapse: separate!important;
	overflow: hidden;
	font: 8pt Verdana;
	color: Black;
}
.dxgvInlineEditRow_Office2010Silver,
.dxgvDataRow_Office2010Silver
{
}
.dxgvInlineEditRow_Office2010Silver td.dxgv
{
	border-bottom: 1px solid #dbdee1;
	border-right: 1px solid #dbdee1;
}
.dxgvInlineEditRow_Office2010Silver .dxgvCommandColumn_Office2010Silver
{
    background: #ebecec;
}
.dxgvDataRowAlt_Office2010Silver
{
	background-color: #f7f7f8;
}
.dxgvFilterRow_Office2010Silver
{
	background-color: White;
}
.dxgvEditForm_Office2010Silver
{
	background-color: #f9fafb;
}
.dxgvEditForm_Office2010Silver td.dxgv
{
	border-bottom: 1px solid #dbdee1;
	padding: 8px 10px 10px;
}
.dxgvEditForm_Office2010Silver td.dxgvIndentCell
{
    background: White;
	border-right: Solid 1px #DBDEE1;
	border-left: Solid 1px #DBDEE1;
	border-top: 0px;
}
.dxgvSelectedRow_Office2010Silver
{
	background-color: #fdf7d9;
    color: Black;
}
.dxgvFocusedRow_Office2010Silver
{
	background: #faedb6;
    color: Black;
}
.dxgvSelectedRow_Office2010Silver .dxgvCommandColumn_Office2010Silver a,
.dxgvFocusedRow_Office2010Silver .dxgvCommandColumn_Office2010Silver a
{
    color:  #5a9ddb;
	text-decoration: none;
}
.dxgvSelectedRow_Office2010Silver .dxgvCommandColumn_Office2010Silver a:hover,
.dxgvFocusedRow_Office2010Silver .dxgvCommandColumn_Office2010Silver a:hover
{
    text-decoration: underline;
}
.dxgvSelectedRow_Office2010Silver .dxgvCommandColumn_Office2010Silver a:visited,
.dxgvFocusedRow_Office2010Silver .dxgvCommandColumn_Office2010Silver a:visited
{
    color: #c983e4;
}

.dxgvPreviewRow_Office2010Silver
{
	background-color: #f7f7f8;
	color: #777777;
}
.dxgvDetailCell_Office2010Silver,
.dxgvPreviewRow_Office2010Silver td.dxgv,
.dxgvEmptyDataRow_Office2010Silver td.dxgv
{
	padding: 20px 2px 20px 4px;
	border-bottom: 1px solid #dbdee1;
	border-top: 0;
	border-left: 0;
	border-right: 0;
}
.dxgvDetailCell_Office2010Silver
{
    border-bottom-color: #dbdee1;
}
.dxgvPreviewRow_Office2010Silver td.dxgv
{
	padding: 10px 10px 10px 15px;
}
.dxgvDetailCell_Office2010Silver
{
	padding: 16px 18px;
}
.dxgvDetailRow_Office2010Silver td.dxgvIndentCell
{
    padding-right: 0px;
    border-bottom: 1px solid #dbdee1;
}
.dxgvEmptyDataRow_Office2010Silver
{
	color: #777777;
}
.dxgvEmptyDataRow_Office2010Silver td.dxgv
{
    border-bottom: 1px solid #dbdee1;
	text-align: center;
}

.dxgvEditFormDisplayRow_Office2010Silver td.dxgv,
.dxgvDataRow_Office2010Silver td.dxgv,
.dxgvDataRowAlt_Office2010Silver td.dxgv,
.dxgvSelectedRow_Office2010Silver td.dxgv,
.dxgvFocusedRow_Office2010Silver td.dxgv
{
	overflow: hidden;
	border-bottom: 1px solid #dbdee1;
	border-right: 1px solid #dbdee1;
	border-top: 0;
	border-left: 0;
	padding: 4px 6px;
}
.dxgvEditFormDisplayRow_Office2010Silver
{
}
.dxgvEditFormDisplayRow_Office2010Silver td.dxgv
{
}
.dxgvEditFormDisplayRow_Office2010Silver td.dxgvIndentCell
{
    background: White;
	border-right: Solid 1px #DBDEE1;
	border-left: Solid 1px #DBDEE1;
	border-top: 0px;
}

.dxgvEditingErrorRow_Office2010Silver
{
	background-color: #e9edf1;
	color: #ba1717;
}
.dxgvEditingErrorRow_Office2010Silver td.dxgv
{
	white-space: pre-wrap;
	border-bottom: 1px solid #dbdee1;
	border-right: 0;
	border-top: 0;
	border-left: 0;
	padding: 6px 10px;
}

.dxgvFilterRow_Office2010Silver td.dxgv
{
	border-bottom: 1px solid #a5acb5;
	border-right-width: 0;
	border-top: 0;
	border-left: 0;
	padding: 2px 3px 2px 2px;
	overflow: hidden;
}
.dxgvGroupRow_Office2010Silver
{
	background-color: White;
}
.dxgvFocusedGroupRow_Office2010Silver
{
	background: #faedb6;
    color: Black;
}
.dxgvGroupRow_Office2010Silver td.dxgv,
.dxgvFocusedGroupRow_Office2010Silver td.dxgv
{
	border: none 0;
	vertical-align: middle;
	white-space: nowrap;
	border-bottom: 1px solid #dbdee1;
	padding: 4px 6px;
}
.dxgvFocusedRow_Office2010Silver td.dxgvIndentCell,
.dxgvFocusedGroupRow_Office2010Silver td.dxgvIndentCell,
.dxgvSelectedRow_Office2010Silver td.dxgvIndentCell
{
	background-color: White!important;
	border-right: 1px solid #dbdee1;
	border-left: 1px solid #dbdee1;
	border-top: 0px;
}
.dxgvHeaderPanel_Office2010Silver
{
	background-color: #e9edf1;
	color: Black;
	padding: 8px 6px;
	border-bottom: 1px solid #a5acb5;
}

.dxgvHeader_Office2010Silver
{
	cursor: pointer;
	white-space: nowrap;
	padding: 5px 6px;
	border: 1px solid #a5acb5;
	background: #e7ebef url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.GridView.gvHeaderBack.png")%>') repeat-x left top;
	overflow: hidden;
	font-weight: normal;
	text-align: left;
}
.dxgvHeader_Office2010Silver,
.dxgvHeader_Office2010Silver table
{
	color: #3c3c3c;
	font: 8pt Verdana;
}
.dxgvHeader_Office2010Silver td
{
	white-space: nowrap;
}
.dxgvHeader_Office2010Silver a
{
	color: #5a9ddb;
	text-decoration: underline;
}
.dxgvCustomization_Office2010Silver,
.dxgvPopupEditForm_Office2010Silver
{
	width: 100%;
	padding: 0 0 0 0;
	margin: 0 0 0 0;
}
.dxgvPopupEditForm_Office2010Silver .dxgvEditingErrorRow_Office2010Silver .dxgv
{
    border-bottom-width: 0;
}
.dxgvGroupPanel_Office2010Silver
{
	white-space: nowrap;
	font-size: 8pt;

	background-color: #e9edf1;
	color: #3c3c3c;
	border-bottom: 1px solid #a5acb5;
	padding: 7px 4px 10px 6px;
}
.dxgvFooter_Office2010Silver
{
	background-color: #f6f7f9;
	white-space: nowrap;
}
.dxgvFooter_Office2010Silver td.dxgv
{
	padding: 6px;
	border-bottom: 1px solid #dbdee1;
	border-right: 0;
}
.dxgvGroupFooter_Office2010Silver
{
	background-color: #f6f7f9;
}
.dxgvGroupFooter_Office2010Silver td.dxgv
{
    padding: 6px;
    border-bottom: 1px solid #dbdee1;
    border-right: 0;
    white-space: nowrap;
}
.dxgvDataRow_Office2010Silver td.dxgvIndentCell,
.dxgvGroupRow_Office2010Silver td.dxgvIndentCell,
.dxgvGroupFooter_Office2010Silver td.dxgvIndentCell
{
    background-color: White;
	border-right: 1px solid #dbdee1;
	border-left: 1px solid #dbdee1;
	border-top: 0px;
}

.dxgvTitlePanel_Office2010Silver,
.dxgvTable_Office2010Silver caption
{
    font-size: 8pt;
	font-weight: normal;
	padding: 3px 3px 5px;
	text-align: center;
	background: #e8ebee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.GridView.gvTitleBack.png")%>') repeat-x left top;
	color: Black;
	border-bottom: 1px solid #a5acb5;
}
.dxgvLoadingDiv_Office2010Silver
{
	background-color:Gray;
	opacity: 0.01;
	filter: alpha(opacity=1);
}
.dxgvStatusBar_Office2010Silver
{
    background: #e9edf1;
	border-top: 1px solid #a5acb5;
}
.dxgvStatusBar_Office2010Silver tr.dxgv
{
	height: 20px;
}
.dxgvCommandColumn_Office2010Silver
{
	padding: 2px;
}
.dxgvCommandColumn_Office2010Silver a
{
	margin: 0px 3px 0px 0px;
	color:  #5a9ddb;
	text-decoration: none;
}
.dxgvCommandColumn_Office2010Silver a:hover
{
	text-decoration: underline;
}
.dxgvCommandColumn_Office2010Silver a:visited
{
	color: #c983e4;
}

.dxgvCommandColumnItem_Office2010Silver
{
}
.dxgvEditFormTable_Office2010Silver
{
	padding: 2px 6px 6px 4px;
	font: 8pt Verdana;
	color: Black;
}
.dxgvEditFormTable_Office2010Silver a
{
    color:  #5a9ddb;
	text-decoration: none;
}
.dxgvEditFormTable_Office2010Silver a:hover
{
    text-decoration: underline;
}
.dxgvEditFormTable_Office2010Silver a:visited
{
    color: #c983e4;
}

.dxgvEditFormCaption_Office2010Silver
{
	padding: 4px 4px 4px 10px;
    white-space: nowrap;
}

.dxgvInlineEditCell_Office2010Silver
{
	padding: 1px;
}

.dxgvEditFormCell_Office2010Silver
{
	padding: 4px;
	border: 0;
}

.dxgvPagerTopPanel_Office2010Silver,
.dxgvPagerBottomPanel_Office2010Silver
{
    background: #e7eaee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.GridView.gvPagerPanelBack.png")%>') repeat-x left top;
}
.dxgvDetailButton_Office2010Silver
{
}

.dxgvFilterBar_Office2010Silver
{
	border-top: 1px solid #a5acb5;
	background: #e9edf1;
}
.dxgvFilterBar_Office2010Silver a
{
	color: #5a9ddb;
	text-decoration: underline;
}
.dxgvFilterBarCheckBoxCell_Office2010Silver
{
	padding: 0 3px;
	padding-right: 7px;
}
.dxgvFilterBarImageCell_Office2010Silver
{
	padding: 0 3px;
	padding-right: 1px;
	cursor: pointer;
}
.dxgvFilterBarExpressionCell_Office2010Silver
{
	font-size: 8pt;
	padding: 5px 5px 8px 0;
	white-space: nowrap;
}
.dxgvFilterBarClearButtonCell_Office2010Silver
{
	font-size: 8pt;
	padding: 5px 6px 8px;
}
.dxgvFilterBuilderMainArea_Office2010Silver
{
	background: white;
	padding: 6px 2px;
}
.dxgvFilterBuilderButtonArea_Office2010Silver
{
	background: #e9edf1;
	border-top: 1px solid #c9cdd2;
	padding: 6px;
}

.dxgvDataRowHover_Office2010Silver
{
	background: #fbf4d7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.GridView.gvHoverBack.png")%>') repeat-x left top;
    color: Black;
}

.dxgvControl_Office2010Silver .dxpControl_Office2010Silver td.dxpCtrl_Office2010Silver,
.dxgvDisabled_Office2010Silver .dxpControl_Office2010Silver td.dxpCtrl_Office2010Silver,

.dxgvControl_Office2010Silver .dxpLite_Office2010Silver,
.dxgvDisabled_Office2010Silver .dxpLite_Office2010Silver
{
	padding-top: 4px;
}