C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.AssignmentBase.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.AssignmentBase.pdb
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Common.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.Actions.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.Authorization.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.CoreHelpers.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.DataAccessLayer.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.Entities.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.DigiFlow.Framework.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.GenericMailHelper.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Engine.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Entities.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Repository.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\NHibernate.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.BaseServices.DAL.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Oracle.DataAccess.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.Authentication.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Digiturk.Workflow.Digiflow.ExceptionEntites.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Iesi.Collections.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Antlr3.Runtime.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\log4net.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Castle.Core.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Castle.DynamicProxy2.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraReports.v10.1.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.RichEdit.v10.1.Core.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraEditors.v10.1.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.Charts.v10.1.Core.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraPivotGrid.v10.1.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraPivotGrid.v10.1.Core.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.Utils.v10.1.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraRichEdit.v10.1.dll
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Castle.Core.xml
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\Castle.DynamicProxy2.xml
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraReports.v10.1.xml
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.RichEdit.v10.1.Core.xml
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraEditors.v10.1.xml
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraPivotGrid.v10.1.xml
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraPivotGrid.v10.1.Core.xml
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.Utils.v10.1.xml
C:\TFS\DigiflowPM\AssignmentBase\bin\Debug\DevExpress.XtraRichEdit.v10.1.xml
C:\TFS\DigiflowPM\AssignmentBase\obj\Debug\Digiturk.Workflow.Digiflow.AssignmentBase.csproj.AssemblyReference.cache
C:\TFS\DigiflowPM\AssignmentBase\obj\Debug\Digiturk.Workflow.Digiflow.AssignmentBase.csproj.CoreCompileInputs.cache
C:\TFS\DigiflowPM\AssignmentBase\obj\Debug\Digiturk.Workflow.Digiflow.AssignmentBase.csproj.CopyComplete
C:\TFS\DigiflowPM\AssignmentBase\obj\Debug\Digiturk.Workflow.Digiflow.AssignmentBase.dll
C:\TFS\DigiflowPM\AssignmentBase\obj\Debug\Digiturk.Workflow.Digiflow.AssignmentBase.pdb
