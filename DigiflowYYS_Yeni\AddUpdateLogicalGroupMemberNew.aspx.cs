﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.Entities.YYS.LogicalGroup;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Digiflow.YYS.Core;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Mail;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using ActiveDirectoryHelpers;
using System.Configuration;
using DevExpress.XtraPrinting;

namespace DigiflowYYS_Yeni
{
    public partial class AddUpdateLogicalGroupMemberNew : YYSSecurePage
    {
        private enum EnumTypeOfUsers
        {
            AdUser = 1,
            StandartUser = 2
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            System.Web.UI.ScriptManager.GetCurrent(Page).RegisterPostBackControl(btnExceleAktar);
            if (!Page.IsPostBack)
            {
                this.Master.ShowMenu(true);
                this.Master.PageTitle = "Mantıksal Grup Üyeleri Ekleme Yeni";
                Session["LogicalGroupId"] = Request.QueryString["LogicalGroupId"];
                LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));

                long IsAdmin = 0;

                if (Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAndFlowAdmin.ToString() || Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAdmin.ToString())
                {
                    IsAdmin = 1;
                }

                drpAkisListesi = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownList("Name", "WorkflowDefId", drpAkisListesi, Digiturk.Workflow.Digiflow.YYS.Core.WorkflowHelper.GetWorkflowsOfAdmin(UserInformation.LoginObject.LoginId, IsAdmin), "İş Akışı Seçiniz", "0");
                drpMantiksalGrupİsmi = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownLogicalGroup("Name", "RequestId", drpMantiksalGrupİsmi, Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.GetAllByWorkflowId(ConvertionHelper.ConvertValue<long>(lg.WfDefId)), "Mantıksal Grup Seçiniz", "0");


                if (Session["LogicalGroupId"] != null)
                {
                    drpMantiksalGrupİsmi.SelectedValue = Session["LogicalGroupId"].ToString();
                }

                drpAkisListesi.SelectedValue = lg.WfDefId.ToString();
                drpKullanicilar = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownList("NAME_SURNAME", "LOGIN_ID", drpKullanicilar, HrHelper.GetUsers(), "KULLANICI SEÇİNİZ", "0");
                txtMantiksalGrupAciklama.Text = lg.Description;
                txtMantiksalGrupAciklama.Enabled = false;
                rdbListKullaniciTipleri.DataTextField = "DescriptionTR";
                rdbListKullaniciTipleri.DataValueField = "RequestId";
                rdbListKullaniciTipleri.DataSource = LogicalGroupMemberTypeHelper.GetAllLogicalGroupMemberTypes();
                rdbListKullaniciTipleri.DataBind();

                if (!string.IsNullOrEmpty(lg.AdGroupName))
                {
                    rdbListKullaniciTipleri.SelectedValue = "5";
                    rdbListKullaniciTipleri_SelectedIndexChanged(this, null);
                }

                //   Mantıksal Grup Üyeleri çekilir ve doldurulur
                if (Session["LogicalGroupId"] != null)
                {
                    #region Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur                    
                    this.Master.PageTitle = "Mantıksal Grup Üyeleri Düzenleme";
                    DtbAllUserListToSave = null;
                    DataTable dtbAllLogicalGroupMembers = GetAllLogicalGroupMembers();
                    GrdTumKaydedileceklerDataSourceRefresh(dtbAllLogicalGroupMembers);
                    #endregion Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur
                }

            }
            if (IsCallback)
            {
                GrdTumKaydedileceklerDataSourceRefresh(DtbAllUserListToSave);
                GrdAdKullanicilariGridDataSourceRefresh(DtbAdGroupUserList);
                //KullaniciEkle(drpDomain.SelectedValue, drpAdGrubu.SelectedValue);
            }
        }

        public void GrdTumKaydedileceklerDataSourceRefresh(DataTable dtb)
        {
            grdTumKaydedilecekler.DataSource = dtb;
            grdTumKaydedilecekler.DataBind();
        }

        public void GrdAdKullanicilariGridDataSourceRefresh(DataTable dtb)
        {
            grdAdGrupKullanicilari.DataSource = dtb;
            grdAdGrupKullanicilari.DataBind();
        }

        /// <summary>
        /// Belirtilen Mantıksal Grubun üyelerini getirir. AD NORMAL KULLANICI OUTSOURCE HEPSI 
        /// </summary>
        private DataTable GetAllLogicalGroupMembers()// TUM KULLANICILARIN OLD GENEL GRİDE BAKIYO, MEMBERS TABLOSUNDA CEKIO
        {
            DtbAllUserListToSave = null;
            long logicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
            DataTable dtbFromDB = LogicalGroupMemberHelper.GetAllMembersOfLogicalGroup(logicalGroupId);
            DataTable dtbUsersToSave = DtbAllUserListToSave;

            foreach (DataRow row in dtbFromDB.Rows)
            {
                DataRow dr = dtbUsersToSave.NewRow();
                dr["LOGICAL_GROUP_MEMBER_ID"] = row["LOGICAL_GROUP_MEMBER_ID"];
                dr["LOGICAL_GROUP_MEMBER_TYPE_ID"] = row["LOGICAL_GROUP_MEMBER_TYPE_ID"];
                dr["LOGICAL_GROUP_ID"] = row["LOGICAL_GROUP_ID"];
                dr["YYS_LG_AD_MEMBERS_ID"] = row["YYS_LG_AD_MEMBERS_ID"];
                dr["USERNAME"] = row["USERNAME"];
                dr["FULLNAME"] = row["FULLNAME"];
                dr["LOGIN_ID"] = row["LOGIN_ID"];
                dr["CONTENT"] = row["CONTENT"];
                dr["DESCRIPTION"] = row["DESCRIPTION"];
                dr["EMAIL"] = row["EMAIL"];
                dr["AD_DOMAIN"] = row["AD_DOMAIN"];
                dr["AD_GROUP"] = row["AD_GROUP"];
                dr["TIP"] = row["TIP"];
                dr["GUID"] = Guid.NewGuid().ToString();

                dtbUsersToSave.Rows.Add(dr);
            }
            DtbAllUserListToSave = dtbUsersToSave;
            return dtbUsersToSave;
        }

        protected void rdbListKullaniciTipleri_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnEkleGenel.Visible = true;
            if (rdbListKullaniciTipleri.Items[0].Selected)
            {
                pnlKullanici.Visible = true;
                pnlOutsource.Visible = false;
                pnlParam.Visible = false;
                pnlTumKullanıcılar.Visible = false;
                pnlAdGroupPart.Visible = false;
            }
            if (rdbListKullaniciTipleri.Items[1].Selected)
            {
                pnlKullanici.Visible = false;
                pnlOutsource.Visible = false;
                pnlParam.Visible = false;
                pnlTumKullanıcılar.Visible = true;
                pnlAdGroupPart.Visible = false;
            }
            if (rdbListKullaniciTipleri.Items[2].Selected)
            {
                pnlKullanici.Visible = false;
                pnlParam.Visible = true;
                pnlOutsource.Visible = false;
                pnlTumKullanıcılar.Visible = false;
                pnlAdGroupPart.Visible = false;
            }
            if (rdbListKullaniciTipleri.Items[3].Selected)
            {
                pnlKullanici.Visible = false;
                pnlParam.Visible = false;
                pnlOutsource.Visible = true;
                pnlTumKullanıcılar.Visible = false;
                pnlAdGroupPart.Visible = false;
            }
            if (rdbListKullaniciTipleri.Items[4].Selected)
            {
                pnlKullanici.Visible = false;
                pnlParam.Visible = false;
                pnlOutsource.Visible = false;
                pnlTumKullanıcılar.Visible = false;
                pnlAdGroupPart.Visible = true;
                btnEkleGenel.Visible = false;
            }
        }

        protected void btnAdGrupSorgula_Click(object sender, EventArgs e)
        {
            DtbAdGroupUserList.Columns.Clear();
            DtbAdGroupUserList.Rows.Clear();
            BuildAdGroupUserListDataTable();
            AdKullaniciEkle(drpDomain.SelectedValue, drpAdGrubu.SelectedValue);
        }

        protected void btnTumunuTemizle_Click(object sender, EventArgs e)
        {
            List<DataRow> rowsToRemove = new List<DataRow>();

            foreach (DataRow row in DtbAllUserListToSave.Rows)
            {
                if (row["LOGICAL_GROUP_MEMBER_TYPE_ID"].ToString() == "5")
                {
                    rowsToRemove.Add(row);
                }
            }

            foreach (DataRow item in rowsToRemove)
            {
                DtbAllUserListToSave.Rows.Remove(item);
            }
            DtbAllUserListToSave.AcceptChanges();

            GrdTumKaydedileceklerDataSourceRefresh(DtbAllUserListToSave);
            this.Master.ShowPopup(false, "Üye Silme", "Tüm üyeler listeden çıkarıldı", false, "");
        }

        public DataTable DtbAdGroupUserList
        {
            get
            {
                if (Session["AdGroupUserList" + Session.SessionID] != null && Session["AdGroupUserList" + Session.SessionID] is DataTable)
                {
                    return (DataTable)Session["AdGroupUserList" + Session.SessionID];
                }
                else
                {
                    Session["AdGroupUserList" + Session.SessionID] = new DataTable();
                    return (DataTable)Session["AdGroupUserList" + Session.SessionID];
                }
            }
            set
            {
                Session["AdGroupUserList" + Session.SessionID] = value;
            }
        }

        public DataTable DtbAdGroupList
        {
            get
            {
                if (Session["AdGroupList" + Session.SessionID] != null && Session["AdGroupList" + Session.SessionID] is DataTable)
                {
                    return (DataTable)Session["AdGroupList" + Session.SessionID];
                }
                else
                {
                    Session["AdGroupList" + Session.SessionID] = new DataTable();
                    return (DataTable)Session["AdGroupList" + Session.SessionID];
                }
            }
            set
            {
                Session["AdGroupList" + Session.SessionID] = value;
            }
        }

        public DataTable DtbAllUserListToSave
        {
            get
            {
                if (Session["AllUserListToSave" + Session.SessionID] != null && Session["AllUserListToSave" + Session.SessionID] is DataTable)
                {
                    return (DataTable)Session["AllUserListToSave" + Session.SessionID];
                }
                else
                {
                    return BuildAllUserListDtbToSave();
                }
            }
            set
            {
                Session["AllUserListToSave" + Session.SessionID] = value;
            }
        }
        public DataTable BuildAllUserListDtbToSave()
        {
            DataTable dtb = new DataTable();
            dtb.Columns.Add(new DataColumn("ID", typeof(string)));
            dtb.Columns["ID"].AutoIncrement = true;
            dtb.Columns["ID"].AutoIncrementSeed = 1;
            dtb.Columns.Add(new DataColumn("LOGICAL_GROUP_MEMBER_ID", typeof(string)));
            dtb.Columns.Add(new DataColumn("LOGICAL_GROUP_MEMBER_TYPE_ID", typeof(string)));
            dtb.Columns.Add(new DataColumn("LOGICAL_GROUP_ID", typeof(string)));
            dtb.Columns.Add(new DataColumn("YYS_LG_AD_MEMBERS_ID", typeof(string)));
            dtb.Columns.Add(new DataColumn("USERNAME", typeof(string)));
            dtb.Columns.Add(new DataColumn("FULLNAME", typeof(string)));
            dtb.Columns.Add(new DataColumn("LOGIN_ID", typeof(string)));
            dtb.Columns.Add(new DataColumn("CONTENT", typeof(string)));
            dtb.Columns.Add(new DataColumn("DESCRIPTION", typeof(string)));
            dtb.Columns.Add(new DataColumn("EMAIL", typeof(string)));
            dtb.Columns.Add(new DataColumn("AD_DOMAIN", typeof(string)));
            dtb.Columns.Add(new DataColumn("AD_GROUP", typeof(string)));
            dtb.Columns.Add(new DataColumn("TIP", typeof(string)));
            dtb.Columns.Add(new DataColumn("GUID", typeof(string)));
            return dtb;
        }

        public void AdKullaniciEkle(string Domain, string AdGrupName)
        {
            ActiveDirectoryHelpers.AdPortalServices.UserInformation[] userlist = ActiveDirectoryHelpers.AdHelper.GetAllUsersOfAdGroup(Domain, AdGrupName);

            FillAdUserListToDataTable(userlist, Domain, AdGrupName);
            FillAdGrid();
        }

        public void FillAdUserListToDataTable(ActiveDirectoryHelpers.AdPortalServices.UserInformation[] userlist, string Domain, string AdGrupName)
        {
            try
            {
                foreach (var item in userlist)
                {
                    if (!CheckExistUser(item.UserName))
                    {
                        DataTable dtbFLoginInfo = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetLoginNamewithNameSurname(item.UserName);
                        DataRow dr = DtbAdGroupUserList.NewRow();
                        dr["USERNAME"] = item.UserName;
                        dr["DISPLAYNAME"] = item.NameSurName;
                        dr["FLOGINSTATUS"] = "0";
                        dr["DOMAIN"] = Domain;
                        dr["AD_GROUP"] = AdGrupName;
                        if (dtbFLoginInfo.Rows.Count > 0)
                        {
                            dr["FLOGIN"] = dtbFLoginInfo.Rows[0]["F_LOGIN_ID"];
                            dr["DISPLAYNAME"] = dtbFLoginInfo.Rows[0]["NAME_SURNAME"];
                            dr["CHECKRESULT"] = "F Login Bulundu";
                            dr["FLOGINSTATUS"] = "1";
                        }
                        else
                        {
                            dr["CHECKRESULT"] = "F Login Bulunamadı";
                        }
                        DtbAdGroupUserList.Rows.Add(dr);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public void FillAdGrid() //ad grubunun kullanıcı isimlerinin gosterıldıgı yer yani bizim ilk grid
        {
            if (DtbAdGroupUserList.Columns.Count == 0)
            {
                BuildAdGroupUserListDataTable();
            }
            grdAdGrupKullanicilari.DataSource = DtbAdGroupUserList;
            grdAdGrupKullanicilari.DataBind();
        }

        public bool CheckExistUser(string UserName)
        {
            bool result = false;
            for (int i = 0; i < DtbAdGroupUserList.Rows.Count; i++)
            {
                if (DtbAdGroupUserList.Rows[i]["USERNAME"].ToString() == UserName)
                {
                    result = true;
                    break;
                }
            }
            return result;
        }

        public void BuildAdGroupUserListDataTable()//ad kullanıcı ksılerının oldugu datatable buraya bide domaın ve grup da ekle
        {
            DtbAdGroupUserList.Columns.Add("USERNAME");
            DtbAdGroupUserList.Columns.Add("DISPLAYNAME");
            DtbAdGroupUserList.Columns.Add("FLOGIN");
            DtbAdGroupUserList.Columns.Add("CHECKRESULT");
            DtbAdGroupUserList.Columns.Add("FLOGINSTATUS");
            DtbAdGroupUserList.Columns.Add("DOMAIN");
            DtbAdGroupUserList.Columns.Add("AD_GROUP");
        }

        protected void drpDomain_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillAdGroupDropdown(drpDomain.SelectedValue);
        }

        /// <summary>
        ///  !!!!!!!!!!!  BURAYI COREHELPERSA KOYDUGUN YENI DLL ILE DEGISTIR !!!!!!!!!!!!!!!!
        /// </summary>
        /// <param name="domain"></param>
        public void FillAdGroupDropdown(string domain)
        {
            try
            {
                ActiveDirectoryHelpers.AdPortalServices.GroupInformation[] gruplist = ActiveDirectoryHelpers.AdHelper.GetAllAdGroupsByDomain(domain);
                drpAdGrubu.Items.Clear();
                drpAdGrubu.Items.Add(new ListItem("0", "---Seçiniz---"));
                foreach (var item in gruplist)
                {
                    drpAdGrubu.Items.Add(new ListItem(item.GroupName, item.GroupName));
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        protected void btnEkleGenel_Click(object sender, EventArgs e)// tiplerden asagı ekleme icin kullanılan buton(parametre kullanıcı outsource vs)
        {
            DataTable dtbAllUsersToSave = DtbAllUserListToSave;

            if (!CheckErrorExist(dtbAllUsersToSave))
            {
                DataRow dr = dtbAllUsersToSave.NewRow();
                dr["LOGICAL_GROUP_MEMBER_TYPE_ID"] = rdbListKullaniciTipleri.SelectedValue;
                dr["LOGICAL_GROUP_ID"] = Session["LogicalGroupId"].ToString();
                dr["TIP"] = rdbListKullaniciTipleri.SelectedItem.Text;
                dr["GUID"] = Guid.NewGuid().ToString();
                switch (rdbListKullaniciTipleri.SelectedValue)
                {
                    //KULLANICI
                    case "1":
                        dr["FULLNAME"] = drpKullanicilar.SelectedItem.Text;
                        dr["LOGIN_ID"] = drpKullanicilar.SelectedValue;
                        break;
                    //TÜM KULLANICILAR
                    case "2":

                        dr["FULLNAME"] = rdbListKullaniciTipleri.SelectedItem.Text;
                        dr["LOGIN_ID"] = "-1";
                        break;
                    //PARAMETRE
                    case "3":
                        dr["CONTENT"] = txtParamIcerik.Text;
                        dr["DESCRIPTION"] = txtParamAciklama.Text;
                        break;
                    //OUTSOURCE
                    case "4":
                        dr["FULLNAME"] = txtOutAdSoyad.Text;
                        dr["EMAIL"] = txtOutEmail.Text;
                        break;
                }
                dtbAllUsersToSave.Rows.Add(dr);
                DtbAllUserListToSave = dtbAllUsersToSave;
                GrdTumKaydedileceklerDataSourceRefresh(DtbAllUserListToSave);
                this.Master.ShowPopup(false, "Bilgi", "Seçilen Kullanıcı Listeye Eklendi !", false, null);
            }
            else
            {
                this.Master.ShowPopup(false, "Hata", "Kayıt Eklenemedi!", true, null);
                return;
            }

        }

        private bool CheckErrorExist(DataTable dtbAllUsersToSave)
        {
            LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
            long IsOnePerson = lg.IsOnePersonGroup;
            bool error = false;

            switch (rdbListKullaniciTipleri.SelectedValue)
            {
                //KULLANICI
                case "1":
                    if (IsOnePerson != 1)
                    {
                        if (UserIsExistInGrid(dtbAllUsersToSave, drpKullanicilar.SelectedValue, "", EnumCheckType.userCheck))
                        {
                            error = true;
                        }
                    }
                    else
                    {
                        if (dtbAllUsersToSave.Rows.Count > 0)
                        {
                            error = true;
                            ErrorMessage = "Bu Grup Tek kişilik olarak ayarlanmıştır !";
                        }
                    }

                    break;
                //TÜM KULLANICILAR
                case "2":
                    if (IsOnePerson != 1)
                    {
                        if (UserIsExistInGrid(dtbAllUsersToSave, "-1", "", EnumCheckType.allUsersCheck))
                        {
                            error = true;
                        }
                        else
                        {
                            if (dtbAllUsersToSave.Rows.Count != 0)
                            {
                                error = true;
                                ErrorMessage = "Tüm kullanıcıları eklemek için önce griddeki tüm kullanıcıları silmelisiniz!";
                            }
                        }
                    }
                    else
                    {
                        error = true;
                        ErrorMessage = "Bu Grup Tek kişilik olarak ayarlanmıştır !";
                    }

                    break;
                //PARAMETRE
                case "3":
                    if (IsOnePerson != 1)
                    {
                        if (UserIsExistInGrid(dtbAllUsersToSave, "", "", EnumCheckType.paramCheck))
                        {
                            error = true;
                        }
                    }
                    else
                    {
                        error = true;
                        ErrorMessage = "Bu Grup Tek kişilik olarak ayarlanmıştır !";
                    }


                    break;
                //OUTSOURCE
                case "4":
                    if (IsOnePerson != 1)
                    {
                        if (UserIsExistInGrid(dtbAllUsersToSave, "", txtOutEmail.Text, EnumCheckType.outsourceCheck))
                        {
                            error = true;
                        }
                    }
                    else
                    {
                        error = true;
                        ErrorMessage = "Bu Grup Tek kişilik olarak ayarlanmıştır !";
                    }
                    break;
            }
            return error;
        }

        private string ErrorMessage
        {
            get
            {
                return Session["ErrorMessage" + Session.SessionID] as string;
            }
            set
            {
                Session["ErrorMessage" + Session.SessionID] = value;
            }
        }

        private enum EnumCheckType
        {
            userCheck = 1,
            allUsersCheck = 2,
            paramCheck = 3,
            outsourceCheck = 4,
            adUserCheck = 5
        }

        private bool UserIsExistInGrid(DataTable dtbAllUsersToSave, string loginId, string email, EnumCheckType checkType)
        {
            bool degerMevcut = false;

            foreach (DataRow row in dtbAllUsersToSave.Rows)
            {
                if (checkType == EnumCheckType.userCheck)
                {
                    if (row["LOGIN_ID"].ToString() == loginId && row["LOGICAL_GROUP_MEMBER_TYPE_ID"].ToString() == "1")
                    {
                        degerMevcut = true;
                        ErrorMessage = "Kullanıcı listede zaten mevcut!";
                        break;
                    }
                    if (row["LOGIN_ID"].ToString() == "-1")
                    {
                        degerMevcut = true;
                        ErrorMessage = "Kayıt eklemek için tüm kullanıcılar seçeneğini kaldırmalısınız";
                        break;
                    }

                }
                if (checkType == EnumCheckType.allUsersCheck)
                {
                    if (row["LOGIN_ID"].ToString() == loginId)
                    {
                        degerMevcut = true;
                        ErrorMessage = "Tüm kullanıcılar listede zaten mevcut!";
                        break;
                    }
                }

                if (checkType == EnumCheckType.paramCheck)
                {

                }
                if (checkType == EnumCheckType.outsourceCheck)
                {
                    if (row["EMAIL"].ToString() == email)
                    {
                        degerMevcut = true;
                        ErrorMessage = "Aynı mail adresine sahip bir outsource listede zaten mevcut!";
                        break;
                    }
                }

            }
            return degerMevcut;

        }

        protected bool IsAdGroupAlreadyExist(DataTable dtAdUsers, DataTable dtMevcutUsers)
        {
            bool sonuc = false;            
            DataTable distinctedAdUsers = dtAdUsers.DefaultView.ToTable(true, "AD_GROUP", "DOMAIN");
            DataTable distinctedAllUsersForLogicalGroup = dtMevcutUsers.DefaultView.ToTable(true, "AD_GROUP", "AD_DOMAIN");
            DataTable distinctedAllUsersCheck = dtMevcutUsers.DefaultView.ToTable(true, "LOGIN_ID");
            

            if ((distinctedAllUsersCheck.AsEnumerable().Where(x => x["LOGIN_ID"].ToString() == "-1").Count() > 0) == false)
            {
                foreach (DataRow dr1 in distinctedAdUsers.Rows)
                {
                    foreach (DataRow dr2 in distinctedAllUsersForLogicalGroup.Rows)
                    {
                        if (dr2["AD_DOMAIN"].ToString() == dr1["DOMAIN"].ToString() && dr2["AD_GROUP"].ToString() == dr1["AD_GROUP"].ToString())
                        {
                            sonuc = true;
                            ErrorMessage = "Eklenmek istenen AD Grubu zaten gridde mevcut!";
                        }                        
                    }
                }
            }
            else
            {
                sonuc = true;
                ErrorMessage = "Gride Ad Grubu eklemesi yapmak için 'Tüm kullanıcılar' kişisini silmelisiniz!";
            }

            return sonuc;
        }

        protected DataTable FilterDatatableFromExistingUser(DataTable dtAdUsers, DataTable dtMevcutUsers)
        {
            DataTable distinctedLoginIdsOfAdUsers = dtAdUsers.DefaultView.ToTable(true, "FLOGIN");
            DataTable distinctedAllUsersCheck = dtMevcutUsers.DefaultView.ToTable(true, "LOGIN_ID");

            foreach (DataRow adUserLoginId in distinctedLoginIdsOfAdUsers.Rows) //ad grubundan gelen kişilerin loginIdleri
            {
                foreach (DataRow mevcutLoginId in distinctedAllUsersCheck.Rows) // işlemden önce mantıksal grubun ieçrisindeki mevcut kisilerin loginId
                {
                    if (mevcutLoginId["LOGIN_ID"].ToString() == adUserLoginId["FLOGIN"].ToString())
                    {
                        dtAdUsers.Rows.Remove(dtAdUsers.AsEnumerable().Where(x => x["FLOGIN"].ToString() == adUserLoginId["FLOGIN"].ToString()).FirstOrDefault());
                    }
                }
            }
            return dtAdUsers;
        }

        protected void btnAdKullanicilariEkle_Click(object sender, EventArgs e)// ad kullanıcılarını genel kaydedilecek gridine ekleme
        {
            LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
            long IsOnePerson = lg.IsOnePersonGroup;
            if (IsOnePerson != 1)
            {
                if (DtbAdGroupUserList.Rows.Count > 0)// gridde ekleycek biri varsa
                {
                    if (!IsAdGroupAlreadyExist(DtbAdGroupUserList, DtbAllUserListToSave))
                    {
                        DtbAdGroupUserList = FilterDatatableFromExistingUser(DtbAdGroupUserList, DtbAllUserListToSave);
                        long logicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
                        DataTable dtbTumKaydedilecekler = DtbAllUserListToSave;

                        foreach (DataRow row in DtbAdGroupUserList.Rows)
                        {
                            if (row["FLOGINSTATUS"].ToString() != "0")
                            {
                                DataRow dr = dtbTumKaydedilecekler.NewRow();
                                dr["LOGICAL_GROUP_MEMBER_TYPE_ID"] = rdbListKullaniciTipleri.SelectedValue;
                                dr["LOGICAL_GROUP_ID"] = Session["LogicalGroupId"].ToString();
                                dr["LOGIN_ID"] = row["FLOGIN"];
                                dr["USERNAME"] = row["USERNAME"];
                                dr["FULLNAME"] = row["DISPLAYNAME"];
                                dr["AD_DOMAIN"] = row["DOMAIN"];
                                dr["AD_GROUP"] = row["AD_GROUP"];
                                dr["TIP"] = "AD KULLANICI";
                                dr["GUID"] = Guid.NewGuid().ToString();
                                dtbTumKaydedilecekler.Rows.Add(dr);
                            }
                        }
                        DtbAllUserListToSave = dtbTumKaydedilecekler;
                        GrdTumKaydedileceklerDataSourceRefresh(DtbAllUserListToSave);
                    }
                    else
                    {
                        this.Master.ShowPopup(false, "Hata", ErrorMessage, true, "Kayıt Eklenemedi!");
                        return;
                    }

                }
            }
            else
            {
                ErrorMessage = "Tek kişilik olarak belirlenmiş bir gruba Ad Grubu eklenemez !";
                this.Master.ShowPopup(false, "Hata", ErrorMessage, true, "Kayıt Eklenemedi!");
                return;
            }

        }

        protected void lnkButSilTumGrd_Click(object sender, EventArgs e)
        {
            LinkButton lnkButton = (LinkButton)sender;
            int index = (lnkButton.NamingContainer as DevExpress.Web.ASPxGridView.GridViewDataItemTemplateContainer).VisibleIndex;
            string guId = grdTumKaydedilecekler.GetRowValues(index, "GUID").ToString();
            string adGroup = grdTumKaydedilecekler.GetRowValues(index, "AD_GROUP").ToString();
            string adDomain = grdTumKaydedilecekler.GetRowValues(index, "AD_DOMAIN").ToString();
            RemoveRowFromDtbAllToSave(guId, adDomain, adGroup);
            GrdTumKaydedileceklerDataSourceRefresh(DtbAllUserListToSave);
        }

        private void RemoveRowFromDtbAllToSave(string guid, string adDomain, string adGroup)
        {

            if (DtbAllUserListToSave != null)
            {
                DataTable dtbTemp = SetDtbAllUserListToSave();
                if (!(string.IsNullOrEmpty(adDomain) && string.IsNullOrEmpty(adGroup)))
                {
                    string silmeSarti = "AD_DOMAIN = '" + adDomain + "'";
                    string silmeSarti2 = "AD_GROUP = '" + adGroup + "'";
                    string birlesikSart = $"{silmeSarti} AND {silmeSarti2}";
                    DataRow[] silinecekSatirlarAD = dtbTemp.Select(birlesikSart);
                    if (silinecekSatirlarAD.Length > 0)
                    {
                        foreach (DataRow row in silinecekSatirlarAD)
                        {
                            dtbTemp.Rows.Remove(row);
                        }
                    }
                }
                else
                {
                    dtbTemp.Rows.Remove(dtbTemp.AsEnumerable().Where(x => x["GUID"].ToString() == guid).FirstOrDefault());
                }

                dtbTemp.AcceptChanges();
                DtbAllUserListToSave = dtbTemp;
            }

        }

        private DataTable SetDtbAllUserListToSave()
        {
            DataTable dtb = BuildAllUserListDtbToSave();

            foreach (DataRow item in DtbAllUserListToSave.Rows)
            {
                DataRow dr = dtb.NewRow();
                dr["ID"] = item["ID"];
                dr["LOGICAL_GROUP_MEMBER_ID"] = item["LOGICAL_GROUP_MEMBER_ID"];
                dr["LOGICAL_GROUP_MEMBER_TYPE_ID"] = item["LOGICAL_GROUP_MEMBER_TYPE_ID"];
                dr["LOGICAL_GROUP_ID"] = item["LOGICAL_GROUP_ID"];
                dr["YYS_LG_AD_MEMBERS_ID"] = item["YYS_LG_AD_MEMBERS_ID"];
                dr["USERNAME"] = item["USERNAME"];
                dr["FULLNAME"] = item["FULLNAME"];
                dr["LOGIN_ID"] = item["LOGIN_ID"];
                dr["CONTENT"] = item["CONTENT"];
                dr["DESCRIPTION"] = item["DESCRIPTION"];
                dr["EMAIL"] = item["EMAIL"];
                dr["AD_DOMAIN"] = item["AD_DOMAIN"];
                dr["AD_GROUP"] = item["AD_GROUP"];
                dr["TIP"] = item["TIP"];
                dr["GUID"] = item["GUID"];

                dtb.Rows.Add(dr);
            }

            return dtb;
        }

        protected void btnKaydetGenel_Click(object sender, EventArgs e)
        {
            LogicalGroup lg = Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.Get(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]));
            long IsOnePerson = lg.IsOnePersonGroup;
            IslemYap(DtbAllUserListToSave, lg);
            ClearSelections();
            this.Master.ShowPopup(false, "Bilgi", "Kaydetme Başarılı!", false, "");
        }


        private void InsertLogicalGroupMembers(DataRow row, long adGroupMapId, long IsOnePersonGroup)
        {
            bool sonuc = true;
            string tip = row["LOGICAL_GROUP_MEMBER_TYPE_ID"].ToString();
            if (tip == "5")//ad kullanıcsı
            {
                if (String.IsNullOrEmpty(row["LOGIN_ID"].ToString()) || (row["LOGIN_ID"].ToString() == "0"))//ve logın ıd si yok
                {
                    sonuc = false;
                }
            }

            if (sonuc)
            {
                LogicalGroupMember lgm = new LogicalGroupMember(); //flogın yoksa ve ad kullanıcısysa ekleme
                lgm.FullName = row["FULLNAME"].ToString();
                lgm.LoginId = string.IsNullOrEmpty(row["LOGIN_ID"].ToString()) ? 0 : long.Parse(row["LOGIN_ID"].ToString());
                lgm.LogicalGroupMemberTypeId = long.Parse(row["LOGICAL_GROUP_MEMBER_TYPE_ID"].ToString());
                lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
                lgm.Content = row["CONTENT"].ToString();
                lgm.Description = row["DESCRIPTION"].ToString();
                lgm.Email = row["EMAIL"].ToString();
                lgm.Created = DateTime.Now;
                lgm.CreatedBy = UserInformation.LoginObject.LoginId;
                lgm.LastUpdated = DateTime.Now;
                lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
                lgm.YYS_LG_AD_MEMBERS_ID = row["LOGICAL_GROUP_MEMBER_TYPE_ID"].ToString() != "5" ? 0 : adGroupMapId;
                bool OnePersonCheck = IsOnePersonGroup == 1 && grdTumKaydedilecekler.VisibleRowCount == 1;
                LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
            }
        }


        public void SendUnDefinedLoginId()
        {
            DataView wv = DtbAllUserListToSave.DefaultView;
            wv.RowFilter = "(LOGICAL_GROUP_MEMBER_TYPE_ID=5 AND LOGIN_ID=0) OR (LOGICAL_GROUP_MEMBER_TYPE_ID=1 AND LOGIN_ID=0)";
            if (wv.Count > 0)
            {
                string FlowName = drpAkisListesi.SelectedItem.Text;
                string YYSGrupName = drpMantiksalGrupİsmi.SelectedItem.Text;
                string AdGrupName = drpDomain.SelectedItem.Text + "/" + drpAdGrubu.SelectedItem.Text;

                string HtmlTable = ConvertDataTableToHTML(wv.ToTable());
                SendMail("<EMAIL>", "<EMAIL>", "<EMAIL>", HtmlTable, "AD Portalde Olup FLogin Eşleşmeyen Kullanıcılar", FlowName, YYSGrupName, AdGrupName);
            }
            wv.RowFilter = "";
        }

        public static string ConvertDataTableToHTML(DataTable dt)
        {
            string html = "<table>";
            //add header row
            html += "<tr>";
            for (int i = 0; i < dt.Columns.Count; i++)
                html += "<td>" + dt.Columns[i].ColumnName + "</td>";
            html += "</tr>";
            //add rows
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                html += "<tr>";
                for (int j = 0; j < dt.Columns.Count; j++)
                    html += "<td>" + dt.Rows[i][j].ToString() + "</td>";
                html += "</tr>";
            }
            html += "</table>";
            return html;
        }

        public bool SendMail(string vfrom, string vTo, string vCC, string vBody, string vSubject, string FlowName, string YYSGrupName, string AdGrupName)
        {
            SmtpClient smtpClient = new SmtpClient();
            MailMessage message = new MailMessage();
            string bodyHtmlHeader = "";
            string bodyHtmlFooter = "";
            try
            {
                MailAddress fromAddress = new MailAddress(vfrom, "DIGIPORT");
                message.From = fromAddress;
                if (vCC != "")
                {
                    message.CC.Add(vCC);
                }
                message.To.Add(vTo);

                bodyHtmlHeader = "<html>";
                bodyHtmlHeader += "<head>";
                bodyHtmlHeader += "<meta http-equiv=Content-Type content=text/html; charset=windows-1254>";
                bodyHtmlHeader += "<style>TD{ font-family:verdana; font-size:9pt; }</style>";
                bodyHtmlHeader += "</head>";
                bodyHtmlHeader += "<body>";
                bodyHtmlHeader += "<table><tr><td><font face=verdana size=2>";
                bodyHtmlHeader += " Akışın Adı: " + FlowName + " <br>";
                bodyHtmlHeader += " YYS Grubun Adı: " + YYSGrupName + " <br>";
                bodyHtmlHeader += " AD Grubun Adı: " + AdGrupName + " <br>";

                bodyHtmlFooter = "</font></td></tr></table>";

                message.Subject = vSubject;
                message.IsBodyHtml = true;
                message.Body = bodyHtmlHeader + vBody + bodyHtmlFooter;
                smtpClient.Host = ConfigurationManager.AppSettings["smtpAdres"].ToString();
                //smtpClient.Host = "************";
                smtpClient.Send(message);
                return true;

            }
            catch (Exception ex)
            {
                string hata = ex.ToString();
                hata = hata.Substring(0, hata.Length);
                return false;
            }
        }


        private void IslemYap(DataTable dtbAllUserListToSave, LogicalGroup lg)
        {
            using (OracleConnection connection = Db.GetOracleConnection(ConnectionType.DefaultConnection))
            {
                connection.Open();
                using (OracleTransaction transaction = Db.GetTransaction_Oracle(connection))
                {
                    try
                    {
                        string adGroup = "";
                        string adDomain = "";
                        long adGroupMapId = 0;
                        long logicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);

                        #region mantıksal gruba ait tum kullanıcıları ve map tablosundakı ad grup map kayıtlarını siler
                        LogicalGroupMemberHelper.DeleteLogicalGroupMembersByLogicalGroupId(logicalGroupId);
                        LogicalGroupAdMapHelper.DeleteAllLogicalGroupMap(logicalGroupId);
                        #endregion mantıksal gruba ait tum kullanıcıları ve map tablosundakı ad grup map kayıtlarını siler

                        for (int i = 0; i < dtbAllUserListToSave.Rows.Count; i++)
                        {
                            if (dtbAllUserListToSave.Rows[i]["LOGICAL_GROUP_MEMBER_TYPE_ID"].ToString() == "5") //ad grup kullanıcısı ise map islemine sok
                            {
                                adDomain = dtbAllUserListToSave.Rows[i]["AD_DOMAIN"].ToString();
                                adGroup = dtbAllUserListToSave.Rows[i]["AD_GROUP"].ToString();

                                if (!LogicalGroupAdMapHelper.CheckAdGroupMapExistForLogicalGroup(logicalGroupId, adDomain, adGroup))//mapi varsa
                                {
                                    #region Yeni AD Grubu Ekleme Süreci
                                    adGroupMapId = LogicalGroupAdMapHelper.AddNewLogicalGroupMapMemberYeni(new LogicalGroupAdMap()
                                    {
                                        LogicalGroupId = logicalGroupId,
                                        AdDomain = adDomain,
                                        AdGroup = adGroup,
                                        LastSenkDate = DateTime.Now,
                                        Created = DateTime.Now,
                                        CreatedBy = 0
                                    });
                                    #endregion

                                    foreach (DataRow dr in dtbAllUserListToSave.Rows)
                                    {
                                        if (dr["AD_DOMAIN"].ToString() == adDomain && dr["AD_GROUP"].ToString() == adGroup)
                                        {
                                            InsertLogicalGroupMembers(dr, adGroupMapId, lg.IsOnePersonGroup);
                                        }
                                    }
                                }

                            }
                            else //ad grubu kullanıcısı degilse normal membersa ekle
                            {
                                InsertLogicalGroupMembers(dtbAllUserListToSave.Rows[i], adGroupMapId, lg.IsOnePersonGroup);
                            }
                        }

                        GrdTumKaydedileceklerDataSourceRefresh(GetAllLogicalGroupMembers());
                        Db.CommitTransaction_Oracle(transaction);
                        Db.CloseConnection(connection);
                        SendUnDefinedLoginId();
                    }
                    catch (Exception ex)
                    {
                        Db.RollbackTransaction_Oracle(transaction);
                        Db.CloseConnection(connection);
                        throw new Exception(ex.Message);
                    }
                }
            }
        }

        protected void drpAkisListesi_SelectedIndexChanged(object sender, EventArgs e)
        {
            drpMantiksalGrupİsmi = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownLogicalGroup("Name", "RequestId", drpMantiksalGrupİsmi, Digiturk.Workflow.Digiflow.YYS.Core.LogicalGroupHelper.GetAllByWorkflowId(ConvertionHelper.ConvertValue<long>(drpAkisListesi.SelectedItem.Value)), "Mantıksal Grup Seçiniz", "0");
            DtbAllUserListToSave = null;
            GrdTumKaydedileceklerDataSourceRefresh(DtbAllUserListToSave);
            txtMantiksalGrupAciklama.Text = drpMantiksalGrupİsmi.SelectedItem.Text;
            ClearSelections();
        }

        protected void drpMantiksalGrupİsmi_SelectedIndexChanged(object sender, EventArgs e)
        {
            Session["LogicalGroupId"] = drpMantiksalGrupİsmi.SelectedItem.Value;
            List<LogicalGroupMember> list = LogicalGroupMemberHelper.GetByLogicalGroupId(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"].ToString()));
            if (Session["LogicalGroupId"] != null)
            {
                #region Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur
                txtMantiksalGrupAciklama.Text = drpMantiksalGrupİsmi.SelectedItem.Text;
                txtMantiksalGrupAciklama.Enabled = false;
                GrdTumKaydedileceklerDataSourceRefresh(GetAllLogicalGroupMembers());
                ClearSelections();
                #endregion Mantıksal Grup Üyeleri Bilgileri Grid e doldurulur
            }
        }

        private void ClearSelections()
        {
            rdbListKullaniciTipleri.ClearSelection();
            drpKullanicilar.SelectedValue = "0";
            txtParamAciklama.Text = "";
            txtParamIcerik.Text = "";
            txtOutAdSoyad.Text = "";
            txtOutEmail.Text = "";
            drpAdGrubu.SelectedValue = "---Seçiniz---";
            pnlKullanici.Visible = false;
            pnlTumKullanıcılar.Visible = false;
            pnlParam.Visible = false;
            pnlOutsource.Visible = false;
            pnlAdGroupPart.Visible = false;
            grdAdGrupKullanicilari.DataSource = null;
            grdAdGrupKullanicilari.DataBind();
        }

        protected void btnExceleAktar_Click(object sender, EventArgs e)
        {
            XlsExportOptions opt2 = new XlsExportOptions();
            opt2.TextExportMode = TextExportMode.Text;
            grdTumKaydedilecekler.DataSource = DtbAllUserListToSave;
            grdTumKaydedilecekler.DataBind();

            grdTumKaydedilecekler.Columns["SİL"].Visible = false;
            grdExcelExporter.GridViewID = "grdTumKaydedilecekler";
            grdExcelExporter.WriteXlsToResponse(opt2);
        }
    }
}