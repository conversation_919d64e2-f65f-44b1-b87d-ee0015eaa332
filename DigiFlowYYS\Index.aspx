﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="Index.aspx.cs" Inherits="Index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table align="center">
        <tr>
            <td></td>
        </tr>
        <tr align="left" valign="middle" style="width: 100%; height: 500px">
            <td>

                <p>
                    Yet<PERSON> Yönetim <PERSON> (YYS), Digiflow sisteminin temel yapısını oluşturan bir
        bağlamdır. Bu bağlam içerisinde Mantıksal Grup tanımlaması,
        <br />
                    Sistem Yöneticisi ve Akış Yöneticisi tanımlanması ve bu kavramlar kullanılarak&nbsp;
        ak<PERSON><PERSON> düzeyinde belirli kurallar oluşturulabilir ve bu kurallar ile
        <br />
                    Digiflow sistemi giriş güvenliği sistemi oluşturulmuş olur.
                </p>
            </td>
        </tr>
    </table>
</asp:Content>