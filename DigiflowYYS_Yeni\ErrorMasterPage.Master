﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="ErrorMasterPage.Master.cs" Inherits="DigiflowYYS_Yeni.ErrorMasterPage" %>

<%@ Register Assembly="DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxMenu" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>DigiFlow</title>
    <script type="text/javascript" src="js/digiturk.js"></script>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
    <link href="css/digiturk.css" rel="stylesheet" type="text/css" />
    <link type="text/css" href="css/blitzer/jquery-ui-1.8.5.custom.css" rel="stylesheet" />
</head>
<body>
    <form id="form1" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server" EnableScriptGlobalization="true"
            EnableScriptLocalization="true">
        </asp:ScriptManager>
        <asp:UpdatePanel ID="MainUpdatePanel" runat="server">
            <ContentTemplate>
                <asp:Button runat="server" ID="HiddenForModal" Style="display: none" />
                <asp:ModalPopupExtender ID="Modal1" runat="server" TargetControlID="HiddenForModal"
                    PopupControlID="PopupPanel" BackgroundCssClass="modalBackground" OkControlID="Button1" />
                <table border="0" width="955" cellspacing="0" cellpadding="0" bgcolor="#000000" align="center">
                    <%--		            <tr>
			            <td align="left" width="100%" style="padding: 5px" colspan="2" bgcolor="#5C2D91">
                            <b><font color="#ffffff">Aktif Kullanıcı:</font></b><asp:Label ID="ActiveUserLabel" runat="server" Text="" ForeColor="White"></asp:Label></td>
		            </tr>--%>
                    <tr>
                        <td align="left" width="100%"
                            style="border-left: 1px solid #000000; border-right: 1px solid #000000;"
                            bgcolor="#FFFFFF" align="top">
                            <asp:Panel ID="TopMenuPanel" runat="server" Visible="True">
                                <table width="100%">
                                    <tr>
                                        <td align="left" width="100%" style="padding: 5px" colspan="2" bgcolor="#5C2D91">
                                            <b><font color="#ffffff" align="center">
                                                
                                                <dx:ASPxLabel ID="FullNameLabel"
                                                    runat="server" ForeColor="White">
                                                </dx:ASPxLabel>
                                                <dx:ASPxLabel ID="ASPxLabel1" runat="server" ForeColor="White" Text=" - ">
                                                </dx:ASPxLabel>
                                                <dx:ASPxLabel ID="AdminTypeLabel" runat="server" ForeColor="White">
                                                </dx:ASPxLabel>
                                            </font></b></td>
                                    </tr>
                                </table>

                                <dx:ASPxMenu ID="ASPxMenu1" runat="server" AutoSeparators="RootOnly" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css" Visible="false"
                                    CssPostfix="Office2010Silver" ShowPopOutImages="True" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                                    Width="100%">
                                    <Items>
                                        <dx:MenuItem NavigateUrl="#" Text="AnaSayfa">
                                        </dx:MenuItem>
                                        <dx:MenuItem Text="Akış Yöneticisi">
                                            <Items>
                                                <dx:MenuItem NavigateUrl="WorkflowAdmins.aspx"
                                                    Text="Akış Yöneticileri Görüntüleme ve Düzenleme">
                                                </dx:MenuItem>
                                                <dx:MenuItem NavigateUrl="AddUpdateWorkflowAdmin.aspx"
                                                    Text="Yeni Akış Yöneticisi Tanımlama">
                                                </dx:MenuItem>
                                            </Items>
                                        </dx:MenuItem>
                                        <dx:MenuItem Text="Mantıksal Gruplar">
                                            <Items>
                                                <dx:MenuItem NavigateUrl="LogicalGroups.aspx"
                                                    Text="Mantıksal Grupları Görüntüleme ve Düzenleme">
                                                </dx:MenuItem>
                                                <dx:MenuItem NavigateUrl="AddUpdateLogicalGroup.aspx"
                                                    Text="Yeni Mantıksal Grup Tanımlama">
                                                </dx:MenuItem>
                                            </Items>
                                        </dx:MenuItem>
                                        <dx:MenuItem Text="Akış Kuralları">
                                            <Items>
                                                <dx:MenuItem NavigateUrl="WorkflowRules.aspx"
                                                    Text="Akış Kuralları Görüntüleme ve Düzenleme">
                                                </dx:MenuItem>
                                                <dx:MenuItem NavigateUrl="AddUpdateWorkflowRule.aspx"
                                                    Text="Yeni Akış Kuralı Tanımlama">
                                                </dx:MenuItem>
                                            </Items>
                                        </dx:MenuItem>
                                        <dx:MenuItem
                                            Text="Statik Atama Kuralları">
                                            <Items>
                                                <dx:MenuItem NavigateUrl="~/StateAuthorizations.aspx"
                                                    Text="Statik Atama Kuralları Görüntüleme ve Düzenleme">
                                                </dx:MenuItem>
                                                <dx:MenuItem NavigateUrl="~/AddUpdateStateAuthorization.aspx"
                                                    Text="Yeni Statik Atama Tanımlama">
                                                </dx:MenuItem>
                                            </Items>
                                        </dx:MenuItem>
                                    </Items>
                                    <LoadingPanelImage Url="~/App_Themes/Office2010Silver/Web/Loading.gif">
                                    </LoadingPanelImage>
                                    <ItemSubMenuOffset FirstItemX="2" LastItemX="2" X="2" />
                                    <ItemSubMenuOffset FirstItemX="2" LastItemX="2" X="2" />
                                    <ItemSubMenuOffset FirstItemX="2" LastItemX="2" X="2" />
                                    <ItemStyle DropDownButtonSpacing="10px" PopOutImageSpacing="10px">
                                        <Paddings Padding="7px" />
                                        <Paddings Padding="7px" />
                                        <Paddings Padding="7px" />
                                    </ItemStyle>
                                    <LoadingPanelStyle ImageSpacing="5px">
                                    </LoadingPanelStyle>
                                    <SubMenuStyle GutterImageSpacing="9px" GutterWidth="13px" />
                                    <SubMenuStyle GutterImageSpacing="9px" GutterWidth="13px" />
                                    <SubMenuStyle GutterImageSpacing="9px" GutterWidth="13px" />
                                </dx:ASPxMenu>
                            </asp:Panel>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" width="100%"
                            style="border-left: 1px solid #000000; border-right: 1px solid #000000;"
                            bgcolor="#FFFFFF" align="top">
                            <asp:Label ID="PageTitleLabel" runat="server" CssClass="PageTitle"></asp:Label>
                            <asp:Literal ID="StatusLiteral" runat="server"></asp:Literal>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" width="100%" style="border-left: 1px solid #000000; border-right: 1px solid #000000;"
                            bgcolor="#FFFFFF" height="400" valign="top">
                            <asp:Panel ID="PopupPanel" runat="server" CssClass="modalPopup">
                                <asp:Literal ID="Literal1" runat="server"></asp:Literal>
                                <br />
                                <p align="center">
                                    <asp:LinkButton ID="Button1" runat="server">Kapat</asp:LinkButton>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:HyperLink ID="HyperLink1" runat="server"
                                    NavigateUrl="javascript:history.go(-1);" Visible="false">Geri Dön</asp:HyperLink>
                                </p>
                            </asp:Panel>
                            <div>
                                Test
                            <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                            </asp:ContentPlaceHolder>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align="center" bgcolor="#5C2D91">
                            <b><font color="#ffffff">Copyright© 2011 Digiturk İş Akışları Sistemi</font></b>
                        </td>
                    </tr>
                </table>
                <br />
                <asp:UpdateProgress ID="up2" runat="server" AssociatedUpdatePanelID="MainUpdatePanel"
                    DisplayAfter="100">
                    <ProgressTemplate>
                        <div id="progressBackgroundFilter"></div>
                        <div id="processMessage">
                            Lütfen bekleyin...<br />
                            <br />
                            <img alt="Lütfen bekleyin..." src="images/processing.gif" />
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
            </ContentTemplate>
        </asp:UpdatePanel>
    </form>
</body>
</html>