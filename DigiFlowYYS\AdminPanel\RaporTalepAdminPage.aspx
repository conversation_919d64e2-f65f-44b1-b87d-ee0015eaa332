﻿<%@ Page Title="" Language="C#" MasterPageFile="~/AdminPanel/AdminPanelMaster.master"
    AutoEventWireup="true" CodeFile="RaporTalepAdminPage.aspx.cs" Inherits="RaporTalepAdminPage" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:UpdatePanel ID="UpdatePanel1" runat="server">
        <ContentTemplate>
            <table align="center" width="400px">
                <tr>
                    <td colspan="2" class="PageTitle"><b>Rapor Talep Admin Ekranları - Uygulama Girişi</b></td>
                </tr>
                <tr>
                    <td colspan="2" class="">
                        <hr />
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Talep Tipi</b>
                    </td>
                    <td>
                        <asp:DropDownList ID="drpTalepTipi" runat="server" AutoPostBack="True"
                            OnSelectedIndexChanged="drpTalepTipi_SelectedIndexChanged">
                            <asp:ListItem>Rapor</asp:ListItem>
                            <asp:ListItem>Kurumsal Uygulama</asp:ListItem>
                        </asp:DropDownList>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Türü</b>
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdpTuru" runat="server" RepeatDirection="Horizontal" AutoPostBack="True"
                            OnSelectedIndexChanged="rdpTuru_SelectedIndexChanged">
                            <asp:ListItem Text="Yeni" Value="Yeni" Selected="True"></asp:ListItem>
                            <asp:ListItem Text="Hata" Value="Hata"></asp:ListItem>
                            <asp:ListItem Text="Revizyon" Value="Revizyon"></asp:ListItem>
                        </asp:RadioButtonList>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Mevcut Uygulamalar</b>
                    </td>
                    <td>
                        <asp:DropDownList ID="drpUygulamalar" runat="server" AutoPostBack="True" OnSelectedIndexChanged="drpUygulamalar_SelectedIndexChanged">
                        </asp:DropDownList>
                        <asp:HiddenField ID="HiddenField1" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Uygulama</b>
                    </td>
                    <td>
                        <asp:TextBox ID="txtUygulama" MaxLength="50" runat="server"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" ControlToValidate="txtUygulama"
                            runat="server" ErrorMessage="Lütfen uygulamayı giriniz !"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Listelerde Göster</b>
                    </td>
                    <td>
                        <asp:CheckBox ID="chkGoster" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td colspan="2"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" align="center">
                        <asp:Button ID="btnKaydet" runat="server" Text="Kaydet" OnClick="btnKaydet_Click" />&nbsp&nbsp&nbsp<asp:Button
                            ID="btnTemizle" runat="server" Text="Temizle" OnClick="btnTemizle_Click" />&nbsp&nbsp&nbsp<asp:Button
                                ID="btnSil" runat="server" Text="Sil" OnClick="btnSil_Click" OnClientClick="return confirm('Bu kayıt silinecek emin misiniz ?')" />
                    </td>
                </tr>
            </table>
        </ContentTemplate>
    </asp:UpdatePanel>
</asp:Content>