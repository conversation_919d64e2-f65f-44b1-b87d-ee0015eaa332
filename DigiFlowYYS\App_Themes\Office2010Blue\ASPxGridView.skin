<%@ Register TagPrefix="dx" Namespace="DevExpress.Data" Assembly="DevExpress.Data.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=B88D1754D700E49A" %>
<%@ Register TagPrefix="dxwgv" Namespace="DevExpress.Web.ASPxGridView" Assembly="DevExpress.Web.ASPxGridView.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>

<dxwgv:ASPxGridView runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css" CssPostfix="Office2010Blue">
	<Images SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css">
        <LoadingPanel Url="~/App_Themes/Office2010Blue/GridView/Loading.gif" />
        <LoadingPanelOnStatusBar Url="~/App_Themes/Office2010Blue/GridView/Loading.gif" />
	</Images>
    <ImagesFilterControl>
        <LoadingPanel Url="~/App_Themes/Office2010Blue/GridView/Loading.gif" />
    </ImagesFilterControl>
    <SettingsPager CurrentPageNumberFormat="{0}" />
    <Styles>
        <Header SortingImageSpacing="5" />
	    <LoadingPanel ImageSpacing="5" />
    </Styles>
    <StylesEditors ButtonEditCellSpacing="0">
        <ProgressBar Height="21" />
    </StylesEditors>
    <StylesPager>
        <PageNumber ForeColor="#3e4846" />
        <Summary ForeColor="#1e395b" />
    </StylesPager>
</dxwgv:ASPxGridView>