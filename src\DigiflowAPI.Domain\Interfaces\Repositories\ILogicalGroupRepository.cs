namespace DigiflowAPI.Domain.Interfaces.Repositories;

public interface ILogicalGroupRepository
{
    // TODO: Replace MemberInfoDto with domain entity or create a domain value object
    // Task<IEnumerable<MemberInfoDto>> GetPersonelList(long logicalGroupId);
    Task<bool> IsExistLogicalGroup(long logicalGroupId, long userId);
    Task<int> GetLogicalGroupID(string logicalGroupName);
    Task<bool> IsDefExistLogicalGroup(long logicalGroupId, long defId, string isAdmin);
}
