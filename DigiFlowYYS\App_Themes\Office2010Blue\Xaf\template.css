/* Common */
body {
    background-color: #D3E4F6;
    color: black;

    font-size: 8pt;
	padding: 0px;
	margin: 0px;
}
a {
    color: #1e395b;
    text-decoration: underline;
}
a:visited {
    color: #8467b2;
    text-decoration: underline;
}
div.PageContent {
    padding: 0px 0px 0px 0px;
}
/* Action Container Panel */
.ACPanel
{
	background-color: #DFEBF9;
	border-top: solid 1px #BBCFE5;
	padding: 2px 5px 2px 20px;
	height: 25px;
}
/* Main table */
.Main {
	background: white;
}
h1 {
	color: Black;

	font-size: 200%;
	font-family: Tahoma;
	font-weight: normal;
	margin: 0px 0px 0px 0px;
	padding: 0px;
}
/* Footer */
.FooterCopyright {
	font-family: Tahoma, Arial, Helvetica, sans-serif;
	font-size: 8pt;
	text-align: right;

	color: #7A818E;
}
/* Dialog */
body.Dialog {
	background-color: White !important;
}
div.DialogPageContent {
	margin: 0px;
}
div.Header {
	width: 100%;
	height: 80px;
	margin: 0px;
}
.DialogContent {
	background-color: White;

	height: 300px;
}
/* Error */
.Error {
	color: Black;
}
/* Links on dark background */
.Footer .menuLinks_Office2010Blue a,
.Footer .menuLinks_Office2010Blue a.dx,
.Security .menuLinks_Office2010Blue a,
.Security .menuLinks_Office2010Blue a.dx,
.TabsContainer .menuLinks_Office2010Blue a,
.TabsContainer .menuLinks_Office2010Blue a.dx,
.Footer .menuLinks_Office2010Blue a:visited,
.Footer .menuLinks_Office2010Blue a.dx:visited,
.Security .menuLinks_Office2010Blue a:visited,
.Security .menuLinks_Office2010Blue a.dx:visited,
.TabsContainer .menuLinks_Office2010Blue a:visited,
.TabsContainer .menuLinks_Office2010Blue a.dx:visited
{
}
/* Controls customization */
.dxnbControl_Office2010Blue,
.dxnbLite_Office2010Blue {
	width: 100% !important;
}
.dxgvControl_Office2010Blue {
	width: 100%;
}
.dxgvHeader_Office2010Blue td {
    white-space:normal;
}
.dxtcControl_Office2010Blue,
.dxtcLite_Office2010Blue {
    width: 100% !important;
}
.NavigationTabsActionContainer .dxtcControl_Office2010Blue,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue {
	background-color: #DFEBF9;
	border-top: solid 1px #BBCFE5;
}
.NavigationTabsActionContainer .dxtcPageContent_Office2010Blue,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-content {
	padding-left: 18px;
    border: none !important;
}
.NavigationTabsActionContainer .dxtcLeftIndentCell_Office2010Blue div,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-leftIndent {
	width: 15px !important;
}
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-tab,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-activeTab,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-leftIndent,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-spacer,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-rightIndent,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-sbWrapper,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-sbIndent,
.NavigationTabsActionContainer .dxtcLite_Office2010Blue .dxtc-sbSpacer
{
	height: 25px;
}
.ACV .dxmVerticalMenuItemWithImage_Office2010Blue,
.ACV .dxmLite_Office2010Blue .menuLinks_Office2010Blue .dxm-content,
.ACV .dxmLite_Office2010Blue .menuLinks_Office2010Blue .dxm-hovered .dxm-content,
.ACV .dxmLite_Office2010Blue .menuLinks_Office2010Blue .dxm-disabled .dxm-content
{
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}
.ACH .dxmLite_Office2010Blue .dxmtb .dxm-hasText,
.ToolBar .dxmLite_Office2010Blue .dxmtb .dxm-hasText
{
	padding-top: 5px !important;
	padding-bottom: 1px !important;
}
/* NavigationHistory (NavigationHistoryActionContainer) */
.NavigationHistoryLinks {
	color: #86888D;
	padding: 0px 0px 0px 2px;
}
.NavigationHistoryLinks a:hover {
	text-decoration: underline;
    color: #1e395b;
}
.NavigationHistoryLinks a, .NavigationHistoryLinks a:visited {
	text-decoration: underline;
    color: #1e395b;
}
.NavigationHistoryLinks a.Current, .NavigationHistoryLinks a.Current:hover, .NavigationHistoryLinks a.Current:visited {
	text-decoration: none;
	color: #86888D;
}
.Security .dxmMenuSeparator_Office2010Blue {
	padding: 0px 4px 0px 11px;
}
.TabsContainer .dxmMenuSeparator_Office2010Blue {
	padding: 0px 10px 0px 16px;
}
.LayoutTabContainerWithNestedFrame > .Item:first-child > .NestedFrame > .ToolBar .dxmMenu_Office2010Blue,
.LayoutTabContainerWithNestedFrame > .Item:first-child > .NestedFrame > .ToolBar .dxmLite_Office2010Blue .dxm-main {
	border-top: 0px;
}
.LayoutTabContainer > .Item > .NestedFrame > .ToolBar .dxmMenu_Office2010Blue,
.LayoutTabContainer > .Item > .NestedFrame > .ToolBar .dxmLite_Office2010Blue .dxm-main {
	border-bottom-width: 0px;
}
.LayoutTabContainerWithNestedFrame > .Item > .NestedFrame > .ToolBar .dxmMenu_Office2010Blue,
.LayoutTabContainerWithNestedFrame > .Item > .NestedFrame > .ToolBar .dxmLite_Office2010Blue .dxm-main
{
	border-left: 0px;
	border-right: 0px;
	border-bottom-width: 1px !important;
}
.Layout .TabControlContent {
	padding: 0px !important;
}
.Layout .TabControlContent > div {
	padding: 0px !important;
}
.HorizontalTemplateHeader,
.VerticalTemplateHeader,
.Header {
	background-color: #D3E4F6;
}
.RecordsNavigationContainer .menuLinks_Office2010Blue {
	padding-left: 8px;
}
.RecordsNavigationContainer .menuLinks_Office2010Blue .dxmMenuItemWithImage_Office2010Blue {
	padding-left: 3px;
}