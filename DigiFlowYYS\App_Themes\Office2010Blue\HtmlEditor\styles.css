/* ----------------- Main ----------------- */
/* Loading panel */
.dxheLoadingDiv_Office2010Blue
{
    background: White;
    opacity: 0.85;
    filter: alpha(opacity=85);
    cursor: wait;
}
.dxheLoadingPanel_Office2010Blue
{
    font: 8pt Verdana;
    color: #1e395b;
}
.dxheLoadingPanel_Office2010Blue td.dx
{
    white-space: nowrap;
    text-align: center;
    padding: 10px 14px;
}

.dxheControl_Office2010Blue
{
    border: 1px solid #8ba0bc;
}

/* Area */
.dxheContentArea_Office2010Blue
{
    padding: 0;
    background-color: #cfddee;
}
.dxheContentArea_Office2010Blue .dxheErrorFrame_Office2010Blue
{
    font-size: 8pt;
    font-family: Verdana;
    color: #D00707;
    background-color: #FBC7C7;
    border-bottom: solid 1px #DEC0C0;
}
.dxheContentArea_Office2010Blue .dxheErrorFrame_Office2010Blue .dxhe {
    padding: 5px;
}
.dxheContentArea_Office2010Blue .dxheErrorFrame_Office2010Blue .dxhe td {
    padding: 0px;
}
.dxheContentArea_Office2010Blue .dxheErrorFrame_Office2010Blue .dxheErrorFrameCloseButton_Office2010Blue {
    cursor: pointer;
}
.dxheViewArea_Office2010Blue,
.dxheViewArea_Office2010Blue.dxeMemo_Office2010Blue
{
    border-width: 0;
}
.dxheHtmlViewArea_Office2010Blue,
.dxheDesignViewArea_Office2010Blue,
.dxhePreviewArea_Office2010Blue
{
    margin: 0px;
    background-color: White;
    background-image: none;
    text-align: left;
    font-size: smaller;
    font: normal 12px Arial;
}
.dxheHtmlViewArea_Office2010Blue td,
.dxheHtmlViewArea_Office2010Blue.dxeMemo_Office2010Blue td,
body.dxheDesignViewArea_Office2010Blue,
body.dxhePreviewArea_Office2010Blue
{
    padding: 4px 0 0 4px;
}
/* Element appearance in DesignView */
body.dxheDesignViewArea_Office2010Blue table.dxEmptyBorderTable_Office2010Blue,
body.dxheDesignViewArea_Office2010Blue table.dxEmptyBorderTable_Office2010Blue td
{
    border: 1px dotted Gray;
}
@media print
{
	body.dxheDesignViewArea_Office2010Blue table.dxEmptyBorderTable_Office2010Blue,
	body.dxheDesignViewArea_Office2010Blue table.dxEmptyBorderTable_Office2010Blue td
	{
		border:0px;
	}
}

/* Status Bar */
.dxheStatusBar_Office2010Blue
{
}
.dxheStatusBar_Office2010Blue .dxheStatusBarTab_Office2010Blue
{
    padding-left: 34px;
    padding-right: 35px;
    padding-bottom: 5px;
    border-bottom-width: 0;
    border-left-width: 0;
}
.dxheStatusBarActiveTab_Office2010Blue
{
}
.dxHtmlEditor_heSizeGrip_Office2010Blue
{
    cursor: se-resize;
}
.dxheSizeGripContainer_Office2010Blue
{
    float: left;
    height: 0;
    width: 100%;
    text-align: right;
    font-size: 0;
    margin-top: -13px;
}
/* ----------------- Dialog Forms ----------------- */
#dxInsertTableForm .buttonsCell,
#dxInsertLinkForm .buttonsCell,
#dxInsertImageForm .buttonsCell,
#dxPasteFromWordForm .buttonsCell,
#dxTableColumnPropertiesForm .buttonsCell,
.dxheCustomDialog_Office2010Blue .dxhecd-Buttons
{
    background-color: #cfddee;
    border-top: 1px solid #aec0d5;
    padding: 11px;
}
.dxheCustomDialog_Office2010Blue .dxhecd-Buttons
{
	text-align: right;
}
#dxInsertTableForm .captionIndent,
#dxInsertLinkForm .captionIndent,
#dxInsertImageForm .captionIndent,
#dxTableColumnPropertiesForm .captionIndent
{
	overflow: hidden;
	height: 5px;
}
#dxInsertTableForm .contentCell,
#dxInsertLinkForm .contentCell,
#dxInsertImageForm .contentCell,
#dxPasteFromWordForm .contentCell,
#dxTableColumnPropertiesForm .contentCell,
.dxheCustomDialog_Office2010Blue .dxhecd-Content
{
	padding: 11px;
}
#dxInsertLinkForm .typeRadionButtonListCell,
#dxInsertImageForm .typeRadionButtonListCell
{
	padding-bottom: 12px;
}
#dxInsertTableForm .separatorCell,
#dxInsertLinkForm .separatorCell,
#dxInsertImageForm .separatorCell,
#dxTableColumnPropertiesForm .separatorCell
{
	height: 7px;
}
#dxInsertTableForm .buttons,
#dxInsertLinkForm .buttons,
#dxInsertImageForm .buttons,
#dxTableColumnPropertiesForm .buttons
{
    padding-top: 3px;
}
#dxInsertTableForm .cancelButton,
#dxInsertLinkForm .cancelButton,
#dxInsertImageForm .cancelButton,
#dxPasteFromWordForm .cancelButton,
#dxTableColumnPropertiesForm .cancelButton
{
    padding-left: 10px;
}
.dxheCustomDialog_Office2010Blue .dxhecd-Cancel
{
	margin-left: 10px;
}
#dxInsertTableForm .captionCell,
#dxInsertTableForm .rowsCaptionCell,
#dxInsertLinkForm .captionCell,
#dxTableColumnPropertiesForm .captionCell,
#dxTableColumnPropertiesForm .rowsCaptionCell
{
    padding-right: 9px;
    padding-top: 3px;
    vertical-align: top;
    white-space: nowrap;
}
#dxInsertTableForm .inputCell,
#dxInsertTableForm .rowsInputCell,
#dxInsertLinkForm .inputCell,
#dxTableColumnPropertiesForm .inputCell,
#dxTableColumnPropertiesForm .rowsInputCell
{
    vertical-align: middle;
}

/* Insert Link Form */
#dxInsertLinkForm .displayPropertiesCell
{
    font-weight: bold;
    padding-top: 10px;
    padding-bottom: 10px;
}
#dxInsertLinkForm .targetCheckBoxCell
{
    padding-top: 10px;
}
/* Insert Image Form */
#dxInsertImageForm .fieldSeparator
{
    height: 9px;
}
#dxInsertImageForm .imagePreview
{
    padding: 10px 0;
    padding-top: 5px;
}
#dxInsertImageForm .fromTheWeb
{
    width: 100%;
}
#dxInsertImageForm .imagePreviewCell
{
    color: #737373;
    border: 1px dashed #cac8c8;
    text-align: center;
    width: 100%;
    height: 170px;
}
#dxInsertImageForm .imagePropertiesCell
{
    padding-left: 20px;
    vertical-align: top;
}
#dxInsertImageForm .moreOptionsCell
{
	padding-top: 11px;
}
#dxInsertImageForm .radioButtonTable
{
	width: 317px;
}
#dxInsertImageForm .saveToServerCheckBoxCell
{
    padding-top: 0;
    padding-bottom: 13px;
}
/* Image Properties Form */
#dxImagePropertiesForm .captionCell
{
    padding-right: 2px;
    white-space: nowrap;
}
#dxImagePropertiesForm .constrainProportionsCell
{
    padding-left: 4px;
    padding-top: 2px;
}
#dxImagePropertiesForm .imageSizeEditorsCell
{
    padding-top: 7px;
    padding-bottom: 10px;
}
#dxImagePropertiesForm .pixelSizeCell
{
    padding-left: 7px;
}
#dxImagePropertiesForm .hSeparator
{
    width: 25px;
    overflow: hidden;
}
#dxImagePropertiesForm .thumbnailFileNameArea
{
    padding-top: 8px;
}
/* IE, FireFox, WebKit*/
#dxImagePropertiesForm .ckbWrapTextCell div
{
	margin-left: -4px;
	padding-top: 2px;
}
/* Opera hack */
@media not all and (-webkit-min-device-pixel-ratio) {
	#dxImagePropertiesForm .ckbWrapTextCell div
	{
		margin-left: 0px;
	}
}

/* PasteFromWord Form */
#dxPasteFromWordForm .pasteContainer
{
	width: 450px;
	height: 300px;
	border: none;
	background-color: white;
}
#dxPasteFromWordForm .pasteContainerCell
{
	padding: 0px;
	padding-top: 10px;
}
#dxPasteFromWordForm .pasteContainerCell td
{
	border: 1px solid #8ba0bc;
}
#dxPasteFromWordForm .checkBoxCell
{
	padding-top: 10px;
}
#dxPasteFromWordForm .contentCell
{
	padding-left: 20px;
	padding-right: 20px;
}

/* Insert table */
#dxInsertTableForm .propFieldSeparator,
#dxTableColumnPropertiesForm .propFieldSeparator
{
    width: 20px;
}
#dxInsertTableForm .propGroupSeparator,
#dxTableColumnPropertiesForm .propGroupSeparator
{
    height: 11px;
}
#dxInsertTableForm .propGroupCell,
#dxInsertTableForm .accessibilityPropGroupCell,
#dxTableColumnPropertiesForm .propGroupCell,
#dxTableColumnPropertiesForm .accessibilityPropGroupCell
{
    font-weight: bold;
    padding-bottom: 7px;
}
#dxInsertTableForm .accessibilityPropGroupCell,
#dxTableColumnPropertiesForm accessibilityPropGroupCell
{
    padding-left: 10px;
}
#dxInsertTableForm .propGroupContentCell,
#dxTableColumnPropertiesForm .propGroupContentCell
{
    padding-left: 20px;
}
#dxInsertTableForm .sizeTypeCell,
#dxTableColumnPropertiesForm .sizeTypeCell
{
    padding-left: 3px;
}
#dxInsertTableForm .rowsInputCell,
#dxTableColumnPropertiesForm .rowsInputCell
{
    padding-left: 9px;
}
#dxInsertTableForm .rowsCaptionCell,
#dxTableColumnPropertiesForm .rowsCaptionCell
{
    padding-right: 0px;
}
#dxInsertTableForm .rowsSeparator,
#dxTableColumnPropertiesForm .rowsSeparator
{
    height: 18px;
}
#dxInsertTableForm .rowsHorizontalSeparator,
#dxTableColumnPropertiesForm .rowsHorizontalSeparator
{
    width: 50px;
}
/*----------------- Toolbars -----------------*/
.dxtbSpacing_Office2010Blue
{
	height: 1px;
}
.dxtbControl_Office2010Blue
{
    font: 8pt Verdana;
    color: Black;
    background-color: #cfddee;
    width: 100%;
    border-bottom: 1px solid #859ebf;
}
.dxtbControl_Office2010Blue td.dxmMenu_Office2010Blue
{
	border: 0;
}
.dxtbComboBoxMenuItem_Office2010Blue
{
    padding: 2px 2px 1px;
}

/* Toolbars Lightweight Mode */
.dxtbControl_Office2010Blue .dxmLite_Office2010Blue .dxm-main
{
    border-width: 0;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem
{
	border-width: 0;
	padding: 2px 2px 1px !important;
}
.dxmLite_Office2010Blue .dxhetipControl_Office2010Blue img
{
	vertical-align: top;
}
.dxtbControl_Office2010Blue .dxmLite_Office2010Blue .dxm-horizontal .dxm-item .dxm-content .dxm-image {
	margin: 0px;
}

/* ToolbarColorButton */
.dxtcbControl_Office2010Blue
{
}
.dxtcbColorDiv_Office2010Blue
{
	width: 16px;
	height: 3px;
	font-size: 0pt;
	background-color: Transparent;
}

/*----------------- RoundPanel -----------------*/
.dxheRP.dxrpControl_Office2010Blue .dxrpcontent
{
    padding: 9px 8px;
}

/*----------------- UploadControl -----------------*/
.dxheucControl_Office2010Blue,
.dxheucEditArea_Office2010Blue
{
    font: 8pt Verdana;
}
.dxheucErrorCell_Office2010Blue
{
    padding-left: 4px;
    font: 8pt Verdana;
    color: Red;
    text-align: left;
}