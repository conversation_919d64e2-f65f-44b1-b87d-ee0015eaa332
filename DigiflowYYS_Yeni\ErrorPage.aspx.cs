﻿using Digiturk.Workflow.Digiflow.WebCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace DigiflowYYS_Yeni
{
    public partial class ErrorPage : YYSSecurePage
    {   /// <summary>
        /// Herhangi bir Say<PERSON> hata aldığında yönlendirilecek ekran
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!Page.IsPostBack)
            {
                if (Session[SessionErrorTitleVariable] != null)
                {
                    lblTitle.Text = Session[SessionErrorTitleVariable].ToString();
                    lblError.Text = Session[SessionErrorMessageVariable].ToString();
                    if (Session[SessionExceptionInstanceVariable] != null)
                    {
                        lblContent.Text = Session[SessionExceptionInstanceVariable].ToString();
                    }
                    else
                    {
                        lblContent.Text = "";
                    }
                }
                else
                {
                    Response.Write(Server.GetLastError().ToString());
                    Response.End();
                }
            }
        }
    }
}