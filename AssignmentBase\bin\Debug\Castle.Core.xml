<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Castle.Core</name>
    </assembly>
    <members>
        <member name="T:Castle.Core.CastleComponentAttribute">
            <summary>
            This attribute is usefull only when you want to register all components
            on an assembly as a batch process. 
            By doing so, the batch register will look 
            for this attribute to distinguish components from other classes.
            </summary>
        </member>
        <member name="T:Castle.Core.LifestyleAttribute">
            <summary>
            Base for Attributes that want to express lifestyle
            chosen by the component.
            </summary>
        </member>
        <member name="M:Castle.Core.LifestyleAttribute.#ctor(Castle.Core.LifestyleType)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.LifestyleAttribute"/> class.
            </summary>
            <param name="type">The type.</param>
        </member>
        <member name="P:Castle.Core.LifestyleAttribute.Lifestyle">
            <summary>
            Gets or sets the lifestyle.
            </summary>
            <value>The lifestyle.</value>
        </member>
        <member name="M:Castle.Core.CastleComponentAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.CastleComponentAttribute"/> class.
            </summary>
            <param name="key">The key.</param>
        </member>
        <member name="M:Castle.Core.CastleComponentAttribute.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.CastleComponentAttribute"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="service">The service.</param>
        </member>
        <member name="M:Castle.Core.CastleComponentAttribute.#ctor(System.String,System.Type,Castle.Core.LifestyleType)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.CastleComponentAttribute"/> class.
            </summary>
            <param name="key">The key.</param>
            <param name="service">The service.</param>
            <param name="lifestyle">The lifestyle.</param>
        </member>
        <member name="P:Castle.Core.CastleComponentAttribute.Service">
            <summary>
            Gets the service.
            </summary>
            <value>The service.</value>
        </member>
        <member name="P:Castle.Core.CastleComponentAttribute.Key">
            <summary>
            Gets the key.
            </summary>
            <value>The key.</value>
        </member>
        <member name="T:Castle.Core.ComponentActivatorAttribute">
            <summary>
            Associates a custom component with a component
            </summary>
        </member>
        <member name="M:Castle.Core.ComponentActivatorAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.ComponentActivatorAttribute"/> class.
            </summary>
            <param name="componentActivatorType">Type of the component activator.</param>
        </member>
        <member name="P:Castle.Core.ComponentActivatorAttribute.ComponentActivatorType">
            <summary>
            Gets the type of the component activator.
            </summary>
            <value>The type of the component activator.</value>
        </member>
        <member name="T:Castle.Core.ComponentProxyBehaviorAttribute">
            <summary>
            Specifies the proxying behavior for a component.
            </summary>
        </member>
        <member name="M:Castle.Core.ComponentProxyBehaviorAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.ComponentProxyBehaviorAttribute"/> class.
            </summary>
        </member>
        <member name="P:Castle.Core.ComponentProxyBehaviorAttribute.UseMarshalByRefProxy">
            <summary>
            Gets or sets a value indicating whether the generated 
            interface proxy should inherit from <see cref="T:System.MarshalByRefObject"/>.
            </summary>
        </member>
        <member name="P:Castle.Core.ComponentProxyBehaviorAttribute.UseSingleInterfaceProxy">
            <summary>
            Determines if the component requires a single interface proxy.
            </summary>
            <value><c>true</c> if the component requires a single interface proxy.</value>
        </member>
        <member name="P:Castle.Core.ComponentProxyBehaviorAttribute.AdditionalInterfaces">
            <summary>
             Gets or sets the additional interfaces used during proxy generation.
            </summary>
        </member>
        <member name="T:Castle.Core.DoNotWireAttribute">
            <summary>
            Marks as property to be skipped and not be wired
            by the IoC container
            </summary>
        </member>
        <member name="T:Castle.Core.InterceptorAttribute">
            <summary>
            Used to declare that a component wants interceptors acting on it.
            </summary>
        </member>
        <member name="M:Castle.Core.InterceptorAttribute.#ctor(System.String)">
            <summary>
            Constructs the InterceptorAttribute pointing to
            a key to a interceptor
            </summary>
            <param name="componentKey"></param>
        </member>
        <member name="M:Castle.Core.InterceptorAttribute.#ctor(System.Type)">
            <summary>
            Constructs the InterceptorAttribute pointing to
            a service
            </summary>
            <param name="interceptorType"></param>
        </member>
        <member name="T:Castle.Core.SingletonAttribute">
            <summary>
            Indicates that the target components wants a
            singleton lifestyle.
            </summary>
        </member>
        <member name="M:Castle.Core.SingletonAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.SingletonAttribute"/> class.
            </summary>
        </member>
        <member name="T:Castle.Core.TransientAttribute">
            <summary>
            Indicates that the target components wants a
            transient lifestyle.
            </summary>
        </member>
        <member name="M:Castle.Core.TransientAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.TransientAttribute"/> class.
            </summary>
        </member>
        <member name="T:Castle.Core.PerThreadAttribute">
            <summary>
            Indicates that the target components wants a
            per thread lifestyle.
            </summary>
        </member>
        <member name="M:Castle.Core.PerThreadAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.PerThreadAttribute"/> class.
            </summary>
        </member>
        <member name="T:Castle.Core.PerWebRequestAttribute">
            <summary>
            Indicates that the target components wants a
            per web request lifestyle.
            </summary>
        </member>
        <member name="T:Castle.Core.PooledAttribute">
            <summary>
            Indicates that the target components wants a
            pooled lifestyle.
            </summary>
        </member>
        <member name="M:Castle.Core.PooledAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.PooledAttribute"/> class
            using the default initial pool size (5) and the max pool size (15).
            </summary>
        </member>
        <member name="M:Castle.Core.PooledAttribute.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.PooledAttribute"/> class.
            </summary>
            <param name="initialPoolSize">Initial size of the pool.</param>
            <param name="maxPoolSize">Max pool size.</param>
        </member>
        <member name="P:Castle.Core.PooledAttribute.InitialPoolSize">
            <summary>
            Gets the initial size of the pool.
            </summary>
            <value>The initial size of the pool.</value>
        </member>
        <member name="P:Castle.Core.PooledAttribute.MaxPoolSize">
            <summary>
            Gets the maximum pool size.
            </summary>
            <value>The size of the max pool.</value>
        </member>
        <member name="T:Castle.Core.CustomLifestyleAttribute">
            <summary>
            Indicates that the target components wants a
            custom lifestyle.
            </summary>
        </member>
        <member name="M:Castle.Core.CustomLifestyleAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.CustomLifestyleAttribute"/> class.
            </summary>
            <param name="lifestyleHandlerType">The lifestyle handler.</param>
        </member>
        <member name="P:Castle.Core.CustomLifestyleAttribute.LifestyleHandlerType">
            <summary>
            Gets the type of the lifestyle handler.
            </summary>
            <value>The type of the lifestyle handler.</value>
        </member>
        <member name="T:Castle.DynamicProxy.IAttributeDisassembler">
            <summary>
            Provides functionality for disassembling instances of attributes to CustomAttributeBuilder form, during the process of emiting new types by Dynamic Proxy.
            </summary>
        </member>
        <member name="M:Castle.DynamicProxy.IAttributeDisassembler.Disassemble(System.Attribute)">
            <summary>
            Disassembles given attribute instance back to corresponding CustomAttributeBuilder.
            </summary>
            <param name="attribute">An instance of attribute to disassemble</param>
            <returns><see cref="T:System.Reflection.Emit.CustomAttributeBuilder"/> corresponding 1 to 1 to given attribute instance, or null reference.</returns>
            <remarks>
            Implementers should return <see cref="T:System.Reflection.Emit.CustomAttributeBuilder"/> that corresponds to given attribute instance 1 to 1,
            that is after calling specified constructor with specified arguments, and setting specified properties and fields with values specified
            we should be able to get an attribute instance identical to the one passed in <paramref name="attribute"/>. Implementer can return null
            if it wishes to opt out of replicating the attribute. Notice however, that for some cases, like attributes passed explicitly by the user
            it is illegal to return null, and doing so will result in exception.
            </remarks>
        </member>
        <member name="T:Castle.Core.Interceptor.IChangeProxyTarget">
            <summary>
            Exposes means to change target objects of proxies and invocations
            </summary>
        </member>
        <member name="M:Castle.Core.Interceptor.IChangeProxyTarget.ChangeInvocationTarget(System.Object)">
            <summary>
            Changes the target object (<see cref="P:Castle.Core.Interceptor.IInvocation.InvocationTarget"/>) of current <see cref="T:Castle.Core.Interceptor.IInvocation"/>.
            </summary>
            <param name="target">The new value of target of invocation.</param>
            <remarks>
            Although the method takes <see cref="T:System.Object"/> the actual instance must be of type assignable to <see cref="P:Castle.Core.Interceptor.IInvocation.TargetType"/>, otherwise an <see cref="T:System.InvalidCastException"/> will be thrown.
            Also while it's technically legal to pass null reference (Nothing in Visual Basic) as <paramref name="target"/>, for obvious reasons Dynamic Proxy will not be able to call the intercepted method on such target.
            In this case last interceptor in the pipeline mustn't call <see cref="M:Castle.Core.Interceptor.IInvocation.Proceed"/> or a <see cref="T:System.NotImplementedException"/> will be throws.
            Also while it's technically legal to pass proxy itself as <paramref name="target"/>, this would create stack overflow.
            In this case last interceptor in the pipeline mustn't call <see cref="M:Castle.Core.Interceptor.IInvocation.Proceed"/> or a <see cref="T:System.InvalidOperationException"/> will be throws.
            </remarks>
            <exception cref="T:System.InvalidCastException">Thrown when <paramref name="target"/> is not assignable to the proxied type.</exception>
        </member>
        <member name="M:Castle.Core.Interceptor.IChangeProxyTarget.ChangeProxyTarget(System.Object)">
            <summary>
            Permanently changes the target object of the proxy. This does not affect target of the current invocation.
            </summary>
            <param name="target">The new value of target of the proxy.</param>
            <remarks>
            Although the method takes <see cref="T:System.Object"/> the actual instance must be of type assignable to proxy's target type, otherwise an <see cref="T:System.InvalidCastException"/> will be thrown.
            Also while it's technically legal to pass null reference (Nothing in Visual Basic) as <paramref name="target"/>, for obvious reasons Dynamic Proxy will not be able to call the intercepted method on such target.
            In this case last interceptor in the pipeline mustn't call <see cref="M:Castle.Core.Interceptor.IInvocation.Proceed"/> or a <see cref="T:System.NotImplementedException"/> will be throws.
            Also while it's technically legal to pass proxy itself as <paramref name="target"/>, this would create stack overflow.
            In this case last interceptor in the pipeline mustn't call <see cref="M:Castle.Core.Interceptor.IInvocation.Proceed"/> or a <see cref="T:System.InvalidOperationException"/> will be throws.
            </remarks>
            <exception cref="T:System.InvalidCastException">Thrown when <paramref name="target"/> is not assignable to the proxied type.</exception>
        </member>
        <member name="T:Castle.Core.Interceptor.IInterceptor">
            <summary>
            New interface that is going to be used by DynamicProxy 2
            </summary>
        </member>
        <member name="T:Castle.Core.Interceptor.IInterceptorSelector">
            <summary>
            Provides an extension point that allows proxies to choose specific interceptors on
            a per method basis.
            </summary>
        </member>
        <member name="M:Castle.Core.Interceptor.IInterceptorSelector.SelectInterceptors(System.Type,System.Reflection.MethodInfo,Castle.Core.Interceptor.IInterceptor[])">
            <summary>
            Selects the interceptors that should intercept calls to the given <paramref name="method"/>.
            </summary>
            <param name="type">The type declaring the method to intercept.</param>
            <param name="method">The method that will be intercepted.</param>
            <param name="interceptors">All interceptors registered with the proxy.</param>
            <returns>An array of interceptors to invoke upon calling the <paramref name="method"/>.</returns>
            <remarks>
            This method is called only once per proxy instance, upon the first call to the
            <paramref name="method"/>. Either an empty array or null are valid return values to indicate
            that no interceptor should intercept calls to the method. Although it is not advised, it is
            legal to return other <see cref="T:Castle.Core.Interceptor.IInterceptor"/> implementations than these provided in
            <paramref name="interceptors"/>.
            </remarks>
        </member>
        <member name="T:Castle.Core.Interceptor.IInvocation">
            <summary>
            Encapsulates an invocation of a proxied method.
            </summary>
        </member>
        <member name="M:Castle.Core.Interceptor.IInvocation.SetArgumentValue(System.Int32,System.Object)">
            <summary>
            Overrides the value of an argument at the given <paramref name="index"/> with the
            new <paramref name="value"/> provided.
            </summary>
            <remarks>
            This method accepts an <see cref="T:System.Object"/>, however the value provided must be compatible
            with the type of the argument defined on the method, otherwise an exception will be thrown.
            </remarks>
            <param name="index">The index of the argument to override.</param>
            <param name="value">The new value for the argument.</param>
        </member>
        <member name="M:Castle.Core.Interceptor.IInvocation.GetArgumentValue(System.Int32)">
            <summary>
            Gets the value of the argument at the specified <paramref name="index"/>.
            </summary>
            <param name="index">The index.</param>
            <returns>The value of the argument at the specified <paramref name="index"/>.</returns>
        </member>
        <member name="M:Castle.Core.Interceptor.IInvocation.GetConcreteMethod">
            <summary>
            Returns the concrete instantiation of the <see cref="P:Castle.Core.Interceptor.IInvocation.Method"/>, with any generic
            parameters bound to real types.
            </summary>
            <returns>
            The concrete instantiation of the <see cref="P:Castle.Core.Interceptor.IInvocation.Method"/>, or the <see cref="P:Castle.Core.Interceptor.IInvocation.Method"/> if
            not a generic method.
            </returns>
            <remarks>Can be slower than calling <see cref="P:Castle.Core.Interceptor.IInvocation.Method"/>.</remarks>
        </member>
        <member name="M:Castle.Core.Interceptor.IInvocation.GetConcreteMethodInvocationTarget">
            <summary>
            Returns the concrete instantiation of <see cref="P:Castle.Core.Interceptor.IInvocation.MethodInvocationTarget"/>, with any
            generic parameters bound to real types.
            </summary>
            <returns>The concrete instantiation of <see cref="P:Castle.Core.Interceptor.IInvocation.MethodInvocationTarget"/>, or
            <see cref="P:Castle.Core.Interceptor.IInvocation.MethodInvocationTarget"/> if not a generic method.</returns>
            <remarks>Can be slower than calling <see cref="P:Castle.Core.Interceptor.IInvocation.MethodInvocationTarget"/>.</remarks>
        </member>
        <member name="M:Castle.Core.Interceptor.IInvocation.Proceed">
            <summary>
            Proceeds the call to the next interceptor in line, and ultimately to the target method.
            </summary>
            <remarks>
            Since interface proxies without a target don't have the target implementation to proceed to,
            it is important, that the last interceptor does not call this method, otherwise a
            <see cref="T:System.NotImplementedException"/> will be thrown.
            </remarks>
        </member>
        <member name="P:Castle.Core.Interceptor.IInvocation.Proxy">
            <summary>
            Gets the proxy object on which the intercepted method is invoked.
            </summary>
            <value>Proxy object on which the intercepted method is invoked.</value>
        </member>
        <member name="P:Castle.Core.Interceptor.IInvocation.InvocationTarget">
            <summary>
            Gets the object on which the invocation is performed. This is different from proxy object
            because most of the time this will be the proxy target object.
            </summary>
            <seealso cref="T:Castle.Core.Interceptor.IChangeProxyTarget"/>
            <value>The invocation target.</value>
        </member>
        <member name="P:Castle.Core.Interceptor.IInvocation.TargetType">
            <summary>
            Gets the type of the target object for the intercepted method.
            </summary>
            <value>The type of the target object.</value>
        </member>
        <member name="P:Castle.Core.Interceptor.IInvocation.Arguments">
            <summary>
            Gets the arguments that the <see cref="P:Castle.Core.Interceptor.IInvocation.Method"/> has been invoked with.
            </summary>
            <value>The arguments the method was invoked with.</value>
        </member>
        <member name="P:Castle.Core.Interceptor.IInvocation.GenericArguments">
            <summary>
            Gets the generic arguments of the method.
            </summary>
            <value>The generic arguments, or null if not a generic method.</value>
        </member>
        <member name="P:Castle.Core.Interceptor.IInvocation.Method">
            <summary>
            Gets the <see cref="T:System.Reflection.MethodInfo"/> representing the method being invoked.
            </summary>
            <value>The <see cref="T:System.Reflection.MethodInfo"/> representing the method being invoked.</value>
        </member>
        <member name="P:Castle.Core.Interceptor.IInvocation.MethodInvocationTarget">
            <summary>
            For interface proxies, this will point to the <see cref="T:System.Reflection.MethodInfo"/> on the target class.
            </summary>
            <value>The method invocation target.</value>
        </member>
        <member name="P:Castle.Core.Interceptor.IInvocation.ReturnValue">
            <summary>
            Gets or sets the return value of the method.
            </summary>
            <value>The return value of the method.</value>
        </member>
        <member name="T:Castle.Core.Interceptor.IOnBehalfAware">
            <summary>
            Interceptors might implement this to receive the
            ComponentModel on behalf of the component where the
            interceptor is acting.
            </summary>
        </member>
        <member name="M:Castle.Core.Interceptor.IProxyTargetAccessor.DynProxyGetTarget">
            <summary>
            Get the proxy target (note that null is a valid target!)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.Interceptor.IProxyTargetAccessor.GetInterceptors">
            <summary>
            Gets the interceptors for the proxy
            </summary>
            <returns></returns>
        </member>
        <member name="T:Castle.Core.IVertex">
            <summary>
            Abstract representation of a vertex.
            </summary>
        </member>
        <member name="P:Castle.Core.GraphNode.Dependers">
            <summary>
            The nodes that dependes on this node
            </summary>
        </member>
        <member name="P:Castle.Core.GraphNode.Dependents">
            <summary>
            The nodes that this node depends
            </summary>
        </member>
        <member name="F:Castle.Core.Internal.VertexColor.White">
            <summary>
            The node has not been visited yet
            </summary>
        </member>
        <member name="F:Castle.Core.Internal.VertexColor.Gray">
            <summary>
            This node is in the process of being visited
            </summary>
        </member>
        <member name="F:Castle.Core.Internal.VertexColor.Black">
            <summary>
            This now was visited
            </summary>
        </member>
        <member name="T:Castle.Core.Internal.ColorsSet">
            <summary>
            Represents a collection of objects
            which are guaranted to be unique 
            and holds a color for them
            </summary>
        </member>
        <member name="T:Castle.Core.Internal.TimestampSet">
            <summary>
            Holds a timestamp (integer) 
            for a given item
            </summary>
        </member>
        <member name="M:Castle.Core.Internal.LinkedList.GetNode(System.Int32)">
            <summary>
            Returns the node at the specified index.
            </summary>
            <param name="index">The lookup index.</param>
            <returns>The node at the specified index.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">
            If the specified <paramref name="index"/> is greater than the
            number of objects within the list.
            </exception>
        </member>
        <member name="M:Castle.Core.Internal.LinkedList.ValidateIndex(System.Int32)">
            <summary>
            Validates the specified index.
            </summary>
            <param name="index">The lookup index.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            If the index is invalid.
            </exception>
        </member>
        <member name="M:Castle.Core.Internal.Lock.Create">
            <summary>
            Creates a new lock.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Castle.Core.IInitializable">
            <summary>
            Lifecycle interface. If implemented by a component,
            the method Initialized will be invoked by the container
            before making the component available to the external world.
            </summary>
        </member>
        <member name="M:Castle.Core.IInitializable.Initialize">
            <summary>
            Implementors should perform any initialization logic.
            </summary>
        </member>
        <member name="T:Castle.Core.IRecyclable">
            <summary>
            Only called for components that 
            belongs to a pool when the component
            comes back to the pool.
            </summary>
        </member>
        <member name="M:Castle.Core.IRecyclable.Recycle">
            <summary>
            Implementors should perform any 
            initialization/clean up.
            </summary>
        </member>
        <member name="T:Castle.Core.IStartable">
            <summary>
            Interface for components that wish to be started by the container
            </summary>
        </member>
        <member name="M:Castle.Core.IStartable.Start">
            <summary>
            Starts this instance.
            </summary>
        </member>
        <member name="M:Castle.Core.IStartable.Stop">
            <summary>
            Stops this instance.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.IExtendedLoggerFactory">
            <summary>
            Provides a factory that can produce either <see cref="T:Castle.Core.Logging.ILogger"/> or
            <see cref="T:Castle.Core.Logging.IExtendedLogger"/> classes.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.ILoggerFactory">
            <summary>
            Manages the instantiation of <see cref="T:Castle.Core.Logging.ILogger"/>s.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILoggerFactory.Create(System.Type)">
            <summary>
            Creates a new logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILoggerFactory.Create(System.String)">
            <summary>
            Creates a new logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILoggerFactory.Create(System.Type,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ILoggerFactory.Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.IExtendedLoggerFactory.Create(System.Type)">
            <summary>
            Creates a new extended logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.IExtendedLoggerFactory.Create(System.String)">
            <summary>
            Creates a new extended logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.IExtendedLoggerFactory.Create(System.Type,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new extended logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.IExtendedLoggerFactory.Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new extended logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.Factories.AbstractExtendedLoggerFactory.Create(System.Type)">
            <summary>
            Creates a new extended logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.Factories.AbstractExtendedLoggerFactory.Create(System.String)">
            <summary>
            Creates a new extended logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.Factories.AbstractExtendedLoggerFactory.Create(System.Type,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new extended logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.Factories.AbstractExtendedLoggerFactory.Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new extended logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.Factories.AbstractExtendedLoggerFactory.Castle#Core#Logging#ILoggerFactory#Create(System.Type)">
            <summary>
            Creates a new logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.Factories.AbstractExtendedLoggerFactory.Castle#Core#Logging#ILoggerFactory#Create(System.String)">
            <summary>
            Creates a new logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.Factories.AbstractExtendedLoggerFactory.Castle#Core#Logging#ILoggerFactory#Create(System.Type,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new logger, getting the logger name from the specified type.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.Factories.AbstractExtendedLoggerFactory.Castle#Core#Logging#ILoggerFactory#Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.Factories.AbstractExtendedLoggerFactory.GetConfigFile(System.String)">
            <summary>
            Gets the configuration file.
            </summary>
            <param name="fileName">i.e. log4net.config</param>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.Logging.AbstractLoggerFactory.GetConfigFile(System.String)">
            <summary>
            Gets the configuration file.
            </summary>
            <param name="fileName">i.e. log4net.config</param>
            <returns></returns>
        </member>
        <member name="T:Castle.Core.Logging.ConsoleFactory">
            <summary>
            Summary description for ConsoleFactory.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.NullLogFactory">
            <summary>
            NullLogFactory used when logging is turned off.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.NullLogFactory.Create(System.String)">
            <summary>
            Creates an instance of ILogger with the specified name.
            </summary>
            <param name="name">Name.</param>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.Logging.NullLogFactory.Create(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates an instance of ILogger with the specified name and LoggerLevel.
            </summary>
            <param name="name">Name.</param>
            <param name="level">Level.</param>
            <returns></returns>
        </member>
        <member name="T:Castle.Core.Logging.StreamLoggerFactory">
            <summary>
            Creates <see cref="T:Castle.Core.Logging.StreamLogger"/> outputing 
            to files. The name of the file is derived from the log name
            plus the 'log' extension.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.TraceLoggerFactory">
            <summary>
            Used to create the TraceLogger implementation of ILogger interface. See <see cref="T:Castle.Core.Logging.TraceLogger"/>. 
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.ConsoleLogger">
            <summary>
            The Logger sending everything to the standard output streams.
            This is mainly for the cases when you have a utility that
            does not have a logger to supply.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.LevelFilteredLogger">
            <summary>
            The Level Filtered Logger class.  This is a base clase which
            provides a LogLevel attribute and reroutes all functions into
            one Log method.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.ILogger">
            <summary>
            Manages logging.
            </summary>
            <remarks>
            This is a facade for the different logging subsystems.
            It offers a simplified interface that follows IOC patterns
            and a simplified priority/level/severity abstraction. 
            </remarks>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Debug(System.String)">
            <summary>
            Logs a debug message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Debug(System.String,System.Exception)">
            <summary>
            Logs a debug message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Debug(System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.DebugFormat(System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.DebugFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.DebugFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.DebugFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Info(System.String)">
            <summary>
            Logs an info message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Info(System.String,System.Exception)">
            <summary>
            Logs an info message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Info(System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.InfoFormat(System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.InfoFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.InfoFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.InfoFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Warn(System.String)">
            <summary>
            Logs a warn message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Warn(System.String,System.Exception)">
            <summary>
            Logs a warn message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Warn(System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.WarnFormat(System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.WarnFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.WarnFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.WarnFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Error(System.String)">
            <summary>
            Logs an error message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Error(System.String,System.Exception)">
            <summary>
            Logs an error message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Error(System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.ErrorFormat(System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.ErrorFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.ErrorFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.ErrorFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Fatal(System.String)">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Fatal(System.String,System.Exception)">
            <summary>
            Logs a fatal message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.Fatal(System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalFormat(System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalError(System.String)">
            <summary>
            Logs a fatal error message.
            </summary>
            <param name="message">The Message</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalError(System.String,System.Exception)">
            <summary>
            Logs a fatal error message.
            </summary>
            <param name="message">The Message</param>
            <param name="exception">The Exception</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.FatalError(System.String,System.Object[])">
            <summary>
            Logs a fatal error message.
            </summary>
            <param name="format">Message format</param>
            <param name="args">Array of objects to write using format</param>
        </member>
        <member name="M:Castle.Core.Logging.ILogger.CreateChildLogger(System.String)">
            <summary>
            Create a new child logger.
            The name of the child logger is [current-loggers-name].[passed-in-name]
            </summary>
            <param name="loggerName">The Subname of this logger.</param>
            <returns>The New ILogger instance.</returns> 
            <exception cref="T:System.ArgumentException">If the name has an empty element name.</exception>
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsDebugEnabled">
            <summary>
            Determines if messages of priority "debug" will be logged.
            </summary>
            <value>True if "debug" messages will be logged.</value> 
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsInfoEnabled">
            <summary>
            Determines if messages of priority "info" will be logged.
            </summary>
            <value>True if "info" messages will be logged.</value> 
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsWarnEnabled">
            <summary>
            Determines if messages of priority "warn" will be logged.
            </summary>
            <value>True if "warn" messages will be logged.</value> 
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsErrorEnabled">
            <summary>
            Determines if messages of priority "error" will be logged.
            </summary>
            <value>True if "error" messages will be logged.</value> 
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsFatalEnabled">
            <summary>
            Determines if messages of priority "fatal" will be logged.
            </summary>
            <value>True if "fatal" messages will be logged.</value> 
        </member>
        <member name="P:Castle.Core.Logging.ILogger.IsFatalErrorEnabled">
            <summary>
            Determines if messages of priority "fatalError" will be logged.
            </summary>
            <value>True if "fatalError" messages will be logged.</value>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.#ctor">
            <summary>
            Creates a new <c>LevelFilteredLogger</c>.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.InitializeLifetimeService">
            <summary>
            Keep the instance alive in a remoting scenario
            </summary>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Debug(System.String)">
            <summary>
            Logs a debug message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Debug(System.String,System.Exception)">
            <summary>
            Logs a debug message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.DebugFormat(System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.DebugFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.DebugFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.DebugFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Debug(System.String,System.Object[])">
            <summary>
            Logs a debug message.
            </summary>
            <param name="format">Message format</param>
            <param name="args">Array of objects to write using format</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Info(System.String)">
            <summary>
            Logs an info message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Info(System.String,System.Exception)">
            <summary>
            Logs an info message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.InfoFormat(System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.InfoFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.InfoFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.InfoFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Info(System.String,System.Object[])">
            <summary>
            Logs an info message.
            </summary>
            <param name="format">Message format</param>
            <param name="args">Array of objects to write using format</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Warn(System.String)">
            <summary>
            Logs a warn message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Warn(System.String,System.Exception)">
            <summary>
            Logs a warn message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.WarnFormat(System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.WarnFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.WarnFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.WarnFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Warn(System.String,System.Object[])">
            <summary>
            Logs a warn message.
            </summary>
            <param name="format">Message format</param>
            <param name="args">Array of objects to write using format</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Error(System.String)">
            <summary>
            Logs an error message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Error(System.String,System.Exception)">
            <summary>
            Logs an error message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.ErrorFormat(System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.ErrorFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.ErrorFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.ErrorFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Error(System.String,System.Object[])">
            <summary>
            Logs an error message.
            </summary>
            <param name="format">Message format</param>
            <param name="args">Array of objects to write using format</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Fatal(System.String)">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Fatal(System.String,System.Exception)">
            <summary>
            Logs a fatal message. 
            </summary>
            <param name="exception">The exception to log</param>
            <param name="message">The message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalFormat(System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalFormat(System.Exception,System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="exception">The exception to log</param>
            <param name="formatProvider">The format provider to use</param>
            <param name="format">Format string for the message to log</param>
            <param name="args">Format arguments for the message to log</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Fatal(System.String,System.Object[])">
            <summary>
            Logs a fatal message.
            </summary>
            <param name="format">Message format</param>
            <param name="args">Array of objects to write using format</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalError(System.String)">
            <summary>
            Logs a fatal error message.
            </summary>
            <param name="message">The Message</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalError(System.String,System.Exception)">
            <summary>
            Logs a fatal error message.
            </summary>
            <param name="message">The Message</param>
            <param name="exception">The Exception</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.FatalError(System.String,System.Object[])">
            <summary>
            Logs a fatal error message.
            </summary>
            <param name="format">Message format</param>
            <param name="args">Array of objects to write using format</param>
        </member>
        <member name="M:Castle.Core.Logging.LevelFilteredLogger.Log(Castle.Core.Logging.LoggerLevel,System.String,System.String,System.Exception)">
            <summary>
            Implementors output the log content by implementing this method only.
            Note that exception can be null
            </summary>
            <param name="loggerLevel"></param>
            <param name="loggerName"></param>
            <param name="message"></param>
            <param name="exception"></param>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.Level">
            <value>
            The <c>LoggerLevel</c> that this logger
            will be using. Defaults to <c>LoggerLevel.Off</c>
            </value>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.Name">
            <value>
            The name that this logger will be using. 
            Defaults to <c>String.Empty</c>
            </value>
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsDebugEnabled">
            <summary>
            Determines if messages of priority "debug" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref="F:Castle.Core.Logging.LoggerLevel.Debug"/> bit</value> 
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsInfoEnabled">
            <summary>
            Determines if messages of priority "info" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref="F:Castle.Core.Logging.LoggerLevel.Info"/> bit</value> 
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsWarnEnabled">
            <summary>
            Determines if messages of priority "warn" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref="F:Castle.Core.Logging.LoggerLevel.Warn"/> bit</value> 
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsErrorEnabled">
            <summary>
            Determines if messages of priority "error" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref="F:Castle.Core.Logging.LoggerLevel.Error"/> bit</value> 
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsFatalEnabled">
            <summary>
            Determines if messages of priority "fatal" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref="F:Castle.Core.Logging.LoggerLevel.Fatal"/> bit</value> 
        </member>
        <member name="P:Castle.Core.Logging.LevelFilteredLogger.IsFatalErrorEnabled">
            <summary>
            Determines if messages of priority "fatal" will be logged.
            </summary>
            <value><c>true</c> if log level flags include the <see cref="F:Castle.Core.Logging.LoggerLevel.Fatal"/> bit</value> 
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.#ctor">
            <summary>
            Creates a new ConsoleLogger with the <c>Level</c>
            set to <c>LoggerLevel.Debug</c> and the <c>Name</c>
            set to <c>String.Empty</c>.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.#ctor(Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new ConsoleLogger with the <c>Name</c>
            set to <c>String.Empty</c>.
            </summary>
            <param name="logLevel">The logs Level.</param>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.#ctor(System.String)">
            <summary>
            Creates a new ConsoleLogger with the <c>Level</c>
            set to <c>LoggerLevel.Debug</c>.
            </summary>
            <param name="name">The logs Name.</param>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.#ctor(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new ConsoleLogger.
            </summary>
            <param name="name">The logs Name.</param>
            <param name="logLevel">The logs Level.</param>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.Log(Castle.Core.Logging.LoggerLevel,System.String,System.String,System.Exception)">
            <summary>
            A Common method to log.
            </summary>
            <param name="loggerLevel">The level of logging</param>
            <param name="loggerName">The name of the logger</param>
            <param name="message">The Message</param>
            <param name="exception">The Exception</param>
        </member>
        <member name="M:Castle.Core.Logging.ConsoleLogger.CreateChildLogger(System.String)">
            <summary>
            Returns a new <c>ConsoleLogger</c> with the name
            added after this loggers name, with a dot in between.
            </summary>
            <param name="loggerName">The added hierarchical name.</param>
            <returns>A new <c>ConsoleLogger</c>.</returns> 
        </member>
        <member name="T:Castle.Core.Logging.DiagnosticsLogger">
            <summary>
            The Logger using standart Diagnostics namespace.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.DiagnosticsLogger.#ctor(System.String)">
            <summary>
            Creates a logger based on <see cref="T:System.Diagnostics.EventLog"/>.
            </summary>
            <param name="logName"><see cref="P:System.Diagnostics.EventLog.Log"/></param>
        </member>
        <member name="M:Castle.Core.Logging.DiagnosticsLogger.#ctor(System.String,System.String)">
            <summary>
            Creates a logger based on <see cref="T:System.Diagnostics.EventLog"/>.
            </summary>
            <param name="logName"><see cref="P:System.Diagnostics.EventLog.Log"/></param>
            <param name="source"><see cref="P:System.Diagnostics.EventLog.Source"/></param>
        </member>
        <member name="M:Castle.Core.Logging.DiagnosticsLogger.#ctor(System.String,System.String,System.String)">
            <summary>
            Creates a logger based on <see cref="T:System.Diagnostics.EventLog"/>.
            </summary>
            <param name="logName"><see cref="P:System.Diagnostics.EventLog.Log"/></param>
            <param name="machineName"><see cref="P:System.Diagnostics.EventLog.MachineName"/></param>
            <param name="source"><see cref="P:System.Diagnostics.EventLog.Source"/></param>
        </member>
        <member name="T:Castle.Core.Logging.NullLogger">
            <summary>
            The Null Logger class.  This is useful for implementations where you need
            to provide a logger to a utility class, but do not want any output from it.
            It also helps when you have a utility that does not have a logger to supply.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.IExtendedLogger">
            <summary>
            Provides an interface that supports <see cref="T:Castle.Core.Logging.ILogger"/> and
            allows the storage and retrieval of Contexts. These are supported in
            both log4net and NLog.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.IExtendedLogger.GlobalProperties">
            <summary>
            Exposes the Global Context of the extended logger. 
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.IExtendedLogger.ThreadProperties">
            <summary>
            Exposes the Thread Context of the extended logger.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.IExtendedLogger.ThreadStacks">
            <summary>
            Exposes the Thread Stack of the extended logger.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.#ctor">
            <summary>
            Creates a new <c>NullLogger</c>.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Debug(System.String)">
            <summary>
            No-op.
            </summary>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Debug(System.String,System.Exception)">
            <summary>
            No-op. 
            </summary>
            <param name="exception">Ignored</param>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Debug(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.DebugFormat(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.DebugFormat(System.Exception,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.DebugFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.DebugFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Info(System.String)">
            <summary>
            No-op.
            </summary>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Info(System.String,System.Exception)">
            <summary>
            No-op. 
            </summary>
            <param name="exception">Ignored</param>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Info(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.InfoFormat(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.InfoFormat(System.Exception,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.InfoFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.InfoFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Warn(System.String)">
            <summary>
            No-op.
            </summary>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Warn(System.String,System.Exception)">
            <summary>
            No-op. 
            </summary>
            <param name="exception">Ignored</param>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Warn(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.WarnFormat(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.WarnFormat(System.Exception,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.WarnFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.WarnFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Error(System.String)">
            <summary>
            No-op.
            </summary>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Error(System.String,System.Exception)">
            <summary>
            No-op. 
            </summary>
            <param name="exception">Ignored</param>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Error(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.ErrorFormat(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.ErrorFormat(System.Exception,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.ErrorFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.ErrorFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Fatal(System.String)">
            <summary>
            No-op.
            </summary>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Fatal(System.String,System.Exception)">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.Fatal(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalFormat(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalFormat(System.Exception,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalFormat(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalFormat(System.Exception,System.IFormatProvider,System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="exception">Ignored</param>
            <param name="formatProvider">Ignored</param>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalError(System.String)">
            <summary>
            No-op.
            </summary>
            <param name="message">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalError(System.String,System.Exception)">
            <summary>
            No-op.
            </summary>
            <param name="message">Ignored</param>
            <param name="exception">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.FatalError(System.String,System.Object[])">
            <summary>
            No-op.
            </summary>
            <param name="format">Ignored</param>
            <param name="args">Ignored</param>
        </member>
        <member name="M:Castle.Core.Logging.NullLogger.CreateChildLogger(System.String)">
            <summary>
            Returns this <c>NullLogger</c>.
            </summary>
            <param name="loggerName">Ignored</param>
            <returns>This ILogger instance.</returns> 
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsDebugEnabled">
            <summary>
            No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsInfoEnabled">
            <summary>
            No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsWarnEnabled">
            <summary>
            No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsErrorEnabled">
            <summary>
            No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsFatalEnabled">
            <summary>
            No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.IsFatalErrorEnabled">
            <summary>
            No-op.
            </summary>
            <value>false</value>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.GlobalProperties">
            <summary>
            Returns empty context properties.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.ThreadProperties">
            <summary>
            Returns empty context properties.
            </summary>
        </member>
        <member name="P:Castle.Core.Logging.NullLogger.ThreadStacks">
            <summary>
            Returns empty context stacks.
            </summary>
        </member>
        <member name="T:Castle.Core.Logging.IContextProperties">
            <summary>
            Interface for Context Properties implementations
            </summary>
            <remarks>
            <para>
            This interface defines a basic property get set accessor.
            </para>
            <para>
            Based on the ContextPropertiesBase of log4net, by Nicko Cadell.
            </para>
            </remarks>
        </member>
        <member name="P:Castle.Core.Logging.IContextProperties.Item(System.String)">
            <summary>
            Gets or sets the value of a property
            </summary>
            <value>
            The value for the property with the specified key
            </value>
            <remarks>
            <para>
            Gets or sets the value of a property
            </para>
            </remarks>
        </member>
        <member name="T:Castle.Core.Logging.StreamLogger">
            <summary>
            The Stream Logger class.  This class can stream log information
            to any stream, it is suitable for storing a log file to disk,
            or to a <c>MemoryStream</c> for testing your components.
            </summary>
            <remarks>
            This logger is not thread safe.
            </remarks>
        </member>
        <member name="M:Castle.Core.Logging.StreamLogger.#ctor(System.String,System.IO.Stream)">
            <summary>
            Creates a new <c>StreamLogger</c> with default encoding 
            and buffer size. Initial Level is set to Debug.
            </summary>
            <param name="name">
            The name of the log.
            </param>
            <param name="stream">
            The stream that will be used for logging,
            seeking while the logger is alive 
            </param>
        </member>
        <member name="M:Castle.Core.Logging.StreamLogger.#ctor(System.String,System.IO.Stream,System.Text.Encoding)">
            <summary>
            Creates a new <c>StreamLogger</c> with default buffer size.
            Initial Level is set to Debug.
            </summary>
            <param name="name">
            The name of the log.
            </param>
            <param name="stream">
            The stream that will be used for logging,
            seeking while the logger is alive 
            </param>
            <param name="encoding">
            The encoding that will be used for this stream.
            <see cref="T:System.IO.StreamWriter"/>
            </param>
        </member>
        <member name="M:Castle.Core.Logging.StreamLogger.#ctor(System.String,System.IO.Stream,System.Text.Encoding,System.Int32)">
            <summary>
            Creates a new <c>StreamLogger</c>. 
            Initial Level is set to Debug.
            </summary>
            <param name="name">
            The name of the log.
            </param>
            <param name="stream">
            The stream that will be used for logging,
            seeking while the logger is alive 
            </param>
            <param name="encoding">
            The encoding that will be used for this stream.
            <see cref="T:System.IO.StreamWriter"/>
            </param>
            <param name="bufferSize">
            The buffer size that will be used for this stream.
            <see cref="T:System.IO.StreamWriter"/>
            </param>
        </member>
        <member name="M:Castle.Core.Logging.StreamLogger.#ctor(System.String,System.IO.StreamWriter)">
            <summary>
            Creates a new <c>StreamLogger</c> with 
            Debug as default Level.
            </summary>
            <param name="name">The name of the log.</param>
            <param name="writer">The <c>StreamWriter</c> the log will write to.</param>
        </member>
        <member name="T:Castle.Core.Logging.TraceLogger">
            <summary>
            The TraceLogger sends all logging to the System.Diagnostics.TraceSource
            built into the .net framework. 
            </summary>
            <remarks>
            Logging can be configured in the system.diagnostics configuration 
            section. 
            
            If logger doesn't find a source name with a full match it will
            use source names which match the namespace partially. For example you can
            configure from all castle components by adding a source name with the
            name "Castle". 
            
            If no portion of the namespace matches the source named "Default" will
            be used.
            </remarks>
        </member>
        <member name="M:Castle.Core.Logging.TraceLogger.#ctor(System.String)">
            <summary>
            Build a new trace logger based on the named TraceSource
            </summary>
            <param name="name">The name used to locate the best TraceSource. In most cases comes from the using type's fullname.</param>
        </member>
        <member name="M:Castle.Core.Logging.TraceLogger.#ctor(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Build a new trace logger based on the named TraceSource
            </summary>
            <param name="name">The name used to locate the best TraceSource. In most cases comes from the using type's fullname.</param>
            <param name="level">The default logging level at which this source should write messages. In almost all cases this
            default value will be overridden in the config file. </param>
        </member>
        <member name="M:Castle.Core.Logging.TraceLogger.CreateChildLogger(System.String)">
            <summary>
            Create a new child logger.
            The name of the child logger is [current-loggers-name].[passed-in-name]
            </summary>
            <param name="loggerName">The Subname of this logger.</param>
            <returns>The New ILogger instance.</returns> 
        </member>
        <member name="T:Castle.Core.Logging.WebLogger">
            <summary>
            The WebLogger sends everything to the HttpContext.Trace 
            </summary>
            <remarks>
            Trace must be enabled on the Asp.Net configuration file (web.config or machine.config)
            </remarks>
        </member>
        <member name="M:Castle.Core.Logging.WebLogger.#ctor">
            <summary>
            Creates a new WebLogger with the priority set to DEBUG.
            </summary>
        </member>
        <member name="M:Castle.Core.Logging.WebLogger.#ctor(Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new WebLogger.
            </summary>
            <param name="logLevel">The Log level typecode.</param>
        </member>
        <member name="M:Castle.Core.Logging.WebLogger.#ctor(System.String)">
            <summary>
            Creates a new WebLogger.
            </summary>
            <param name="name">The Log name.</param>
        </member>
        <member name="M:Castle.Core.Logging.WebLogger.#ctor(System.String,Castle.Core.Logging.LoggerLevel)">
            <summary>
            Creates a new WebLogger.
            </summary>
            <param name="name">The Log name.</param>
            <param name="loggerLevel">The Log level typecode.</param>
        </member>
        <member name="M:Castle.Core.Logging.WebLogger.Log(Castle.Core.Logging.LoggerLevel,System.String,System.String,System.Exception)">
            <summary>
            A Common method to log.
            </summary>
            <param name="loggerLevel">The level of logging</param>
            <param name="loggerName">The Log name.</param>
            <param name="message">The Message</param>
            <param name="exception">The Exception</param>
        </member>
        <member name="M:Castle.Core.Logging.WebLogger.CreateChildLogger(System.String)">
            <summary>
            Just returns this logger (<c>WebLogger</c> is not hierarchical).
            </summary>
            <param name="loggerName">Ignored</param>
            <returns>This ILogger instance.</returns> 
        </member>
        <member name="M:Castle.Core.Logging.WebLogger.TryToGetTraceContext">
            <summary>
            Tries to get the current http context's trace context.
            </summary>
            <returns>The current http context's trace context or null if none is 
            available</returns>
        </member>
        <member name="T:Castle.Core.Logging.LoggerLevel">
            <summary>
            Supporting Logger levels.
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Off">
            <summary>
            Logging will be off
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Fatal">
            <summary>
            Fatal logging level
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Error">
            <summary>
            Error logging level
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Warn">
            <summary>
            Warn logging level
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Info">
            <summary>
            Info logging level
            </summary>
        </member>
        <member name="F:Castle.Core.Logging.LoggerLevel.Debug">
            <summary>
            Debug logging level
            </summary>
        </member>
        <member name="T:Castle.Core.Configuration.Xml.XmlConfigurationDeserializer">
            <summary>
            Pendent
            </summary>
        </member>
        <member name="M:Castle.Core.Configuration.Xml.XmlConfigurationDeserializer.Deserialize(System.Xml.XmlNode)">
            <summary>
            Deserializes the specified node into an abstract representation of configuration.
            </summary>
            <param name="node">The node.</param>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.Configuration.Xml.XmlConfigurationDeserializer.GetConfigValue(System.String)">
            <summary>
            If a config value is an empty string we return null, this is to keep
            backward compability with old code
            </summary>
        </member>
        <member name="T:Castle.Core.Configuration.AbstractConfiguration">
            <summary>
            This is an abstract <see cref="T:Castle.Core.Configuration.IConfiguration"/> implementation
            that deals with methods that can be abstracted away
            from underlying implementations.
            </summary>
            <remarks>
            <para><b>AbstractConfiguration</b> makes easier to implementers 
            to create a new version of <see cref="T:Castle.Core.Configuration.IConfiguration"/></para>
            </remarks>
        </member>
        <member name="T:Castle.Core.Configuration.IConfiguration">
            <summary>
            <see cref="T:Castle.Core.Configuration.IConfiguration"/> is a interface encapsulating a configuration node
            used to retrieve configuration values.
            </summary>
        </member>
        <member name="M:Castle.Core.Configuration.IConfiguration.GetValue(System.Type,System.Object)">
            <summary>
            Gets the value of the node and converts it 
            into specified <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The <see cref="T:System.Type"/></param>
            <param name="defaultValue">
            The Default value returned if the convertion fails.
            </param>
            <returns>The Value converted into the specified type.</returns>
        </member>
        <member name="P:Castle.Core.Configuration.IConfiguration.Name">
            <summary>
            Gets the name of the node.
            </summary>
            <value>
            The Name of the node.
            </value> 
        </member>
        <member name="P:Castle.Core.Configuration.IConfiguration.Value">
            <summary>
            Gets the value of the node.
            </summary>
            <value>
            The Value of the node.
            </value> 
        </member>
        <member name="P:Castle.Core.Configuration.IConfiguration.Children">
            <summary>
            Gets an <see cref="T:Castle.Core.Configuration.ConfigurationCollection"/> of <see cref="T:Castle.Core.Configuration.IConfiguration"/>
            elements containing all node children.
            </summary>
            <value>The Collection of child nodes.</value>
        </member>
        <member name="P:Castle.Core.Configuration.IConfiguration.Attributes">
            <summary>
            Gets an <see cref="T:System.Collections.IDictionary"/> of the configuration attributes.
            </summary>
        </member>
        <member name="M:Castle.Core.Configuration.AbstractConfiguration.GetValue(System.Type,System.Object)">
            <summary>
            Gets the value of the node and converts it
            into specified <see cref="T:System.Type"/>.
            </summary>
            <param name="type">The <see cref="T:System.Type"/></param>
            <param name="defaultValue">
            The Default value returned if the convertion fails.
            </param>
            <returns>The Value converted into the specified type.</returns>
        </member>
        <member name="P:Castle.Core.Configuration.AbstractConfiguration.Name">
            <summary>
            Gets the name of the <see cref="T:Castle.Core.Configuration.IConfiguration"/>.
            </summary>
            <value>
            The Name of the <see cref="T:Castle.Core.Configuration.IConfiguration"/>.
            </value>
        </member>
        <member name="P:Castle.Core.Configuration.AbstractConfiguration.Value">
            <summary>
            Gets the value of <see cref="T:Castle.Core.Configuration.IConfiguration"/>.
            </summary>
            <value>
            The Value of the <see cref="T:Castle.Core.Configuration.IConfiguration"/>.
            </value>
        </member>
        <member name="P:Castle.Core.Configuration.AbstractConfiguration.Children">
            <summary>
            Gets all child nodes.
            </summary>
            <value>The <see cref="T:Castle.Core.Configuration.ConfigurationCollection"/> of child nodes.</value>
        </member>
        <member name="P:Castle.Core.Configuration.AbstractConfiguration.Attributes">
            <summary>
            Gets node attributes.
            </summary>
            <value>
            All attributes of the node.
            </value>
        </member>
        <member name="T:Castle.Core.Configuration.ConfigurationCollection">
            <summary>
            A collection of <see cref="T:Castle.Core.Configuration.IConfiguration"/> objects.
            </summary>
        </member>
        <member name="M:Castle.Core.Configuration.ConfigurationCollection.#ctor">
            <summary>
            Creates a new instance of <c>ConfigurationCollection</c>.
            </summary>
        </member>
        <member name="M:Castle.Core.Configuration.ConfigurationCollection.#ctor(System.Collections.Generic.IEnumerable{Castle.Core.Configuration.IConfiguration})">
            <summary>
            Creates a new instance of <c>ConfigurationCollection</c>.
            </summary>
        </member>
        <member name="T:Castle.Core.Configuration.MutableConfiguration">
            <summary>
            Summary description for MutableConfiguration.
            </summary>
        </member>
        <member name="M:Castle.Core.Configuration.MutableConfiguration.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.Configuration.MutableConfiguration"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Castle.Core.Configuration.MutableConfiguration.Value">
            <summary>
            Gets the value of <see cref="T:Castle.Core.Configuration.IConfiguration"/>.
            </summary>
            <value>
            The Value of the <see cref="T:Castle.Core.Configuration.IConfiguration"/>.
            </value>
        </member>
        <member name="T:Castle.Core.LifestyleType">
            <summary>
            Enumeration used to mark the component's lifestyle.
            </summary>
        </member>
        <member name="F:Castle.Core.LifestyleType.Undefined">
            <summary>
            No lifestyle specified.
            </summary>
        </member>
        <member name="F:Castle.Core.LifestyleType.Singleton">
            <summary>
            Singleton components are instantiated once, and shared
            between all clients.
            </summary>
        </member>
        <member name="F:Castle.Core.LifestyleType.Thread">
            <summary>
            Thread components have a unique instance per thread.
            </summary>
        </member>
        <member name="F:Castle.Core.LifestyleType.Transient">
            <summary>
            Transient components are created on demand.
            </summary>
        </member>
        <member name="F:Castle.Core.LifestyleType.Pooled">
            <summary>
            Optimization of transient components that keeps
            instance in a pool instead of always creating them.
            </summary>
        </member>
        <member name="F:Castle.Core.LifestyleType.PerWebRequest">
            <summary>
            PerWebRequest components are created once per Http Request
            </summary>
        </member>
        <member name="F:Castle.Core.LifestyleType.Custom">
            <summary>
            Any other logic to create/release components.
            </summary>
        </member>
        <member name="T:Castle.Core.PropertiesInspectionBehavior">
            <summary>
            
            </summary>
        </member>
        <member name="T:Castle.Core.ComponentModel">
            <summary>
            Represents the collection of information and
            meta information collected about a component.
            </summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.name">
            <summary>Name (key) of the component</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.service">
            <summary>Service exposed</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.implementation">
            <summary>Implementation for the service</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.extended">
            <summary>Extended properties</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.lifestyleType">
            <summary>Lifestyle for the component</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.customLifestyle">
            <summary>Custom lifestyle, if any</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.customComponentActivator">
            <summary>Custom activator, if any</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.dependencies">
            <summary>Dependencies the kernel must resolve</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.constructors">
            <summary>All available constructors</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.properties">
            <summary>All potential properties that can be setted by the kernel</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.lifecycleSteps">
            <summary>Steps of lifecycle</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.parameters">
            <summary>External parameters</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.configuration">
            <summary>Configuration node associated</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.interceptors">
            <summary>Interceptors associated</summary>
        </member>
        <member name="F:Castle.Core.ComponentModel.customDependencies">
            <summary>/// Custom dependencies/// </summary>
        </member>
        <member name="M:Castle.Core.ComponentModel.#ctor(System.String,System.Type,System.Type)">
            <summary>
            Constructs a ComponentModel
            </summary>
        </member>
        <member name="M:Castle.Core.ComponentModel.Requires(System.Predicate{Castle.Core.PropertySet}[])">
            <summary>
            Requires the selected property dependencies.
            </summary>
            <param name="selectors">The property selector.</param>
        </member>
        <member name="M:Castle.Core.ComponentModel.Requires``1">
            <summary>
            Requires the property dependencies of type <typeparamref name="D"/>.
            </summary>
            <typeparam name="D">The dependency type.</typeparam>
        </member>
        <member name="P:Castle.Core.ComponentModel.Name">
            <summary>
            Sets or returns the component key
            </summary>
        </member>
        <member name="P:Castle.Core.ComponentModel.Service">
            <summary>
            Gets or sets the service exposed.
            </summary>
            <value>The service.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.Implementation">
            <summary>
            Gets or sets the component implementation.
            </summary>
            <value>The implementation.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.RequiresGenericArguments">
            <summary>
            Gets or sets a value indicating whether the component requires generic arguments.
            </summary>
            <value>
            <c>true</c> if generic arguments are required; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Castle.Core.ComponentModel.ExtendedProperties">
            <summary>
            Gets or sets the extended properties.
            </summary>
            <value>The extended properties.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.Constructors">
            <summary>
            Gets the constructors candidates.
            </summary>
            <value>The constructors.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.Properties">
            <summary>
            Gets the properties set.
            </summary>
            <value>The properties.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.Configuration">
            <summary>
            Gets or sets the configuration.
            </summary>
            <value>The configuration.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.LifecycleSteps">
            <summary>
            Gets the lifecycle steps.
            </summary>
            <value>The lifecycle steps.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.LifestyleType">
            <summary>
            Gets or sets the lifestyle type.
            </summary>
            <value>The type of the lifestyle.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.InspectionBehavior">
            <summary>
            Gets or sets the strategy for
            inspecting public properties 
            on the components
            </summary>
        </member>
        <member name="P:Castle.Core.ComponentModel.CustomLifestyle">
            <summary>
            Gets or sets the custom lifestyle.
            </summary>
            <value>The custom lifestyle.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.CustomComponentActivator">
            <summary>
            Gets or sets the custom component activator.
            </summary>
            <value>The custom component activator.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.Interceptors">
            <summary>
            Gets the interceptors.
            </summary>
            <value>The interceptors.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.Parameters">
            <summary>
            Gets the parameter collection.
            </summary>
            <value>The parameters.</value>
        </member>
        <member name="P:Castle.Core.ComponentModel.Dependencies">
            <summary>
            Dependencies are kept within constructors and
            properties. Others dependencies must be 
            registered here, so the kernel (as a matter 
            of fact the handler) can check them
            </summary>
        </member>
        <member name="P:Castle.Core.ComponentModel.CustomDependencies">
            <summary>
            Gets the custom dependencies.
            </summary>
            <value>The custom dependencies.</value>
        </member>
        <member name="T:Castle.Core.ConstructorCandidate">
            <summary>
            Represents a constructor of the component 
            that the container can use to initialize it properly.
            </summary>
        </member>
        <member name="M:Castle.Core.ConstructorCandidate.#ctor(System.Reflection.ConstructorInfo,Castle.Core.DependencyModel[])">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.ConstructorCandidate"/> class.
            </summary>
            <param name="constructorInfo">The constructor info.</param>
            <param name="dependencies">The dependencies.</param>
        </member>
        <member name="P:Castle.Core.ConstructorCandidate.Constructor">
            <summary>
            Gets the ConstructorInfo (from reflection).
            </summary>
            <value>The constructor.</value>
        </member>
        <member name="P:Castle.Core.ConstructorCandidate.Dependencies">
            <summary>
            Gets the dependencies this constructor candidate exposes.
            </summary>
            <value>The dependencies.</value>
        </member>
        <member name="T:Castle.Core.ConstructorCandidateCollection">
            <summary>
            Collection of <see cref="T:Castle.Core.ConstructorCandidate"/>
            </summary>
        </member>
        <member name="P:Castle.Core.ConstructorCandidateCollection.FewerArgumentsCandidate">
            <summary>
            Gets the fewer arguments candidate.
            </summary>
            <value>The fewer arguments candidate.</value>
        </member>
        <member name="T:Castle.Core.DependencyModel">
            <summary>
            Represents a dependency (other component or a 
            fixed value available through external configuration).
            </summary>
        </member>
        <member name="M:Castle.Core.DependencyModel.#ctor(Castle.Core.DependencyType,System.String,System.Type,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.DependencyModel"/> class.
            </summary>
            <param name="type">The type.</param>
            <param name="dependencyKey">The dependency key.</param>
            <param name="targetType">Type of the target.</param>
            <param name="isOptional">if set to <c>true</c> [is optional].</param>
        </member>
        <member name="M:Castle.Core.DependencyModel.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:Castle.Core.DependencyModel.GetHashCode">
            <summary>
            Serves as a hash function for a particular type, suitable
            for use in hashing algorithms and data structures like a hash table.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:Castle.Core.DependencyModel.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to the current <see cref="T:System.Object"/>.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with the current <see cref="T:System.Object"/>.</param>
            <returns>
            	<see langword="true"/> if the specified <see cref="T:System.Object"/> is equal to the
            current <see cref="T:System.Object"/>; otherwise, <see langword="false"/>.
            </returns>
        </member>
        <member name="P:Castle.Core.DependencyModel.DependencyType">
            <summary>
            Gets or sets the type of the dependency.
            </summary>
            <value>The type of the dependency.</value>
        </member>
        <member name="P:Castle.Core.DependencyModel.DependencyKey">
            <summary>
            Gets or sets the dependency key.
            </summary>
            <value>The dependency key.</value>
        </member>
        <member name="P:Castle.Core.DependencyModel.TargetType">
            <summary>
            Gets the type of the target.
            </summary>
            <value>The type of the target.</value>
        </member>
        <member name="P:Castle.Core.DependencyModel.IsOptional">
            <summary>
            Gets or sets whether this dependency is optional.
            </summary>
            <value>
            	<c>true</c> if this dependency is optional; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Castle.Core.DependencyModelCollection">
            <summary>
            Collection of <see cref="T:Castle.Core.DependencyModel"/>.
            </summary>
        </member>
        <member name="M:Castle.Core.DependencyModelCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.DependencyModelCollection"/> class.
            </summary>
        </member>
        <member name="M:Castle.Core.DependencyModelCollection.#ctor(System.Collections.Generic.IEnumerable{Castle.Core.DependencyModel})">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.DependencyModelCollection"/> class.
            </summary>
            <param name="dependencies">The dependencies.</param>
        </member>
        <member name="T:Castle.Core.InterceptorReference">
            <summary>
            Represents an reference to a Interceptor component.
            </summary>
        </member>
        <member name="M:Castle.Core.InterceptorReference.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.InterceptorReference"/> class.
            </summary>
            <param name="componentKey">The component key.</param>
        </member>
        <member name="M:Castle.Core.InterceptorReference.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.InterceptorReference"/> class.
            </summary>
            <param name="serviceType">Type of the service.</param>
        </member>
        <member name="M:Castle.Core.InterceptorReference.ForKey(System.String)">
            <summary>
            Gets an <see cref="T:Castle.Core.InterceptorReference"/> for the component key.
            </summary>
            <param name="key">The component key.</param>
            <returns>The <see cref="T:Castle.Core.InterceptorReference"/></returns>
        </member>
        <member name="M:Castle.Core.InterceptorReference.ForType(System.Type)">
            <summary>
            Gets an <see cref="T:Castle.Core.InterceptorReference"/> for the service.
            </summary>
            <param name="service">The service.</param>
            <returns>The <see cref="T:Castle.Core.InterceptorReference"/></returns>
        </member>
        <member name="M:Castle.Core.InterceptorReference.ForType``1">
            <summary>
            Gets an <see cref="T:Castle.Core.InterceptorReference"/> for the service.
            </summary>
            <typeparam name="T">The service type.</typeparam>
            <returns>The <see cref="T:Castle.Core.InterceptorReference"/></returns>
        </member>
        <member name="P:Castle.Core.InterceptorReference.ServiceType">
            <summary>
            Gets the type of the service.
            </summary>
            <value>The type of the service.</value>
        </member>
        <member name="P:Castle.Core.InterceptorReference.ComponentKey">
            <summary>
            Gets the interceptor component key.
            </summary>
            <value>The component key.</value>
        </member>
        <member name="P:Castle.Core.InterceptorReference.ReferenceType">
            <summary>
            Gets the type of the reference.
            </summary>
            <value>The type of the reference.</value>
        </member>
        <member name="T:Castle.Core.InterceptorReferenceCollection">
            <summary>
            Collection of <see cref="T:Castle.Core.InterceptorReference"/>
            </summary>
        </member>
        <member name="M:Castle.Core.InterceptorReferenceCollection.Add(Castle.Core.InterceptorReference)">
            <summary>
            Adds the specified item.
            </summary>
            <param name="item">The interceptor.</param>
        </member>
        <member name="M:Castle.Core.InterceptorReferenceCollection.AddFirst(Castle.Core.InterceptorReference)">
            <summary>
            Adds the specified interceptor as the first.
            </summary>
            <param name="item">The interceptor.</param>
        </member>
        <member name="M:Castle.Core.InterceptorReferenceCollection.AddLast(Castle.Core.InterceptorReference)">
            <summary>
            Adds the specified interceptor as the last.
            </summary>
            <param name="item">The interceptor.</param>
        </member>
        <member name="M:Castle.Core.InterceptorReferenceCollection.Insert(System.Int32,Castle.Core.InterceptorReference)">
            <summary>
            Inserts the specified interceptor at the specified index.
            </summary>
            <param name="index">The index.</param>
            <param name="item">The interceptor.</param>
        </member>
        <member name="M:Castle.Core.InterceptorReferenceCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            When implemented by a class, copies the elements of
            the <see cref="T:System.Collections.ICollection"/> to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"/> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection"/>. The <see cref="T:System.Array"/> must have zero-based indexing.</param>
            <param name="index">The zero-based index in <paramref name="array"/> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">
            	<paramref name="array"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is less than zero.</exception>
            <exception cref="T:System.ArgumentException">
            	<para>
            		<paramref name="array"/> is multidimensional.</para>
            	<para>-or-</para>
            	<para>
            		<paramref name="index"/> is equal to or greater than the length of <paramref name="array"/>.</para>
            	<para>-or-</para>
            	<para>The number of elements in the source <see cref="T:System.Collections.ICollection"/> is greater than the available space from <paramref name="index"/> to the end of the destination <paramref name="array"/>.</para>
            </exception>
            <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.Collections.ICollection"/> cannot be cast automatically to the type of the destination <paramref name="array"/>.</exception>
        </member>
        <member name="M:Castle.Core.InterceptorReferenceCollection.GetEnumerator">
            <summary>
            Returns an enumerator that can iterate through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/>
            that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Castle.Core.InterceptorReferenceCollection.AddIfNotInCollection(Castle.Core.InterceptorReference)">
            <summary>
            Adds the interceptor to the end of the interceptors list if it does not exist already.
            </summary>
            <param name="interceptorReference">The interceptor reference.</param>
        </member>
        <member name="P:Castle.Core.InterceptorReferenceCollection.HasInterceptors">
            <summary>
            Gets a value indicating whether this instance has interceptors.
            </summary>
            <value>
            	<c>true</c> if this instance has interceptors; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Castle.Core.InterceptorReferenceCollection.Count">
            <summary>
            Gets the number of
            elements contained in the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
        </member>
        <member name="P:Castle.Core.InterceptorReferenceCollection.SyncRoot">
            <summary>
            Gets an object that
            can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
        </member>
        <member name="P:Castle.Core.InterceptorReferenceCollection.IsSynchronized">
            <summary>
            Gets a value
            indicating whether access to the <see cref="T:System.Collections.ICollection"/> is synchronized
            (thread-safe).
            </summary>
            <value></value>
        </member>
        <member name="T:Castle.Core.LifecycleStepCollection">
            <summary>
            Represents a collection of ordered lifecycle steps.
            </summary>
        </member>
        <member name="M:Castle.Core.LifecycleStepCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.LifecycleStepCollection"/> class.
            </summary>
        </member>
        <member name="M:Castle.Core.LifecycleStepCollection.GetCommissionSteps">
            <summary>
            Returns all steps for the commission phase
            </summary>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.LifecycleStepCollection.GetDecommissionSteps">
            <summary>
            Returns all steps for the decommission phase
            </summary>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.LifecycleStepCollection.Add(Castle.Core.LifecycleStepType,System.Object)">
            <summary>
            Adds a step to the commission or decomission phases.
            </summary>
            <param name="type"></param>
            <param name="stepImplementation"></param>
        </member>
        <member name="M:Castle.Core.LifecycleStepCollection.AddFirst(Castle.Core.LifecycleStepType,System.Object)">
            <summary>
            Adds a step as the first step to execute for the commission or decomission phase.
            </summary>
            <param name="type"></param>
            <param name="stepImplementation"></param>
        </member>
        <member name="M:Castle.Core.LifecycleStepCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the elements of
            the <see cref="T:System.Collections.ICollection"/> to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"/> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection"/>. The <see cref="T:System.Array"/> must have zero-based indexing.</param>
            <param name="index">The zero-based index in <paramref name="array"/> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">
            	<paramref name="array"/> is <see langword="null"/>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is less than zero.</exception>
            <exception cref="T:System.ArgumentException">
            	<para>
            		<paramref name="array"/> is multidimensional.</para>
            	<para>-or-</para>
            	<para>
            		<paramref name="index"/> is equal to or greater than the length of <paramref name="array"/>.</para>
            	<para>-or-</para>
            	<para>The number of elements in the source <see cref="T:System.Collections.ICollection"/> is greater than the available space from <paramref name="index"/> to the end of the destination <paramref name="array"/>.</para>
            </exception>
            <exception cref="T:System.InvalidCastException">The type of the source <see cref="T:System.Collections.ICollection"/> cannot be cast automatically to the type of the destination <paramref name="array"/>.</exception>
        </member>
        <member name="M:Castle.Core.LifecycleStepCollection.GetEnumerator">
            <summary>
            Returns an enumerator that can iterate through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/>
            that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="P:Castle.Core.LifecycleStepCollection.HasCommissionSteps">
            <summary>
            Gets a value indicating whether this instance has commission steps.
            </summary>
            <value>
            	<c>true</c> if this instance has commission steps; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Castle.Core.LifecycleStepCollection.HasDecommissionSteps">
            <summary>
            Gets a value indicating whether this instance has decommission steps.
            </summary>
            <value>
            	<c>true</c> if this instance has decommission steps; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Castle.Core.LifecycleStepCollection.Count">
            <summary>
            Gets the number of
            elements contained in the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
        </member>
        <member name="P:Castle.Core.LifecycleStepCollection.SyncRoot">
            <summary>
            Gets an object that
            can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
        </member>
        <member name="P:Castle.Core.LifecycleStepCollection.IsSynchronized">
            <summary>
            Gets a value
            indicating whether access to the <see cref="T:System.Collections.ICollection"/> is synchronized
            (thread-safe).
            </summary>
            <value></value>
        </member>
        <member name="T:Castle.Core.MethodMetaModel">
            <summary>
            Represents meta information associated with a method
            (not yet defined)
            </summary>
        </member>
        <member name="M:Castle.Core.MethodMetaModel.#ctor(Castle.Core.Configuration.IConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.MethodMetaModel"/> class.
            </summary>
            <param name="configNode">The config node.</param>
        </member>
        <member name="P:Castle.Core.MethodMetaModel.ConfigNode">
            <summary>
            Gets the config node.
            </summary>
            <value>The config node.</value>
        </member>
        <member name="T:Castle.Core.MethodMetaModelCollection">
            <summary>
            Collection of <see cref="T:Castle.Core.MethodMetaModel"/>
            </summary>
        </member>
        <member name="P:Castle.Core.MethodMetaModelCollection.MethodInfo2Model">
            <summary>
            Gets the method info2 model.
            </summary>
            <value>The method info2 model.</value>
        </member>
        <member name="T:Castle.Core.ParameterModel">
            <summary>
            Represents a parameter. Usually the parameter
            comes from the external world, ie, an external configuration.
            </summary>
        </member>
        <member name="M:Castle.Core.ParameterModel.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.ParameterModel"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Castle.Core.ParameterModel.#ctor(System.String,Castle.Core.Configuration.IConfiguration)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.ParameterModel"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="P:Castle.Core.ParameterModel.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Castle.Core.ParameterModel.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:Castle.Core.ParameterModel.ConfigValue">
            <summary>
            Gets the config value.
            </summary>
            <value>The config value.</value>
        </member>
        <member name="T:Castle.Core.ParameterModelCollection">
            <summary>
            Collection of <see cref="T:Castle.Core.ParameterModel"/>
            </summary>
        </member>
        <member name="M:Castle.Core.ParameterModelCollection.Add(System.String,System.String)">
            <summary>
            Adds the specified name.
            </summary>
            <param name="name">The name.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Castle.Core.ParameterModelCollection.Add(System.String,Castle.Core.Configuration.IConfiguration)">
            <summary>
            Adds the specified name.
            </summary>
            <param name="name">The name.</param>
            <param name="configNode">The config node.</param>
        </member>
        <member name="M:Castle.Core.ParameterModelCollection.Contains(System.Object)">
            <summary>
            Determines whether this collection contains the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns>
            <c>true</c> if yes; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Castle.Core.ParameterModelCollection.Add(System.Object,System.Object)">
            <summary>
            Adds the specified key.
            </summary>
            <remarks>
            Not implemented
            </remarks>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Castle.Core.ParameterModelCollection.Clear">
            <summary>
            Clears this instance.
            </summary>
            <remarks>
            Not implemented
            </remarks>
        </member>
        <member name="M:Castle.Core.ParameterModelCollection.Remove(System.Object)">
            <summary>
            Removes the specified key.
            </summary>
            <param name="key">The key.</param>
            <remarks>
            Not implemented
            </remarks>
        </member>
        <member name="M:Castle.Core.ParameterModelCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            Copy the content to the specified array
            </summary>
            <param name="array">target array</param>
            <param name="index">target index</param>
            <remarks>
            Not implemented
            </remarks>
        </member>
        <member name="M:Castle.Core.ParameterModelCollection.GetEnumerator">
            <summary>
            Returns an enumerator that can iterate through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/>
            that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="P:Castle.Core.ParameterModelCollection.Keys">
            <summary>
            Gets the keys.
            </summary>
            <value>The keys.</value>
            <remarks>
            Not implemented
            </remarks>
        </member>
        <member name="P:Castle.Core.ParameterModelCollection.Values">
            <summary>
            Gets the values.
            </summary>
            <value>The values.</value>
            <remarks>
            Not implemented
            </remarks>
        </member>
        <member name="P:Castle.Core.ParameterModelCollection.IsReadOnly">
            <summary>
            Gets a value indicating whether this instance is read only.
            </summary>
            <value>
            	<c>true</c> if this instance is read only; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Castle.Core.ParameterModelCollection.IsFixedSize">
            <summary>
            Gets a value indicating whether this instance is fixed size.
            </summary>
            <value>
            	<c>true</c> if this instance is fixed size; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Castle.Core.ParameterModelCollection.Item(System.Object)">
            <summary>
            Gets the <see cref="T:Castle.Core.ParameterModel"/> with the specified key.
            </summary>
            <value></value>
        </member>
        <member name="P:Castle.Core.ParameterModelCollection.Count">
            <summary>
            Gets the count.
            </summary>
            <value>The count.</value>
        </member>
        <member name="P:Castle.Core.ParameterModelCollection.SyncRoot">
            <summary>
            Gets the sync root.
            </summary>
            <value>The sync root.</value>
        </member>
        <member name="P:Castle.Core.ParameterModelCollection.IsSynchronized">
            <summary>
            Gets a value indicating whether this instance is synchronized.
            </summary>
            <value>
            	<c>true</c> if this instance is synchronized; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Castle.Core.PropertySet">
            <summary>
            Represents a property and the respective dependency.
            </summary>
        </member>
        <member name="M:Castle.Core.PropertySet.#ctor(System.Reflection.PropertyInfo,Castle.Core.DependencyModel)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.PropertySet"/> class.
            </summary>
            <param name="propertyInfo">The property info.</param>
            <param name="dependency">The dependency.</param>
        </member>
        <member name="P:Castle.Core.PropertySet.Property">
            <summary>
            Gets the property.
            </summary>
            <value>The property.</value>
        </member>
        <member name="P:Castle.Core.PropertySet.Dependency">
            <summary>
            Gets the dependency.
            </summary>
            <value>The dependency.</value>
        </member>
        <member name="T:Castle.Core.PropertySetCollection">
            <summary>
            Collection of <see cref="T:Castle.Core.PropertySet"/>
            </summary>
        </member>
        <member name="M:Castle.Core.PropertySetCollection.FindByPropertyInfo(System.Reflection.PropertyInfo)">
            <summary>
            Finds a PropertySet the by PropertyInfo.
            </summary>
            <param name="info">The info.</param>
            <returns></returns>
        </member>
        <member name="T:Castle.Core.Resource.IResource">
            <summary>
            Represents a 'streamable' resource. Can
            be a file, a resource in an assembly.
            </summary>
        </member>
        <member name="M:Castle.Core.Resource.IResource.GetStreamReader">
            <summary>
            Returns a reader for the stream
            </summary>
            <remarks>
            It's up to the caller to dispose the reader.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.Resource.IResource.GetStreamReader(System.Text.Encoding)">
            <summary>
            Returns a reader for the stream
            </summary>
            <remarks>
            It's up to the caller to dispose the reader.
            </remarks>
            <param name="encoding"></param>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.Resource.IResource.CreateRelative(System.String)">
            <summary>
            Returns an instance of <see cref="T:Castle.Core.Resource.IResource"/>
            created according to the <c>relativePath</c>
            using itself as the root.
            </summary>
            <param name="relativePath"></param>
            <returns></returns>
        </member>
        <member name="P:Castle.Core.Resource.IResource.FileBasePath">
            <summary>
            
            </summary>
            <remarks>
            Only valid for resources that
            can be obtained through relative paths
            </remarks>
        </member>
        <member name="T:Castle.Core.Resource.AbstractStreamResource">
            <summary>
            
            </summary>
        </member>
        <member name="F:Castle.Core.Resource.AbstractStreamResource.createStream">
            <summary>
            This returns a new stream instance each time it is called.
            It is the responsability of the caller to dispose of this stream
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.AssemblyResourceFactory">
            <summary>
            
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.IResourceFactory">
            <summary>
            Depicts the contract for resource factories.
            </summary>
        </member>
        <member name="M:Castle.Core.Resource.IResourceFactory.Accept(Castle.Core.Resource.CustomUri)">
            <summary>
            Used to check whether the resource factory
            is able to deal with the given resource
            identifier.
            </summary>
            <remarks>
            Implementors should return <c>true</c>
            only if the given identificator is supported
            by the resource factory
            </remarks>
            <param name="uri"></param>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.Resource.IResourceFactory.Create(Castle.Core.Resource.CustomUri)">
            <summary>
            Creates an <see cref="T:Castle.Core.Resource.IResource"/> instance
            for the given resource identifier
            </summary>
            <param name="uri"></param>
            <returns></returns>
        </member>
        <member name="M:Castle.Core.Resource.IResourceFactory.Create(Castle.Core.Resource.CustomUri,System.String)">
            <summary>
            Creates an <see cref="T:Castle.Core.Resource.IResource"/> instance
            for the given resource identifier
            </summary>
            <param name="uri"></param>
            <param name="basePath"></param>
            <returns></returns>
        </member>
        <member name="T:Castle.Core.Resource.FileResource">
            <summary>
            
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.FileResourceFactory">
            <summary>
            
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.StaticContentResource">
            <summary>
            Adapts a static string content as an <see cref="T:Castle.Core.Resource.IResource"/>
            </summary>
        </member>
        <member name="T:Castle.Core.Resource.UncResource">
            <summary>
            Enable access to files on network shares
            </summary>
        </member>
        <member name="T:Castle.Core.Smtp.DefaultSmtpSender">
            <summary>
            Default <see cref="T:Castle.Core.Smtp.IEmailSender"/> implementation.
            </summary>
        </member>
        <member name="T:Castle.Core.Smtp.IEmailSender">
            <summary>
            Email sender abstraction.
            </summary>
        </member>
        <member name="M:Castle.Core.Smtp.IEmailSender.Send(System.String,System.String,System.String,System.String)">
            <summary>
            Sends a mail message.
            </summary>
            <param name="from">From field</param>
            <param name="to">To field</param>
            <param name="subject">E-mail's subject</param>
            <param name="messageText">message's body</param>
        </member>
        <member name="M:Castle.Core.Smtp.IEmailSender.Send(System.Net.Mail.MailMessage)">
            <summary>
            Sends a <see cref="T:System.Net.Mail.MailMessage">message</see>. 
            </summary>
            <param name="message"><see cref="T:System.Net.Mail.MailMessage">Message</see> instance</param>
        </member>
        <member name="M:Castle.Core.Smtp.IEmailSender.Send(System.Collections.Generic.IEnumerable{System.Net.Mail.MailMessage})">
            <summary>
            Sends multiple <see cref="T:System.Net.Mail.MailMessage">messages</see>. 
            </summary>
            <param name="messages">List of <see cref="T:System.Net.Mail.MailMessage">messages</see></param>
        </member>
        <member name="M:Castle.Core.Smtp.DefaultSmtpSender.#ctor(System.String)">
            <summary>
            This service implementation
            requires a host name in order to work
            </summary>
            <param name="hostname">The smtp server name</param>
        </member>
        <member name="M:Castle.Core.Smtp.DefaultSmtpSender.Send(System.String,System.String,System.String,System.String)">
            <summary>
            Sends a message. 
            </summary>
            <exception cref="T:System.ArgumentNullException">If any of the parameters is null</exception>
            <param name="from">From field</param>
            <param name="to">To field</param>
            <param name="subject">e-mail's subject</param>
            <param name="messageText">message's body</param>
        </member>
        <member name="M:Castle.Core.Smtp.DefaultSmtpSender.Send(System.Net.Mail.MailMessage)">
            <summary>
            Sends a message. 
            </summary>
            <exception cref="T:System.ArgumentNullException">If the message is null</exception>
            <param name="message">Message instance</param>
        </member>
        <member name="M:Castle.Core.Smtp.DefaultSmtpSender.Configure(System.Net.Mail.SmtpClient)">
            <summary>
            Configures the sender
            with port information and eventual credential
            informed
            </summary>
            <param name="smtpClient">Message instance</param>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Port">
            <summary>
            Gets or sets the port used to 
            access the SMTP server
            </summary>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Hostname">
            <summary>
            Gets the hostname.
            </summary>
            <value>The hostname.</value>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.AsyncSend">
            <summary>
            Gets or sets a value which is used to 
            configure if emails are going to be sent asyncrhonously or not.
            </summary>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Timeout">
            <summary>
            Gets or sets a value that specifies 
            the amount of time after which a synchronous Send call times out.
            </summary>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.UseSsl">
            <summary>
            Gets or sets a value indicating whether the email should be sent using 
            a secure communication channel.
            </summary>
            <value><c>true</c> if should use SSL; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Domain">
            <summary>
            Gets or sets the domain.
            </summary>
            <value>The domain.</value>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.UserName">
            <summary>
            Gets or sets the name of the user.
            </summary>
            <value>The name of the user.</value>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.Password">
            <summary>
            Gets or sets the password.
            </summary>
            <value>The password.</value>
        </member>
        <member name="P:Castle.Core.Smtp.DefaultSmtpSender.HasCredentials">
            <summary>
            Gets a value indicating whether credentials were informed.
            </summary>
            <value>
            <see langword="true"/> if this instance has credentials; otherwise, <see langword="false"/>.
            </value>
        </member>
        <member name="T:Castle.Core.IServiceEnabledComponent">
            <summary>
            Defines that the implementation wants a 
            <see cref="T:System.IServiceProvider"/> in order to 
            access other components. The creator must be aware
            that the component might (or might not) implement 
            the interface.
            </summary>
            <remarks>
            Used by Castle Project components to, for example, 
            gather logging factories
            </remarks>
        </member>
        <member name="T:Castle.Core.IServiceProviderEx">
            <summary>
            Increments <c>IServiceProvider</c> with a generic service resolution operation.
            </summary>
        </member>
        <member name="T:Castle.Core.IServiceProviderExAccessor">
            <summary>
            This interface should be implemented by classes
            that are available in a bigger context, exposing
            the container to different areas in the same application.
            <para>
            For example, in Web application, the (global) HttpApplication
            subclasses should implement this interface to expose 
            the configured container
            </para>
            </summary>
        </member>
        <member name="T:Castle.Core.Pair`2">
            <summary>
            General purpose class to represent a standard pair of values. 
            </summary>
            <typeparam name="TFirst">Type of the first value</typeparam>
            <typeparam name="TSecond">Type of the second value</typeparam>
        </member>
        <member name="M:Castle.Core.Pair`2.#ctor(`0,`1)">
            <summary>
            Constructs a pair with its values
            </summary>
            <param name="first"></param>
            <param name="second"></param>
        </member>
        <member name="T:Castle.Core.ProxyServices">
            <summary>
            List of utility methods related to dynamic proxy operations
            </summary>
        </member>
        <member name="M:Castle.Core.ProxyServices.IsDynamicProxy(System.Type)">
            <summary>
            Determines whether the specified type is a proxy generated by
            DynamicProxy (1 or 2).
            </summary>
            <param name="type">The type.</param>
            <returns>
            	<c>true</c> if it is a proxy; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Castle.Core.ReflectionBasedDictionaryAdapter">
            <summary>
            Pendent
            </summary>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Castle.Core.ReflectionBasedDictionaryAdapter"/> class.
            </summary>
            <param name="target">The target.</param>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.Contains(System.Object)">
            <summary>
            Determines whether the <see cref="T:System.Collections.IDictionary"/> object contains an element with the specified key.
            </summary>
            <param name="key">The key to locate in the <see cref="T:System.Collections.IDictionary"/> object.</param>
            <returns>
            true if the <see cref="T:System.Collections.IDictionary"/> contains an element with the key; otherwise, false.
            </returns>
            <exception cref="T:System.ArgumentNullException">
            	<paramref name="key"/> is null. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.Add(System.Object,System.Object)">
            <summary>
            Adds an element with the provided key and value to the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <param name="key">The <see cref="T:System.Object"/> to use as the key of the element to add.</param>
            <param name="value">The <see cref="T:System.Object"/> to use as the value of the element to add.</param>
            <exception cref="T:System.ArgumentNullException">
            	<paramref name="key"/> is null. </exception>
            <exception cref="T:System.ArgumentException">An element with the same key already exists in the <see cref="T:System.Collections.IDictionary"/> object. </exception>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.IDictionary"/> is read-only.-or- The <see cref="T:System.Collections.IDictionary"/> has a fixed size. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.Clear">
            <summary>
            Removes all elements from the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.IDictionary"/> object is read-only. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.System#Collections#IDictionary#GetEnumerator">
            <summary>
            Returns an <see cref="T:System.Collections.IDictionaryEnumerator"/> object for the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IDictionaryEnumerator"/> object for the <see cref="T:System.Collections.IDictionary"/> object.
            </returns>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.Remove(System.Object)">
            <summary>
            Removes the element with the specified key from the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <param name="key">The key of the element to remove.</param>
            <exception cref="T:System.ArgumentNullException">
            	<paramref name="key"/> is null. </exception>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.IDictionary"/> object is read-only.-or- The <see cref="T:System.Collections.IDictionary"/> has a fixed size. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:System.Collections.ICollection"/> to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"/> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection"/>. The <see cref="T:System.Array"/> must have zero-based indexing.</param>
            <param name="index">The zero-based index in <paramref name="array"/> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">
            	<paramref name="array"/> is null. </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            	<paramref name="index"/> is less than zero. </exception>
            <exception cref="T:System.ArgumentException">
            	<paramref name="array"/> is multidimensional.-or- <paramref name="index"/> is equal to or greater than the length of <paramref name="array"/>.-or- The number of elements in the source <see cref="T:System.Collections.ICollection"/> is greater than the available space from <paramref name="index"/> to the end of the destination <paramref name="array"/>. </exception>
            <exception cref="T:System.ArgumentException">The type of the source <see cref="T:System.Collections.ICollection"/> cannot be cast automatically to the type of the destination <paramref name="array"/>. </exception>
        </member>
        <member name="M:Castle.Core.ReflectionBasedDictionaryAdapter.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.Item(System.Object)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> with the specified key.
            </summary>
            <value></value>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.Keys">
            <summary>
            Gets an <see cref="T:System.Collections.ICollection"/> object containing the keys of the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <value></value>
            <returns>An <see cref="T:System.Collections.ICollection"/> object containing the keys of the <see cref="T:System.Collections.IDictionary"/> object.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.Values">
            <summary>
            Gets an <see cref="T:System.Collections.ICollection"/> object containing the values in the <see cref="T:System.Collections.IDictionary"/> object.
            </summary>
            <value></value>
            <returns>An <see cref="T:System.Collections.ICollection"/> object containing the values in the <see cref="T:System.Collections.IDictionary"/> object.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IDictionary"/> object is read-only.
            </summary>
            <value></value>
            <returns>true if the <see cref="T:System.Collections.IDictionary"/> object is read-only; otherwise, false.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.IsFixedSize">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.IDictionary"/> object has a fixed size.
            </summary>
            <value></value>
            <returns>true if the <see cref="T:System.Collections.IDictionary"/> object has a fixed size; otherwise, false.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.Count">
            <summary>
            Gets the number of elements contained in the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
            <returns>The number of elements contained in the <see cref="T:System.Collections.ICollection"/>.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.SyncRoot">
            <summary>
            Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.
            </summary>
            <value></value>
            <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection"/>.</returns>
        </member>
        <member name="P:Castle.Core.ReflectionBasedDictionaryAdapter.IsSynchronized">
            <summary>
            Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection"/> is synchronized (thread safe).
            </summary>
            <value></value>
            <returns>true if access to the <see cref="T:System.Collections.ICollection"/> is synchronized (thread safe); otherwise, false.</returns>
        </member>
    </members>
</doc>
