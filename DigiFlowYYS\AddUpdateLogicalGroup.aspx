﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true"
    CodeFile="AddUpdateLogicalGroup.aspx.cs" Inherits="AddUpdateLogicalGroup" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" cellpadding="0" cellspacing="0" width="50%" align="center">
        <asp:Panel ID="WorkflowPanel" runat="server" Visible="true">
            <tr>
                <td style="padding: 5px; font-weight: 700;" align="left" width="100%" valign="top">A<PERSON><PERSON><PERSON>
                </td>
            </tr>
            <tr>
                <td style="padding: 5px" align="left" width="100%" valign="top">
                    <dx:ASPxComboBox ID="WorkFlowCombobox" runat="server" AutoPostBack="True" Width="250px" ValueType="System.Int64">
                    </dx:ASPxComboBox>
                </td>
            </tr>
        </asp:Panel>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <b>Grup İsmi</b>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <dx:ASPxTextBox ID="txtGroupName" runat="server" Width="350px" MaxLength="1024">
                </dx:ASPxTextBox>
                <asp:RequiredFieldValidator ID="rqfGroupName" runat="server" ControlToValidate="txtGroupName"
                    ErrorMessage="Lütfen bir grup ismi yazın." ValidationGroup="1"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <b>Açıklama</b>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <asp:TextBox ID="txtDescription" runat="server" Height="50px"
                    MaxLength="1024" onkeyup="return maxLength(this,'1024');"
                    onChange="return maxLength(this,'1024');"
                    TextMode="MultiLine" ValidationGroup="1"
                    Width="300px"></asp:TextBox>
                <asp:RequiredFieldValidator ID="rqfDescription" runat="server" ControlToValidate="txtDescription"
                    ErrorMessage="Lütfen bir açıklama yazın." ValidationGroup="1"></asp:RequiredFieldValidator>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <dx:ASPxCheckBox ID="cbCanBeDeleted" runat="server" Text="Silinebilir Bir Grup">
                </dx:ASPxCheckBox>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <dx:ASPxCheckBox ID="cbIsOnePersonGroup" runat="server"
                    Text="Tek Kişilik Bir Grup">
                </dx:ASPxCheckBox>
            </td>
        </tr>
        <tr>
            <td align="center" style="padding: 5px" valign="top" width="100%">
                <dx:ASPxButton ID="SaveASPxButton" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                    CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                    Text="Kaydet" OnClick="SaveASPxButton_Click" CausesValidation="true" ValidationGroup="1">
                </dx:ASPxButton>
            </td>
        </tr>
    </table>
</asp:Content>