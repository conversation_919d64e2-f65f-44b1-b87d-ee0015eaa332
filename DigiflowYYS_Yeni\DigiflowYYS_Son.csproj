﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9D11324B-B908-43EF-8D83-03A6D5509110}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DigiflowYYS_Yeni</RootNamespace>
    <AssemblyName>DigiflowYYS_Yeni</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication>disabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>enabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <Use64BitIISExpress />
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ActiveDirectoryHelpers, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\ActiveDirectoryHelpers.dll</HintPath>
    </Reference>
    <Reference Include="AjaxControlToolkit">
      <HintPath>\\dtl1iis3\Deployment\AjaxControlToolkit.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\SharedAssemblies\DevExpress\DevExpress.Data.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Design.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\SharedAssemblies\DevExpress\DevExpress.Design.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v12.1.Core, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Reports.v12.1.Designer, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\SharedAssemblies\DevExpress\DevExpress.Reports.v12.1.Designer.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxEditors.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\SharedAssemblies\DXperience 12.1\Bin\Framework\DevExpress.Web.ASPxEditors.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxGridView.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\SharedAssemblies\DXperience 12.1\Bin\Framework\DevExpress.Web.ASPxGridView.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxGridView.v12.1.Export, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\SharedAssemblies\DXperience 12.1\Bin\Framework\DevExpress.Web.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\SharedAssemblies\DevExpress\DevExpress.Xpo.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Common.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Common.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.BaseServices.DAL">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.BaseServices.DAL.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Library">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.Library.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Repository">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.BaseServices">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.BaseServices.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Common">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Common.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authentication">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Authentication.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authorization">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.ExceptionEntites">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.ExceptionEntites.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WebCore, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WebCore.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowServicesHelper">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Repository">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="NHibernate">
      <HintPath>\\dtl1iis3\Deployment\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate.ByteCode.LinFu">
      <HintPath>\\dtl1iis3\Deployment\NHibernate.ByteCode.LinFu.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=4.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\ODPNET\x86\Net4\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="AddUpdateLogicalGroupMemberNew.aspx" />
    <Content Include="AdminTestPage.aspx" />
    <Content Include="bin\hibernate.cfg.xml" />
    <Content Include="css\blitzer\images\ui-bg_diagonals-thick_75_f3d8d8_40x40.png" />
    <Content Include="css\blitzer\images\ui-bg_dots-small_65_a6a6a6_2x2.png" />
    <Content Include="css\blitzer\images\ui-bg_flat_0_333333_40x100.png" />
    <Content Include="css\blitzer\images\ui-bg_flat_65_ffffff_40x100.png" />
    <Content Include="css\blitzer\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="css\blitzer\images\ui-bg_glass_55_fbf8ee_1x400.png" />
    <Content Include="css\blitzer\images\ui-bg_highlight-hard_100_eeeeee_1x100.png" />
    <Content Include="css\blitzer\images\ui-bg_highlight-hard_100_f6f6f6_1x100.png" />
    <Content Include="css\blitzer\images\ui-bg_highlight-soft_15_cc0000_1x100.png" />
    <Content Include="css\blitzer\images\ui-icons_004276_256x240.png" />
    <Content Include="css\blitzer\images\ui-icons_cc0000_256x240.png" />
    <Content Include="css\blitzer\images\ui-icons_ffffff_256x240.png" />
    <Content Include="css\blitzer\jquery-ui-1.8.5.custom.css" />
    <Content Include="css\bootstrap%404.4.1_dist_css_bootstrap.min.css" />
    <Content Include="css\bootstrap.min.css" />
    <Content Include="css\digiturk.css" />
    <Content Include="css\jquery-ui.css" />
    <Content Include="css\select2.min.css" />
    <Content Include="js\bootstrap.min.js" />
    <Content Include="js\jquery-3.3.1.min.js" />
    <Content Include="js\jquery-ui.min.js" />
    <Content Include="js\jquery.min.js" />
    <Content Include="js\select2.min.js" />
    <Content Include="js\jquery-1.9.min.js" />
    <Content Include="YYSLogicalGroupSenkronizasyon.asmx" />
    <None Include="DigiFlowYYS.sln" />
    <Content Include="Global.asax" />
    <Content Include="images\1x1.png" />
    <Content Include="images\Accept.gif" />
    <Content Include="images\Arrow.png" />
    <Content Include="images\Decline.gif" />
    <Content Include="images\errorIcon.gif" />
    <Content Include="images\GrayDot.png" />
    <Content Include="images\MenuBullet.png" />
    <Content Include="images\plus_icon_trans.gif" />
    <Content Include="images\processing.gif" />
    <Content Include="images\wf.gif" />
    <Content Include="images\Wf.png" />
    <Content Include="js\digiturk.js" />
    <None Include="Properties\PublishProfiles\KeremYYSPublish.pubxml" />
    <None Include="Web.config" />
    <Content Include="AddUpdateLogicalGroup.aspx" />
    <Content Include="AddUpdateLogicalGroupMember.aspx" />
    <Content Include="AddUpdateStateAuthorization.aspx" />
    <Content Include="AddUpdateWorkflowAdmin.aspx" />
    <Content Include="AddUpdateWorkflowRule.aspx" />
    <Content Include="LogicalGroups.aspx" />
    <Content Include="SID.aspx" />
    <Content Include="StateAuthorizations.aspx" />
    <Content Include="TopluOnay.aspx" />
    <Content Include="MonitoringWorkflowAdmin.aspx" />
    <Content Include="UserControls\OrganizationTreeWebUserControl.ascx" />
    <Content Include="ErrorPage.aspx" />
    <Content Include="WorkflowRules.aspx" />
    <Content Include="WorkflowAdmins.aspx" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AddUpdateLogicalGroupMemberNew.aspx.cs">
      <DependentUpon>AddUpdateLogicalGroupMemberNew.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AddUpdateLogicalGroupMemberNew.aspx.designer.cs">
      <DependentUpon>AddUpdateLogicalGroupMemberNew.aspx</DependentUpon>
    </Compile>
    <Compile Include="AdminPanel\AdminPanelMasterPage.Master.cs">
      <DependentUpon>AdminPanelMasterPage.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminPanel\AdminPanelMasterPage.Master.designer.cs">
      <DependentUpon>AdminPanelMasterPage.Master</DependentUpon>
    </Compile>
    <Compile Include="AdminTestPage.aspx.cs">
      <DependentUpon>AdminTestPage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AdminTestPage.aspx.designer.cs">
      <DependentUpon>AdminTestPage.aspx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\DevExpress_Web_ASPxGridView_v10_2.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DevExpress_Web_ASPxGridView_v10_2.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\Resource.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\Resource.en.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource.en.resx</DependentUpon>
    </Compile>
    <Compile Include="ErrorMasterPage.Master.cs">
      <DependentUpon>ErrorMasterPage.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ErrorMasterPage.Master.designer.cs">
      <DependentUpon>ErrorMasterPage.Master</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="AddUpdateLogicalGroup.aspx.cs">
      <DependentUpon>AddUpdateLogicalGroup.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AddUpdateLogicalGroup.aspx.designer.cs">
      <DependentUpon>AddUpdateLogicalGroup.aspx</DependentUpon>
    </Compile>
    <Compile Include="AddUpdateLogicalGroupMember.aspx.cs">
      <DependentUpon>AddUpdateLogicalGroupMember.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AddUpdateLogicalGroupMember.aspx.designer.cs">
      <DependentUpon>AddUpdateLogicalGroupMember.aspx</DependentUpon>
    </Compile>
    <Compile Include="AddUpdateStateAuthorization.aspx.cs">
      <DependentUpon>AddUpdateStateAuthorization.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AddUpdateStateAuthorization.aspx.designer.cs">
      <DependentUpon>AddUpdateStateAuthorization.aspx</DependentUpon>
    </Compile>
    <Compile Include="AddUpdateWorkflowAdmin.aspx.cs">
      <DependentUpon>AddUpdateWorkflowAdmin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AddUpdateWorkflowAdmin.aspx.designer.cs">
      <DependentUpon>AddUpdateWorkflowAdmin.aspx</DependentUpon>
    </Compile>
    <Compile Include="AddUpdateWorkflowRule.aspx.cs">
      <DependentUpon>AddUpdateWorkflowRule.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="AddUpdateWorkflowRule.aspx.designer.cs">
      <DependentUpon>AddUpdateWorkflowRule.aspx</DependentUpon>
    </Compile>
    <Compile Include="LogicalGroups.aspx.cs">
      <DependentUpon>LogicalGroups.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LogicalGroups.aspx.designer.cs">
      <DependentUpon>LogicalGroups.aspx</DependentUpon>
    </Compile>
    <Compile Include="SID.aspx.cs">
      <DependentUpon>SID.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SID.aspx.designer.cs">
      <DependentUpon>SID.aspx</DependentUpon>
    </Compile>
    <Compile Include="Site1.Master.cs">
      <DependentUpon>Site1.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Site1.Master.designer.cs">
      <DependentUpon>Site1.Master</DependentUpon>
    </Compile>
    <Compile Include="StateAuthorizations.aspx.cs">
      <DependentUpon>StateAuthorizations.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="StateAuthorizations.aspx.designer.cs">
      <DependentUpon>StateAuthorizations.aspx</DependentUpon>
    </Compile>
    <Compile Include="TopluOnay.aspx.cs">
      <DependentUpon>TopluOnay.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="TopluOnay.aspx.designer.cs">
      <DependentUpon>TopluOnay.aspx</DependentUpon>
    </Compile>
    <Compile Include="MonitoringWorkflowAdmin.aspx.cs">
      <DependentUpon>MonitoringWorkflowAdmin.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MonitoringWorkflowAdmin.aspx.designer.cs">
      <DependentUpon>MonitoringWorkflowAdmin.aspx</DependentUpon>
    </Compile>
    <Compile Include="UserControls\OrganizationTreeWebUserControl.ascx.cs">
      <DependentUpon>OrganizationTreeWebUserControl.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserControls\OrganizationTreeWebUserControl.ascx.designer.cs">
      <DependentUpon>OrganizationTreeWebUserControl.ascx</DependentUpon>
    </Compile>
    <Compile Include="ErrorPage.aspx.cs">
      <DependentUpon>ErrorPage.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ErrorPage.aspx.designer.cs">
      <DependentUpon>ErrorPage.aspx</DependentUpon>
    </Compile>
    <Compile Include="WorkflowRules.aspx.cs">
      <DependentUpon>WorkflowRules.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WorkflowRules.aspx.designer.cs">
      <DependentUpon>WorkflowRules.aspx</DependentUpon>
    </Compile>
    <Compile Include="WorkflowAdmins.aspx.cs">
      <DependentUpon>WorkflowAdmins.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WorkflowAdmins.aspx.designer.cs">
      <DependentUpon>WorkflowAdmins.aspx</DependentUpon>
    </Compile>
    <Compile Include="YYSLogicalGroupSenkronizasyon.asmx.cs">
      <DependentUpon>YYSLogicalGroupSenkronizasyon.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="AdminPanel\AdminPanelMasterPage.Master" />
    <None Include="App_Data\PublishProfiles\KeremPublish.pubxml" />
    <None Include="App_Data\PublishProfiles\RecepPublish.pubxml" />
    <None Include="vwd.webinfo" />
    <Content Include="ErrorMasterPage.Master" />
    <Content Include="Site1.Master" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="website.publishproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\DevExpress_Web_ASPxGridView_v10_2.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>DevExpress_Web_ASPxGridView_v10_2.designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_GlobalResources\Resource.en.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>Resource.en.designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_GlobalResources\Resource.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>Resource.designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_LocalResources\OrganizationTreeWebUserControl.en.resx" />
    <Content Include="App_LocalResources\OrganizationTreeWebUserControl.resx" />
    <EmbeddedResource Include="licenses.licx" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Code\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Digiturk.Workflow.Digiflow.YYS.Core\Digiturk.Workflow.Digiflow.YYS.Core.csproj">
      <Project>{fc3cabd6-9696-4ef8-8153-ad999e715b19}</Project>
      <Name>Digiturk.Workflow.Digiflow.YYS.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>61679</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:61679/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>