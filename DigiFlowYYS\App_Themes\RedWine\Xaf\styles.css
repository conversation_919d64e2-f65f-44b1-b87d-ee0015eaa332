﻿span, label, a, .GroupHeader, .StaticText, .TextCell, .Layout .Caption {
	font-family: Tahoma, Arial, Helvetica, sans-serif;
}
/* Layout Group Header */
.GroupHeader td.Label {
	width: 100%;
	color: black;
	font-size: 14pt;
	padding: 0px 5px 0px 5px;
}
/* Layout Group Content */
.Layout .Caption {
	/* width: 150px; */
	color: Black;
	vertical-align: middle;
	padding: 2px 0px 2px 0px;
	white-space: nowrap;
}
.Layout .StaticText {
	color: #4f4f4f;
}
.Layout .Item .ACH
{
	margin: 0px !important;
}
/* Standard Grid*/
.Grid
{
	width: 100%;
	background-color: White;
}
.GridList a
{
	color: Black;
	text-decoration: none;
}
.GridList a:hover
{
	color: black;
	text-decoration: underline;
}
.GridList
{
	width: 100%;
	color: Black;
	text-align: left;
	font-weight: normal;
}
.EmptyGridList
{
	font-weight: bold;
	font-size: 85%;
	color: #7F7F7F;
	text-align: center;
	padding: 4px;
}
.GridHeader
{
	border: solid 1px #86BBEC;
}
.GridHeaderItem
{
	border: solid 1px #86BBEC;
	background-color: #C4DFF8;
	font-size: 85%;
	font-weight: normal;
	padding: 4px 7px 4px 7px;
}
.GridItem
	{
	padding: 3px 7px 3px 7px;
	border: solid 1px #C2C2C2;
	color: #1E72C2;
	font-size: 85%;
	font-family:Tahoma;
	_word-wrap: break-word;
	_word-break : break-word;
}
.GridLastRow
{
}
.GridHoverRow td
{
	cursor: hand;
	cursor: pointer;
	background-color: #F7F7F7;
}
.GridPager
{
	padding: 5px;
	color: Gray;
}
.GridPager A
{
	text-decoration: underline;
	color: #1E72C2;
}
/* Obsolete: DataGridContextMenu */
.ContextMenuHeader
{
	border: solid 1px #86BBEC;
	background-color: #C4DFF8;
	width: 0;
}
.ContextMenuItem
{
	width: 0;
	padding: 2px 5px 2px 5px;
	border: solid 1px #C2C2C2;
	text-align:center;
	vertical-align:middle;
	white-space: nowrap;
}
.ContextMenuItem *
{
	white-space: nowrap;
}
/* Error info */
.ErrorMessage {
	width: 100%;
	background-color: #FEE2C4;
	border: solid 1px #E3BFA5;
	vertical-align: middle;
}
.ErrorLabel {
	text-align: left;
	color: black;
}
/* Progress Control */
.ProgressHover
{
	background-color: white;
	filter:alpha(opacity=50);
	opacity:0.5;
}
.Progress
{
	border:none;
	background-color:transparent;
	vertical-align:middle;
	position:absolute;
}
.Progress td
{
	background-color:transparent;
}
/* Obsolete: ActionContainerBase and HorizontalSingleChoiceAsOperationActionItem */
.ActionLabel {
	font-size: 85%;
	color: Black;
}
.ParametrizedActionControl {
	margin: 2px 1px;
}
.dxm-item .ParametrizedActionControl {
	margin: 0px !important;
}