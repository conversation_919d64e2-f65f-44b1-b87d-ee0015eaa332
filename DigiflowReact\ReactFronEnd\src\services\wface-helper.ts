import IOC from "@wface/ioc";
import { UserContext, AppContext } from "@wface/store";
import {useEffect, useState} from "react";
import axios from "axios";


export const getUser = () => {
    let appContext: AppContext;
    if (IOC.isBound("AppContext")) {
        appContext = IOC.get<AppContext>("AppContext");
    }
    return appContext.cache["user"];
};
export const setUser = (user) => {
    let appContext: AppContext;
    if (IOC.isBound("AppContext")) {
        appContext = IOC.get<AppContext>("AppContext");
    }
    appContext.cache["user"] = user
};
