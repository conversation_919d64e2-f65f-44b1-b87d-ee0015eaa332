/* -- ASPxCallbackPanel -- */
.dxcpLoadingPanel
{
	font: 9pt Tahoma;
	color: #303030;
}
.dxcpLoadingPanel td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}
.dxcpLoadingPanelWithContent
{
	font: 9pt Tahoma;
	color: #303030;
	border: solid 1px #9F9F9F;
	background-color: White;
}
.dxcpLoadingPanelWithContent td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}
.dxcpLoadingDiv
{
	background-color: Gray;
	opacity: 0.01;
	filter:progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=1);
}
/* Disabled */
.dxcpDisabled
{
	color: #acacac;
	cursor: default;
}

/* -- ASPxCloudControl -- */
.dxccControl a:hover
{
    text-decoration:underline!important;
}
.dxccControl a
{
	text-decoration:none!important;
	color: #1E3695;
}
.dxccControl
{
	font-family: Tahoma, Verdana, Arial;
	text-decoration:none;
	color: #1E3695;
	background-color: #FFFFFF;
}
/* Disabled */
.dxccDisabled
{
	color: #808080;
	cursor: default;
}

/* -- ASPxDataView -- */
.dxdvControl
{
	font: 9pt Tahoma;
	color: black;
}
.dxdvControl td.dxdvCtrl
{
	padding: 12px 40px 12px 40px;
}
.dxdvLoadingPanel
{
	border: solid 1px #9F9F9F;
	background-color: white;
	font: 9pt Tahoma;
	color: #303030;
}
.dxdvLoadingPanel td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}
.dxdvContent
{
}
.dxdvItem,
.dxdvFlowItem
{
	font: 9pt Tahoma;
	color: #787878;
	border: solid 1px #A8A8A8;
	background-color: #F0F0F0;
	padding: 12px 12px 12px 14px;
	height: 180px; /*if IE*/
	height: expression("154px");
}
.dxdvFlowItem
{
	float: left;
	overflow: hidden;
}
.dxdvFlowItemsContainer
{
}
.dxdvEmptyItem
{
	font: 9pt Tahoma;
	color: #787878;
	text-align: left;
	vertical-align: top;
	padding: 12px 12px 12px 14px;
	height: 180px;
	/*if IE*/
	height:expression("154px");
}
.dxdvPagerPanel
{
}
.dxdvEmptyData
{
    color: Gray;
}
/* Disabled */
.dxdvDisabled
{
	color: #808080;
	cursor: default;
}

/* -- ASPxHeadline -- */
.dxhlControl
{
	font: 10px Verdana;
	color: black;
}
.dxhlContent
{
	font: 8pt Verdana;
	color: black;
}
.dxhlDate
{
	color: Gray;
	white-space: nowrap;
}
.dxhlHeader
{
	font: 10pt Tahoma;
	color: #464646;
	font-weight: bold;
	line-height: 17px;
}
.dxhlDateHeader
{
	font: 10pt Tahoma;
	color: Gray;
	font-weight: normal;
}
.dxhlLeftPanel
{
	font: 10px Verdana;
	color: black;
}
.dxhlRightPanel
{
	font: 10px Verdana;
	color: black;
}
.dxhlDateLeftPanel
{
	font: 10px Verdana;
	color: Gray;
	white-space: nowrap;
}
.dxhlDateRightPanel
{
	font: 10px Verdana;
	color: Gray;
	white-space: nowrap;
}
.dxhlTailDiv
{
	font: 10px Verdana;
	color: Black;
}
.dxhlTailDiv a
{
	color: #1E3695;
}
.dxhlTailDiv a:hover
{
    text-decoration: none;
}
.dxhlTailDiv a:visited
{
    color: #996085;
}
.dxhlContent a.dxhl
{
	color: #1E3695;
}
.dxhlContent a.dxhl:hover
{
    text-decoration: none;
}
.dxhlContent a.dxhl:visited
{
    color: #996085;
}
/* Disabled */
.dxhlDisabled
{
	color: #acacac;
	cursor: default;
}

/* -- ASPxLoadingPanel -- */
.dxlpLoadingPanel
{
	font: 9pt Tahoma;
	color: #303030;
	background-color: white;
	border: solid 1px #9F9F9F;
}
.dxlpLoadingPanel td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}
.dxlpLoadingDiv
{
	background-color: #777777;
	opacity: 0.7;
	filter:progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=70);
}
/* -- ASPxMenu -- */
.dxmControl
{
	font: 9pt Tahoma;
	color: black;
}
.dxmControl a,
.dxmMenu a,
.dxmVerticalMenu a,
.dxmSubMenu a
{
	color: black;
	text-decoration: none;
}
.dxmLoadingPanel
{
	font: 9pt Tahoma;
	color: #303030;
}
.dxmLoadingPanel td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 6px 6px 6px 6px;
}
.dxmMenu,
.dxmVerticalMenu
{
	font: 9pt Tahoma;
	color: black;
	background-color: #F0F0F0;
	border: solid 1px #A8A8A8;
	padding: 2px 2px 2px 2px;
}
.dxmMenuGutter,
.dxmMenuRtlGutter
{
}

.dxmMenuSeparator .dx,
.dxmMenuFullHeightSeparator .dx
{
	font-size: 0;
	line-height: 0;
	overflow: hidden;
	width: 1px;
	height: 1px;
}
.dxmMenuSeparator,
.dxmMenuFullHeightSeparator
{
	width: 1px;
}

.dxmMenuSeparator .dx,
.dxmMenuFullHeightSeparator,
.dxmMenuVerticalSeparator
{
	background-color: #A8A8A8;
	width: 1px;
}
.dxmMenuSeparator .dx
{
	height: 13px;
}
.dxmMenuFullHeightSeparator
{
	display: none;
}
.dxmMenuVerticalSeparator
{
	width: 100%;
	height: 1px;
}

.dxmMenuItem,
.dxmMenuItemWithImage,
.dxmMenuItemWithPopOutImage,
.dxmMenuItemWithImageWithPopOutImage,
.dxmVerticalMenuItem,
.dxmVerticalMenuItemWithImage,
.dxmVerticalMenuItemWithPopOutImage,
.dxmVerticalMenuItemWithImageWithPopOutImage,
.dxmVerticalMenuRtlItem,
.dxmVerticalMenuRtlItemWithImage,
.dxmVerticalMenuRtlItemWithPopOutImage,
.dxmVerticalMenuRtlItemWithImageWithPopOutImage,
.dxmMenuLargeItem,
.dxmMenuLargeItemWithImage,
.dxmMenuLargeItemWithPopOutImage,
.dxmMenuLargeItemWithImageWithPopOutImage,
.dxmVerticalMenuLargeItem,
.dxmVerticalMenuLargeItemWithImage,
.dxmVerticalMenuLargeItemWithPopOutImage,
.dxmVerticalMenuLargeItemWithImageWithPopOutImage,
.dxmVerticalMenuLargeRtlItem,
.dxmVerticalMenuLargeRtlItemWithImage,
.dxmVerticalMenuLargeRtlItemWithPopOutImage,
.dxmVerticalMenuLargeRtlItemWithImageWithPopOutImage
{
	font: 9pt Tahoma;
	color: black;
	white-space: nowrap;
}
.dxmMenuItem,
.dxmMenuItemWithImage
{
	padding: 4px 8px 5px;
}
.dxmMenuItemWithPopOutImage,
.dxmMenuItemWithImageWithPopOutImage
{
	padding: 4px 6px 5px 8px;
}
.dxmVerticalMenuItem
{
	padding: 4px 19px 5px 8px;
}
.dxmVerticalMenuRtlItem
{
	padding: 4px 8px 5px 19px;
}
.dxmVerticalMenuItemWithImage
{
	padding: 4px 19px 5px 3px;
}
.dxmVerticalMenuRtlItemWithImage
{
	padding: 4px 3px 5px 19px;
}
.dxmVerticalMenuItemWithPopOutImage
{
	padding: 4px 6px 5px 8px;
}
.dxmVerticalMenuRtlItemWithPopOutImage
{
	padding: 4px 8px 5px 6px;
}
.dxmVerticalMenuItemWithImageWithPopOutImage
{
	padding: 4px 6px 5px 3px;
}
.dxmVerticalMenuRtlItemWithImageWithPopOutImage
{
	padding: 4px 3px 5px 6px;
}
.dxmMenuLargeItem,
.dxmMenuLargeItemWithImage
{
	padding: 4px 12px 5px 11px;
}
.dxmMenuLargeItemWithPopOutImage,
.dxmMenuLargeItemWithImageWithPopOutImage
{
	padding: 4px 6px 5px 11px;
}
.dxmVerticalMenuLargeItem,
.dxmVerticalMenuLargeItemWithImage
{
	padding: 6px 12px 7px 11px;
}
.dxmVerticalMenuLargeRtlItem,
.dxmVerticalMenuLargeRtlItemWithImage
{
	padding: 6px 11px 7px 12px;
}
.dxmVerticalMenuLargeItemWithPopOutImage,
.dxmVerticalMenuLargeItemWithImageWithPopOutImage
{
	padding: 6px  6px 7px 11px;
}
.dxmVerticalMenuLargeRtlItemWithPopOutImage,
.dxmVerticalMenuLargeRtlItemWithImageWithPopOutImage
{
	padding: 6px  11px 7px 6px;
}
.dxmMenuItemDropDownButton,
.dxmMenuLargeItemDropDownButton
{
	padding-right: 5px;
	padding-left: 5px;
}
.dxmMenuRtlItemDropDownButton,
.dxmMenuLargeRtlItemDropDownButton
{
	padding-right: 5px;
	padding-left: 5px;
}
.dxmVerticalMenuItemDropDownButton,
.dxmVerticalMenuLargeItemDropDownButton
{
	padding-right: 5px;
	padding-left: 5px;
}
.dxmVerticalMenuRtlItemDropDownButton,
.dxmVerticalMenuLargeRtlItemDropDownButton
{
	padding-right: 5px;
	padding-left: 5px;
}
.dxmMenuItemSelected,
.dxmMenuItemSelectedWithImage,
.dxmMenuItemSelectedWithPopOutImage,
.dxmMenuItemSelectedWithImageWithPopOutImage,
.dxmVerticalMenuItemSelected,
.dxmVerticalMenuItemSelectedWithImage,
.dxmVerticalMenuItemSelectedWithPopOutImage,
.dxmVerticalMenuItemSelectedWithImageWithPopOutImage,
.dxmVerticalMenuRtlItemSelected,
.dxmVerticalMenuRtlItemSelectedWithImage,
.dxmVerticalMenuRtlItemSelectedWithPopOutImage,
.dxmVerticalMenuRtlItemSelectedWithImageWithPopOutImage,
.dxmMenuLargeItemSelected,
.dxmMenuLargeItemSelectedWithImage,
.dxmMenuLargeItemSelectedWithPopOutImage,
.dxmMenuLargeItemSelectedWithImageWithPopOutImage,
.dxmVerticalMenuLargeItemSelected,
.dxmVerticalMenuLargeItemWithImageSelected,
.dxmVerticalMenuLargeItemSelectedWithPopOutImage,
.dxmVerticalMenuLargeItemSelectedWithImageWithPopOutImage,
.dxmVerticalMenuLargeRtlItemSelected,
.dxmVerticalMenuLargeRtlItemWithImageSelected,
.dxmVerticalMenuLargeRtlItemSelectedWithPopOutImage,
.dxmVerticalMenuLargeRtlItemSelectedWithImageWithPopOutImage
{
	background-color: white;
	border: solid 1px #888888;
}
.dxmMenuItemSelected,
.dxmMenuItemSelectedWithImage
{
	padding: 3px 7px 4px 7px;
}
.dxmMenuItemSelectedWithPopOutImage,
.dxmMenuItemSelectedWithImageWithPopOutImage
{
	padding: 3px 5px 4px 7px;
}
.dxmVerticalMenuItemSelected
{
	padding: 3px 18px 4px 7px;
}
.dxmVerticalMenuRtlItemSelected
{
	padding: 3px 7px 4px 18px;
}
.dxmVerticalMenuItemSelectedWithImage
{
	padding: 3px 18px 4px 2px;
}
.dxmVerticalMenuRtlItemSelectedWithImage
{
	padding: 3px 2px 4px 18px;
}
.dxmVerticalMenuItemSelectedWithPopOutImage
{
	padding: 3px 5px 4px 7px;
}
.dxmVerticalMenuRtlItemSelectedWithPopOutImage
{
	padding: 3px 7px 4px 5px;
}
.dxmVerticalMenuItemSelectedWithImageWithPopOutImage
{
	padding: 3px 5px 4px 2px;
}
.dxmVerticalMenuRtlItemSelectedWithImageWithPopOutImage
{
	padding: 3px 2px 4px 5px;
}
.dxmMenuLargeItemSelected,
.dxmMenuLargeItemSelectedWithImage
{
	padding: 3px 11px 4px 10px;
}
.dxmMenuLargeItemSelectedWithPopOutImage,
.dxmMenuLargeItemSelectedWithImageWithPopOutImage
{
	padding: 3px 5px 4px 10px;
}
.dxmVerticalMenuLargeItemSelected,
.dxmVerticalMenuLargeItemSelectedWithImage
{
	padding: 5px 11px 6px 10px;
}
.dxmVerticalMenuLargeRtlItemSelected,
.dxmVerticalMenuLargeRtlItemSelectedWithImage
{
	padding: 5px 10px 6px 11px;
}
.dxmVerticalMenuLargeItemSelectedWithPopOutImage,
.dxmVerticalMenuLargeItemSelectedWithImageWithPopOutImage
{
	padding: 5px 5px 6px 10px;
}
.dxmVerticalMenuLargeRtlItemSelectedWithPopOutImage,
.dxmVerticalMenuLargeRtlItemSelectedWithImageWithPopOutImage
{
	padding: 5px 10px 6px 5px;
}
.dxmMenuItemDropDownButtonSelected,
.dxmMenuLargeItemDropDownButtonSelected
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmMenuRtlItemDropDownButtonSelected,
.dxmMenuLargeRtlItemDropDownButtonSelected
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmVerticalMenuItemDropDownButtonSelected,
.dxmVerticalMenuLargeItemDropDownButtonSelected
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmVerticalMenuRtlItemDropDownButtonSelected,
.dxmVerticalMenuLargeRtlItemDropDownButtonSelected
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmMenuItemChecked,
.dxmMenuItemCheckedWithImage,
.dxmMenuItemCheckedWithPopOutImage,
.dxmMenuItemCheckedWithImageWithPopOutImage,
.dxmVerticalMenuItemChecked,
.dxmVerticalMenuItemCheckedWithImage,
.dxmVerticalMenuItemCheckedWithPopOutImage,
.dxmVerticalMenuItemCheckedWithImageWithPopOutImage,
.dxmVerticalMenuRtlItemChecked,
.dxmVerticalMenuRtlItemCheckedWithImage,
.dxmVerticalMenuRtlItemCheckedWithPopOutImage,
.dxmVerticalMenuRtlItemCheckedWithImageWithPopOutImage,
.dxmMenuLargeItemChecked,
.dxmMenuLargeItemCheckedWithImage,
.dxmMenuLargeItemCheckedWithPopOutImage,
.dxmMenuLargeItemCheckedWithImageWithPopOutImage,
.dxmVerticalMenuLargeItemChecked,
.dxmVerticalMenuLargeItemWithImageChecked,
.dxmVerticalMenuLargeItemCheckedWithPopOutImage,
.dxmVerticalMenuLargeItemCheckedWithImageWithPopOutImage,
.dxmVerticalMenuLargeRtlItemChecked,
.dxmVerticalMenuLargeRtlItemWithImageChecked,
.dxmVerticalMenuLargeRtlItemCheckedWithPopOutImage,
.dxmVerticalMenuLargeRtlItemCheckedWithImageWithPopOutImage
{
	background-color: white;
	border: solid 1px #888888;
}
.dxmMenuItemChecked,
.dxmMenuItemCheckedWithImage
{
	padding: 3px 7px 4px 7px;
}
.dxmMenuItemCheckedWithPopOutImage,
.dxmMenuItemCheckedWithImageWithPopOutImage
{
	padding: 3px 5px 4px 7px;
}
.dxmVerticalMenuItemChecked
{
	padding: 3px 18px 4px 7px;
}
.dxmVerticalMenuRtlItemChecked
{
	padding: 3px 7px 4px 18px;
}
.dxmVerticalMenuItemCheckedWithImage
{
	padding: 3px 18px 4px 2px;
}
.dxmVerticalMenuRtlItemCheckedWithImage
{
	padding: 3px 2px 4px 18px;
}
.dxmVerticalMenuItemCheckedWithPopOutImage
{
	padding: 3px 5px 4px 7px;
}
.dxmVerticalMenuRtlItemCheckedWithPopOutImage
{
	padding: 3px 7px 4px 5px;
}
.dxmVerticalMenuItemCheckedWithImageWithPopOutImage
{
	padding: 3px 5px 4px 2px;
}
.dxmVerticalMenuRtlItemCheckedWithImageWithPopOutImage
{
	padding: 3px 2px 4px 5px;
}
.dxmMenuLargeItemChecked,
.dxmMenuLargeItemCheckedWithImage
{
	padding: 3px 11px 4px 10px;
}
.dxmMenuLargeItemCheckedWithPopOutImage,
.dxmMenuLargeItemCheckedWithImageWithPopOutImage
{
	padding: 3px 5px 4px 10px;
}
.dxmVerticalMenuLargeItemChecked,
.dxmVerticalMenuLargeItemCheckedWithImage
{
	padding: 5px 11px 6px 10px;
}
.dxmVerticalMenuLargeRtlItemChecked,
.dxmVerticalMenuLargeRtlItemCheckedWithImage
{
	padding: 5px 10px 6px 11px;
}
.dxmVerticalMenuLargeItemCheckedWithPopOutImage,
.dxmVerticalMenuLargeItemCheckedWithImageWithPopOutImage
{
	padding: 5px 5px 6px 10px;
}
.dxmVerticalMenuLargeRtlItemCheckedWithPopOutImage,
.dxmVerticalMenuLargeRtlItemCheckedWithImageWithPopOutImage
{
	padding: 5px 10px 6px 5px;
}
.dxmMenuItemDropDownButtonChecked,
.dxmMenuLargeItemDropDownButtonChecked
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmMenuRtlItemDropDownButtonChecked,
.dxmMenuLargeRtlItemDropDownButtonChecked
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmVerticalMenuItemDropDownButtonChecked,
.dxmVerticalMenuLargeItemDropDownButtonChecked
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmVerticalMenuRtlItemDropDownButtonChecked,
.dxmVerticalMenuLargeRtlItemDropDownButtonChecked
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmMenuItemHover,
.dxmMenuItemHoverWithImage,
.dxmMenuItemHoverWithPopOutImage,
.dxmMenuItemHoverWithImageWithPopOutImage,
.dxmVerticalMenuItemHover,
.dxmVerticalMenuItemHoverWithImage,
.dxmVerticalMenuItemHoverWithPopOutImage,
.dxmVerticalMenuItemHoverWithImageWithPopOutImage,
.dxmVerticalMenuRtlItemHover,
.dxmVerticalMenuRtlItemHoverWithImage,
.dxmVerticalMenuRtlItemHoverWithPopOutImage,
.dxmVerticalMenuRtlItemHoverWithImageWithPopOutImage,
.dxmMenuLargeItemHover,
.dxmMenuLargeItemHoverWithImage,
.dxmMenuLargeItemHoverWithPopOutImage,
.dxmMenuLargeItemHoverWithImageWithPopOutImage,
.dxmVerticalMenuLargeItemHover,
.dxmVerticalMenuLargeItemHoverWithImage,
.dxmVerticalMenuLargeItemHoverWithPopOutImage,
.dxmVerticalMenuLargeItemHoverWithImageWithPopOutImage,
.dxmVerticalMenuLargeRtlItemHover,
.dxmVerticalMenuLargeRtlItemHoverWithImage,
.dxmVerticalMenuLargeRtlItemHoverWithPopOutImage,
.dxmVerticalMenuLargeRtlItemHoverWithImageWithPopOutImage
{
	background-color: #CBCBCB;
	border: solid 1px #888888;
}
.dxmMenuItemHover,
.dxmMenuItemHoverWithImage
{
	padding: 3px 7px 4px 7px;
}
.dxmMenuItemHoverWithPopOutImage,
.dxmMenuItemHoverWithImageWithPopOutImage
{
	padding: 3px 5px 4px 7px;
}
.dxmVerticalMenuItemHover
{
	padding: 3px 18px 4px 7px;
}
.dxmVerticalMenuRtlItemHover
{
	padding: 3px 7px 4px 18px;
}
.dxmVerticalMenuItemHoverWithImage
{
	padding: 3px 18px 4px 2px;
}
.dxmVerticalMenuRtlItemHoverWithImage
{
	padding: 3px 2px 4px 18px;
}
.dxmVerticalMenuItemHoverWithPopOutImage
{
	padding: 3px 5px 4px 7px;
}
.dxmVerticalMenuRtlItemHoverWithPopOutImage
{
	padding: 3px 7px 4px 5px;
}
.dxmVerticalMenuItemHoverWithImageWithPopOutImage
{
	padding: 3px 5px 4px 2px;
}
.dxmVerticalMenuRtlItemHoverWithImageWithPopOutImage
{
	padding: 3px 2px 4px 5px;
}
.dxmMenuLargeItemHover,
.dxmMenuLargeItemHoverWithImage
{
	padding: 3px 11px 4px 10px;
}
.dxmMenuLargeItemHoverWithPopOutImage,
.dxmMenuLargeItemHoverWithImageWithPopOutImage
{
	padding: 3px 5px 4px 10px;
}
.dxmVerticalMenuLargeItemHover,
.dxmVerticalMenuLargeItemHoverWithImage
{
	padding: 5px 11px 6px 10px;
}
.dxmVerticalMenuLargeRtlItemHover,
.dxmVerticalMenuLargeRtlItemHoverWithImage
{
	padding: 5px 10px 6px 11px;
}
.dxmVerticalMenuLargeItemHoverWithPopOutImage,
.dxmVerticalMenuLargeItemHoverWithImageWithPopOutImage
{
	padding: 5px 5px 6px 10px;
}
.dxmVerticalMenuLargeRtlItemHoverWithPopOutImage,
.dxmVerticalMenuLargeRtlItemHoverWithImageWithPopOutImage
{
	padding: 5px 10px 6px 5px;
}
.dxmMenuItemDropDownButtonHover,
.dxmMenuLargeItemDropDownButtonHover
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmMenuRtlItemDropDownButtonHover,
.dxmMenuLargeRtlItemDropDownButtonHover
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmVerticalMenuItemDropDownButtonHover,
.dxmVerticalMenuLargeItemDropDownButtonHover
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmVerticalMenuRtlItemDropDownButtonHover,
.dxmVerticalMenuLargeRtlItemDropDownButtonHover
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmSubMenu
{
	font: 9pt Tahoma;
	color: black;
	background-color: white;
	border: solid 1px #666666;
	padding: 1px;
}
.dxmSubMenuGutter,
.dxmSubMenuRtlGutter
{
	background-color: #E0E0E0;
}
.dxmSubMenuSeparator
{
	background-color: #A8A8A8;
	width: 100%;
	height: 1px;
}
.dxmSubMenuItem,
.dxmSubMenuItemWithImage,
.dxmSubMenuItemWithPopOutImage,
.dxmSubMenuItemWithImageWithPopOutImage,
.dxmSubMenuRtlItem,
.dxmSubMenuRtlItemWithImage,
.dxmSubMenuRtlItemWithPopOutImage,
.dxmSubMenuRtlItemWithImageWithPopOutImage
{
	font: 9pt Tahoma;
	color: black;
	white-space: nowrap;
}
.dxmSubMenuItem,
.dxmSubMenuItemWithImage
{
	padding: 4px 19px 5px 3px;
}
.dxmSubMenuRtlItem,
.dxmSubMenuRtlItemWithImage
{
	padding: 4px 3px 5px 19px;
}
.dxmSubMenuItemWithPopOutImage,
.dxmSubMenuItemWithImageWithPopOutImage
{
	padding: 4px 6px 5px 3px;
}
.dxmSubMenuRtlItemWithPopOutImage,
.dxmSubMenuRtlItemWithImageWithPopOutImage
{
	padding: 4px 3px 5px 6px;
}
.dxmSubMenuItemDropDownButton
{
	padding-right: 5px;
	padding-left: 5px;
}
.dxmSubMenuRtlItemDropDownButton
{
	padding-right: 5px;
	padding-left: 5px;
}
.dxmSubMenuItemSelected,
.dxmSubMenuItemSelectedWithImage,
.dxmSubMenuItemSelectedWithPopOutImage,
.dxmSubMenuItemSelectedWithImageWithPopOutImage,
.dxmSubMenuRtlItemSelected,
.dxmSubMenuRtlItemSelectedWithImage,
.dxmSubMenuRtlItemSelectedWithPopOutImage,
.dxmSubMenuRtlItemSelectedWithImageWithPopOutImage
{
	background-color: #D8D8D8;
	border: solid 1px #888888;
}
.dxmSubMenuItemSelected,
.dxmSubMenuItemSelectedWithImage
{
	padding: 3px 18px 4px 2px;
}
.dxmSubMenuRtlItemSelected,
.dxmSubMenuRtlItemSelectedWithImage
{
	padding: 3px 2px 4px 18px;
}
.dxmSubMenuItemSelectedWithPopOutImage,
.dxmSubMenuItemSelectedWithImageWithPopOutImage
{
	padding: 3px 5px 4px 2px;
}
.dxmSubMenuRtlItemSelectedWithPopOutImage,
.dxmSubMenuRtlItemSelectedWithImageWithPopOutImage
{
	padding: 3px 2px 4px 5px;
}
.dxmSubMenuItemDropDownButtonSelected
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmSubMenuRtlItemDropDownButtonSelected
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmSubMenuItemChecked,
.dxmSubMenuItemCheckedWithImage,
.dxmSubMenuItemCheckedWithPopOutImage,
.dxmSubMenuItemCheckedWithImageWithPopOutImage
.dxmSubMenuRtlItemChecked,
.dxmSubMenuRtlItemCheckedWithImage,
.dxmSubMenuRtlItemCheckedWithPopOutImage,
.dxmSubMenuRtlItemCheckedWithImageWithPopOutImage
{
}
.dxmSubMenuItemDropDownButtonChecked
{
}
.dxmSubMenuRtlItemDropDownButtonChecked
{
}
.dxmSubMenuItemHover,
.dxmSubMenuItemHoverWithImage,
.dxmSubMenuItemHoverWithPopOutImage,
.dxmSubMenuItemHoverWithImageWithPopOutImage,
.dxmSubMenuRtlItemHover,
.dxmSubMenuRtlItemHoverWithImage,
.dxmSubMenuRtlItemHoverWithPopOutImage,
.dxmSubMenuRtlItemHoverWithImageWithPopOutImage
{
	background-color: #F2F2F2;
	border: solid 1px #888888;
}
.dxmSubMenuItemHover,
.dxmSubMenuItemHoverWithImage
{
	padding: 3px 18px 4px 2px;
}
.dxmSubMenuRtlItemHover,
.dxmSubMenuRtlItemHoverWithImage
{
	padding: 3px 2px 4px 18px;
}
.dxmSubMenuItemHoverWithPopOutImage,
.dxmSubMenuItemHoverWithImageWithPopOutImage
{
	padding: 3px 5px 4px 2px;
}
.dxmSubMenuRtlItemHoverWithPopOutImage,
.dxmSubMenuRtlItemHoverWithImageWithPopOutImage
{
	padding: 3px 2px 4px 5px;
}
.dxmSubMenuItemDropDownButtonHover
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmSubMenuRtlItemDropDownButtonHover
{
	padding-right: 4px;
	padding-left: 4px;
}
.dxmSubMenuBorderCorrector
{
    position: absolute;
    border: 0px;
    padding: 0px;
}

.dxmMenuItemSpacing,
.dxmMenuLargeItemSpacing,
.dxmMenuItemSeparatorSpacing,
.dxmMenuLargeItemSeparatorSpacing
{
	width: 2px;
}
.dxmVerticalMenuItemSpacing,
.dxmVerticalMenuItemSeparatorSpacing
{
	height: 1px;
}
.dxmVerticalMenuLargeItemSpacing,
.dxmVerticalMenuLargeItemSeparatorSpacing
{
	height: 2px;
}
.dxmSubMenuItemSpacing,
.dxmSubMenuItemSeparatorSpacing
{
	height: 1px;
}

.dxmMenuItemLeftImageSpacing
{
	padding-right: 4px;
}
.dxmMenuItemRightImageSpacing
{
	padding-left: 4px;
}
.dxmVerticalMenuItemLeftImageSpacing,
.dxmVerticalMenuItemRightImageSpacing,
.dxmSubMenuItemImageSpacing
{
	width: 1px;
	padding-left: 0px !important;
	padding-right: 0px !important;
	border-left-width: 0px !important;
	border-right-width: 0px !important;
}
.dxmVerticalMenuItemLeftImageSpacing div,
.dxmVerticalMenuItemRightImageSpacing div
{
	width: 4px;
	height: 1px;
}
.dxmMenuItemTopImageSpacing,
.dxmVerticalMenuItemTopImageSpacing
{
	margin-bottom: 4px;
}
.dxmMenuItemBottomImageSpacing,
.dxmVerticalMenuItemBottomImageSpacing
{
	margin-top: 4px;
}
.dxmSubMenuItemImageSpacing div
{
	width: 7px;
	height: 1px;
}

.dxmScrollUpButton,
.dxmScrollDownButton
{
    border: solid 1px #888888;
    background-color: #E0E0E0;
    cursor: pointer;
    font-size: 0px;
    padding: 1px;
    text-align: center;
}
.dxmScrollUpButton
{
    margin-bottom: 1px;
}
.dxmScrollDownButton
{
    margin-top: 1px;
}
.dxmScrollButtonHover
{
    background-color: #F2F2F2;
}
.dxmScrollButtonPressed
{
    background-color: #D8D8D8;
}
.dxmScrollButtonDisabled
{
    cursor: default;
}
.dxmScrollArea
{
    overflow: hidden;
    position: relative;
}

/* Disabled */
.dxmDisabled
{
	color: #acacac;
	cursor: default;
}

/*                             */
/* -- ASPxMenu Toolbar mode -- */
/*                             */

td.dxmtb.dxmMenu
{
    padding: 2px;
}

.dxmtb .dxmMenuItem,
.dxmtb .dxmMenuItemWithImage,
.dxmtb .dxmMenuItemWithPopOutImage,
.dxmtb .dxmMenuItemWithImageWithPopOutImage
{
    padding: 4px 5px 5px 5px;
}

.dxmtb .dxmMenuItemSelected,
.dxmtb .dxmMenuItemSelectedWithImage,
.dxmtb .dxmMenuItemSelectedWithPopOutImage,
.dxmtb .dxmMenuItemSelectedWithImageWithPopOutImage,
.dxmtb .dxmMenuItemChecked,
.dxmtb .dxmMenuItemCheckedWithImage,
.dxmtb .dxmMenuItemCheckedWithPopOutImage,
.dxmtb .dxmMenuItemCheckedWithImageWithPopOutImage,
.dxmtb .dxmMenuItemHover,
.dxmtb .dxmMenuItemHoverWithImage,
.dxmtb .dxmMenuItemHoverWithPopOutImage,
.dxmtb .dxmMenuItemHoverWithImageWithPopOutImage
{
    padding: 3px 4px 4px 4px;
}
.dxmtb .dxmMenuItemHoverWithImage.dxmMenuItemLeftImageSpacing,
.dxmtb .dxmMenuItemSelectedWithImage.dxmMenuItemLeftImageSpacing,
.dxmtb .dxmMenuItemCheckedWithImage.dxmMenuItemLeftImageSpacing,
.dxmtb .dxmMenuItemHoverWithImageWithPopOutImage.dxmMenuItemLeftImageSpacing,
.dxmtb .dxmMenuItemSelectedWithImageWithPopOutImage.dxmMenuItemLeftImageSpacing,
.dxmtb .dxmMenuItemCheckedWithImageWithPopOutImage.dxmMenuItemLeftImageSpacing
{
    padding-right: 5px;
}
.dxmtb .dxmMenuItemHoverWithImage.dxmMenuItemRightImageSpacing,
.dxmtb .dxmMenuItemSelectedWithImage.dxmMenuItemRightImageSpacing,
.dxmtb .dxmMenuItemCheckedWithImage.dxmMenuItemRightImageSpacing,
.dxmtb .dxmMenuItemHoverWithImageWithPopOutImage.dxmMenuItemRightImageSpacing,
.dxmtb .dxmMenuItemSelectedWithImageWithPopOutImage.dxmMenuItemRightImageSpacing,
.dxmtb .dxmMenuItemCheckedWithImageWithPopOutImage.dxmMenuItemRightImageSpacing
{
    padding-left: 5px;
}
.dxmtb .dxmMenuItemSpacing,
.dxmtb .dxmMenuItemSeparatorSpacing
{
	width: 2px;
}

/*                     */
/* -- ASPxMenu Lite -- */
/*                     */
.dxm-rtl
{
	direction: ltr;
}
.dxm-rtl .dxm-content
{
	direction: rtl;
}

.dxm-ltr .dxm-main,
.dxm-ltr .dxm-horizontal ul.dx
{
	float: left;
}
.dxm-rtl .dxm-main,
.dxm-rtl .dxm-horizontal ul.dx
{
	float: right;
}
.dxm-popup
{
	position: relative;
}
ul.dx
{
	list-style: none none outside;
	margin: 0;
	padding: 0;

	background-repeat: repeat-y;
	background-position: left top;
}
.dxm-rtl ul.dx
{
	background-position: right top;
}
.dxm-image,
.dxm-pImage
{
	border-width: 0px;
	vertical-align: top;
}
.dxm-popOut,
.dxm-spacing,
.dxm-separator,
.dxm-separator b
{
	font-size: 0px;
	line-height: 0px;
	display: block;
}

.dxm-ltr .dxm-horizontal .dxm-item,
.dxm-ltr .dxm-horizontal .dxm-spacing,
.dxm-ltr .dxm-horizontal .dxm-separator,
.dxm-ltr .dxm-content
{
    float: left;
}
.dxm-rtl .dxm-horizontal .dxm-item,
.dxm-rtl .dxm-horizontal .dxm-spacing,
.dxm-rtl .dxm-horizontal .dxm-separator,
.dxm-rtl .dxm-content
{
    float: right;
}

.dxm-vertical .dxm-image-r .dxm-popOut
{
	float: left;
}
.dxm-vertical .dxm-image-l .dxm-popOut
{
	float: right;
}

.dxm-ltr .dxm-horizontal .dxm-popOut
{
    float: left;
}
.dxm-ltr .dxm-vertical .dxm-image-t .dxm-popOut,
.dxm-ltr .dxm-vertical .dxm-image-b .dxm-popOut,
.dxm-ltr .dxm-popup .dxm-popOut
{
	float: right;
}

.dxm-rtl .dxm-horizontal .dxm-popOut
{
    float: right;
}
.dxm-rtl .dxm-vertical .dxm-image-t .dxm-popOut,
.dxm-rtl .dxm-vertical .dxm-image-b .dxm-popOut,
.dxm-rtl .dxm-popup .dxm-popOut
{
	float: left;
}

.dxm-ie7 .dxm-vertical ul.dx,
.dxm-ie7 .dxm-popup ul.dx
{
	height: 1%;
}
.dxm-ie7 .dxm-vertical .dxm-item,
.dxm-ie7 .dxm-popup .dxm-item
{
	margin-bottom: -2px;
}
.dxm-ie7 .dxm-vertical .dxm-spacing,
.dxm-ie7 .dxm-popup .dxm-spacing
{
	margin-bottom: -1px;
}
.dxm-ie7 .dxm-vertical .dxm-item,
.dxm-ie7 .dxm-vertical .dxm-spacing,
.dxm-ie7 .dxm-vertical .dxm-separator,
.dxm-ie7 .dxm-popup .dxm-item,
.dxm-ie7 .dxm-popup .dxm-spacing,
.dxm-ie7 .dxm-popup .dxm-separator
{
	zoom: 1;
}
.dxm-vertical .dxm-separator b,
.dxm-popup .dxm-separator b
{
	margin: 0px auto;
}
.dxm-ie7 .dxm-vertical .dxm-separator b,
.dxm-ie7 .dxm-popup .dxm-separator b
{
	margin: 0px;
}
.dxm-ie7 .dxm-vertical .dxm-separator,
.dxm-ie7 .dxm-popup .dxm-separator
{
	text-align: center;
}
/* Horizontal align = Center */
.dxm-haCenter
{
    padding-left: 0px !important;
    padding-right: 0px !important;
    overflow: hidden;
}
.dxm-haCenter .dxm-haWrapper,
.dxm-haCenter .dxm-content
{
    position: relative;
}
.dxm-ltr .dxm-image-l .dxm-haCenter .dxm-haWrapper,
.dxm-ltr .dxm-image-t .dxm-haCenter .dxm-haWrapper,
.dxm-ltr .dxm-image-b .dxm-haCenter .dxm-haWrapper
{
    float: left;
    left: 50%;
}
.dxm-rtl .dxm-image-l .dxm-haCenter .dxm-haWrapper,
.dxm-rtl .dxm-image-t .dxm-haCenter .dxm-haWrapper,
.dxm-rtl .dxm-image-b .dxm-haCenter .dxm-haWrapper
{
    float: right;
    right: 50%;
}
.dxm-ltr .dxm-image-l .dxm-haCenter .dxm-content,
.dxm-ltr .dxm-image-t .dxm-haCenter .dxm-content,
.dxm-ltr .dxm-image-b .dxm-haCenter .dxm-content
{
    left: -50%;
}
.dxm-rtl .dxm-image-l .dxm-haCenter .dxm-content,
.dxm-rtl .dxm-image-t .dxm-haCenter .dxm-content,
.dxm-rtl .dxm-image-b .dxm-haCenter .dxm-content
{
    right: -50%;
}
.dxm-ltr .dxm-image-r .dxm-haCenter .dxm-haWrapper
{
    float: right;
    right: 50%;
}
.dxm-rtl .dxm-image-r .dxm-haCenter .dxm-haWrapper
{
    float: left;
    left: 50%;
}
.dxm-ltr .dxm-image-r .dxm-haCenter .dxm-content
{
    right: -50%;
}
.dxm-rtl .dxm-image-r .dxm-haCenter .dxm-content
{
    left: -50%;
}

/* Appearance */
.dxmLite .dxm-main
{
	border: solid 1px #A8A8A8;
    background-color: #F0F0F0;
    padding: 2px;
}

.dxmLite .dxm-vertical
{
	width: 150px;
}

.dxmLite .dxm-popup
{
	border: solid 1px #666666;
	background-color: white;
	padding: 1px;
}

.dxmBC
{
	background-color: white;
}

.dxmLite ul.dx
{
	font: 9pt Tahoma;
}
.dxmLite .dxm-popup .dxm-gutter
{
	background-image: url('<%=WebResource("DevExpress.Web.Images.mPopupBack.gif")%>');
}

.dxmLite .dxm-item
{
	cursor: default;
}

.dxmLite .dxm-image-t .dxm-item,
.dxmLite .dxm-image-b .dxm-item,
.dxmLite .dxm-content
{
	text-align: center;
	white-space: nowrap;
}

.dxmLite,
.dxmLite .dxm-content a.dx
{
	color: Black;
}
.dxmLite .dxm-disabled,
.dxmLite .dxm-disabled .dxm-content a.dx
{
	color: #acacac;
}

.dxmLite .dxm-content a.dx
{
	text-decoration: none;
}

.dxmLite .dxm-item
{
	border-width: 1px;
}
.dxm-ltr.dxmLite .dxm-popOut,
.dxm-rtl.dxmLite .dxm-image-l .dxm-popOut
{
	border-width: 0 0 0 1px;
}
.dxm-ltr.dxmLite .dxm-image-r .dxm-popOut,
.dxm-rtl.dxmLite .dxm-popOut
{
	border-width: 0 1px 0 0;
}
.dxmLite .dxm-item,
.dxmLite .dxm-popOut
{
	border-color: transparent;
	border-style: solid;
}

/* Checked, Selected, Hovered */
.dxmLite .dxm-main .dxm-checked,
.dxmLite .dxm-selected,
.dxmLite .dxm-hovered,
.dxmLite .dxm-main .dxm-dropDownMode.dxm-checked .dxm-popOut,
.dxmLite .dxm-dropDownMode.dxm-selected .dxm-popOut,
.dxmLite .dxm-dropDownMode.dxm-hovered .dxm-popOut
{
	border-color: #888888;
}
.dxmLite .dxm-main .dxm-checked,
.dxmLite .dxm-main .dxm-selected
{
	background-color: white;
}
.dxmLite .dxm-main .dxm-hovered
{
	background-color: #CBCBCB;
}
.dxmLite .dxm-popup .dxm-selected
{
	background-color: #D8D8D8;
}
.dxmLite .dxm-popup .dxm-hovered
{
	background-color: #F2F2F2;
}

/* Content */
.dxmLite .dxm-horizontal .dxm-image-l .dxm-content,
.dxmLite .dxm-horizontal .dxm-image-r .dxm-content
{
	padding: 3px 7px 4px;
}
.dxmLite .dxm-horizontal .dxm-image-t .dxm-content,
.dxmLite .dxm-horizontal .dxm-image-b .dxm-content
{
	padding: 3px 11px 4px 10px;
}
.dxmLite .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-item .dxm-content,
.dxmLite .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-item .dxm-content,
.dxmLite .dxm-horizontal .dxm-image-t .dxm-noImage .dxm-content,
.dxmLite .dxm-horizontal .dxm-image-b .dxm-noImage .dxm-content {
    padding: 4px 11px 5px 10px;
}
.dxmLite .dxm-horizontal .dxm-image-l .dxm-subMenu .dxm-content,
.dxmLite .dxm-horizontal .dxm-image-t .dxm-subMenu .dxm-content,
.dxmLite .dxm-horizontal .dxm-image-b .dxm-subMenu .dxm-content
{
	padding-right: 3px;
}
.dxmLite .dxm-horizontal .dxm-image-l .dxm-dropDownMode .dxm-content
{
	padding-right: 4px;
}
.dxmLite .dxm-horizontal .dxm-image-r .dxm-subMenu .dxm-content
{
	padding-left: 3px;
	padding-right: 5px;
}
.dxmLite .dxm-horizontal .dxm-image-r .dxm-dropDownMode .dxm-content
{
	padding-left: 4px;
	padding-right: 5px;
}
.dxmLite .dxm-horizontal .dxm-image-t .dxm-dropDownMode .dxm-content,
.dxmLite .dxm-horizontal .dxm-image-b .dxm-dropDownMode .dxm-content
{
	padding-right: 8px;
}

.dxmLite .dxm-vertical .dxm-image-l .dxm-content,
.dxmLite .dxm-vertical .dxm-image-r .dxm-content,
.dxmLite .dxm-popup .dxm-content
{
	padding: 3px 18px 4px 2px;
}
.dxm-rtl.dxmLite .dxm-vertical .dxm-image-l .dxm-content,
.dxm-rtl.dxmLite .dxm-vertical .dxm-image-r .dxm-content,
.dxm-rtl.dxmLite .dxm-popup .dxm-content
{
	padding: 3px 2px 4px 18px;
}
.dxmLite .dxm-vertical .dxm-image-r .dxm-noSubMenu .dxm-content,
.dxmLite .dxm-vertical .dxm-image-r .dxm-subMenu .dxm-content,
.dxmLite .dxm-vertical .dxm-image-r .dxm-dropDownMode .dxm-content
{
	padding-right: 5px;
	padding-left: 14px;
}
.dxmLite .dxm-vertical .dxm-image-t .dxm-content,
.dxmLite .dxm-vertical .dxm-image-b .dxm-content
{
	padding: 5px 10px 6px;
}

/* Image */
.dxmLite .dxm-horizontal .dxm-image-l .dxm-image,
.dxmLite .dxm-horizontal.dxmtb .dxm-image-l .dxm-hasText .dxm-image
{
	margin-right: 4px;
}
.dxmLite .dxm-horizontal .dxm-image-r .dxm-image,
.dxmLite .dxm-horizontal.dxmtb .dxm-image-r .dxm-hasText .dxm-image
{
	margin-left: 4px;
}
.dxmLite .dxm-image-t .dxm-image
{
	margin-bottom: 4px;
}
.dxmLite .dxm-image-b .dxm-image
{
	margin-top: 4px;
}
.dxmLite .dxm-vertical .dxm-image-l .dxm-image
{
	margin-right: 7px;
}
.dxmLite .dxm-vertical .dxm-image-r .dxm-image
{
	margin-left: 7px;
}
.dxm-ltr.dxmLite .dxm-popup .dxm-image
{
	margin-right: 12px;
}
.dxm-rtl.dxmLite .dxm-popup .dxm-image
{
	margin-left: 12px;
}

/* Image replacement */
.dxm-ltr.dxmLite .dxm-vertical .dxm-image-l.dxm-noImages .dxm-content,
.dxm-ltr.dxmLite .dxm-vertical .dxm-image-r.dxm-noImages .dxm-content
{
	padding-left: 7px;
}
.dxm-rtl.dxmLite .dxm-vertical .dxm-image-l.dxm-noImages .dxm-content,
.dxm-rtl.dxmLite .dxm-vertical .dxm-image-r.dxm-noImages .dxm-content
{
	padding-right: 7px;
}
.dxmLite .dxm-vertical .dxm-image-l .dxm-noImage
{
	padding-left: 21px;
}
.dxmLite .dxm-vertical .dxm-image-r .dxm-noImage
{
	padding-right: 21px;
}
.dxm-ltr.dxmLite .dxm-popup .dxm-gutter.dxm-noImages .dxm-item,
.dxm-ltr.dxmLite .dxm-popup .dxm-noImage
{
	padding-left: 26px;
}
.dxm-rtl.dxmLite .dxm-popup .dxm-gutter.dxm-noImages .dxm-item,
.dxm-rtl.dxmLite .dxm-popup .dxm-noImage
{
	padding-right: 26px;
}

/* PopOut */
.dxmLite .dxm-horizontal .dxm-image-l .dxm-popOut,
.dxmLite .dxm-horizontal .dxm-image-r .dxm-popOut,
.dxmLite .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-popOut,
.dxmLite .dxm-horizontal .dxm-image-t .dxm-noImage .dxm-popOut,
.dxmLite .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-popOut,
.dxmLite .dxm-horizontal .dxm-image-b .dxm-noImage .dxm-popOut
{
	padding-top: 9px;
	padding-bottom: 9px;
}
.dxmLite .dxm-horizontal .dxm-image-t .dxm-popOut,
.dxmLite .dxm-horizontal .dxm-image-b .dxm-popOut
{
	padding-top: 27px;
	padding-bottom: 27px;
}
.dxmLite .dxm-horizontal .dxm-image-l .dxm-popOut,
.dxmLite .dxm-horizontal .dxm-image-t .dxm-popOut,
.dxmLite .dxm-horizontal .dxm-image-b .dxm-popOut
{
	padding-right: 5px;
}
.dxmLite .dxm-horizontal .dxm-image-r .dxm-popOut
{
	padding-left: 7px;
}
.dxmLite .dxm-horizontal .dxm-dropDownMode .dxm-popOut
{
	padding-left: 4px;
	padding-right: 4px;
}

.dxmLite .dxm-vertical .dxm-image-l .dxm-popOut,
.dxmLite .dxm-vertical .dxm-image-r .dxm-popOut,
.dxmLite .dxm-popup .dxm-popOut
{
	padding-top: 7px;
	padding-bottom: 7px;
}
.dxmLite .dxm-vertical .dxm-image-t.dxm-noImages .dxm-popOut,
.dxmLite .dxm-vertical .dxm-image-t .dxm-noImage .dxm-popOut,
.dxmLite .dxm-vertical .dxm-image-b.dxm-noImages .dxm-popOut,
.dxmLite .dxm-vertical .dxm-image-b .dxm-noImage .dxm-popOut
{
	padding-top: 8px;
	padding-bottom: 9px;
}
.dxmLite .dxm-vertical .dxm-image-t .dxm-popOut,
.dxmLite .dxm-vertical .dxm-image-b .dxm-popOut
{
	padding-top: 26px;
	padding-bottom: 27px;
}
.dxmLite .dxm-vertical .dxm-image-l .dxm-popOut,
.dxmLite .dxm-vertical .dxm-image-r .dxm-popOut,
.dxmLite .dxm-vertical .dxm-image-t .dxm-popOut,
.dxmLite .dxm-vertical .dxm-image-b .dxm-popOut,
.dxmLite .dxm-popup .dxm-popOut
{
	padding-left: 4px;
	padding-right: 4px;
}

/* PopOut replacement */
.dxmLite .dxm-vertical .dxm-image-l .dxm-noSubMenu,
.dxm-ltr.dxmLite .dxm-vertical .dxm-image-t .dxm-noSubMenu,
.dxm-ltr.dxmLite .dxm-vertical .dxm-image-b .dxm-noSubMenu,
.dxm-ltr.dxmLite .dxm-popup .dxm-noSubMenu
{
	padding-right: 13px;
}
.dxmLite .dxm-vertical .dxm-image-r .dxm-noSubMenu,
.dxm-rtl.dxmLite .dxm-vertical .dxm-image-t .dxm-noSubMenu,
.dxm-rtl.dxmLite .dxm-vertical .dxm-image-b .dxm-noSubMenu,
.dxm-rtl.dxmLite .dxm-popup .dxm-noSubMenu
{
	padding-left: 13px;
}

/* Spacings */
.dxmLite .dxm-horizontal .dxm-spacing
{
	width: 2px;
	height: 1px;
}
.dxmLite .dxm-vertical .dxm-image-l .dxm-spacing,
.dxmLite .dxm-vertical .dxm-image-r .dxm-spacing,
.dxmLite .dxm-popup .dxm-spacing
{
	height: 1px;
}
.dxmLite .dxm-vertical .dxm-image-t .dxm-spacing,
.dxmLite .dxm-vertical .dxm-image-b .dxm-spacing
{
	height: 2px;
}
.dxmLite .dxm-horizontal .dxm-separator
{
	margin: 0px 2px;
}
.dxmLite .dxm-vertical .dxm-image-l .dxm-separator,
.dxmLite .dxm-vertical .dxm-image-r .dxm-separator,
.dxmLite .dxm-popup .dxm-separator
{
	margin: 1px 0px;
}
.dxm-ie7.dxmLite .dxm-vertical .dxm-image-l .dxm-separator,
.dxm-ie7.dxmLite .dxm-vertical .dxm-image-r .dxm-separator,
.dxm-ie7.dxmLite .dxm-popup .dxm-separator
{
	margin-top: 0px;
}
.dxmLite .dxm-vertical .dxm-image-t .dxm-separator,
.dxmLite .dxm-vertical .dxm-image-b .dxm-separator
{
	margin: 2px 0px;
}
.dxm-ie7.dxmLite .dxm-vertical .dxm-image-t .dxm-separator,
.dxm-ie7.dxmLite .dxm-vertical .dxm-image-b .dxm-separator
{
	margin-top: 1px;
}

/* Separator */
.dxmLite .dxm-separator b
{
	background-color: #A8A8A8;
}
.dxmLite .dxm-horizontal .dxm-separator b
{
	height: 13px;
	width: 1px;
}
.dxmLite .dxm-vertical .dxm-separator b,
.dxmLite .dxm-popup .dxm-separator b
{
	height: 1px;
}
.dxmLite .dxm-horizontal .dxm-separator b,
.dxmLite .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-separator b,
.dxmLite .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-separator b
{
	margin-top: 6px;
}
.dxmLite .dxm-horizontal .dxm-image-t .dxm-separator b,
.dxmLite .dxm-horizontal .dxm-image-b .dxm-separator b
{
	margin-top: 23px;
}
.dxmLite .dxm-popup .dxm-gutter .dxm-separator
{
	padding-left: 29px;
}
/* Scroll elements */
.dxmLite .dxm-scrollUpBtn,
.dxmLite .dxm-scrollDownBtn
{
    border: solid 1px #888888;
    background-color: #E0E0E0;
    cursor: pointer;
    font-size: 0px;
    padding: 1px;
    text-align: center;
}
.dxmLite .dxm-scrollUpBtn
{
    margin-bottom: 1px;
}
.dxmLite .dxm-scrollDownBtn
{
    margin-top: 1px;
}
.dxmLite .dxm-scrollBtnHovered
{
    background-color: #F2F2F2;
}
.dxmLite .dxm-scrollBtnPressed
{
    background-color: #D8D8D8;
}
.dxmLite .dxm-scrollBtnDisabled
{
    cursor: default;
}
.dxmLite .dxm-scrollArea
{
    overflow: hidden;
    position: relative;
}

/*                                  */
/* -- ASPxMenu Lite Toolbar mode -- */
/*                                  */
.dxmLite .dxm-main.dxmtb {
    padding: 2px;
}

.dxmLite .dxm-horizontal.dxmtb .dxm-image-l .dxm-content,
.dxmLite .dxm-horizontal.dxmtb .dxm-image-l .dxm-subMenu .dxm-content,
.dxmLite .dxm-horizontal.dxmtb .dxm-image-l .dxm-subMenu.dxm-noImage .dxm-content,
.dxmLite .dxm-horizontal.dxmtb .dxm-image-l .dxm-dropDownMode.dxm-noImage .dxm-content {
	padding: 3px 4px 4px;
}
.dxmLite .dxm-horizontal.dxmtb .dxm-image-l.dxm-noImages .dxm-item .dxm-content,
.dxmLite .dxm-horizontal.dxmtb .dxm-image-l .dxm-noImage .dxm-content {
    padding: 4px 4px 5px;
}
.dxmLite .dxm-horizontal.dxmtb .dxm-image-l .dxm-dropDownMode .dxm-content {
	padding: 3px 5px 4px 4px;
}

.dxmLite .dxm-horizontal.dxmtb .dxm-image-l .dxm-image {
	margin-right: 0px;
}
.dxmLite .dxm-popup.dxmtb .dxm-image {
	margin-right: 10px;
}

.dxmLite .dxm-horizontal.dxmtb .dxm-image-l .dxm-popOut,
.dxmLite .dxm-horizontal.dxmtb .dxm-dropDownMode .dxm-popOut {
    padding: 10px 3px;
}

.dxmLite .dxm-horizontal.dxmtb .dxm-spacing {
	width: 4px;
	height: 23px;
}
.dxmLite .dxm-horizontal.dxmtb .dxm-separator {
	margin: 0px 2px;
	height: 23px;
}

/* -- ASPxNavBar -- */
.dxnbControl
{
	font: 9pt Tahoma;
	color: black;
	background-color: white;
}
.dxnbControl td.dxnbCtrl
{
    padding: 11px;
}
.dxnbControl a
{
	color: #1E3695;
}
.dxnbLoadingPanel
{
	font: 9pt Tahoma;
	color: #303030;
}
.dxnbLoadingPanel td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}
.dxnbGroupHeader
{
	font: bold 9pt Tahoma;
	color: black;
	background-color: #E0E0E0;
	border: solid 1px #A8A8A8;
	padding: 4px 10px 4px 10px;
}
.dxnbGroupHeader table.dxnb
{
	font: bold 9pt Tahoma;
	color: black;
}
.dxnbGroupHeader td.dxnb
{
	white-space: nowrap;
}
.dxnbGroupHeaderCollapsed
{
	font: bold 9pt Tahoma;
	color: black;
	background-color: #E0E0E0;
	border: solid 1px #A8A8A8;
	padding: 4px 10px 4px 10px;
}
.dxnbGroupHeaderCollapsed table.dxnb
{
	font: bold 9pt Tahoma;
	color: black;
}
.dxnbGroupHeaderCollapsed td.dxnb
{
	white-space: nowrap;
}
.dxnbGroupContent
{
	font: 9pt Tahoma;
	color: #1E3695;
	border: solid 1px #A8A8A8;
	padding: 5px 5px 5px 5px;
}
.dxnbItem,
.dxnbLargeItem,
.dxnbBulletItem
{
	font: 9pt Tahoma;
	color: #1E3695;
}
.dxnbItem
{
	padding-top: 4px;
	padding-right: 5px;
	padding-bottom: 5px;
	padding-left: 5px;
}
.dxnbLargeItem
{
	padding-top: 6px;
	padding-right: 12px;
	padding-bottom: 7px;
	padding-left: 12px;
}
.dxnbBulletItem
{
	margin-bottom: 9px;
}
.dxnbItemSelected,
.dxnbLargeItemSelected,
.dxnbBulletItemSelected
{
	background-color: #D8D8D8;
	border: solid 1px #888888;
}
.dxnbItemSelected
{
	padding-top: 3px;
	padding-right: 4px;
	padding-bottom: 4px;
	padding-left: 4px;
}
.dxnbLargeItemSelected
{
	padding-top: 5px;
	padding-right: 11px;
	padding-bottom: 6px;
	padding-left: 11px;
}
.dxnbItemHover,
.dxnbLargeItemHover,
.dxnbBulletItemHover
{
	background-color: #F2F2F2;
	border: solid 1px #888888;
}
.dxnbItemHover
{
	padding-top: 3px;
	padding-right: 4px;
	padding-bottom: 4px;
	padding-left: 4px;
}
.dxnbLargeItemHover
{
	padding-top: 5px;
	padding-right: 11px;
	padding-bottom: 6px;
	padding-left: 11px;
}
.dxnbGroupHeader,
.dxnbGroupHeaderCollapsed
{
    text-align: left;
}
.dxnbItem,
.dxnbItemHover,
.dxnbItemSelected,
.dxnbBulletItem,
.dxnbBulletItemHover,
.dxnbBulletItemSelected
{
    text-align: left;
}
.dxnbLargeItem,
.dxnbLargeItemHover,
.dxnbLargeItemSelected
{
    text-align: center;
}
.dxnbGroupHeaderHover
{
}
.dxnbGroupHeaderCollapsedHover
{
}
/* Spacings */
.dxnbGroupSpacing,
.dxnbItemSpacing
{
	width: 100%;
	height: 1px;
}
.dxnbGroupSpacing
{
	height: 13px;
}
.dxnbImgCellLeft
{
	padding-right: 4px;
}
.dxnbImgCellRight
{
	padding-left: 4px;
}
.dxnbLargeItemImgTop
{
	margin-bottom: 3px;
}
.dxnbLargeItemImgBottom
{
	margin-top: 3px;
}
/* Disabled */
.dxnbDisabled,
.dxnbDisabled table.dxnb
{
	color: #acacac;
	cursor: default;
}

/* -- ASPxNavBar Lite -- */
.dxnbLite
{
    color: Black;
    background: white;
	font: 9pt Tahoma;
	list-style: none none outside;
    margin: 0;
    padding: 11px;
    float: left;
    width: 200px;
}
.dxnbLite a
{
	color: #1E3695;
}
.dxnbLite .dxnb-gr
{
	margin-bottom: 13px;
}
.dxnbLite .dxnb-header,
.dxnbLite .dxnb-headerCollapsed
{
	background: #E0E0E0;
	border: solid 1px #A8A8A8;
	font-weight: bold;
	overflow: hidden;
    padding: 4px 10px;
	cursor: pointer;
	clear: both;
}
.dxnbLite .dxnb-content
{
    list-style: none none outside;
    margin: 0;
    padding: 5px;
    overflow: hidden;
	border: solid 1px #A8A8A8;
	border-top-width: 0;
}
.dxnb-noHeads .dxnb-content
{
	border-top-width: 1px;
}
.dxnbLite .dxnb-item,
.dxnbLite .dxnb-large,
.dxnbLite .dxnb-bullet
{
	color: #1E3695;
    clear: both;
    overflow: hidden;
    cursor: default;
}
.dxnbLite .dxnb-item,
.dxnbLite .dxnb-large,
.dxnbLite .dxnb-tmpl
{
	margin-bottom: 1px;
}
.dxnbLite .dxnb-item
{
	padding: 4px 5px 5px;
}
.dxnbLite .dxnb-large
{
	padding: 6px 12px 7px;
}
.dxnbLite .dxnb-bullet,
.dxnbLite .dxnb-bulletHover,
.dxnbLite .dxnb-bulletSelected
{
    padding: 0 5px;
    overflow: visible;
    margin-bottom: 9px;
}
.dxnbLite .dxnb-itemSelected,
.dxnbLite .dxnb-itemHover
{
	padding: 3px 4px 4px;
}
.dxnbLite .dxnb-largeSelected,
.dxnbLite .dxnb-largeHover
{
	padding: 5px 11px 6px;
}
.dxnbLite .dxnb-itemSelected,
.dxnbLite .dxnb-largeSelected
{
	background-color: #D8D8D8;
	border: solid 1px #888888;
}
.dxnbLite .dxnb-itemHover,
.dxnbLite .dxnb-largeHover
{
	background-color: #F2F2F2;
	border: solid 1px #888888;
}
.dxnbLite .dxnb-header,
.dxnbLite .dxnb-headerCollapsed,
.dxnbLite .dxnb-item,
.dxnbLite .dxnb-itemHover,
.dxnbLite .dxnb-itemSelected,
.dxnbLite .dxnb-bullet,
.dxnbLite .dxnb-bulletHover,
.dxnbLite .dxnb-bulletSelected
{
    text-align: left;
}
.dxnbLite .dxnb-large,
.dxnbLite .dxnb-largeHover,
.dxnbLite .dxnb-largeSelected
{
    text-align: center;
}
.dxnbLite .dxnb-headerHover
{
}
.dxnbLite .dxnb-headerCollapsedHover
{
}
.dxnbLite .dxnb-last
{
	margin-bottom: 0;
}
.dxnbLite .dxnb-btn,
.dxnbLite .dxnb-btnLeft,
.dxnbLite .dxnb-img
{
	border-width: 0;
}

.dxnbLite .dxnb-btn
{
	float: right;
	margin-left: 4px;
}
.dxnbLite .dxnb-btnLeft
{
	float: left;
	margin-right: 4px;
}
.dxnbLite .dxnb-img
{
	margin:0 4px 0 0;
	float: left;
}
.dxnbLite .dxnb-right .dxnb-item .dxnb-img,
.dxnbLite .dxnb-rtlHeader .dxnb-img
{
	float: right;
	margin: 0 0 0 4px;
}
.dxnbLite .dxnb-top .dxnb-large .dxnb-img
{
	margin-bottom: 3px;
}
.dxnbLite .dxnb-bottom .dxnb-large .dxnb-img
{
	margin-top: 3px;
}
.dxnbLite .dxnb-large .dxnb-img
{
    display: block;
    float: none;
    margin-left: auto;
    margin-right: auto;
}
.dxnbLiteDisabled,
.dxnbLite .dxnbLiteDisabled,
.dxnbLiteDisabled a,
.dxnbLiteDisabled .dxnb-item,
.dxnbLiteDisabled .dxnb-large,
.dxnbLiteDisabled .dxnb-bullet,
.dxnbLiteDisabled .dxnb-header,
.dxnbLiteDisabled .dxnb-headerCollapsed
{
	color: #acacac;
	cursor: default;
}

/* -- ASPxNewsControl -- */
.dxncControl
{
	font: 10px Tahoma;
	color: black;
	background-color: White;
}
.dxncControl td.dxncCtrl
{
	padding: 18px 18px 18px 18px;
}
.dxncLoadingPanel
{
	border: solid 1px #9F9F9F;
	background-color: white;
	font: 9pt Tahoma;
	color: #303030;
}
.dxncLoadingPanel td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}
.dxncContent
{
}
.dxncPagerPanel
{
	padding: 0px 3px 0px 3px;
}
.dxncItem
{
	font: 9pt Tahoma;
	vertical-align: top;
	border-bottom: solid 1px #A8A8A8;
	padding: 12px 8px 12px 8px;
}
.dxncEmptyItem
{
	font: 10px Tahoma;
	vertical-align: top;
	padding: 12px 12px 12px 14px;
}
.dxncBackToTop
{
	font: 10px Tahoma;
	color: #404040;
}
.dxncBackToTop a
{
	font: 10px Tahoma;
	color: #0d45b7;
}
.dxncBackToTop a:hover
{
	font: 10px Tahoma;
	color: #5494ea;
}
.dxncBackToTop a:visited
{
	font: 10px Tahoma;
	color: #ab59a6;
}

/* Headline */
.dxncItemContent
{
	font: 8pt Verdana;
	color: black;
}
.dxncItemDate
{
	color: Gray;
	white-space: nowrap;
}
.dxncItemHeader
{
	font: 10pt Tahoma;
	color: #464646;
	font-weight: bold;
	line-height: 17px;
}
.dxncItemHeader .dxncItemDate
{
	font: 10pt Tahoma;
	color: Gray;
	font-weight: normal;
}
.dxncItemLeftPanel
{
	font: 10px Verdana;
	color: black;
}
.dxncItemRightPanel
{
	font: 10px Verdana;
	color: black;
}
.dxncItemDateLeftPanel
{
	font: 10px Verdana;
	color: Gray;
	white-space: nowrap;
}
.dxncItemDateRightPanel
{
	font: 10px Verdana;
	color: Gray;
	white-space: nowrap;
}
.dxncItemTailDiv
{
	font: 10px Verdana;
	color: Black;
}
.dxncItemTailDiv a
{
	color: #1E3695;
}
.dxncItemTailDiv a:hover
{
    text-decoration: none;
}
.dxncItemTailDiv a:visited
{
    color: #996085;
}
.dxncItemContent a.dxhl
{
	color: #1E3695;
}
.dxncItemContent a.dxhl:hover
{
    text-decoration: none;
}
.dxncItemContent a.dxhl:visited
{
    color: #996085;
}
.dxncEmptyData
{
    color: Gray;
}
/* Disabled */
.dxncDisabled,
.dxncDisabled a,
.dxncDisabled a:hover
{
	color: #acacac;
	cursor: default;
}

/* -- ASPxPager -- */
.dxpControl
{
	font: 9pt Tahoma;
	color: black;
}
.dxpControl td.dxpCtrl
{
    padding: 5px 2px 5px 2px;
}
.dxpButton
{
	font: 9pt Tahoma;
	color: #394EA2;
	text-decoration: underline;
	white-space: nowrap;
	text-align: center;
	vertical-align: middle;
}
.dxpButton a
{
	font: 9pt Tahoma;
	color: #394EA2;
	text-decoration: underline;
	white-space: nowrap;
}
.dxpDisabledButton
{
	font: 9pt Tahoma;
	color: black;
	text-decoration: none;
}
.dxpPageNumber
{
	font: 9pt Tahoma;
	color: #394EA2;
	text-decoration: underline;
	text-align: center;
	vertical-align: middle;
	padding: 1px 5px 0px 5px;
}
.dxpPageNumber a
{
	font: 9pt Tahoma;
	color: #394EA2;
	text-decoration: underline;
}
.dxpCurrentPageNumber
{
	font: 9pt Tahoma;
	color: black;
	font-weight: bold;
	text-decoration: none;
	padding: 1px 3px 0px 3px;
	white-space: nowrap;
}
.dxpSummary
{
	font: 9pt Tahoma;
	color: black;
	white-space: nowrap;
	text-align: center;
	vertical-align: middle;
	padding: 1px 4px 0px 4px;
}
.dxpSeparator
{
	background-color: #CCCCCC;
}
/* Disabled */
.dxpDisabled
{
	color: #acacac;
	border-color: #808080;
	cursor: default;
}

/* -- ASPxPager Lite -- */

.dxpLite
{
	font: 9pt Tahoma;
	color: black;
	padding: 5px 2px;
	float: left;
}

.dxpLite .dxp-summary,
.dxpLite .dxp-sep,
.dxpLite .dxp-button,
.dxpLite .dxp-num,
.dxpLite .dxp-current,
.dxpLite .dxp-ellip
{
	display: block;
	float: left;
	margin-left: 4px;
	font-weight: normal;
}
.dxpLite .dxp-lead
{
	margin-left: 0 !important;
}

.dxpLite a
{
	color: #394EA2;
	text-decoration: underline;
}

.dxpLite .dxp-button
{
	color: #394EA2;
	white-space: nowrap;
	text-align: center;
	cursor: pointer;
	text-decoration: underline;
}
.dxpLite .dxp-button img
{
	border: 0;
	vertical-align: middle;
	text-decoration: none;
}
.dxpLite .dxp-wideButton
{
	padding: 0 5px;
}
.dxpLite .dxp-disabledButton
{
	text-decoration: none;
	color: #acacac;
	cursor: default;
}

.dxpLite .dxp-num
{
	color: #394EA2;
	text-decoration: underline;
	padding: 2px 5px 1px;
	cursor: pointer;
}

.dxpLite .dxp-current
{
	color: Black;
	text-decoration: none;
	font-weight: bold;
	padding: 2px 3px 1px;
	cursor: text;
}

.dxpLite .dxp-summary,
.dxpLite .dxp-ellip
{
	white-space: nowrap;
	padding: 2px 4px 1px;
}

.dxpLite .dxp-sep
{
	background: #cccccc;
    width: 1px;
    height: 11px;
    margin-top: 5px;
}

.dxpLiteDisabled,
.dxpLiteDisabled a,
.dxpLiteDisabled .dxp-summary,
.dxpLiteDisabled .dxp-sep,
.dxpLiteDisabled .dxp-button,
.dxpLiteDisabled .dxp-num,
.dxpLiteDisabled .dxp-current,
.dxpLiteDisabled .dxp-ellip
{
	color: #acacac;
	border-color: #808080;
	cursor: default;
}

/* -- ASPxPopupControl -- */
.dxpcControl
{
	font: 9pt Tahoma;
	color: black;
	background-color: white;
	border: solid 1px #8B8B8B;
}
.dxpcControl a
{
	color: #1E3695;
}
.dxpcCloseButton
{
	font: 9pt Tahoma;
	color: black;
	padding: 1px 1px 1px 1px;
}
.dxpcCloseButtonHover
{
	font: 9pt Tahoma;
	color: black;
}
.dxpcContent
{
	font: 9pt Tahoma;
	color: #010000;
	white-space: normal;
	padding: 9px 12px 9px 12px;
	vertical-align:top;
}
.dxpcFooter
{
	font: 9pt Tahoma;
	color: #858585;
	background-color: #F3F3F3;
	border-top: solid 1px #E0E0E0;
}
.dxpcFooter td.dxpc
{
	font: 9pt Tahoma;
	color: #858585;
	white-space: nowrap;
	padding: 6px 12px 8px 12px;
}
.dxpcHeader
{
	font: 9pt Tahoma;
	color: #404040;
	background-color: #DCDCDC;
	border-bottom: solid 1px #C9C9C9;
}
.dxpcHeader td.dxpc
{
	font: 9pt Tahoma;
	color: #404040;
	white-space: nowrap;
}
.dxpcModalBackground
{
	background-color: #777777;
	opacity: 0.7;
	filter:progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=70);
}
.dxpcLoadingPanel
{
	font: 9pt Tahoma;
	color: #303030;
	background-color: white;
	border: solid 1px #9F9F9F;
}
.dxpcLoadingPanel td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}
.dxpcLoadingDiv
{
	background-color: Gray;
	opacity: 0.01;
	filter:progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=1);
}

/* Disabled */
.dxpcDisabled
{
	color: #808080;
	cursor: default;
}

/* -- ASPxRoundPanel -- */
.dxrpControl td.dxrp,
.dxrpControlGB td.dxrp
{
	font-size: 9pt;
	font-family: Tahoma, Verdana, Arial;
	color: #000000;
}
/* Header */
.dxrpControl .dxrpHeader,
.dxrpControl .dxrpHeader td.dxrp,
.dxrpControlGB span.dxrpHeader
{
    font-size: 9pt;
	font-family: Tahoma, Verdana, Arial;
	color: #313131;
}
.dxrpControl .dxrpHeader
{
    background-color: #DEDEDE;
	border-bottom: 1px solid #C6C6C6;
}
.dxrpControl .dxrpHI,
.dxrpControl .dxrpHeader,
.dxrpControl .dxrpHeader td.dxrp
{
	vertical-align: top;
	white-space: nowrap;
}
/* Header image */
.dxrpControl .dxrpHI
{
    padding-right: 4px;
}
.dxrpControl .dxrpHIR
{
    padding-left: 4px;
}
/* Content */
.dxrpControl .dxrpcontent,
.dxrpControlGB .dxrpcontent
{
	vertical-align: top;
}
.dxrpControl .dxrpcontent
{
    background-color: #F7F7F7;
}
/* Edges */
.dxrpControl .dxrpTE,
.dxrpControl .dxrpHLE,
.dxrpControl .dxrpHRE
{
	background-color: #DEDEDE;
}
.dxrpControl .dxrpLE,
.dxrpControl .dxrpRE,
.dxrpControl .dxrpBE,
.dxrpControl .dxrpNHTE
{
	background-color: #F7F7F7;
}
.dxrpControl .dxrpTE,
.dxrpControl .dxrpNHTE,
.dxrpControlGB .dxrpNHTE
{
	border-top: 1px solid #8B8B8B;
}
.dxrpControl .dxrpLE,
.dxrpControl .dxrpHLE,
.dxrpControlGB .dxrpLE,
.dxrpControlGB .dxrpHLE
{
	border-left: 1px solid #8B8B8B;
}
.dxrpControl .dxrpRE,
.dxrpControl .dxrpHRE,
.dxrpControlGB .dxrpRE
{
	border-right: 1px solid #8B8B8B;
}
.dxrpControl .dxrpHLE,
.dxrpControl .dxrpHRE
{
	border-bottom: 1px solid #C6C6C6;
}
.dxrpControl .dxrpBE,
.dxrpControlGB .dxrpBE
{
	border-bottom: 1px solid #8B8B8B;
}
.dxrpControlGB .dxrpcontent,
.dxrpControlGB .dxrpHeader,
.dxrpControlGB .dxrpLE,
.dxrpControlGB .dxrpRE,
.dxrpControlGB .dxrpBE,
.dxrpControlGB .dxrpNHTE
{
	background-color: White;
}
/* Disabled */
.dxrpDisabled,
.dxrpDisabled td.dxrp
{
	color: #acacac;
	cursor: default;
}

/* -- ASPxSiteMapControl -- */
.dxsmControl a:hover
{
    text-decoration:none!important;
}
.dxsmControl a:visited
{
    color:#996085!important;
}

.dxsmControl
{
	color: #1E3695;
	background-color: white;
	font-family: Verdana, Tahoma, Arial;
	font-size: 11px;
	border: Solid 1px #A8A8A8;
}
/* - Category Level - */
.dxsmCategoryLevel,
.dxsmCategoryLevel a
{
    color: #1E3695;
    font-weight: bold;
    font-size: 13.5pt;
    font-family: Verdana;
    text-decoration: none;
}
.dxsmCategoryLevel
{
    white-space: nowrap;
    padding: 0px 0px 5px 0px;
}
.dxsmCategoryLevel
{
    border-bottom: solid 1px #B8B8B8;
}
 /*flow layout*/
.dxsmLevelCategoryFlow,
.dxsmLevelCategoryFlow a
{
    color: #1E3695;
    font-weight: bold;
    font-size: 13.5pt;
    font-family: Tahoma, Verdana, Arial;
	text-decoration: underline;
}
/* - Level 0 - */
.dxsmLevel0,
.dxsmLevel0 a,
.dxsmLevel0Categorized a,
.dxsmLevel0Categorized
{
    color: #1E3695;
    font-weight: bold;
    font-size: 9pt;
    font-family: Verdana;
    text-decoration: none;
}
.dxsmLevel0,
.dxsmLevel0Categorized
{
    white-space: nowrap;
    padding: 0px 0px 2px 0px;
}
.dxsmLevel0
{
    border-bottom:solid 1px #B8B8B8;
    padding: 0px 0px 5px 0px;
}
 /*flow layout*/
.dxsmLevel0Flow,
.dxsmLevel0Flow a,
.dxsmLevel0CategorizedFlow a,
.dxsmLevel0CategorizedFlow
{
    color: #1E3695;
    font-family: Tahoma, Verdana, Arial;
    font-weight: bold;
    font-size: 9pt;
	text-decoration: underline;
}
.dxsmLevel0Flow
{
    padding: 0px 0px 0px 0px;
}
.dxsmLevel0Flow
{
    text-decoration: none;
}

/* - Level 1 - */
.dxsmLevel1,
.dxsmLevel1 a,
.dxsmLevel1Categorized a,
.dxsmLevel1Categorized
{
    font-family: Tahoma;
    color: #3C55B9;
    font-size: 9pt;
    text-decoration: none;
}
.dxsmLevel1,
.dxsmLevel1Categorized
{
    white-space: nowrap;
    padding: 0px 0px 0px 0px;
}

/*flow layout*/
.dxsmLevel1Flow,
.dxsmLevel1Flow a,
.dxsmLevel1CategorizedFlow,
.dxsmLevel1CategorizedFlow a
{
    color: #3C55B9;
    font-family: Tahoma, Verdana, Arial;
    font-size: 9pt;
	text-decoration: underline;
}
.dxsmLevel1Flow
{
    text-decoration: none;
    padding: 0px 0px 0px 0px;
}

/* - Level 2 - */
.dxsmLevel2,
.dxsmLevel2 a,
.dxsmLevel2Categorized a,
.dxsmLevel2Categorized
{
    font-size: 8pt;
    font-family: Tahoma;
    color: #5078DC;
    text-decoration: none;
}
.dxsmLevel2,
.dxsmLevel2Categorized
{
    white-space:nowrap;
    padding: 0px 0px 0px 0px;
}
/*flow layout*/
.dxsmLevel2Flow,
.dxsmLevel2Flow a
{
    color: #5078DC;
    font-size: 8pt;
    font-family: Tahoma, Verdana, Arial;
	text-decoration:underline;
}
.dxsmLevel2Flow
{
    padding: 0px 0px 0px 0px;
}
/* - Level 3 - */
.dxsmLevel3,
.dxsmLevel3 a
{
    font-size: 7pt;
    font-family: Tahoma;
    color: #999999;
    text-decoration: none;
}
.dxsmLevel3
{
    white-space: nowrap;
    padding: 0px 0px 0px 0px;
}
/*flow layout*/
.dxsmLevel3Flow,
.dxsmLevel3Flow a
{
    color: #999999;
    font-size: 7pt;
    font-family: Tahoma, Verdana, Arial;
	text-decoration: underline;
}
/* - Level 4 - */
.dxsmLevel4,
.dxsmLevel4 a
{
    font-size: 6pt;
    font-family: Tahoma;
    color: #A8A8A8;
    text-decoration: none;
}
.dxsmLevel4
{
    white-space: nowrap;
    padding: 0px 0px 0px 0px;
}
/*flow layout*/
.dxsmLevel4Flow,
.dxsmLevel4Flow a
{
    color: #A8A8A8;
    font-family: Tahoma, Verdana, Arial;
    font-size: 6pt;
	text-decoration: underline;
}
.dxsmLevel4Flow
{
    padding: 0px 0px 0px 0px;
}
/* - Other Levels - */
.dxsmLevelOther
{
    font-size: 9px;
    font-family: Tahoma;
    color: #A8A8A8;
    text-decoration: none;
}
.dxsmLevelOther
{
    white-space:nowrap;
    padding: 0px 0px 0px 0px;
}
/*flow layout*/
.dxsmLevelOtherFlow,
.dxsmLevelOtherFlow a
{
    color: #A8A8A8;
    font-family: Tahoma, Verdana, Arial;
    font-size: 9pt;
	text-decoration: underline;
}
/* Disabled */
.dxsmDisabled
{
	color: #acacac;
	cursor: default;
}

/* -- ASPxTabControl, ASPxPageControl -- */
.dxtcControl
{
	font: 9pt Tahoma;
	color: black;
}
.dxtcLoadingPanel
{
	font: 9pt Tahoma;
	color: #303030;
}
.dxtcLoadingPanel td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}

/* Tab Hyperlink*/
.dxtcTab a,
.dxtcTabWithTabPositionLeft a,
.dxtcTabWithTabPositionBottom a,
.dxtcTabWithTabPositionRight a,
.dxtcActiveTab a,
.dxtcActiveTabWithTabPositionBottom a,
.dxtcActiveTabWithTabPositionLeft a,
.dxtcActiveTabWithTabPositionRight a,
.dxtcTabHover a,
.dxtcTabHoverWithTabPositionBottom a,
.dxtcTabHoverWithTabPositionLeft a,
.dxtcTabHoverWithTabPositionRight a
{
	text-decoration: none;
	color: black;
}

.dxtcActiveTab,
.dxtcActiveTabWithTabPositionBottom,
.dxtcActiveTabWithTabPositionLeft,
.dxtcActiveTabWithTabPositionRight
{
	font: 9pt Tahoma;
	color: black;
	border: solid 1px #A8A8A8;
	padding: 3px 12px 4px 12px;
	background-color: #FFFFFF;
	text-align: center;
}
.dxtcActiveTabWithTabPositionLeft,
.dxtcActiveTabWithTabPositionRight
{
	padding: 3px 13px 4px 12px;
}
/* Active Tab */
.dxtcActiveTab table.dxtc,
.dxtcActiveTabWithTabPositionBottom table.dxtc,
.dxtcActiveTabWithTabPositionLeft table.dxtc,
.dxtcActiveTabWithTabPositionRight table.dxtc
{
	font: 9pt Tahoma;
	color: black;
}
.dxtcActiveTab td.dxtc,
.dxtcActiveTabWithTabPositionBottom td.dxtc,
.dxtcActiveTabWithTabPositionLeft td.dxtc,
.dxtcActiveTabWithTabPositionRight td.dxtc
{
	white-space: nowrap;
    background-color: transparent!important;
    background-image: url('')!important;
    border-width: 0px!important;
    padding: 0px!important;
}
.dxtcActiveTabHover
{
	background-color: white;
}
/* Tab */
.dxtcTab,
.dxtcTabWithTabPositionLeft,
.dxtcTabWithTabPositionBottom,
.dxtcTabWithTabPositionRight
{
	font: 9pt Tahoma;
	color: black;
	background-color: #E0E0E0;
	border: solid 1px #A8A8A8;
	padding: 3px 12px 4px 12px;
	text-align: center;
}
.dxtcTab table.dxtc,
.dxtcTabWithTabPositionBottom table.dxtc,
.dxtcTabWithTabPositionLeft table.dxtc,
.dxtcTabWithTabPositionRight table.dxtc
{
	font: 9pt Tahoma;
	color: black;
}
.dxtcTab td.dxtc,
.dxtcTabWithTabPositionBottom td.dxtc,
.dxtcTabWithTabPositionLeft td.dxtc,
.dxtcTabWithTabPositionRight td.dxtc
{
	white-space: nowrap;
    background-color: transparent!important;
    background-image: url('')!important;
    border-width: 0px!important;
    padding: 0px!important;
}
.dxtcTabWithTabPositionBottom
{
}
.dxtcTabWithTabPositionLeft
{
}
.dxtcTabWithTabPositionRight
{
}
/* Hover */
.dxtcTabHover,
.dxtcTabHoverWithTabPositionBottom,
.dxtcTabHoverWithTabPositionLeft,
.dxtcTabHoverWithTabPositionRight
{
	background-color: #F2F2F2;
}
.dxtcPageContent,
.dxtcPageContentWithTabPositionBottom,
.dxtcPageContentWithTabPositionLeft,
.dxtcPageContentWithTabPositionRight,
.dxtcPageContentWithoutTabs
{
	font: 9pt Tahoma;
	color: black;
	background-color: white;
	vertical-align: top;
}
.dxtcContent,
.dxtcContentWithTabPositionBottom,
.dxtcContentWithTabPositionLeft,
.dxtcContentWithTabPositionRight
{
	font: 9pt Tahoma;
	color: black;
	border: solid 1px #A8A8A8;
	background-color: white;
	vertical-align: top;
}
.dxtcControl td.dxtcTabsCell,
.dxtcControl td.dxtcTabsCellWithTabPositionBottom,
.dxtcControl td.dxtcTabsCellWithTabPositionLeft,
.dxtcControl td.dxtcTabsCellWithTabPositionRight
{
}
/* Scrolling */
.dxtcScrollButtonCell
{
	border: none;
	width: 1px;
	vertical-align: top;
}
.dxtcTabsCellWithTabPositionBottom .dxtcScrollButtonCell
{
	vertical-align: bottom;
}
.dxtcScrollButtonSeparator,
.dxtcScrollButtonSeparator div
{
	height: 1px;
	width: 1px;
}
.dxtcScrollButtonIndent,
.dxtcScrollButtonIndent div
{
	height: 1px;
	width: 5px;
}
.dxtcScrollButton
{
	border: solid 1px #7F7F7F;
	background: #E8E7E8 url('<%=WebResource("DevExpress.Web.Images.tcScrollBtnBack.gif")%>') repeat-x;
    cursor: pointer;
    text-align: center;
    padding: 6px;
}
.dxtcScrollButtonHover
{
	background: #F4F4F4 url('<%=WebResource("DevExpress.Web.Images.tcScrollBtnHoverBack.gif")%>') repeat-x;
}
.dxtcScrollButtonPressed
{
	background: #8D8D8D;
}
.dxtcScrollButtonDisabled
{
	cursor: default;
	border-color: #C3C3C3;
	background: #E6E7E8 url('<%=WebResource("DevExpress.Web.Images.tcScrollBtnDisabledBack.gif")%>') repeat-x;
}
/* Misc */
.dxtcLeftAlignCell,
.dxtcTabsCellWithTabPositionBottom .dxtcLeftAlignCell
{
	text-align: left;
}
.dxtcRightAlignCell,
.dxtcTabsCellWithTabPositionBottom .dxtcRightAlignCell
{
	text-align: right;
}
/* Disabled */
.dxtcDisabled,
.dxtcDisabled table.dxtc
{
	color: #acacac;
	cursor: default;
}

/* -- ASPxTabControl Lite -- */
.dxtcLite
{
	overflow: hidden;
    float: left;
}
.dxtcLite .dxtc-strip,
.dxtcLite .dxtc-wrapper
{
    list-style: none outside none;
    float: left;
    padding: 0;
    margin: 0;
    _overflow: hidden;
}
.dxtcLite .dxtc-tab,
.dxtcLite .dxtc-activeTab,
.dxtcLite .dxtc-leftIndent,
.dxtcLite .dxtc-spacer,
.dxtcLite .dxtc-rightIndent,
.dxtcLite .dxtc-sbWrapper,
.dxtcLite .dxtc-sbIndent,
.dxtcLite .dxtc-sbSpacer
{
	display: block;
    height: 21px;

    margin: 0;
}
.dxtcLite .dxtc-lineBreak
{
	float: none;
	display: block;
	clear: both;
	height: 0;
	width: 0;
	font-size: 0;
	line-height: 0;
	overflow: hidden;
	visibility: hidden;
}
.dxtcLite .dxtc-tab,
.dxtcLite .dxtc-activeTab,
.dxtcLite.dxtc-noSpacing .dxtc-tab.dxtc-lead,
.dxtcLite.dxtc-noSpacing .dxtc-activeTab.dxtc-lead,
.dxtcLite.dxtc-noSpacing .dxtc-tab,
.dxtcLite.dxtc-noSpacing .dxtc-activeTab
{
	border: solid 1px #A8A8A8;
}
.dxtcLite.dxtc-noSpacing .dxtc-tab,
.dxtcLite.dxtc-noSpacing .dxtc-activeTab
{
	border-left: none;
}
.dxtcLite .dxtc-tab,
.dxtcLite .dxtc-activeTab
{
	background: #E0E0E0;
    float: left;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
}
.dxtcLite .dxtc-activeTab
{
    border-bottom: solid 1px white;
    background: white;
}
.dxtcLite .dxtc-tab a
{
	text-decoration: none;
	color: black;
}
.dxtcLite .dxtc-tabHover
{
	background: #F2F2F2;
}
.dxtcLite .dxtc-leftIndent,
.dxtcLite .dxtc-spacer,
.dxtcLite .dxtc-rightIndent,
.dxtcLite .dxtc-sbWrapper,
.dxtcLite .dxtc-sbIndent,
.dxtcLite .dxtc-sbSpacer
{
    float: left;
    border-width: 0;
    border-top: solid 1px transparent;
    border-bottom: solid 1px #A8A8A8;
    overflow: hidden;

    _border-top-color: #000001;
 	_zoom: 1;
	_filter:progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite .dxtc-spacer
{
    width: 1px;
}
.dxtcLite .dxtc-leftIndent,
.dxtcLite .dxtc-rightIndent
{
    width: 5px;
}
.dxtcLite .dxtc-link
{
	padding: 3px 12px 4px;
	display: block;
	font-size: 0;
    text-decoration: none;
    height: 100%;
    _float: left;
}
.dxtcLite .dxtc-text,
.dxtcLite .dxtc-leftIndent,
.dxtcLite .dxtc-rightIndent
{
    color: #333333;
	font: 12px Tahoma;
    font-weight: normal;
    text-decoration: none;
    white-space: nowrap;
}
.dxtcLite .dxtc-img
{
	border: none;
	margin: 0 3px -4px 0;
	width: 16px;
	height: 16px;
}
.dxtcLite.dxtc-rtl .dxtc-img
{
	margin: 0 0 -4px 3px;
}
.dxtcLite .dxtc-content
{
	font: 9pt Tahoma;
	color: black;
	background-color: #FFFFFF;
    float:left;
    clear:left;
    border: solid 1px #A8A8A8;
    overflow: hidden;
    padding: 11px;
    background: white;
}
.dxtcLite.dxtc-top .dxtc-content
{
	border-top: none !important;
}
/* Rtl */
.dxtcLite.dxtc-rtl,
.dxtcLite.dxtc-rtl .dxtc-content,
.dxtcLite.dxtc-rtl .dxtc-strip,
.dxtcLite.dxtc-rtl .dxtc-wrapper,
.dxtcLite.dxtc-rtl .dxtc-leftIndent,
.dxtcLite.dxtc-rtl .dxtc-spacer,
.dxtcLite.dxtc-rtl .dxtc-rightIndent,
.dxtcLite.dxtc-rtl .dxtc-sbWrapper,
.dxtcLite.dxtc-rtl .dxtc-sbIndent,
.dxtcLite.dxtc-rtl .dxtc-sbSpacer,
.dxtcLite.dxtc-rtl .dxtc-tab,
.dxtcLite.dxtc-rtl .dxtc-activeTab
{
	float: right;
}
.dxtc-top.dxtc-rtl .dxtc-content,
.dxtc-bottom.dxtc-rtl .dxtc-strip,
.dxtc-bottom.dxtc-rtl .dxtc-wrapper
{
	clear: right !important;
}
.dxtc-left.dxtc-rtl .dxtc-strip
{
	float: left;
}
.dxtcLite.dxtc-rtl .dxtc-content,
.dxtcLite.dxtc-rtl .dxtc-strip,
.dxtcLite.dxtc-rtl .dxtc-wrapper
{
	*float: left;
}
.dxtcLite.dxtc-rtl .dxtc-content
{
	*clear: left !important;
}
/* Scrolling */
.dxtcLite .dxtc-sb
{
	background: #E8E7E8 url('<%=WebResource("DevExpress.Web.Images.tcScrollBtnBack.gif")%>') repeat-x;
	border: solid 1px #7F7F7F;
    cursor: pointer;
    padding: 5px;
    font-size: 0;
}
.dxtcLite.dxtc-bottom .dxtc-sb
{
	margin-top: 2px;
}
.dxtcLite .dxtc-sbHover
{
	background: #F4F4F4 url('<%=WebResource("DevExpress.Web.Images.tcScrollBtnHoverBack.gif")%>') repeat-x;
}
.dxtcLite .dxtc-sbPressed
{
	background: #8D8D8D;
}
.dxtcLite .dxtc-sbDisabled
{
	cursor: default;
	border-color: #C3C3C3;
	background: #E6E7E8 url('<%=WebResource("DevExpress.Web.Images.tcScrollBtnDisabledBack.gif")%>') repeat-x;
}
.dxtcLite .dxtc-sb img
{
	border: none 0;
}
.dxtcLite .dxtc-sbIndent
{
	width: 5px;
}
.dxtcLite .dxtc-sbSpacer
{
	width: 1px;
}
/* Multi-row */
.dxtcLite .dxtc-n
{
	_display: inline;
}
.dxtcLiteDisabled,
.dxtcLiteDisabled .dxtc-text,
.dxtcLiteDisabled .dxtc-activeTab .dxtc-text,
.dxtcLiteDisabled .dxtc-content
{
	color: #acacac;
	cursor: default;
}
/* bottom  */
.dxtcLite.dxtc-bottom .dxtc-strip,
.dxtcLite.dxtc-bottom .dxtc-wrapper
{
	clear: left;
	*float: none;
}
.dxtcLite.dxtc-bottom .dxtc-leftIndent,
.dxtcLite.dxtc-bottom .dxtc-spacer,
.dxtcLite.dxtc-bottom .dxtc-rightIndent,
.dxtcLite.dxtc-bottom .dxtc-sbWrapper,
.dxtcLite.dxtc-bottom .dxtc-sbIndent,
.dxtcLite.dxtc-bottom .dxtc-sbSpacer
{
    border-top: solid 1px #A8A8A8;
    border-bottom: solid 1px transparent;

    _border-bottom-color: #000001;
	_zoom: 1;
	_filter:progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite.dxtc-bottom .dxtc-activeTab
{
    border-top: solid 1px white;
    border-bottom: solid 1px #A8A8A8;
    background: white;
}
.dxtcLite.dxtc-bottom .dxtc-content
{
    border: solid 1px #A8A8A8;
    border-bottom: none !important;
}
/* left */
.dxtcLite.dxtc-left .dxtc-tab,
.dxtcLite.dxtc-left .dxtc-activeTab,
.dxtcLite.dxtc-left .dxtc-leftIndent,
.dxtcLite.dxtc-left .dxtc-spacer,
.dxtcLite.dxtc-left .dxtc-rightIndent
{
	float: none;
	clear: none;
	width: auto;
	height: auto;

	*float: left;
	*clear: both;
}
.dxtcLite.dxtc-left .dxtc-activeTab,
.dxtc-left.dxtc-noSpacing .dxtc-tab,
.dxtc-left.dxtc-noSpacing .dxtc-activeTab,
.dxtc-left.dxtc-noSpacing .dxtc-tab.dxtc-lead,
.dxtc-left.dxtc-noSpacing .dxtc-activeTab.dxtc-lead
{
	 border: solid 1px #A8A8A8
}
.dxtc-left.dxtc-noSpacing .dxtc-tab,
.dxtc-left.dxtc-noSpacing .dxtc-activeTab
{
	border-top: none;
}
.dxtcLite.dxtc-left .dxtc-activeTab
{
    border-right: solid 1px white;
    background: white;
}
.dxtcLite.dxtc-left .dxtc-leftIndent,
.dxtcLite.dxtc-left .dxtc-spacer,
.dxtcLite.dxtc-left .dxtc-rightIndent
{
	border: none 0;
    border-right: solid 1px #A8A8A8;
    border-left: solid 1px transparent;
    width: auto;

    _border-left-color: #000001;
	_zoom: 1;
	_filter:progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite.dxtc-left .dxtc-leftIndent,
.dxtcLite.dxtc-left .dxtc-rightIndent
{
	height: 5px;
}
.dxtcLite.dxtc-left .dxtc-spacer
{
	height: 1px;
}
.dxtcLite.dxtc-left .dxtc-content
{
    border: solid 1px #A8A8A8;
    border-left: none !important;
    float: left;
    clear: none;
}
/* right */
.dxtcLite.dxtc-right .dxtc-tab,
.dxtcLite.dxtc-right .dxtc-activeTab,
.dxtcLite.dxtc-right .dxtc-leftIndent,
.dxtcLite.dxtc-right .dxtc-spacer,
.dxtcLite.dxtc-right .dxtc-rightIndent
{
	float: none;
	clear: none;
	width: auto;
	height: auto;

	*float: left;
	*clear: both;
}
.dxtcLite.dxtc-right .dxtc-activeTab,
.dxtc-right.dxtc-noSpacing .dxtc-tab,
.dxtc-right.dxtc-noSpacing .dxtc-activeTab,
.dxtc-right.dxtc-noSpacing .dxtc-tab.dxtc-lead,
.dxtc-right.dxtc-noSpacing .dxtc-activeTab.dxtc-lead
{
	 border: solid 1px #A8A8A8
}
.dxtc-right.dxtc-noSpacing .dxtc-tab,
.dxtc-right.dxtc-noSpacing .dxtc-activeTab
{
	border-top: none;
}
.dxtcLite.dxtc-right .dxtc-activeTab
{
    border-left: solid 1px white;
    background: white;
}
.dxtcLite.dxtc-right .dxtc-leftIndent,
.dxtcLite.dxtc-right .dxtc-spacer,
.dxtcLite.dxtc-right .dxtc-rightIndent
{
	border: none 0;
    border-left: solid 1px #A8A8A8;
    border-right: solid 1px transparent;

    _border-right-color: #000001;
	_zoom: 1;
	_filter:progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite.dxtc-right .dxtc-leftIndent,
.dxtcLite.dxtc-right .dxtc-rightIndent
{
	height: 5px;
}
.dxtcLite.dxtc-right .dxtc-spacer
{
	height: 1px;
}
.dxtcLite.dxtc-right .dxtc-content
{
    border: solid 1px #A8A8A8;
    border-right: none !important;
    float: left;
    clear: none;
}
/* Services rules */
.dxtcLite.dxtc-noTabs .dxtc-content
{
	border: solid 1px #A8A8A8 !important;
}

/* -- ASPxTitleIndex -- */
.dxtiControl a:hover
{
    text-decoration: none!important;
}
.dxtiControl a:visited
{
    color: #996085!important;
}
.dxtiControl
{
	font: 8pt Tahoma;
	color: #1E3695;
	background-color: white;
	font-family: Tahoma, Arial;
	border: Solid 1px #A8A8A8;
}
.dxtiLoadingPanel
{
	border: solid 1px #9F9F9F;
	background-color: white;
	font: 9pt Tahoma;
	color: #303030;
}
.dxtiLoadingPanel td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}
.dxtiItem,
.dxtiItem a
{
	font: 9pt Tahoma;
	color: #0147A6;
	text-decoration: none;
}
.dxtiItem
{
	white-space: nowrap;
}
.dxtiGroupHeader,
.dxtiGroupHeaderCategorized
{
	font: 16pt Verdana;
	text-decoration: none;
}
.dxtiGroupHeader,
.dxtiGroupHeaderCategorized
{
	white-space:nowrap;
	padding: 0px 0px 3px 0px;
}
.dxtiGroupHeaderCategorized
{
    border-bottom: Solid 1px #C0C0C0;
    padding-bottom: 2px;
}
/* - GroupHeaderText - */
.dxtiGroupHeaderText
{
    background-color: #B4B4B4;
    color: #FFFFFF;
}
.dxtiGroupHeaderText
{
    padding: 2px 7px 2px 7px;
}
.dxtiGroupHeaderTextCategorized
{
    color: #666666;
    font-size: 18pt;
}
.dxtiGroupHeaderTextCategorized
{
    padding-left: 7px;
    padding-right: 7px;
    padding-top: 2px;
}
/* - FilterBox - */
.dxtiFilterBoxInfoText
{
    font: 7pt Verdana;
    color: #999999;
    font-weight: normal;
    padding-top: 0px;
    padding-bottom: 4px;
}
.dxtiFilterBoxEdit
{
    font-size
    : 9pt;
    width: 158px;
    border: Solid 1px #9F9F9F;
    padding-left: 3px;
}
.dxtiFilterBox,
.dxtiFilterBox table
{
    color: #898989;
    font-size: 9pt;
    font-weight: bold;
}
.dxtiFilterBox
{
    background-color: #E8E8E8;
    padding: 15px;
}
/* - IndexPanel - */
.dxtiIndexPanel
{
    padding-bottom: 10px;
    text-decoration: none;
}
.dxtiIndexPanelItem,
.dxtiIndexPanelItem a,
.dxtiCurrentIndexPanelItem
{
    color: #1153AD;
    font-family: Verdana;
    text-decoration: none;
}
.dxtiIndexPanelItem a:hover
{
    color: #5494ea;
}

.dxtiIndexPanelItem,
.dxtiCurrentIndexPanelItem
{
    padding: 2px 6px 2px 6px;
}
.dxtiCurrentIndexPanelItem
{
    color: #000000;
    background-color: #E0E0E0;
}
/* - BackToTop - */
.dxtiBackToTop,
.dxtiBackToTop a
{
    font-size: 7pt;
    text-decoration: none;
    color: #0d45b7;
}
.dxtiBackToTop a:hover
{
    color: #5494ea;
}
.dxtiBackToTop a:visited
{
    color: #ab59a6;
}

.dxtiBackToTop
{
    padding: 0px 0px 12px 98px;
}
.dxtiBackToTopRtl
{
    padding: 0px 98px 12px 0px;
}
/* Disabled */
.dxtiDisabled
{
	color: #acacac;
	cursor: default;
}
/* -- ASPxUploadControl -- */
.dxucControl,
.dxucEditArea
{
    font-size: 10pt;
    font-family: Tahoma, Verdana, Arial;
}
.dxucErrorCell
{
    font-size: 9pt;
    font-family: Tahoma, Verdana, Arial;
    color: Red;
    text-align: left;
}
.dxucButton,
.dxucButton a
{
    font-size: 10pt;
    font-family: Tahoma, Verdana, Arial;
    color: #394ea2;
    cursor: pointer;
    white-space: nowrap;
}
/* ProgressBar */
.dxucProgressBar
{
    border: Solid 1px #696969;
    background-color: #FFFFFF;
}
.dxucProgressBar,
.dxucProgressBar td.dx
{
    font-family: Tahoma, Verdana, Arial;
    font-size: 9pt;
   	color: Black;
}
.dxucProgressBar .dxucPBMainCell,
.dxucProgressBar td.dx
{
    padding: 0;
}
.dxucProgressBarIndicator
{
    background-color: #E0DFDF;
}
/* Disabled */
.dxucDisabled,
.dxucDisabled a
{
	color: #acacac;
	cursor: default;
    font-size: 10pt;
}

/* -- ASPxSplitter -- */
.dxsplControl,
.dxsplVSeparator,
.dxsplHSeparator
{
	background-color: White;
}
.dxsplControl,
.dxsplVSeparator,
.dxsplHSeparator,
.dxsplPane,
.dxsplPaneCollapsed,
.dxsplVSeparator,
.dxsplHSeparator,
.dxsplVSeparatorCollapsed,
.dxsplHSeparatorCollapsed
{
	border: solid 0px #8C8C8C;
}
.dxsplPane,
.dxsplPaneCollapsed
{
	border-width: 1px;
}
.dxsplPaneCollapsed
{
	border-right-width: 0px;
	border-bottom-width: 0px;
}
.dxsplVSeparatorHover
{
	cursor: w-resize;
}
.dxsplHSeparatorHover
{
	cursor: n-resize;
}
.dxsplVSeparatorCollapsed
{
	border-top-width: 1px;
	border-bottom-width: 1px;
}
.dxsplHSeparatorCollapsed
{
	border-left-width: 1px;
	border-right-width: 1px;
}
.dxsplVSeparatorCollapsed,
.dxsplHSeparatorCollapsed
{
	cursor: default !important;
}
.dxsplVSeparatorButton
{
	cursor: pointer;
	padding: 5px 0px;
}
.dxsplHSeparatorButton
{
	cursor: pointer;
	padding: 0px 5px;
}
.dxsplVSeparatorHover,
.dxsplHSeparatorHover,
.dxsplVSeparatorButtonHover,
.dxsplHSeparatorButtonHover
{
	background-color: #DCDCDC;
}
.dxsplResizingPointer
{
	background-image: url('<%=WebResource("DevExpress.Web.Images.splResizingPointer.gif")%>');
	background-repeat: repeat;
}
.dxsplResizingPointer,
.dxsplS
{
	font-size: 0px;
	line-height: 0px;
}
.dxsplLCC,
.dxsplCC,
.dxsplS
{
	overflow: hidden;
}
.dxsplLCC,
.dxsplCC,
.dxsplP
{
	width: 100%;
	height: 100%;
}
.dxsplLCC
{
	padding: 8px 8px 8px 8px;
}

/* -- ASPxTreeView -- */
.dxtvControl
{
	float: left;
}

.dxtvControl li
{
	font-family: Tahoma, Verdana, Arial;
	font-size: 9pt;
	overflow-y: hidden;
}

.dxtvControl ul
{
	list-style-type: none;
	margin: 0;
    padding: 0;
	overflow-y: hidden;
}

.dxtvControl a {
	color: black;
	text-decoration: none;
}

.dxtvControl .dxtv-ln
{
	background-image: url('<%=WebResource("DevExpress.Web.Images.tvLine.gif")%>');
	background-repeat: repeat-y;
	vertical-align: top;
}

.dxtvControl .dxtv-nd
{
    margin-top: 1px;
	float: left;
    padding: 1px;
	cursor: pointer;
	display: block;
	text-decoration: none;
	color: Black;
    outline: 0 none;
}

.dxtvControl .dxtv-elbNoLn,
.dxtvControl .dxtv-elb
{
	width: 26px;
	height: 21px;
	vertical-align: top;
	float: left;
}

.dxtvControl .dxtv-elb
{
	background-image: url('<%=WebResource("DevExpress.Web.Images.tvElbow.gif")%>');
	background-repeat:no-repeat;
}

.dxtvControl .dxtv-btn
{
	margin-left: 8px;
	margin-top: 3px;
	cursor: pointer;
}

.dxtvControl .dxtv-subnd
{
	margin-left: 22px;
}

.dxtvControl .dxtv-ndImg
{
	padding-left: 5px;
	float: left;
	vertical-align: middle;
	cursor: pointer;
}

.dxtvControl .dxtv-ndTxt
{
	padding: 3px 4px 3px 4px;
	float: left;
	white-space: nowrap;
	vertical-align: middle;
	cursor: pointer;
}

.dxtvControl .dxtv-ndChk
{
	padding: 0;
	float: left;
	vertical-align: middle;
    cursor: default;
    margin: 4px 3px 3px 6px;
    /*for IE6-7*/
    *margin: 0 0 0 2px;
}

.dxtvControl .dxtv-ndTmpl
{
	float: left;
	white-space: nowrap;
}

.dxtvControl .dxtv-ndSel,
.dxtvControl .dxtv-ndHov
{
	border: solid 1px #888888;
	padding: 0;
}

.dxtvControl .dxtv-ndSel
{
	background-color: #D8D8D8;
	cursor: default;
}

.dxtv-ndSel .dxtv-ndTxt,
.dxtv-ndSel .dxtv-ndImg
{
	cursor: default;
}

.dxtvControl .dxtv-ndHov
{
	background-color: #F2F2F2;
    cursor: pointer;
}

.dxtv-ndHov .dxtv-ndTxt,
.dxtv-ndHov .dxtv-ndImg
{
	cursor: pointer;
}

.dxtvControl .dxtv-clr,
.dxtvControl .dxtv-clrIE7
{
	clear:both;
	font-size:0;
	height:0;
	visibility:hidden;
	width:0;
    display:block;
}

.dxtvControl .dxtv-clr
{
    line-height:0;
}

.dxtvControl.dxtvRtl,
.dxtvControl.dxtvRtl .dxtv-nd,
.dxtvControl.dxtvRtl .dxtv-elbNoLn,
.dxtvControl.dxtvRtl .dxtv-elb,
.dxtvControl.dxtvRtl .dxtv-ndTxt,
.dxtvControl.dxtvRtl .dxtv-ndImg,
.dxtvControl.dxtvRtl .dxtv-ndChk,
.dxtvControl.dxtvRtl .dxtv-ndTmpl
{
    float: right;
}

.dxtvControl.dxtvRtl .dxtv-elb,
.dxtvControl.dxtvRtl .dxtv-ln
{
    background-position: right top;
}

.dxtvControl.dxtvRtl .dxtv-elb
{
    background-image: url('<%=WebResource("DevExpress.Web.Images.tvElbowRtl.gif")%>');
}

.dxtvControl.dxtvRtl .dxtv-btn
{
    margin: 3px 8px 0 0;
}

.dxtvControl.dxtvRtl .dxtv-subnd
{
    margin: 0 22px 0 0;
}

.dxtvControl.dxtvRtl .dxtv-ndImg
{
    padding: 0 5px 0 0;
}

.dxtvControl.dxtvRtl.OperaRtlFix .dxtv-btn
{
    margin: 3px 0 0 8px;
}

.dxtvControl.dxtvRtl .dxtv-ndChk
{
    margin: 4px 6px 3px 3px;
    /*for IE6-7*/
    *margin: 0 2px 0 0;
}

.dxtvControl.dxtvRtl.OperaRtlFix .dxtv-subnd
{
    overflow-x: hidden;
}

.dxtvDisabled,
.dxtvControl .dxtvDisabled,
.dxtvDisabled a,
.dxtvDisabled .dxtv-ndTxt,
.dxtvDisabled .dxtv-ndImg,
.dxtvDisabled .dxtv-btn,
.dxtvDisabled .dxtv-nd
{
	color: #acacac;
	cursor: default;
}

.dxtvLoadingPanelWithContent
{
	font: 9pt Tahoma;
	color: #303030;
	background:#FFFFFF;
	border:1px solid #D6D6D6;
}
.dxtvLoadingPanelWithContent td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px 12px 12px 12px;
}

.dx-clear
{
	display: block;
	clear: both;
	height: 0;
	width: 0;
	font-size: 0;
	line-height: 0;
	overflow: hidden;
	visibility: hidden;
}

/* ASPxFileManager */
.dxfmControl
{
	font-size: 9pt;
	font-family: Tahoma;
	outline: 0px;
}
.dxfmDisabled
{
	color:#ACACAC;
}

/* FileManager - Splitter */
.dxfmControl .dxsplControl
{
	border-width: 1px;
	border-color: #7F7F7F;
}
.dxfmControl .dxsplPane
{
	border-width: 0px;
	background-color: White;
}
.dxfmControl .dxsplLCC {
	outline-width: 0px;
	padding: 4px;
}
.dxfmControl .dxsplVSeparator
{
	width:3px;
	background: url('<%=WebResource("DevExpress.Web.Images.fmSplitterSeparator.gif")%>') right;
	background-repeat:repeat-y;
	border: 0px;
}
.dxfmControl .dxsplHSeparator
{
    border: 0px;
	background-color: #E0E0E0;
}

/* FileManager - TreeView */
.dxfmControl .dxtvControl
{
	margin-left: -5px;
}
.dxfmControl .dxtvControl .dxtv-nd .dxtv-ndTxt
{
	padding-left: 1px;
}
.dxfmControl .dxtvControl .dxtv-nd
{
	padding-left: 3px;
	margin-bottom: 0px;
}
.dxfmControl .dxtvControl .dxfm-folderSI
{
	border: dotted 1px #888888;
	padding: 0px 0px 0px 2px;
}
.dxfmControl .dxtvControl .dxtv-ndHov
{
	border: solid 1px #888888;
	padding-left: 2px;
}
.dxfmControl .dxtvControl .dxtv-ndSel
{
	padding-left: 2px;
}
.dxfmControl .dxtvControl .dxtv-ndImg
{
	padding: 0px;
	margin-right: 3px;
	margin-top: 2px;
}

/* FileManager - File */
.dxfmControl .dxfm-file
{
	float: left;
	text-align: center;
	cursor: pointer;
	white-space: nowrap;

	padding: 5px;
	margin: 5px;
}
.dxfmDisabled .dxfm-file
{
	cursor: default;
}
.dxfmControl .dxfm-fileSI
{
	border: dotted 1px #888888;
}
.dxfmControl .dxfm-fileSA
{
	background-color: #D8D8D8;
	border: solid 1px #888888;
}
.dxfmControl .dxfm-fileH
{
	background: #F2F2F2;
    border: solid 1px #888888;
}
.dxfmControl .dxfm-content
{
	overflow: hidden;
}
.dxfmControl .dxfm-content div
{
	overflow: hidden;
	width: 100%;
	white-space: nowrap;
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
}
.dxfmControl .dxfm-content div
{
	height: 18px;
}
.dxfmControl .dxfm-content .dxfm-highlight
{
	background:none repeat scroll 0 0 #CFCFCF;
	color:#333333;
	font-weight: bold;
}

/* FileManager - Toolbar */
.dxfmControl .dxfm-toolbar
{
	background-color: #F3F3F3;
}
.dxfmControl .dxfm-toolbar.dxsplPane table.dxfm
{
	width: 100%;
}
.dxfmControl .dxfm-toolbar.dxsplPane .dxfm-filter
{
	text-align: right;
	vertical-align: top;
	white-space: nowrap;
}
.dxfmControl .dxfm-toolbar.dxsplPane .dxfm-filter input
{
    border: 1px solid #9F9F9F;
	margin: 4px 4px 0px 3px;
	width: 150px;
	height: 16px;
	font: 9pt Tahoma;
}
.dxfmControl .dxfm-toolbar.dxsplPane .dxfm-path input
{
    border: 1px solid #9F9F9F;
	width: 250px;
	height: 16px;
	font: 9pt Tahoma;
}

/* FileManager - Toolbar - Light */
.dxfmControl .dxfm-toolbar .dxsplLCC
{
	padding: 5px;
}
.dxfmControl .dxfm-toolbar .dxmLite .dxm-main
{
	margin-top: 0px;
    border-width: 0px;
    background-color: transparent;
    background-image: none;
}
.dxfmControl .dxfm-toolbar .dxmLite .dxm-horizontal.dxmtb .dxm-separator
{
	margin: 0px 11px;
}
.dxfmControl .dxfm-toolbar .dxmLite .dxfm-path
{
	padding-left: 1px;
}
.dxfmControl .dxfm-toolbar .dxmLite .dxfm-path input
{
	margin: 1px 8px 0px 4px;
}
.dxfmControl .dxfm-toolbar .dxmLite .dxm-item .dxm-content
{
	padding-top: 4px;
}

/* FileManager - Toolbar - Classic */
.dxfmControl .dxfm-toolbar .dxmMenu
{
	border-width: 0px;
	background-color: #F3F3F3;
	background-image: none;
	padding-top: 1px;
	padding-left: 3px;
}
.dxfmControl .dxfm-toolbar .dxmMenu .dxmMenuSeparator
{
	padding: 0px 11px;
}
.dxfmControl .dxfm-toolbar .dxmMenu .dxmMenuItemSeparatorSpacing
{
	width: 1px;
}
.dxfmControl .dxfm-toolbar .dxmMenu .dxmMenuItemSpacing
{
	width: 0px;
	display: block;
}
.dxfmControl .dxfm-toolbar .dxmMenu .dxmMenuItem.dxfm-path
{
	padding-right: 0px;
	padding-left: 0px;
	padding-top: 3px;
}
.dxfmControl .dxfm-toolbar .dxmMenu .dxmMenuItem
{
	padding-top: 1px;
	padding-left: 3px;
	background-image: none;
	background-color: transparent;
}
.dxfmControl .dxfm-toolbar .dxmMenu .dxfm-path input
{
	margin: 0px 8px 0px 4px;
}

/* FileManager - UploadPanel */
.dxfmControl .dxfm-uploadPanel
{
	background-color: #F3F3F3;
	text-align: right;
}
.dxfmControl .dxfm-uploadPanel.dxsplPane table.dxfm-uploadPanelTable
{
	display: inline-block;
	margin-right: 5px;
	margin-top: 1px;
}
.dxfmControl .dxfm-uploadPanel.dxsplPane table.dxfm-uploadPanelTable .dxucControl
{
	margin-right: 10px;
}
.dxfmControl .dxfm-uploadPanel.dxsplPane table.dxfm-uploadPanelTable a
{
	color: #1B3F91;
}
.dxfmControl .dxfm-uploadPanel.dxsplPane table.dxfm-uploadPanelTable a.dxfm-uploadDisable
{
	color: #777777;
	cursor: default;
}

/* FileManager - Create, Rename input */
.dxfmControl .dxfm-cInput,
.dxfmControl .dxfm-rInput
{
    border: solid 1px #9f9f9f;
	padding: 1px;
	font: 9pt Tahoma;
	outline-width: 0px;
	margin:0px;
}

/* FileManager - LoadingPanel */
.dxfmControl .dxfmLoadingPanel
{
	background-color:white;
	border:1px solid #9F9F9F;
	color:#303030;
	font:9pt Tahoma;
}
.dxfmControl .dxfmLoadingPanel td.dx {
	padding:12px;
	text-align:center;
	white-space:nowrap;
}

/* FileManager - Move PopupControl */
.dxfmControl .dxpcContent
{
	padding: 5px 0px 0px 0px;
	background-color: White;
}
.dxfmControl .dxpcContent .dxfm-mpFoldersC
{
	overflow:auto;
	padding: 0px 0px 20px 5px;
}
.dxfmControl .dxpcContent .dxfm-mpButtonC
{
	margin-top: 20px;
	background-color: #F3F3F3;
	border-top: 1px solid #E0E0E0;
	padding: 10px;
	text-align: right;
	padding: 10px 15px 10px 10px;
}
.dxfmControl .dxpcContent .dxfm-mpButtonC a
{
	margin-left: 12px;
	color: #1B3F91;
}