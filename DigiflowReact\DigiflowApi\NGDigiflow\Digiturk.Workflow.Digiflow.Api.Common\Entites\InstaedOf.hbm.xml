﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Api.Common"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="InsteadOfUser,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_INSTAED_OF_USER" schema="DT_WORKFLOW">
		<id name="RequestId" type="long" column="WF_DF_INSTAED_OF_USER_ID">
			<generator class="trigger-identity"></generator>
		</id>
		<property name="LoginId" column="LOGIN_ID"/>
		<property name="InsteadOfLoginId" column="INSTAED_OF_LOGIN_ID"/>
		<property name="Created" column="CREATED" />
		<property name="LastUpdated" column="LAST_UPDATED" />
		<property name="CreatedBy" column="CREATED_BY" />
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
	</class>
</hibernate-mapping>
