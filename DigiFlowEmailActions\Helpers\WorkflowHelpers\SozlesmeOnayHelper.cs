﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class SozlesmeOnayHelper
    {
        internal static void SaveLastCommend(long entityRefId, string comment)
        {
            Digiturk.Workflow.Digiflow.Entities.ContractsRequest RequestObject = WFRepository<ContractsRequest>.GetEntity(entityRefId);
            RequestObject.LastComment = comment;
            ActionHelpers.EntitySave(RequestObject);
        }
    }
}
