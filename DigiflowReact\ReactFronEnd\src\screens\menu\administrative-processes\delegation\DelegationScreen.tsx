import * as React from 'react'
import {useEffect, useState} from 'react'
import {WButton, WCheckbox, WDatePicker, WGrid, WSelect, WTextField} from '@wface/components'
import {useTranslation} from "react-i18next"
import HeaderComponent from "../../../../components/HeaderComponent";
import FooterComponent from "../../../../components/FooterComponent";
import axios from "axios";
import Api from "../../../../services/http-service";

function DelegationScreen() {
    const {t} = useTranslation()
    const [workflowList, setWorkflowList] = useState([])
    const [startDate, setStartDate] = useState(null)
    const [finishDate, setFinishDate] = useState(null)

    const GetWorkflowList = () => {
        Api.GetData("getworkflowlist").then(result => {
            setWorkflowList(result as any)
        })
    }

    useEffect(() => {
        GetWorkflowList()
    }, [])


    return (
        <WGrid style={{margin: 20,}}>
            <HeaderComponent/>

            <WGrid container alignItems='center' direction='row' style={{marginTop: 20, marginBottom: 20}}>
                <WGrid item md={6} xs={12}>
                    <WSelect
                        id=""
                        style={{marginTop: 20,}}
                        label={t('department')}
                        options={workflowList.map(repo => {
                            return {
                                label: repo.WfDefinitionId,
                                value: repo.WfDefinitionId
                            };
                        })}
                    />
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WSelect
                        id=""
                        style={{marginTop: 20,}}
                        label={t('division')}
                        options={workflowList.map(repo => {
                            return {
                                label: repo.WfDefinitionId,
                                value: repo.WfDefinitionId
                            };
                        })}
                    />
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WSelect
                        id=""
                        style={{marginTop: 20,}}
                        label={t('unit')}
                        options={workflowList.map(repo => {
                            return {
                                label: repo.WfDefinitionName,
                                value: repo.WfDefinitionName
                            };
                        })}
                    />
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WSelect
                        id=""
                        style={{marginTop: 20,}}
                        label={t('team')}
                        options={[
                            {label: 'Ağ Güvenliği Yönetimi Birimi', value: 'kurumsal_cozumler_gelistirme'},
                            {
                                label: 'Çağrı Merkezi Altyapı Yönetimi ve Yardım Masası Birimi',
                                value: 'onyuz_uygulamalari_gelistirme'
                            },
                        ]}
                    />
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WSelect
                        id=""
                        style={{marginTop: 20,}}
                        label={t('user')}
                        options={[
                            {label: 'Ali', value: 'kurumsal_cozumler_gelistirme'},
                            {label: 'Ayşe', value: 'onyuz_uygulamalari_gelistirme'},
                            {label: 'Fatma', value: 'onyuz_uygulamalari_gelistirme'},
                            {label: 'Ahmet', value: 'onyuz_uygulamalari_gelistirme'},
                            {label: 'Mehmet', value: 'onyuz_uygulamalari_gelistirme'},
                            {label: 'Zeynep', value: 'onyuz_uygulamalari_gelistirme'},
                            {label: 'Mustafa', value: 'onyuz_uygulamalari_gelistirme'},
                        ]}
                    />
                </WGrid>
            </WGrid>

            <WGrid container alignItems='center' direction='row' style={{marginTop: 20, marginBottom: 20}}>
                {workflowList.map(repo =>
                    <WGrid item md={2} xs={6} key={repo.id}>
                        <WCheckbox label={repo.WfDefinitionName} id="" style={{color: "black"}}/>
                    </WGrid>
                )}
            </WGrid>

            <WGrid container alignItems='center' direction='row' style={{marginTop: 20, marginBottom: 20}}>
                <WGrid item md={6} xs={12}>
                    <WDatePicker id="" label={t('starting_date')} value={startDate} onChange={value => setStartDate(value)}/>
                </WGrid>
                <WGrid item md={6} xs={12}>
                    <WDatePicker id="" label={t('end_date')} value={finishDate} onChange={value => setFinishDate(value)}/>
                </WGrid>
            </WGrid>

            <WTextField label={t('description')} fullWidth={true}/>
            <WButton id="" style={{marginTop: 25, color: "black", background: "#d3d3d3"}} variant="outlined"
                     fullWidth={true}>{t('create_request')}</WButton>

            <WGrid container alignItems='center' direction='column'
                   style={{marginTop: 50, marginBottom: 20, background: "#662e85"}}>
                <h3 style={{textAlign: "center", color: "white", fontSize: 16}}>{t('history')}</h3>
                <WGrid container alignItems='center' direction='column' style={{background: "white", height: 400}}>
                </WGrid>
            </WGrid>

            <FooterComponent/>
        </WGrid>
    )
}

export default DelegationScreen
