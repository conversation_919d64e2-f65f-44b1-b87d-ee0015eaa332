.dxpgControl_Office2010Silver
{
	color: #3c3c3c;
	font: 8pt Verdana;
	border: 1px solid #868b91;
	background-color: white;
}
.dxpgContainerCell_Office2010Silver
{
	vertical-align: top;
}
.dxpgMainTable_Office2010Silver
{
	color: #0a0a0a;
	font: 8pt Verdana;
	border-width: 0;
	border-collapse: separate;
	width: 100%;
}
.dxpgMainTable_Office2010Silver caption
{
	font-size: 8pt;
	font-weight: normal;
	padding: 3px 3px 5px;
	text-align: center;
	background: #e8ebee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.TitleBack.png")%>') repeat-x left top;
	color: Black;
	border-bottom: 1px solid #a5acb5;
}
.dxpgHeader_Office2010Silver
{
	border-width: 0;
	color: #3c3c3c;
	background: #e7ebef url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.HeaderBack.png")%>') repeat-x left top;
	cursor: pointer;
	white-space: nowrap;
}
.dxpgHeaderTable_Office2010Silver
{
	border-width: 1px;
	width: 100%;
}
.dxpgHeaderGroupButton_Office2010Silver
{
	padding-left: 4px;
	vertical-align: middle;
}
.dxpgHeaderText_Office2010Silver
{
	padding: 3px 6px;
}
.dxpgHeaderSort_Office2010Silver
{
	padding: 0 4px 0 0;
	vertical-align: middle;
}
.dxpgHeaderFilter_Office2010Silver
{
	padding 2px 2px 2px 0;
	vertical-align: middle;
}
.dxpgHeaderHover_Office2010Silver
{
	background: #f0f3f5 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.HeaderHoverBack.png")%>') repeat-x left top;
}
.dxpgArea_Office2010Silver, .dxpgArea_Office2010Silver table
{
	color: #3c3c3c;
	font: 8pt Verdana;
}
.dxpgArea_Office2010Silver
{
	border-width: 1px;
	border-color: #a5acb5;
	border-style: none;
	background-color: White;
}
.dxpgColumnArea_Office2010Silver
{
	border-bottom-style: solid;
}
.dxpgRowArea_Office2010Silver
{
}
.dxpgDataArea_Office2010Silver
{
}
.dxpgFilterArea_Office2010Silver
{
	background-color: #e9edf1;
	color: #3c3c3c;
	border-bottom-style: solid;
}
.dxpgEmptyArea_Office2010Silver
{
	cursor: default;
	padding: 6px;
}
.dxpgColumnFieldValue_Office2010Silver
{
    color: #3c3c3c;
	background: #e7ebef url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.HeaderBack.png")%>') repeat-x left top;
	border: 1px solid #a5acb5;
	border-left-style: solid;
	border-bottom-style: solid;
	border-right-style: none;
	border-top-style: none;
	padding: 4px 6px;
	font-weight: normal;
	text-align: left;
}
.dxpgColumnTotalFieldValue_Office2010Silver
{
	background: #dee0e4 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.TotalBack.png")%>') repeat-x left top;
	border-left-style: solid;
	border-bottom-style: solid;
	border-right-style: none;
	border-top-style: none;
}
.dxpgColumnGrandTotalFieldValue_Office2010Silver
{
	background: #d5d9dc url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.GrandTotalBack.png")%>') repeat-x left top;
	border-left-style: solid;
	border-bottom-style: solid;
	border-right-style: none;
	border-top-style: none;
}
.dxpgRowFieldValue_Office2010Silver
{
    color: #3c3c3c;
	background: #e7ebef url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.HeaderBack.png")%>') repeat-x left top;
	border: 1px solid #a5acb5;
	border-left-style: none;
	border-bottom-style: none;
	border-right-style: solid;
	border-top-style: solid;
	padding: 3px 6px;
	font-weight: normal;
	text-align: left;
}
.dxpgRowTotalFieldValue_Office2010Silver
{
	background: #dee0e4 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.TotalBack.png")%>') repeat-x left top;
	border-left-style: none;
	border-bottom-style: none;
	border-right-style: solid;
	border-top-style: solid;
}
.dxpgRowTreeFieldValue_Office2010Silver
{
	padding: 0px;
	font: 0pt;
}
.dxpgRowGrandTotalFieldValue_Office2010Silver
{
	background: #d5d9dc url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.GrandTotalBack.png")%>') repeat-x left top;
	border-left-style: none;
	border-bottom-style: none;
	border-right-style: solid;
	border-top-style: solid;
	padding-top: 5px;
	padding-bottom: 5px;
}
.dxpgCollapsedButton_Office2010Silver
{
	vertical-align: -2px;
	border: 0;
	margin-right: 5px;
}
.dxpgSortByColumnImage_Office2010Silver
{
	vertical-align: -1px;
	border: 0;
	margin-left: 5px;
}
.dxpgCell_Office2010Silver
{
	text-align: right;
	background-color: White;
	border-color: #d2d2d2;
	border-width: 1px;
	border-top-style: solid;
	border-left-style: solid;
	border-bottom-style: none;
	border-right-style: none;
	padding: 3px 4px;
	white-space: nowrap;
}
.dxpgKPICell_Office2010Silver
{
	text-align: center;
	vertical-align: middle;
}
.dxpgTotalCell_Office2010Silver
{
	background-color: #f7f7f8;
}
.dxpgGrandTotalCell_Office2010Silver
{
	background-color: #e5e7e7;
}
.dxpgFilterWindow_Office2010Silver
{
	color: Black;
	font: 8pt Verdana;
	border: 1px solid #a5acb5;
}
.dxpgFilterItemsArea_Office2010Silver
{
	color: Black;
	background-color: White;
}
.dxpgFilterItem_Office2010Silver
{
	font: 8pt Verdana;
    white-space: nowrap;
}
.dxpgFilterButton_Office2010Silver
{
	font: 8pt Verdana;
	padding: 2px 6px;
}
.dxpgFilterButtonPanel_Office2010Silver
{
	font: 8pt Verdana;
	color: Black;
	background-color: #e9edf1;
	border-top: 1px solid #a5acb5;
}
.dxpgLoadingDiv_Office2010Silver
{
	background-color: White;
	opacity: 0.01;
	filter: alpha(opacity=1);
}
.dxpgTopPager_Office2010Silver,
.dxpgBottomPager_Office2010Silver
{
    background: #e7eaee url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Silver.PivotGrid.PagerBack.png")%>') repeat-x left top;
    border-color: #bbc1c7;
    border-style: solid;
}
.dxpgTopPager_Office2010Silver
{
    border-width: 0 0 1px 0;
}
.dxpgBottomPager_Office2010Silver
{
	border-width: 1px 0 0 0;
}
.dxpgCustomizationFieldsHeader_Office2010Silver
{
	color: #3c3c3c;
	font: 8pt Verdana;
}
.dxpgCustomizationFieldsContent_Office2010Silver
{
	padding: 0px !important;
}
.dxpgLoadingPanel_Office2010Silver
{
	font: 8pt Verdana;
    color: #585e68;
    background: White;
	border: 1px solid #80858d;
}
.dxpgLoadingPanel_Office2010Silver td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxpgMenuItem_Office2010Silver
{
	font: 8pt Verdana;
}
.dxpgDataHeadersImage_Office2010Silver
{
	margin-right: 5px;
	vertical-align: -2px;
}
.dxpgPrefilterPanelContainer_Office2010Silver
{
	border-bottom-style: none;
	border-left-style: none;
	border-right-style: none;
}
.dxpgPrefilterPanel_Office2010Silver
{
	border: none;
	background: #e9edf1;
}
.dxpgPrefilterPanelLink_Office2010Silver
{
	color: #5a9ddb;
	text-decoration: underline;
}
.dxpgPrefilterPanelCheckBoxCell_Office2010Silver
{
	padding: 0 3px;
	padding-right: 7px;
}
.dxpgPrefilterPanelImageCell_Office2010Silver
{
	padding: 0 3px;
	padding-right: 1px;
	cursor: pointer;
}
.dxpgPrefilterPanelExpressionCell_Office2010Silver
{
	font-size: 8pt;
	padding: 5px 5px 8px 0;
	white-space: nowrap;
}
.dxpgPrefilterPanelClearButtonCell_Office2010Silver
{
	font-size: 8pt;
	padding: 5px 6px 8px;
}
.dxpgFilterBuilderMainArea_Office2010Silver
{
	background: white;
	padding: 6px 2px;
}
.dxpgFilterBuilderButtonArea_Office2010Silver
{
	background: #e9edf1;
	border-top: 1px solid #c9cdd2;
	padding: 6px;
}
.dxpgGroupSeparator_Office2010Silver
{
	vertical-align: middle;
}
.dxpgCustFieldsFilterAreaHeaders_Office2010Silver,
.dxpgCustFieldsRowAreaHeaders_Office2010Silver,
.dxpgCustFieldsColumnAreaHeaders_Office2010Silver,
.dxpgCustFieldsDataAreaHeaders_Office2010Silver,
.BottomPanelOnly1by4 .dxpgFLFRDiv_Office2010Silver,
.BottomPanelOnly1by4 .dxpgFLCDDiv_Office2010Silver
{
    width: 100%;
    height: 50%;
}
.StackedDefault .dxpgCustFieldsFieldList_Office2010Silver
{
    height: 33%;
}
.StackedDefault .dxpgFLFRDiv_Office2010Silver
{
    width: 50%;
    height: 66%;
    float: left;
}
.StackedDefault .dxpgFLCDDiv_Office2010Silver
{
    width: 50%;
    height: 66%;
    float: right;
}
.StackedDefault .dxpgCustFieldsFilterAreaHeaders_Office2010Silver .dxpgFLTextDiv_Office2010Silver,
.StackedDefault .dxpgCustFieldsColumnAreaHeaders_Office2010Silver .dxpgFLTextDiv_Office2010Silver
{
    height: 47px;
}
.TopPanelOnly .dxpgCustFieldsFieldList_Office2010Silver
{
    width: 100%;
    height: 100%;
}
.TopPanelOnly .dxpgFLDefereDiv_Office2010Silver .dxeBase_Office2010Silver,
.TopPanelOnly .dxpgFLDefereDiv_Office2010Silver .dxpgFLDefereDB_Office2010Silver,
.BottomPanelOnly1by4 .dxpgCustFieldsFieldList_Office2010Silver,
.TopPanelOnly .dxpgFLFRDiv_Office2010Silver,
.TopPanelOnly .dxpgFLCDDiv_Office2010Silver,
.BottomPanelOnly2by2 .dxpgCustFieldsFieldList_Office2010Silver,
.TopPanelOnly .dxpgFLTextDiv_Office2010Silver div
{
    display: none;
}
.TopPanelOnly .dxpgFLTextDiv_Office2010Silver
{
    height: 10px;
}
.TopPanelOnly .dxpgFLDefereDiv_Office2010Silver
{
    height: 16px;
}
.BottomPanelOnly2by2 .dxpgFLFRDiv_Office2010Silver,
.StackedSideBySide .dxpgCustFieldsFieldList_Office2010Silver
{
    width: 50%;
    height: 100%;
    float: left;
}
.BottomPanelOnly2by2 .dxpgFLCDDiv_Office2010Silver
{
    width: 50%;
    height: 100%;
    float: right;
}
.StackedSideBySide .dxpgFLFRDiv_Office2010Silver,
.StackedSideBySide .dxpgFLCDDiv_Office2010Silver
{
    width: 50%;
    height: 50%;
    float: right;
}
.dxpgCustFields_Office2010Silver
{
    display: block;
    position: relative;
}
.dxpgFLListDiv_Office2010Silver div
{
    border: 1px solid #bbc1c7;
    position: relative;
    display: block;
    height: 100%;
    padding: 1px;
    background: White;
    overflow: hidden;
}
.dxpgFLListDiv_Office2010Silver div div
{
    height: 100%;
    padding: 0px;
    border-color: #bbc1c7;
    border-style: solid;
    border-width: 0;
    overflow-y: auto;
}
.DragOver .dxpgFLListDiv_Office2010Silver div
{
    background: #ffd324;
}
.DragOver .dxpgFLListDiv_Office2010Silver div div
{
    background: White;
}
.dxpgFLListDiv_Office2010Silver
{
    padding: 0px 3px;
}
.dxpgFLButtonDiv_Office2010Silver .dxbButton_Office2010Silver div.dxb
{
    padding: 2px 8px 1px;
}
.dxpgFLButtonDiv_Office2010Silver .dxbButton_Office2010Silver div.dxbf
{
	border: 1px dotted Black;
	padding: 1px 7px 0px;
}
.dxpgFLTextDiv_Office2010Silver
{
    height: 28px;
}
.dxpgFLTextDiv_Office2010Silver div
{
   display: block;
   float: left;
   margin: -17px 0px 3px;
   left: 6px;
   top: 100%;
   position: relative;
}
.dxpgFLButtonDiv_Office2010Silver
{
    float: right;
    height: 28px;
    position:relative;
    z-index:1;
}
.dxpgFLDefereDiv_Office2010Silver
{
    height: 46px;
}
.dxpgCustFieldsDiv_Office2010Silver
{
     clear: both;
     padding: 0px 9px;
}
.dxpgFLButton_Office2010Silver
{
     margin: 7px 12px 0px 0px;
     width: 40px;
     height: 23px;
}
.dxpgFLDefereDiv_Office2010Silver .dxeBase_Office2010Silver
{
     float: left;
     display: block;
     border-collapse: separate;
     padding: 14px 0px 0px 0px;
     margin-left: 9px;
}
.dxpgFLDefereDB_Office2010Silver
{
    float: right;
    display: block;
    padding: 0px 12px 0px 0px;
    margin-top: 12px;
}
.dxpgFLDefereDiv_Office2010Silver .dxbButton_Office2010Silver div.dxb
{
    padding: 2px 14px;
}
.dxpgFLDefereDiv_Office2010Silver .dxbButton_Office2010Silver div.dxbf
{
    padding: 1px 13px;
    border: 1px dotted Black;
}
.dxpgFLListDiv_Office2010Silver table
{
 width:100%;
 table-layout:fixed;
 overflow:visible;
}
.dxpgFLListDiv_Office2010Silver table table td
{
     overflow:hidden;
}
div.dxpgFLTextImgDiv_Office2010Silver
{
    display:block;
    height:16px;
    width:16px;
    margin:-18px 0px 3px 0px;
    left:3px;
}