﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class SodexoHelper
    {
        internal static void InsertToContextForApproval(Digiturk.Workflow.Entities.FLogin assignedUser, Digiturk.Workflow.Common.WFContext currentWFContext)
        {
            if (assignedUser.LoginId != 0)
            {
                DataTable dtPersonelTitle = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.EmployeesRequestHelper.GetPersonelTitleFromPoldy(assignedUser.LoginId);

                if (dtPersonelTitle.Rows.Count > 0)
                {
                    currentWFContext.Parameters.AddOrChangeItem("title_adi", dtPersonelTitle.Rows[0]["title_adi"].ToString());
                    currentWFContext.Save();
                }
                dtPersonelTitle = null;
            }
        }
    }
}
