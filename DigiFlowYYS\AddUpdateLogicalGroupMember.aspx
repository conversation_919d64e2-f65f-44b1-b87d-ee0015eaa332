﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true"
    CodeFile="AddUpdateLogicalGroupMember.aspx.cs" Inherits="AddUpdateLogicalGroupMembers" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxDataView" TagPrefix="dx1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style type="text/css">
        .style1 {
            width: 100%;
        }

        .style3 {
            width: 153px;
        }

        .style4 {
            width: 138px;
        }

        .style5 {
            width: 11%;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" cellpadding="0" cellspacing="0" width="70%" align="center">
        <asp:Panel ID="WorkflowPanel" runat="server" Visible="true">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <table class="style1">
                        <tr>
                            <td class="style3">
                                <b>Akış Listesi</b>
                            </td>
                            <td>
                                <dx:ASPxComboBox ID="WorkFlowCombobox" runat="server" AutoPostBack="True" Width="350px"
                                    OnSelectedIndexChanged="WorkFlowCombobox_SelectedIndexChanged" ValueType="System.Int64">
                                </dx:ASPxComboBox>
                            </td>
                        </tr>
                    </table>
                    &nbsp;
                </td>
            </tr>
        </asp:Panel>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <table class="style1">
                    <tr>
                        <td class="style3">
                            <b>Mantıksal Grup İsmi </b>
                        </td>
                        <td>
                            <dx:ASPxComboBox ID="ASPxComboBoxLogicalGroup" runat="server" OnSelectedIndexChanged="ASPxComboBoxLogicalGroup_SelectedIndexChanged"
                                ValueType="System.Int64" Width="350px" AutoPostBack="True">
                            </dx:ASPxComboBox>
                        </td>
                    </tr>
                    <tr>
                        <td class="style3"></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="style3">
                            <b>Mantıksal Grup Açıklama </b>
                        </td>
                        <td>
                            <asp:TextBox Enabled="false" ID="txtDescription" runat="server" TextMode="MultiLine" Style="font-family: Microsoft Sans Serif, Sans-Serif;"
                                Height="70px" Width="350px"></asp:TextBox>
                        </td>
                    </tr>
                </table>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <table class="ui-accordion">
                    <tr>
                        <td class="style4">
                            <b>Kullanıcı Tipi</b>
                        </td>
                    </tr>
                    <tr>
                        <td class="style4">
                            <asp:RadioButtonList ID="rblMemberType" runat="server" AutoPostBack="True" OnSelectedIndexChanged="rblMemberType_SelectedIndexChanged"
                                Height="22px" Width="282px">
                            </asp:RadioButtonList>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <asp:Panel ID="UserSelectionPanel" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Kullanıcılar</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <asp:DropDownList ID="UsersASPxComboBox" runat="server" Width="250px">
                    </asp:DropDownList>
                    <%-- <asp:LinkButton ID="lnkAdd" runat="server" OnClick="LinkButtonFlogin_click">Ekle</asp:LinkButton>--%>
                </td>
            </tr>
            <br />
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxButton ID="lnkAdd" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                        CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                        Text="Ekle" OnClick="LinkButtonFlogin_click">
                    </dx:ASPxButton>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="AllUserSelectionPanel" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Tüm Kullanıcılar</b>
                </td>
            </tr>
            <br />
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxButton ID="ButtonAllUsers" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                        CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                        Text="Ekle" OnClick="LinkButtonAllUsers_click">
                    </dx:ASPxButton>
                    <%-- <asp:LinkButton ID="lnkAllUsers" runat="server" OnClick="LinkButtonAllUsers_click">Ekle</asp:LinkButton>--%>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="OutsourceUserPanel" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Ad - Soyad</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxTextBox ID="txtFullName" runat="server" Width="350px" MaxLength="1024">
                    </dx:ASPxTextBox>
                    <asp:RequiredFieldValidator ID="rqFullName" runat="server" ControlToValidate="txtFullName"
                        ErrorMessage="Lütfen isim bilgisi yazın." ValidationGroup="2"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>E - Posta</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxTextBox ID="txtEmail" runat="server" Width="350px" MaxLength="1024">
                    </dx:ASPxTextBox>
                    <asp:RequiredFieldValidator ID="rqEmail" runat="server" ControlToValidate="txtEmail"
                        ErrorMessage="Lütfen bir Email adresi yazın." ValidationGroup="2"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="revEmail" runat="server" ErrorMessage="Lütfen geçerli bir Email adresi yazın."
                        ValidationGroup="2" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                        ControlToValidate="txtEmail"></asp:RegularExpressionValidator>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxButton ID="ButtonOutsource" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                        CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                        Text="Ekle" OnClick="LinkButtonOutsource_click" CausesValidation="true" ValidationGroup="2">
                    </dx:ASPxButton>
                    <%-- <asp:LinkButton ID="LinkButtonOutsource" runat="server" OnClick="LinkButtonOutsource_click"
                        CausesValidation="true" ValidationGroup="2">Ekle</asp:LinkButton>--%>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="ParameterPanel" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>İçerik</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxTextBox ID="txtContent" runat="server" Width="350px" MaxLength="1024">
                    </dx:ASPxTextBox>
                    <asp:RequiredFieldValidator ID="rqContent" runat="server" ControlToValidate="txtContent"
                        ErrorMessage="Lütfen parametre giriniz" ValidationGroup="3"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Açıklama</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxTextBox ID="txtDescriptions" runat="server" Width="350px" MaxLength="1024">
                    </dx:ASPxTextBox>
                    <asp:RequiredFieldValidator ID="rqDescriptions" runat="server" ControlToValidate="txtDescriptions"
                        ErrorMessage="Lütfen açıklama giriniz" ValidationGroup="3"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxButton ID="LinkParameter" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                        CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                        Text="Ekle" OnClick="LinkParameter_click" CausesValidation="true" ValidationGroup="2">
                    </dx:ASPxButton>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="PnlAdMembers" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>AD Grupları</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <table>
                        <tr>
                                <td> Domain </td>
                                <td> : </td>
                                <td><asp:DropDownList ID="DrpDomain" runat="server" Width="250px" OnTextChanged="DrpDomain_TextChanged" AutoPostBack="true"> 
                                        <asp:ListItem Value="0">---Seçiniz---</asp:ListItem>
                                        <asp:ListItem Value="DIGITURK">DIGITURK</asp:ListItem>
                                        <asp:ListItem Value="DIGITURKCC">DIGITURKCC</asp:ListItem>
                                    </asp:DropDownList> </td>
                                <td>AD Grubu </td>
                                <td> : </td>
                                <td><asp:DropDownList ID="DrpGrupList" runat="server" Width="250px"> 
                                        <asp:ListItem Value="0">---Seçiniz---</asp:ListItem>    
                                        <%--<asp:ListItem Value="TECH CORP">TECH CORP</asp:ListItem>    
                                        <asp:ListItem Value="TECH ARCH Heads">TECH ARCH Heads</asp:ListItem>
	                                    <asp:ListItem Value="TECH CCSW">TECH CCSW</asp:ListItem>
	                                    <asp:ListItem Value="TECH CDCP Heads">TECH CDCP Heads</asp:ListItem>
	                                    <asp:ListItem Value="TECH CONSULTANTS">TECH CONSULTANTS</asp:ListItem>
	                                    <asp:ListItem Value="TECH CORP">TECH CORP</asp:ListItem>
	                                    <asp:ListItem Value="TECH CORP ERP">TECH CORP ERP</asp:ListItem>
	                                    <asp:ListItem Value="TECH CRM">TECH CRM</asp:ListItem>
	                                    <asp:ListItem Value="TECH CRM ANLTX">TECH CRM ANLTX</asp:ListItem>
	                                    <asp:ListItem Value="TECH CUSTOMER CARE">TECH CUSTOMER CARE</asp:ListItem>
	                                    <asp:ListItem Value="TECH DSS">TECH DSS</asp:ListItem>
	                                    <asp:ListItem Value="TECH ENT Heads">TECH ENT Heads</asp:ListItem>
	                                    <asp:ListItem Value="TECH ENTERPRISE SOLUTIONS">TECH ENTERPRISE SOLUTIONS</asp:ListItem>--%>
                                    </asp:DropDownList> 

                                </td>
                                <td> 
                                    <dx:ASPxButton ID="BtnAdSorgula" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                                    CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                                    Text="Sorgula" OnClick="BtnAdSorgula_Click">
                                </dx:ASPxButton>
                                </td>
                        </tr>
                    </table>
                </td>
            </tr>
             <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Ad Grubu Üyeleri</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxGridView ID="GrdAdMembersUsers" runat="server" AutoGenerateColumns="False"
                    Width="100%" KeyFieldName="UserName">
                    <Columns>
                        <dx:GridViewDataTextColumn Caption="Kullanıcı Adı" FieldName="UserName" ShowInCustomizationForm="True"
                            VisibleIndex="2">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Görünen Adı" FieldName="DisplayName" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Login Id" FieldName="Flogin" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Sonuç Kontrol" FieldName="CheckResult" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" AllowSelectSingleRowOnly="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterRowMenu="True" ShowFilterBar="Auto" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Mantıksal üyeyi bu gruptan silmek istediğinize emin misiniz?"></SettingsText>
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
                </td>
            </tr>
            <tr>
                <td align="center"> 
                        <dx:ASPxButton ID="BtnAktar" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                            CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                            Text="Aktar" OnClick="BtnAktar_Click">
                        </dx:ASPxButton>
                    <br />
                    <br />
                    <dx:ASPxButton ID="BtnJobs" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                            CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                            Text="Tüm AD Gruplarını Güncelle" OnClick="BtnJobs_Click">
                        </dx:ASPxButton>
                </td>
            </tr>
        </asp:Panel>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <b>Grup Üyeleri</b>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">&nbsp;
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <dx:ASPxGridView ID="gvLogicalMembers" runat="server" AutoGenerateColumns="False"
                    Width="100%" KeyFieldName="RequestId" OnRowCommand="gvLogicalMembers_RowCommand"
                    OnRowDeleting="gvLogicalMembers_RowDeleting" OnHtmlDataCellPrepared="gvLogicalMembers_HtmlDataCellPrepared">
                    <Columns>
                        <dx:GridViewDataTextColumn Caption="ID" FieldName="RequestId" VisibleIndex="1">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Parametre Adı" FieldName="Content" ShowInCustomizationForm="True"
                            VisibleIndex="1">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Açıklama" FieldName="Description" ShowInCustomizationForm="True"
                            VisibleIndex="2">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="LoginId" FieldName="LoginId" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Ad Soyad" FieldName="FullName" ShowInCustomizationForm="True"
                            VisibleIndex="4">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Email" FieldName="Email" ShowInCustomizationForm="True"
                            VisibleIndex="5">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Mantıksal Grup Tipi" FieldName="LogicalGroupMemberTypeId"
                            ShowInCustomizationForm="True" VisibleIndex="6">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <%--   <dx:GridViewCommandColumn Caption="Sil" ShowInCustomizationForm="True" VisibleIndex="7"
                            Name="Sil">
                            <DeleteButton Text="Sil" Visible="True">
                            </DeleteButton>
                            <ClearFilterButton Visible="True">
                            </ClearFilterButton>
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewCommandColumn>--%>
                        <dx:GridViewDataButtonEditColumn Caption="Sil" VisibleIndex="7" FieldName="RequestId">
                            <DataItemTemplate>
                                <dx:ASPxButton ID="DeleteButton" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                    <ClientSideEvents Click="function(s, e) {
	 e.processOnServer = confirm('Mantıksal grup üyesini silmek istediğinizden emin misiniz ?');}" />
                                </dx:ASPxButton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn>
                        <dx:GridViewDataTextColumn Caption="Sıra No" VisibleIndex="0" Width="40px">
                            <DataItemTemplate>
                                <%# Container.ItemIndex + 1 %>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" AllowSelectSingleRowOnly="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterRowMenu="True" ShowFilterBar="Auto" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Mantıksal üyeyi bu gruptan silmek istediğinize emin misiniz?"></SettingsText>
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
                <table align="center" class="style5">
                    <tr>
                        <td align="left">
                            <dx:ASPxButton ID="LinkButtonRemoveAll" runat="server" OnClick="LinkButtonRemoveAll_Click"
                                Visible="False" Text="Tümünü Sil" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                                CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                                Width="100px">
                            </dx:ASPxButton>
                        </td>
                    </tr>
                </table>
                &nbsp;
            </td>
        </tr>
        <tr>
            <td align="center" style="padding: 5px" valign="top" width="100%">&nbsp;
            </td>
        </tr>
    </table>
</asp:Content>