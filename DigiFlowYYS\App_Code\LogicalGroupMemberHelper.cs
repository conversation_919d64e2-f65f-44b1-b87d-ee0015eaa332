﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System.Collections.Generic;
using System.Data;

/// <summary>
/// Mantıksal grup üyeleri için kullanılan ve kullanılabilecek olan fonksiyonları barındırır.
/// </summary>
public class LogicalGroupMemberHelper
{
    /// <summary>
    /// Mantıksal Grupda Kullanıcı Var mı? Varsa True döner
    /// </summary>
    /// <param name="LogicalGroupId"></param>
    /// <param name="LoginId"></param>
    /// <returns></returns>
    public static bool CheckFLoginLogicalGroup(long LogicalGroupId,long LoginId)
    {
        string SQL = string.Format("Select * from DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS where LOGICAL_GROUP_ID = {0} and LOGIN_ID = {1}",LogicalGroupId,LoginId);
        DataTable dtb = Db.ExecuteDataTable(SQL,ConnectionType.DefaultConnection);
        return dtb.Rows.Count>0;
    }
    /// <summary>
    /// Secilen Logical Group Id'ye bagli olan tum Logical Group Memberlari listeler.
    /// </summary>
    /// <param name="logicalGroupId"></param>
    /// <returns></returns>
    public static List<LogicalGroupMember> GetByLogicalGroupId(long logicalGroupId)
    {
        List<LogicalGroupMember> allLgm = new List<LogicalGroupMember>();
        string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID";

        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
        dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

        if (dt != null && dt.Rows.Count > 0)
        {
            foreach (DataRow dr in dt.Rows)
            {
                LogicalGroupMember lgm = ConvertDataRow(dr);

                if (!allLgm.Contains(lgm))
                {
                    allLgm.Add(lgm);
                }
            }
        }
        return allLgm;
    }

    /// <summary>
    /// Secilen Logical Group Id'nin üyeleri arasında tüm kullanıcılar var mı kontrolü yapılır
    /// </summary>
    /// <param name="logicalGroupId"></param>
    /// <returns></returns>
    public static bool HasAllUsersInMembers(long logicalGroupId)
    {
        string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID and DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGIN_ID=-1";

        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
        dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

        if (dt != null && dt.Rows.Count > 0)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /// <summary>
    /// Secilen Logical Group Id'ye bagli olan tum kayitlari siler.
    /// </summary>
    /// <param name="logicalGroupMemberId"></param>
    /// <returns></returns>
    public static int DeleteLogicalGroupMembersByLogicalGroupId(long logicalGroupId)
    {
        string query = "DELETE FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID";
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }

    /// <summary>
    /// Sadece gelen primary key e ait olan mantıksal üyeyi siler
    /// </summary>
    /// <param name="logicalGroupMemberId"></param>
    /// <returns></returns>
    public static int DeleteLogicalGroupMember(long logicalGroupMemberId)
    {
        string query = "DELETE FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_MEMBER_ID=:LOGICAL_GROUP_MEMBER_ID";
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("LOGICAL_GROUP_MEMBER_ID", logicalGroupMemberId);
        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }

    public static int DeleteLogicalGroupMemberWithGroupOfLogin(long logicalGroupId,long LoginId)
    {
        string query = "DELETE FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID AND LOGIN_ID=:LOGIN_ID";
        OracleParameter[] p = new OracleParameter[2];
        p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
        p[1] = new OracleParameter("LOGIN_ID", LoginId);
        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }

    /// <summary>
    /// Verilen DataRow'u Logical Group Member nesnesine donusturur.
    /// </summary>
    /// <param name="dr"></param>
    /// <returns></returns>
    public static LogicalGroupMember ConvertDataRow(DataRow dr)
    {
        LogicalGroupMember lgm = new LogicalGroupMember();
        lgm.RequestId = ConvertionHelper.ConvertValue<long>(dr["LOGICAL_GROUP_MEMBER_ID"]);
        lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(dr["LOGICAL_GROUP_ID"]);
        lgm.Content = dr["CONTENT"].ToString();
        lgm.Description = dr["DESCRIPTION"].ToString();
        lgm.Email = dr["EMAIL"].ToString();
        lgm.FullName = dr["FULLNAME"].ToString();
        lgm.IsAdTransfer = ConvertionHelper.ConvertValue<long>(dr["IS_ADTRANSFER"]);
        lgm.LogicalGroupMemberTypeId = ConvertionHelper.ConvertValue<long>(dr["LOGICAL_GROUP_MEMBER_TYPE_ID"]);
        lgm.LoginId = ConvertionHelper.ConvertValue<long>(dr["LOGIN_ID"]);
        return lgm;
    }

    /// <summary>
    /// Yeni bir Logical Group Member kaydi yaratir.
    /// </summary>
    /// <param name="lgm"></param>
    /// <returns></returns>
    public static int AddNewLogicalGroupMember(LogicalGroupMember lgm)
    {
        string query = @"INSERT INTO DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS (
                   LOGICAL_GROUP_MEMBER_TYPE_ID, LOGICAL_GROUP_ID, CONTENT, DESCRIPTION, LOGIN_ID, FULLNAME, EMAIL,IS_ADTRANSFER, CREATED, CREATED_BY)
          VALUES (:LOGICAL_GROUP_MEMBER_TYPE_ID,:LOGICAL_GROUP_ID,:CONTENT,:DESCRIPTION,:LOGIN_ID,:FULLNAME,:EMAIL,:IS_ADTRANSFER,:CREATED,:CREATED_BY )";

        OracleParameter[] p = new OracleParameter[10];
        p[0] = new OracleParameter(":LOGICAL_GROUP_MEMBER_TYPE_ID", lgm.LogicalGroupMemberTypeId);
        p[1] = new OracleParameter(":LOGICAL_GROUP_ID", lgm.LogicalGroupId);
        p[2] = new OracleParameter(":CONTENT", lgm.Content);
        p[3] = new OracleParameter(":DESCRIPTION", lgm.Description);
        p[4] = new OracleParameter(":LOGIN_ID", lgm.LoginId);
        p[5] = new OracleParameter(":FULLNAME", lgm.FullName);
        p[6] = new OracleParameter(":EMAIL", lgm.Email);
        p[7] = new OracleParameter(":IS_ADTRANSFER", lgm.IsAdTransfer);
        p[8] = new OracleParameter(":CREATED", lgm.Created);
        p[9] = new OracleParameter(":CREATED_BY", lgm.CreatedBy);
        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }
}