﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="TopluOnay.aspx.cs" Inherits="TopluOnay" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v10.2" Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v10.2" Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="asp" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style type="text/css">
        .style1 {
            width: 96%;
        }

        .style2 {
            width: 141px;
        }

        .auto-style6 {
            width: 48%;
        }

        .auto-style8 {
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" cellpadding="0" cellspacing="0" width="65%" align="center">

        <tr>

            <td align="left" style="padding: 5px" valign="top" class="auto-style8">Kullanıcı Adı</td>

            <td align="left" valign="top" class="auto-style6">
                <asp:DropDownList ID="UsersASPxComboBox" runat="server">
                </asp:DropDownList>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="UsersASPxComboBox" ErrorMessage="Kullanıcı Seçiniz!" InitialValue="0" ValidationGroup="Kaydet">*</asp:RequiredFieldValidator>
                <asp:ValidatorCalloutExtender ID="RequiredFieldValidator1_ValidatorCalloutExtender" runat="server" TargetControlID="RequiredFieldValidator1">
                </asp:ValidatorCalloutExtender>
            </td>
        </tr>

        <tr>
            <td align="left" style="padding: 5px" valign="top" class="auto-style8">İş Akışları</td>
            <td class="auto-style6">
                <asp:DropDownList ID="drpIsAkislari" runat="server">
                </asp:DropDownList>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="drpIsAkislari" ErrorMessage="İş Akışı Seçiniz!" ValidationGroup="Kaydet" InitialValue="0">*</asp:RequiredFieldValidator>
                <asp:ValidatorCalloutExtender ID="RequiredFieldValidator2_ValidatorCalloutExtender" runat="server" TargetControlID="RequiredFieldValidator2">
                </asp:ValidatorCalloutExtender>
            </td>
        </tr>

        <tr>
            <td align="left" style="padding: 5px" valign="top" class="auto-style8">Alan Adı</td>
            <td class="auto-style6">
                <asp:TextBox ID="txtAlanAdi" runat="server" Enabled="False">AMOUNTTL</asp:TextBox>
            </td>
        </tr>

        <tr>
            <td align="left" style="padding: 5px" valign="top" class="auto-style8">Alan Değeri</td>
            <td class="auto-style6">
                <asp:TextBox ID="txtAlanDegeri" runat="server">0</asp:TextBox>
            </td>
        </tr>

        <tr>
            <td align="left" style="padding: 5px" valign="top" class="auto-style8">&nbsp;</td>
            <td class="auto-style6">
                <asp:Button ID="btnKaydet" runat="server" Text="Kaydet" OnClick="btnKaydet_Click" ValidationGroup="Kaydet" />
            </td>
        </tr>

        <tr>
            <td align="left" style="padding: 5px" valign="top" class="auto-style8" colspan="2">
                <dx:ASPxGridView ID="WorkFlowRulesGridView" runat="server" AutoGenerateColumns="False"
                    Width="100%" KeyFieldName="WF_WORKFLOW_DEF_ID" OnRowCommand="gvLogicalMembers_RowCommand">
                    <Columns>

                        <%--    <dx:GridViewCommandColumn ButtonType="Button" Caption="Sil" VisibleIndex="4">
                            <DeleteButton Visible="true"></DeleteButton>
                             <HeaderStyle HorizontalAlign="Center" />
                             <cellstyle horizontalalign="Center">
                             </cellstyle>
                        </dx:GridViewCommandColumn>--%>

                        <%-- <dx:GridViewDataButtonEditColumn Caption="Sil" VisibleIndex="8" FieldName="RequestId">
                            <DataItemTemplate>
                                <dx:ASPxButton ID="DeleteButton" AutoPostBack="true" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteRuleButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                </dx:ASPxButton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn>--%>

                        <dx:GridViewDataTextColumn Caption="Sıra No" VisibleIndex="0" Width="40px">
                            <DataItemTemplate>
                                <%# Container.ItemIndex + 1 %>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Kullanıcı Adı" FieldName="NAME_SURNAME" VisibleIndex="1">
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Login Id" FieldName="LOGIN_ID" VisibleIndex="2" Visible="False">
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="İş Akışı" FieldName="NAME" VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Workflow Name" FieldName="WF_WORKFLOW_DEF_ID" VisibleIndex="4" Visible="False">
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Alan Adı" FieldName="WF_WORKFLOW_ENTITY" ShowInCustomizationForm="True" VisibleIndex="5">
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Alan Değeri" FieldName="WF_WORKFLOW_ENTITY_VALUE" VisibleIndex="6">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>

                        <dx:GridViewDataButtonEditColumn Caption="Sil" VisibleIndex="7" FieldName="RequestId">
                            <DataItemTemplate>
                                <dx:ASPxButton ID="DeleteButton" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteRuleButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                    <ClientSideEvents Click="function(s, e) {e.processOnServer = confirm('Seçili satırı silmek istediğinize emin misiniz?');}" />
                                </dx:ASPxButton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn>

                        <dx:GridViewDataTextColumn Caption="ID" VisibleIndex="8" Visible="False">
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterBar="Auto" ShowFilterRowMenu="True" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Seçili satırı silmek istediğinize emin misiniz?" />
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
            </td>
        </tr>

        <tr>
            <td align="left" style="padding: 5px" valign="top" class="auto-style8" colspan="2">&nbsp;</td>
        </tr>
    </table>
</asp:Content>