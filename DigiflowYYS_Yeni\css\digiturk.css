﻿body
{
    font-family: Arial,sans-serif;
    font-size:12px;
	color: #000;
    margin:0;
    padding:0;
    text-align:left;
    unicode-bidi:embed;
}

.modalBackground {
	background-color:Gray;
	filter:alpha(opacity=70);
	opacity:0.7;
}

.modalPopup {
	background-color:#ffffdd;
	border-width:3px;
	border-style:solid;
	border-color:Gray;
	padding:3px;
	width:250px;
}

.updateProgress
{
    border-width: 1px;
    border-style: solid;
    background-color: #FFFFFF;
    position: absolute;
    width: 180px;
    height: 65px;
}

.PageTitle {
    font-family: Arial,sans-serif;
    font-size: 15px;
    font-weight: bold;
    color: #5c2d91;
}
  /*
#progressBackgroundFilter {
    position:fixed;
    top:0px;
    bottom:0px;
    left:0px;
    right:0px;
    overflow:hidden;
    padding:0;
    margin:0;
    background-color:#000;
    filter:alpha(opacity=50);
    opacity:0.5;
    z-index:1000;
}
*/
#processMessage {
    position:fixed;
    top:30%;
    left:43%;
    padding:10px;
    width:14%;
    z-index:1001;
    background-color:#fff;
    border:solid 1px #000;
}


/* Main menu container */
.custom-menu.dxmMenu_Office2010Silver {
    background-color: #5C2D91 !important; /* Purple background */
    border: 1px solid #000000 !important; /* Black border */
}

    /* Menu items */
    .custom-menu.dxmMenu_Office2010Silver .dxmMenuItem_Office2010Silver {
        color: white !important; /* White text */
        background-color: #5C2D91 !important; /* Match menu background */
        padding: 10px !important; /* Adjust padding */
        font-size: 14px !important; /* Larger font */
        border-radius: 4px !important; /* Rounded corners */
    }

        /* Menu items on hover */
        .custom-menu.dxmMenu_Office2010Silver .dxmMenuItem_Office2010Silver.dxmMenuItemHover_Office2010Silver {
            background-color: #4b2173 !important; /* Darker purple on hover */
        }

    /* Submenu container */
    .custom-menu.dxmMenu_Office2010Silver .dxmSubMenu_Office2010Silver {
        background-color: white !important; /* White background */
        border: 1px solid #5C2D91 !important; /* Purple border */
    }

        /* Submenu items */
        .custom-menu.dxmMenu_Office2010Silver .dxmSubMenu_Office2010Silver .dxmMenuItem_Office2010Silver {
            color: #5C2D91 !important; /* Purple text */
            background-color: white !important; /* White background */
            padding: 8px !important; /* Adjust padding */
        }

            /* Submenu items on hover */
            .custom-menu.dxmMenu_Office2010Silver .dxmSubMenu_Office2010Silver .dxmMenuItem_Office2010Silver.dxmMenuItemHover_Office2010Silver {
                background-color: #e0d8ea !important; /* Light purple on hover */
            }

.dxmSubMenu_Office2010Silver tr td:first-child, .dxmSubMenu_Office2010Silver tr td:nth-child(2) {
    display: none !important;
}
.dxmSubMenu_Office2010Silver tr td:last-child a {
    padding-left: 10px;
}