import * as React from 'react'
import { useEffect, useState } from 'react'
import {WButton, WCard, WDatePicker, WGrid, WSelect, WTabContainer, WTabPage, WTextField} from '@wface/components'
import { useTranslation } from "react-i18next"
import axios from "axios";
import Api from "../services/http-service";


function CommandListComponent() {
    const { t } = useTranslation()
    const [date, setDate] = useState(null)

    return(
        <WGrid container alignItems='center' direction="column" style={{marginTop: 20, marginBottom: 0,}}>
            <WGrid style={{background: "#662e85", width: 1000}}>
                <h3 style={{textAlign: "center", color: "white", fontSize: 16,}}>{t('command_list')}</h3>
                <WCard>
                    <WTabContainer defaultValue={0} centered variant="fullWidth" indicatorColor="secondary">
                        <WTabPage id="" label={t("accept_or_reject")}>
                            <WTextField label={t('description')} fullWidth={true}/>
                            <WGrid container alignItems='center' direction="row" style={{marginTop: 20, marginBottom: 20}}>
                                <WGrid item md={2} xs={12} />
                                <WGrid item md={3} xs={12}>
                                    <WButton id="" variant="outlined" fullWidth={true} style={{color: "black", background: "#d3d3d3"}}>{t("accept")}</WButton>
                                </WGrid>
                                <WGrid item md={2} xs={12} />
                                <WGrid item md={3} xs={12}>
                                    <WButton id="" variant="outlined" fullWidth={true} style={{color: "black", background: "#d3d3d3"}}>{t("reject")}</WButton>
                                </WGrid>
                                <WGrid item md={2} xs={12} />
                            </WGrid>
                        </WTabPage>

                        <WTabPage id="" label={t("forward")}>
                            <WTextField label={t('description')} fullWidth={true}/>
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('department')}
                                value={"teknoloji_grup_baskanligi"}
                                isDisabled={true}
                                options={[
                                    {label: 'Teknolojiden Sorumlu Grup Başkanlığı', value: 'teknoloji_grup_baskanligi'},
                                ]}
                            />
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('division')}
                                value={"kurumsal_cozumler_gelistirme"}
                                isDisabled={true}
                                options={[
                                    {label: 'Kurumsal Çözümler Geliştirme Direktörlüğü', value: 'kurumsal_cozumler_gelistirme'},
                                ]}
                            />
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('unit')}
                                value={"kurumsal_cozumler_mudurlugu"}
                                isDisabled={true}
                                options={[
                                    {label: 'Kurumsal Çözümler Müdürlüğü', value: "kurumsal_cozumler_mudurlugu"},
                                ]}
                            />
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('team')}
                                value={"kurumsal_cozumler_birimi"}
                                isDisabled={true}
                                options={[
                                    {label: 'Kurumsal Çözümler Birimi', value: 'kurumsal_cozumler_birimi'},
                                ]}
                            />
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('user')}
                                options={[
                                    {label: 'Digiflow Admin', value: 'admin'},
                                    {label: 'Tarık Gökhan Kızılırnak', value: 'tarik'},
                                    {label: 'Zülfiye Tezemir', value: 'zulfiye'},
                                ]}
                            />
                            <WButton id="" variant="outlined" fullWidth={true} style={{marginTop: 20, color: "black", background: "#d3d3d3"}}>{t("forward")}</WButton>
                        </WTabPage>

                        <WTabPage id="" label={t("post_comment")}>
                            <WTextField label={t('description')} fullWidth={true}/>
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('department')}
                                options={[
                                    {label: 'Ali', value: 'kurumsal_cozumler_gelistirme'},
                                    {label: 'Ayşe', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Fatma', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Ahmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mehmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Zeynep', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mustafa', value: 'onyuz_uygulamalari_gelistirme'},
                                ]}
                            />
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('division')}
                                options={[
                                    {label: 'Ali', value: 'kurumsal_cozumler_gelistirme'},
                                    {label: 'Ayşe', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Fatma', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Ahmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mehmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Zeynep', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mustafa', value: 'onyuz_uygulamalari_gelistirme'},
                                ]}
                            />
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('unit')}
                                options={[
                                    {label: 'Ali', value: 'kurumsal_cozumler_gelistirme'},
                                    {label: 'Ayşe', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Fatma', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Ahmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mehmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Zeynep', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mustafa', value: 'onyuz_uygulamalari_gelistirme'},
                                ]}
                            />
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('team')}
                                options={[
                                    {label: 'Ali', value: 'kurumsal_cozumler_gelistirme'},
                                    {label: 'Ayşe', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Fatma', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Ahmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mehmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Zeynep', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mustafa', value: 'onyuz_uygulamalari_gelistirme'},
                                ]}
                            />
                            <WSelect
                                id=""
                                style={{marginTop: 10,}}
                                label={t('user')}
                                options={[
                                    {label: 'Ali', value: 'kurumsal_cozumler_gelistirme'},
                                    {label: 'Ayşe', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Fatma', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Ahmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mehmet', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Zeynep', value: 'onyuz_uygulamalari_gelistirme'},
                                    {label: 'Mustafa', value: 'onyuz_uygulamalari_gelistirme'},
                                ]}
                            />
                            <WButton id="" variant="outlined" fullWidth={true} style={{marginTop: 20, color: "black", background: "#d3d3d3"}}>{t("post_comment")}</WButton>
                        </WTabPage>

                        <WTabPage id="" label={t("suspend_or_resume")}>
                            <WTextField label={t('description')} fullWidth={true}/>
                            <WDatePicker id="" label={t('date')} style={{marginTop: 10,}} value={date} onChange={value => setDate(value)}/>
                            <WGrid container alignItems='center' direction="row" style={{marginTop: 20, marginBottom: 20}}>
                                <WGrid item md={2} xs={12} />
                                <WGrid item md={3} xs={12}>
                                    <WButton id="" variant="outlined" fullWidth={true} style={{color: "black", background: "#d3d3d3"}}>{t("suspend")}</WButton>
                                </WGrid>
                                <WGrid item md={2} xs={12} />
                                <WGrid item md={3} xs={12}>
                                    <WButton id="" variant="outlined" fullWidth={true} style={{color: "black", background: "#d3d3d3"}}>{t("resume")}</WButton>
                                </WGrid>
                                <WGrid item md={2} xs={12} />
                            </WGrid>
                        </WTabPage>

                        <WTabPage id="" label={t("cancel")}>
                            <WTextField label={t('description')} fullWidth={true}/>
                            <WGrid container alignItems='center' direction="row" style={{marginTop: 20, marginBottom: 20}}>
                                <WGrid item md={4} xs={12} />
                                <WGrid item md={4} xs={12}>
                                    <WButton id="" variant="outlined" fullWidth={true} style={{color: "black", background: "#d3d3d3"}}>{t("cancel")}</WButton>
                                </WGrid>
                                <WGrid item md={4} xs={12} />
                            </WGrid>
                        </WTabPage>

                        <WTabPage id="" label={t("add_comment")}>
                            <WTextField label={t('description')} fullWidth={true}/>
                            <WGrid container alignItems='center' direction="row" style={{marginTop: 20, marginBottom: 20}}>
                                <WGrid item md={4} xs={12} />
                                <WGrid item md={4} xs={12}>
                                    <WButton id="" variant="outlined" fullWidth={true} style={{color: "black", background: "#d3d3d3"}}>{t("add_comment")}</WButton>
                                </WGrid>
                                <WGrid item md={4} xs={12} />
                            </WGrid>
                        </WTabPage>
                    </WTabContainer>
                </WCard>
            </WGrid>
        </WGrid>
    )
}

export default CommandListComponent
