/* ----------------- Main ----------------- */
/* Loading panel */
.dxheLoadingDiv_RedWine
{
    background: white;
    opacity: 0.85;
    filter: alpha(opacity=85);
    cursor: wait;
}
.dxheLoadingPanel_RedWine
{
	font: 9pt Tahoma;
	color: #767676;
}
.dxheLoadingPanel_RedWine td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 12px;
}

.dxheControl_RedWine
{
    border: Solid 1px #8A0A37;
}

/* Area */
.dxheErrorFrame_RedWine
{
    font-size: 9pt;
    font-family: Tahoma, Verdana, Arial;
    color: #D00707;
    background-color: #FBC7C7;
    border-bottom: solid 1px #DEC0C0;
}
.dxheErrorFrame_RedWine .dxhe {
    padding: 5px;
}
.dxheErrorFrame_RedWine .dxhe td {
    padding: 0px;
}
.dxheErrorFrame_RedWine .dxheErrorFrameCloseButton_RedWine {
    cursor: pointer;
}
.dxheContentArea_RedWine
{
    padding: 4px;
    background-color: #B55476;
}
.dxheViewArea_RedWine
{
    border: Solid 1px #A74768;
}
.dxheHtmlViewEdit_RedWine,
.dxheDesignViewArea_RedWine,
.dxhePreviewArea_RedWine
{
    margin: 0px;
    background-color: #FFFFFF;
    background-image: none;
    text-align: left;
    font-size: smaller;
    font: normal 12px Arial;
}
td.dxheHtmlViewEdit_RedWine, body.dxheDesignViewArea_RedWine, body.dxhePreviewArea_RedWine
{
    padding: 0px;
    padding-left: 4px;
    padding-bottom: 0px;
}
/* Element appearance in DesignView */
body.dxheDesignViewArea_RedWine table.dxEmptyBorderTable,
body.dxheDesignViewArea_RedWine table.dxEmptyBorderTable td  {
    border:1px dotted gray;
}
@media print
{
	body.dxheDesignViewArea_RedWine table.dxEmptyBorderTable,
	body.dxheDesignViewArea_RedWine table.dxEmptyBorderTable td  {
		border:0px;
	}
}
/* Status Bar */
.dxheStatusBar_RedWine
{
}
.dxheStatusBarTab_RedWine
{
}
.dxheStatusBar_RedWine .dxheStatusBarActiveTab_RedWine
{
    background-color: White;
}
.dxHtmlEditor_heSizeGrip_RedWine
{
    cursor: se-resize;
}
.dxheSizeGripContainer_RedWine
{
    float: left;
    height: 0;
    width: 100%;
    text-align: right;
    font-size: 0;
    margin-top: -16px;
}
/* ----------------- Dialog Forms ----------------- */
#dxInsertTableForm .buttonsCell,
#dxInsertLinkForm .buttonsCell,
#dxInsertImageForm .buttonsCell,
#dxPasteFromWordForm .buttonsCell,
#dxTableColumnPropertiesForm .buttonsCell,
.dxheCustomDialog_RedWine .dxhecd-Buttons
{
	background-color: #EED8E3;
	border-top: 1px #CB9FB4 solid;

    padding: 11px;
}
.dxheCustomDialog_RedWine .dxhecd-Buttons
{
	text-align: right;
}
#dxInsertTableForm .captionIndent,
#dxInsertLinkForm .captionIndent,
#dxInsertImageForm .captionIndent,
#dxTableColumnPropertiesForm .captionIndent
{
	overflow: hidden;
	height: 5px;
}
#dxInsertTableForm .contentCell,
#dxInsertLinkForm .contentCell,
#dxInsertImageForm .contentCell,
#dxPasteFromWordForm .contentCell,
#dxTableColumnPropertiesForm .contentCell,
.dxheCustomDialog_RedWine .dxhecd-Content
{
	padding: 11px;
}
#dxInsertLinkForm .typeRadionButtonListCell,
#dxInsertImageForm .typeRadionButtonListCell
{
	padding-bottom: 12px;
}
#dxInsertTableForm .separatorCell,
#dxInsertLinkForm .separatorCell,
#dxInsertImageForm .separatorCell,
#dxTableColumnPropertiesForm .separatorCell
{
	height: 7px;
}
#dxInsertTableForm .buttons,
#dxInsertLinkForm .buttons,
#dxInsertImageForm .buttons,
#dxTableColumnPropertiesForm .buttons
{
    padding-top: 3px;
}
#dxInsertTableForm .cancelButton,
#dxInsertLinkForm .cancelButton,
#dxInsertImageForm .cancelButton,
#dxPasteFromWordForm .cancelButton,
#dxTableColumnPropertiesForm .cancelButton
{
    padding-left: 10px;
}
.dxheCustomDialog_RedWine .dxhecd-Cancel
{
	margin-left: 10px;
}
#dxInsertTableForm .captionCell,
#dxInsertTableForm .rowsCaptionCell,
#dxInsertLinkForm .captionCell,
#dxTableColumnPropertiesForm .captionCell,
#dxTableColumnPropertiesForm .rowsCaptionCell
{
    padding-right: 9px;
    padding-top: 3px;
    vertical-align: top;
    white-space: nowrap;
}
#dxInsertTableForm .inputCell,
#dxInsertTableForm .rowsInputCell,
#dxInsertLinkForm .inputCell,
#dxTableColumnPropertiesForm .inputCell,
#dxTableColumnPropertiesForm .rowsInputCell
{
    vertical-align: middle;
}

/* Insert Link Form */
#dxInsertLinkForm .displayPropertiesCell
{
    font-weight: bold;
    padding-top: 10px;
    padding-bottom: 10px;
}
#dxInsertLinkForm .targetCheckBoxCell
{
    padding-top:10px;
}
/* Insert Image Form */
#dxInsertImageForm .fieldSeparator
{
    height: 9px;
}
#dxInsertImageForm .imagePreview
{
    padding: 10px 0px;
    padding-top: 5px;
}
#dxInsertImageForm .fromTheWeb
{
    width: 100%;
}
#dxInsertImageForm .imagePreviewCell
{
    color: #878787;
    border: dashed 1px #cac8c8;
    text-align:center;
    width: 100%;
    height: 170px;
}
#dxInsertImageForm .imagePropertiesCell
{
    padding-left: 20px;
    vertical-align:top;
}
#dxInsertImageForm .moreOptionsCell
{
	padding-top: 11px;
}
#dxInsertImageForm .radioButtonTable
{
	width: 317px;
}
#dxInsertImageForm .saveToServerCheckBoxCell
{
    padding-top:0px;
    padding-bottom: 13px;
}
/* Image Properties Form */
#dxImagePropertiesForm .captionCell
{
    padding-right: 2px;
    white-space: nowrap;
}
#dxImagePropertiesForm .constrainProportionsCell
{
    padding-left: 4px;
    padding-top: 2px;
}
#dxImagePropertiesForm .imageSizeEditorsCell
{
    padding-top: 7px;
    padding-bottom: 10px;
}
#dxImagePropertiesForm .pixelSizeCell
{
    padding-left: 7px;
}
#dxImagePropertiesForm .hSeparator
{
    width: 25px;
    overflow: hidden;
}
#dxImagePropertiesForm .thumbnailFileNameArea
{
    padding-top: 8px;
}
/* IE, FireFox, WebKit*/
#dxImagePropertiesForm .ckbWrapTextCell div
{
	margin-left: -4px;
	padding-top: 2px;
}
/* Opera hack */
@media not all and (-webkit-min-device-pixel-ratio) {
	#dxImagePropertiesForm .ckbWrapTextCell div
	{
		margin-left: 0px;
	}
}

/* PasteFromWord Form */
#dxPasteFromWordForm .pasteContainer
{
	width: 450px;
	height: 300px;
	border: none;
	background-color: white;
}
#dxPasteFromWordForm .pasteContainerCell
{
	padding:0px;
	padding-top: 10px;
}
#dxPasteFromWordForm .pasteContainerCell td
{
	border: Solid 1px #BC758E;
}
#dxPasteFromWordForm .checkBoxCell
{
	padding-top: 10px;
}
#dxPasteFromWordForm .contentCell
{
	padding-left: 20px;
	padding-right: 20px;
}

/* Insert table */
#dxInsertTableForm .propFieldSeparator, #dxTableColumnPropertiesForm .propFieldSeparator
{
    width: 20px;
}
#dxInsertTableForm .propGroupSeparator, #dxTableColumnPropertiesForm .propGroupSeparator
{
    height: 11px;
}
#dxInsertTableForm .propGroupCell, #dxInsertTableForm .accessibilityPropGroupCell,
#dxTableColumnPropertiesForm .propGroupCell, #dxTableColumnPropertiesForm .accessibilityPropGroupCell
{
    font-weight: bold;
    padding-bottom: 7px;
}
#dxInsertTableForm .accessibilityPropGroupCell,
#dxTableColumnPropertiesForm accessibilityPropGroupCell
{
    padding-left: 10px;
}
#dxInsertTableForm .propGroupContentCell,
#dxTableColumnPropertiesForm .propGroupContentCell
{
    padding-left: 20px;
}
#dxInsertTableForm .sizeTypeCell,
#dxTableColumnPropertiesForm .sizeTypeCell
{
    padding-left: 3px;
}
#dxInsertTableForm .rowsInputCell,
#dxTableColumnPropertiesForm .rowsInputCell
{
    padding-left: 9px;
}
#dxInsertTableForm .rowsCaptionCell,
#dxTableColumnPropertiesForm .rowsCaptionCell
{
    padding-right: 0px;
}
#dxInsertTableForm .rowsSeparator,
#dxTableColumnPropertiesForm .rowsSeparator
{
    height: 18px;
}
#dxInsertTableForm .rowsHorizontalSeparator,
#dxTableColumnPropertiesForm .rowsHorizontalSeparator
{
    width: 50px;
}
/*----------------- Toolbars -----------------*/
.dxtbSpacing_RedWine
{
	height: 1px;
}
.dxtbControl_RedWine
{
	font: 9pt Tahoma;
	color: black;
	background-color: #A42652;
    border-bottom: Solid 1px #8A0A37;
}

.dxtbControl_RedWine td.dxmtb.dxmMenu_RedWine
{
	border-width: 0px;
}
.dxtbComboBoxMenuItem_RedWine
{
    padding: 2px 0px;
    padding-right: 1px;
}

/* Toolbars Lightweight Mode */
.dxtbControl_RedWine .dxmLite_RedWine .dxm-main.dxmtb {
    border-width: 0px;
}
.dxheControl_RedWine .dxtbControl_RedWine .dxmLite_RedWine .dxm-horizontal.dxmtb .dxm-image-l .dxm-dropDownMode .dxm-popOut,
.dxheControl_RedWine .dxtbControl_RedWine .dxmLite_RedWine .dxm-horizontal.dxmtb .dxm-image-l .dxm-popOut {
    padding: 8px 5px 8px 5px;
}
.dxmLite_RedWine .dxm-horizontal.dxmtb .dxm-image-l .dxtb-comboBoxMenuItem {
    border-width: 0px;
    padding: 2px 0px;
    padding-right: 1px;
}
.dxmLite_RedWine .dxhetipControl_RedWine img
{
	vertical-align: top;
}
.dxtbControl_RedWine .dxmLite_RedWine .dxm-horizontal .dxm-item .dxm-content .dxm-image {
	margin: 0px;
}

/* ToolbarColorButton */
.dxtcbControl_RedWine { }
.dxtcbColorDiv_RedWine
{
	width: 16px;
	height: 4px;
	font-size:0pt;
	background-color: Red;
}

/*----------------- RoundPanel -----------------*/
.dxheRP.dxrpControl_RedWine .dxrpcontent
{
    padding: 9px 8px;
}

/*----------------- UploadControl -----------------*/
.dxheucControl_RedWine,
.dxheucEditArea_RedWine
{
    font: 9pt Tahoma, Verdana, Arial;
}
.dxheucErrorCell_RedWine
{
    padding-left: 4px;
    font: 10px Tahoma, Verdana, Arial;
    color: Red;
    text-align: left;
}