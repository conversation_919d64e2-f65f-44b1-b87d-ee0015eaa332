﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WindowsFormsApplication6
{
    public static class QueryClass
    {
        public static string WF_DEF_QUERY = @"Select * from Framework.f_wf_workflow_def where WF_WORKFLOW_DEF_ID = :Deger";
        public static string WF_DEF_MAX_QUERY = @"Select max(WF_WORKFLOW_DEF_ID) + 1 from Framework.f_wf_workflow_def";
        public static string WF_STATE_QUERY = @"Select  FRAMEWORK.F_WF_STATE_DEF.* from  FRAMEWORK.F_WF_STATE_DEF Inner Join FRAMEWORK.F_WF_WORKFLOW_DEF On FRAMEWORK.F_WF_STATE_DEF.WF_WORKFLOW_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID   where FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID= :Deger   Order By FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER,  FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID Desc";
        public static string WF_STATE_QUERY_MAX = @"select max(WF_STATE_DEF_ID) +1 from FRAMEWORK.F_WF_STATE_DEF";
        public static string WF_ACT_QUERY = @"SELECT FRAMEWORK.F_WF_ACTION_DEF.* FROM FRAMEWORK.F_WF_ACTION_DEF INNER JOIN FRAMEWORK.F_WF_STATE_DEF ON FRAMEWORK.F_WF_ACTION_DEF.WF_STATE_DEF_ID = FRAMEWORK.F_WF_STATE_DEF.WF_STATE_DEF_ID INNER JOIN FRAMEWORK.F_WF_WORKFLOW_DEF ON FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID = FRAMEWORK.F_WF_STATE_DEF.WF_WORKFLOW_DEF_ID WHERE FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID = :Deger ORDER BY FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER, FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID DESC";
        public static string WF_ACT_QUERY_MAX = @"Select max(WF_ACTION_DEF_ID) + 1 from FRAMEWORK.F_WF_ACTION_DEF";

        public static string WF_ACT_TASK_QUERY = @"Select  FRAMEWORK.F_WF_ACTION_TASK_DEF.* from  FRAMEWORK.F_WF_ACTION_TASK_DEF  Inner Join FRAMEWORK.F_WF_ACTION_DEF On FRAMEWORK.F_WF_ACTION_DEF.WF_ACTION_DEF_ID =FRAMEWORK.F_WF_ACTION_TASK_DEF.WF_ACTION_TASK_DEF_ID Inner Join FRAMEWORK.F_WF_STATE_DEF On FRAMEWORK.F_WF_STATE_DEF.WF_STATE_DEF_ID =FRAMEWORK.F_WF_ACTION_DEF.WF_STATE_DEF_ID Inner Join FRAMEWORK.F_WF_WORKFLOW_DEF  On FRAMEWORK.F_WF_STATE_DEF.WF_WORKFLOW_DEF_ID =FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID  where FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID= :Deger order by F_WF_ACTION_TASK_DEF.WF_ACTION_TASK_DEF_ID";
        public static string WF_ACT_TASK_QUERY_MAX = @"select max(WF_ACTION_TASK_DEF_ID)+1 from  F_WF_ACTION_TASK_DEF";

        public static string WF_ACT_CLASS_QUERY = @"SELECT FRAMEWORK.F_WF_ACTION_CLASS_DEF.* FROM FRAMEWORK.F_WF_ACTION_CLASS_DEF INNER JOIN FRAMEWORK.F_WF_ACTION_DEF ON FRAMEWORK.F_WF_ACTION_DEF.WF_ACTION_DEF_ID = FRAMEWORK.F_WF_ACTION_CLASS_DEF.WF_ACTION_CLASS_DEF_ID INNER JOIN FRAMEWORK.F_WF_STATE_DEF ON FRAMEWORK.F_WF_STATE_DEF.WF_STATE_DEF_ID = FRAMEWORK.F_WF_ACTION_DEF.WF_STATE_DEF_ID INNER JOIN FRAMEWORK.F_WF_WORKFLOW_DEF ON FRAMEWORK.F_WF_STATE_DEF.WF_WORKFLOW_DEF_ID = FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID WHERE FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID = :Deger ORDER BY FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER, FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID DESC";
        public static string WF_ACT_CLASS_QUERY_MAX = @"select max(WF_ACTION_CLASS_DEF_ID) + 1 from FRAMEWORK.F_WF_ACTION_CLASS_DEF";

        public static string WF_TRANS_DEF_QUERY = @"SELECT FRAMEWORK.F_WF_TRANSITION_DEF.*  FROM FRAMEWORK.F_WF_TRANSITION_DEF  INNER JOIN FRAMEWORK.f_wf_state_Def Fstate ON Fstate.WF_STATE_DEF_ID = FRAMEWORK.F_WF_TRANSITION_DEF.FROM_STATE_DEF_ID INNER JOIN FRAMEWORK.f_wf_state_Def Tstate ON Tstate.WF_STATE_DEF_ID =FRAMEWORK. F_WF_TRANSITION_DEF.TO_STATE_DEF_ID INNER JOIN FRAMEWORK.f_wf_workflow_def FWF ON FWF.WF_WORKFLOW_DEF_ID = FSTATE.WF_WORKFLOW_DEF_ID INNER JOIN FRAMEWORK.f_wf_workflow_def TWF ON TWF.WF_WORKFLOW_DEF_ID = Tstate.WF_WORKFLOW_DEF_ID WHERE FWF.WF_WORKFLOW_DEF_ID = :Deger OR TWF.WF_WORKFLOW_DEF_ID = :Deger";
        public static string WF_TRANS_DEF_QUERY_STATE = @"SELECT  FSTATE.NAME as S, tstate.name as ToS,FRAMEWORK.F_WF_TRANSITION_DEF.* FROM FRAMEWORK.F_WF_TRANSITION_DEF  INNER JOIN FRAMEWORK.f_wf_state_Def Fstate ON Fstate.WF_STATE_DEF_ID = FRAMEWORK.F_WF_TRANSITION_DEF.FROM_STATE_DEF_ID INNER JOIN FRAMEWORK.f_wf_state_Def Tstate ON Tstate.WF_STATE_DEF_ID =FRAMEWORK. F_WF_TRANSITION_DEF.TO_STATE_DEF_ID INNER JOIN FRAMEWORK.f_wf_workflow_def FWF ON FWF.WF_WORKFLOW_DEF_ID = FSTATE.WF_WORKFLOW_DEF_ID INNER JOIN FRAMEWORK.f_wf_workflow_def TWF ON TWF.WF_WORKFLOW_DEF_ID = Tstate.WF_WORKFLOW_DEF_ID WHERE (FWF.WF_WORKFLOW_DEF_ID = :Deger1 OR TWF.WF_WORKFLOW_DEF_ID = :Deger1) and FRAMEWORK.F_WF_TRANSITION_DEF.FROM_STATE_DEF_ID = :Deger2 order by ORDERNO";

        public static string WF_TRANS_DEF_RULE_QUERY_ = @"SELECT  FSTATE.NAME as fromS,tstate.name as ToS,FRAMEWORK.F_WF_TRANSITION_DEF.* FROM FRAMEWORK.F_WF_TRANSITION_DEF  INNER JOIN FRAMEWORK.f_wf_state_Def Fstate ON Fstate.WF_STATE_DEF_ID = FRAMEWORK.F_WF_TRANSITION_DEF.FROM_STATE_DEF_ID INNER JOIN FRAMEWORK.f_wf_state_Def Tstate ON Tstate.WF_STATE_DEF_ID =FRAMEWORK. F_WF_TRANSITION_DEF.TO_STATE_DEF_ID INNER JOIN FRAMEWORK.f_wf_workflow_def FWF ON FWF.WF_WORKFLOW_DEF_ID = FSTATE.WF_WORKFLOW_DEF_ID INNER JOIN FRAMEWORK.f_wf_workflow_def TWF ON TWF.WF_WORKFLOW_DEF_ID = Tstate.WF_WORKFLOW_DEF_ID WHERE WF_TRANSITION_DEF_ID = :Deger1";

        public static string WF_TRANS_DEF_QUERY_MAX = @"select max(WF_TRANSITION_DEF_ID) + 1 from FRAMEWORK.F_WF_TRANSITION_DEF";

        public static string YYS_ADMIN_QUERY = @"select * from   DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS where  WF_DEF_ID= :Deger";

        public static string YYS_LOGICAL_GROUP_QUERY = @"select * from   DT_WORKFLOW.YYS_LOGICAL_GROUPS where  WF_DEF_ID=:Deger";
        public static string YYS_LOGICAL_GROUP_QUERY_MAX = @"select max(LOGICAL_GROUP_ID) from   DT_WORKFLOW.YYS_LOGICAL_GROUPS";

        public static string YYS_LOGICAL_MEMBER_QUERY = @"select * from DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS where LOGICAL_GROUP_ID= :Deger";

        public static string YYS_STATIK_QUERY = @"select * from  DT_WORKFLOW.YYS_STATE_AUTHORIZATION where WF_DEF_ID= :Deger";

        public static string YYS_ACTION_QUERY = @"Select * from  DT_WORKFLOW.YYS_ACTION_AUTHORIZATION where WF_DEF_ID= :Deger";

        public static string WF_ASSINGMENT_QUERY_MAX = @"select SQ_F_WF_ASSIGNMENT.nextval from dual";


 
    }
}
