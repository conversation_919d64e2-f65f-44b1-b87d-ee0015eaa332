﻿using DigiFlowEmailActions.Helpers.WorkflowHelpers;
using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.DataAccessLayer;
using Digiturk.Workflow.Digiflow.Entities.Enums;
using Digiturk.Workflow.Digiflow.Framework;
using Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.DigiFlow.Framework.Action;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers
{
    public class MyWorkflowHelpers
    {
        private static readonly string yillikIzinAkisSayfasi = "Leave.aspx";
        private static readonly string sodexoAkisSayfasi = "SodexhoRequest.aspx";
        private static readonly string maasAvansAkisSayfasi = "AdvanceS.aspx";
        private static readonly string isAvansAkisSayfasi = "AdvanceW.aspx";
        private static readonly string delegasyonFormAkisSayfasi = "NDelegation.aspx";
        private static readonly string sozlesmeOnayAkisSayfasi = "ContratRequest.aspx";
        private static readonly string odemeTalepAkisSayfasi = "PaymentRequest.aspx";
        private static readonly string purchaseForPaymentRequestSayfasi = "PurchaseForPaymentRequest.aspx";
        private static readonly string uyeTicariFiyatIstisnaRequestSayfasi = "UyeTicariFiyatIstisnaRequest.aspx";
        private static readonly string uyeTicariUcretIadeSayfasi = "UyeTicariUcretIade.aspx";
        private static readonly string uyeTicariBayiYetkilendirmeRequestSayfasi = "UyeTicariBayiYetkilendirmeRequest.aspx";
        private static readonly string uyeTicariGrupDegisiklikRequestSayfasi = "UyeTicariGrupDegisiklikRequest.aspx";
        private static readonly string jobQuitFormSayfasi = "JobQuitForm.aspx";
        private static readonly string performansHedefGirisiAkisSayfasi = "PrfHedef.aspx";
        private static readonly string performansHedefRevizyonAkisSayfasi = "PrfHedefRevizyon.aspx";
        private static readonly string masrafFormuAkisSayfasi = "ExpenseFormRequest.aspx";
        private static readonly string alternatifYoneticiAkisSayfasi = "AlternativeManagerRequest.aspx";
        private static readonly string bayiPersonelFotografRequestAkisSayfasi = "BayiPersonelFotografRequest.aspx";

        
        private static long logDefGroupIdAction = 659; //live
        public static FLogin GetAssignedUser(long wfInstanceId, List<long> listAssingnLogins, long senderFLoginId, FWfActionTaskInstance taskInstance)
        {
            if (FormDelegation(wfInstanceId, listAssingnLogins, senderFLoginId))
            {
                /// Form Delege edilmişse
                if (listAssingnLogins.Count > 1)
                {
                    // Birden Fazla Kişiye Delege edilmişse bu kişi kimin delegesi ise burdan delege edilenin Id si dönmeli.
                    //WorkflowDelegationHelper.(AssignToId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                    foreach (var item in listAssingnLogins)
                    {
                        if (WorkFlowInformationHelper.DelegationCheck(wfInstanceId, item, senderFLoginId, DateTime.Now))
                        {
                            FLogin AssignedUsr = WFRepository<FLogin>.GetEntity(item);
                            return AssignedUsr;
                        }
                    }
                    if (listAssingnLogins.Contains(senderFLoginId))
                    {
                        return WFRepository<FLogin>.GetEntity(senderFLoginId);
                    }
                    else
                    {
                        return FormInformationHelper.GetAssignedUser(taskInstance);
                    }
                }
                else
                {
                    return FormInformationHelper.GetAssignedUser(taskInstance);
                }
            }
            else
            {
                if (listAssingnLogins.Contains(senderFLoginId))
                {
                    return WFRepository<FLogin>.GetEntity(senderFLoginId);
                }
                else
                {
                    return FormInformationHelper.GetAssignedUser(taskInstance);
                }
            }
        }
        private static bool FormDelegation(long wfInstanceId, List<long> listAssingnLogins, long senderFLoginId)
        {
            if (wfInstanceId == 0) return false;
            foreach (var item in listAssingnLogins)
            {
                if (WorkFlowInformationHelper.DelegationCheck(wfInstanceId, item, senderFLoginId, DateTime.Now))
                {
                    return true;
                }
            }
            return false;
        }
        public static bool ApproveAuthorizationControl(long senderFLoginId, long wfInstanceId, long wfWorkflowDefId, List<long> listAssignLogins, List<long> listDelegateAssignLogins)
        {
            //return listAssignLogins.Contains(senderFLoginId) || listDelegateAssignLogins.Contains(senderFLoginId);
            return true;//UNUTMA
        }
        public static bool RejectAuthorizationControl(long senderFLoginId, long wfInstanceId, long wfWorkflowDefId, List<long> listAssignLogins, List<long> listDelegateAssignLogins)
        {
            //return listAssignLogins.Contains(senderFLoginId) || listDelegateAssignLogins.Contains(senderFLoginId);
            return true;//UNUTMA
        }

        public static bool CancelAuthorizationControl(long senderFLoginId, long ownerLoginId, long wfWorkflowDefId)
        {
            bool IsFlowAdmin = CheckIsFlowAdmin(senderFLoginId, wfWorkflowDefId);
            bool IsSystemAdmin = CheckIsSystemAdmin(senderFLoginId);
            return ownerLoginId == senderFLoginId || IsFlowAdmin || IsSystemAdmin;
        }

        public static void ApproveButton_Click(FWfWorkflowInstance CurrentWfIns, long senderFLoginId, long WfInstanceId, List<long> listAssingnLogins, FWfActionTaskInstance taskInstance, long WfWorkflowDefId, WFContext CurrentWFContext, string comment, ref string errorUserMsg_Tr, ref string errorUserMsg_En)
        {
            long sodexoAkisId = 0, isAvansAkisId = 0, maasAvansAkisId = 0, delegasyonFormAkisId = 0, sozlesmeOnayAkisId = 0, purchaseForPaymentRequestId = 0, uyeTicariFiyatIstisnaRequestId = 0, uyeTicariUcretIadeId = 0, uyeTicariBayiYetkilendirmeRequestId = 0, uyeTicariGrupDegisiklikRequestId = 0, performansHedefGirisiAkisId = 0, performansHedefRevizyonAkisId = 0, alternatifYoneticiAkisId=0, bayiPersonelFotografRequestAkisId=0;

            sodexoAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(sodexoAkisSayfasi));
            isAvansAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(isAvansAkisSayfasi));
            maasAvansAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(maasAvansAkisSayfasi));
            delegasyonFormAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(delegasyonFormAkisSayfasi));
            sozlesmeOnayAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(sozlesmeOnayAkisSayfasi));
            purchaseForPaymentRequestId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(purchaseForPaymentRequestSayfasi));
            uyeTicariFiyatIstisnaRequestId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(uyeTicariFiyatIstisnaRequestSayfasi));
            uyeTicariUcretIadeId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(uyeTicariUcretIadeSayfasi));
            uyeTicariBayiYetkilendirmeRequestId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(uyeTicariBayiYetkilendirmeRequestSayfasi));
            uyeTicariGrupDegisiklikRequestId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(uyeTicariGrupDegisiklikRequestSayfasi));
            performansHedefGirisiAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(performansHedefGirisiAkisSayfasi));
            performansHedefRevizyonAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(performansHedefRevizyonAkisSayfasi));
            alternatifYoneticiAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(alternatifYoneticiAkisSayfasi));
            bayiPersonelFotografRequestAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(bayiPersonelFotografRequestAkisSayfasi));

            FLogin LoginObject = WFRepository<FLogin>.GetEntity(senderFLoginId);
            FLogin AssignedUser = MyWorkflowHelpers.GetAssignedUser(WfInstanceId, listAssingnLogins, senderFLoginId, taskInstance);
            FlowAdminOprObject FlowAdminOprs = new FlowAdminOprObject(WfInstanceId, WfWorkflowDefId);
            if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != performansHedefGirisiAkisId && CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != performansHedefRevizyonAkisId)
            {
                if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == sodexoAkisId)
                    SodexoHelper.InsertToContextForApproval(AssignedUser, CurrentWFContext);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == maasAvansAkisId)
                    IsMaasAvansHelper.SaveForAdvanceFinalRequest(CurrentWFContext, CurrentWfIns, "AdvanceS.aspx", AssignedUser, LoginObject);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == isAvansAkisId)
                    IsMaasAvansHelper.SaveForAdvanceFinalRequest(CurrentWFContext, CurrentWfIns, "AdvanceW.aspx", AssignedUser, LoginObject);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == delegasyonFormAkisId)
                    DelegasyonFormHelper.DelegationFormControl(CurrentWfIns.EntityRefId.Value, ref errorUserMsg_Tr, ref errorUserMsg_En);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == sozlesmeOnayAkisId)
                    SozlesmeOnayHelper.SaveLastCommend(CurrentWfIns.EntityRefId.Value, comment);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == purchaseForPaymentRequestId)
                    PurchaseForPaymentRequestHelper.InsertThePurchaseLogicalGroup(CurrentWfIns, LoginObject.LoginId);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == uyeTicariFiyatIstisnaRequestId)
                    UyeTicariFiyatIstisnaRequestFlowHelper.SetProsedur("Yes", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == uyeTicariUcretIadeId)
                    UyeTicariFiyatIstisnaRequestFlowHelper.SetUcretIadeProsedur("Yes", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == uyeTicariBayiYetkilendirmeRequestId)
                    UyeTicariBayiYetkilendirmeRequestFlowHelper.SetProsedur("Yes", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == uyeTicariGrupDegisiklikRequestId)
                    UyeTicariGrupDegisiklikRequestFlowHelper.SetProsedur("Yes", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == alternatifYoneticiAkisId)
                    AlternatifYoneticiAkisHelper.AkisKontrolveOnay(CurrentWfIns.EntityRefId.Value, CurrentWFContext, WfInstanceId, LoginObject, ref errorUserMsg_Tr, ref errorUserMsg_En);
                else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == bayiPersonelFotografRequestAkisId)
                    BayiPersonelFotografRequestFlowHelper.SetProsedur("Yes", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);

                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ApprovalWorkFlow(WfInstanceId, taskInstance, LoginObject, CurrentWFContext, AssignedUser, true, comment);
                MyWorkflowHelpers.FlowAdminOperationChecking(FlowAdminOprs, senderFLoginId, WfWorkflowDefId);

                long YorumGrup = Convert.ToInt64(LogicalGroupHelper.LogicalGroupIDBul("Satinalma_YorumGrup"));
                DataTable DtYorum = LogicalGroupHelper.GetLoginPersonelList(YorumGrup);
                using (Digiturk.Workflow.Repository.UnitOfWork.Start())
                {
                    Digiturk.Workflow.DigiFlow.Framework.Action.ContextList ContextList = new Digiturk.Workflow.DigiFlow.Framework.Action.ContextList();
                    ContextList.AddOrChangeItems("Yorum", comment);
                    ContextList.AddOrChangeItems("WorkFlowInsId", CurrentWfIns.WfWorkflowInstanceId.ToString());
                    ContextList.AddOrChangeItems("OnayDurum", "onaylandı.");
                    ContextList.AddOrChangeItems("IslemYapan", WfDataHelpers.GetLoginNameSurname(senderFLoginId));
                    ContextList.AddOrChangeItems("TaskScreenLink", CurrentWFContext.Parameters["TaskScreenLink"].ToString());
                    Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAllAcceptLoginList(LoginObject, CurrentWfIns.WfWorkflowInstanceId, DtYorum, 1006, ContextList);
                    DtYorum = null;
                }
            }
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == performansHedefGirisiAkisId)
                PerformansHedefGirisHelper.AkisiOnayla(CurrentWFContext,comment, LoginObject,taskInstance);
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == performansHedefRevizyonAkisId)
                PerformansRevizyonHelper.AkisiOnayla(CurrentWFContext, comment, LoginObject, taskInstance);
        }
        public static void RejectButton_Click(FWfWorkflowInstance CurrentWfIns, long senderFLoginId, long WfInstanceId, List<long> listAssingnLogins, FWfActionTaskInstance taskInstance, long WfWorkflowDefId, WFContext CurrentWFContext, string comment, ref string errorUserMsg_Tr, ref string errorUserMsg_En)
        {
            FLogin LoginObject = WFRepository<FLogin>.GetEntity(senderFLoginId);
            FLogin AssignedUser = MyWorkflowHelpers.GetAssignedUser(WfInstanceId, listAssingnLogins, senderFLoginId, taskInstance);
            FlowAdminOprObject FlowAdminOprs = new FlowAdminOprObject(WfInstanceId, WfWorkflowDefId);

            long uyeTicariFiyatIstisnaRequestId = 0, uyeTicariUcretIadeId = 0, uyeTicariBayiYetkilendirmeRequestId = 0, uyeTicariGrupDegisiklikRequestId = 0, jobQuitFormId = 0, alternatifYoneticiAkisId=0, bayiPersonelFotografRequestAkisId=0;
            uyeTicariFiyatIstisnaRequestId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(uyeTicariFiyatIstisnaRequestSayfasi));
            uyeTicariUcretIadeId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(uyeTicariUcretIadeSayfasi));
            uyeTicariBayiYetkilendirmeRequestId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(uyeTicariBayiYetkilendirmeRequestSayfasi));
            uyeTicariGrupDegisiklikRequestId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(uyeTicariGrupDegisiklikRequestSayfasi));
            jobQuitFormId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(jobQuitFormSayfasi));
            alternatifYoneticiAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(alternatifYoneticiAkisSayfasi));
            bayiPersonelFotografRequestAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(bayiPersonelFotografRequestAkisSayfasi));

            if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == uyeTicariFiyatIstisnaRequestId)
                UyeTicariFiyatIstisnaRequestFlowHelper.SetProsedur("No", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == uyeTicariUcretIadeId)
                UyeTicariFiyatIstisnaRequestFlowHelper.SetUcretIadeProsedur("No", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == uyeTicariBayiYetkilendirmeRequestId)
                UyeTicariBayiYetkilendirmeRequestFlowHelper.SetProsedur("No", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == uyeTicariGrupDegisiklikRequestId)
                UyeTicariGrupDegisiklikRequestFlowHelper.SetProsedur("No", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == jobQuitFormId)
                JobQuitFormFlowHelper.RejectWorkFlow(CurrentWFContext, CurrentWfIns, taskInstance);
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == alternatifYoneticiAkisId)
                AlternatifYoneticiAkisHelper.RejectWorkFlow(CurrentWFContext);
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == bayiPersonelFotografRequestAkisId)
                BayiPersonelFotografRequestFlowHelper.SetProsedur("No", CurrentWFContext, CurrentWfIns, taskInstance, LoginObject, comment, ref errorUserMsg_Tr, ref errorUserMsg_En);

            Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.RejectWorkFlow(WfInstanceId, taskInstance, LoginObject, CurrentWFContext, AssignedUser, WorkflowHistoryActionType.REJECTED, comment);
            MyWorkflowHelpers.FlowAdminOperationChecking(FlowAdminOprs, senderFLoginId, WfWorkflowDefId);
        }

        public static void CancelButton_Click(FWfWorkflowInstance CurrentWfIns, WFContext CurrentWFContext, long senderFLoginId, long WfInstanceId, string comment, FWfActionTaskInstance CurrentActionTaskInstance, List<long> listAssingnLogins)
        {
            FlowAdminOprObject FlowAdminOprs = new FlowAdminOprObject(WfInstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            FLogin LoginObject = WFRepository<FLogin>.GetEntity(senderFLoginId);
            FLogin AssignedUser = MyWorkflowHelpers.GetAssignedUser(WfInstanceId, listAssingnLogins, senderFLoginId, CurrentActionTaskInstance);
            List<FLogin> ToList = new List<FLogin>();
            ContextObject SenderObj = new ContextObject();
            #region İptal Öncesi Atanan kişilere yapılan görüntüleme yetkilerini iptal et
            List<long> AssignToIdListLong = GetAssignToIdListLong(WfInstanceId);
            for (int i = 0; i < AssignToIdListLong.Count; i++)
            {
                Digiturk.Workflow.Entities.FWfAssignment asgnmt = new Digiturk.Workflow.Entities.FWfAssignment();
                asgnmt.WfAssignmentId = ConvertionHelper.ConvertValue<long>(Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.GetAssignmentId(AssignToIdListLong[i].ToString(), WfInstanceId.ToString(), "WFVIEW", false));
                if (asgnmt.WfAssignmentId != 0)
                {
                    Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.DeleteAssignment(asgnmt.WfAssignmentId);
                }
            }

            #endregion İptal Öncesi Atanan kişilere yapılan görüntüleme yetkilerini iptal et

            if (CurrentWfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd == "COMPLETED")
            {
                CancelEndFlow(CurrentWfIns);
            }
            Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.CancelWorkflow(CurrentWfIns, LoginObject, comment);
            CurrentWfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WfInstanceId);

            #region Form İptal edildikten sonra gerekli mailing yapılıyor

            bool isLogical = Digiturk.Workflow.Digiflow.WorkFlowHelpers.LogicalGroupHelper.IsDefExistLogicalGroup(logDefGroupIdAction, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, "0");

            if (!isLogical)
            {
                if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                {
                    return;
                }
            }
            FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
            ToList.Add(ToListItem);
            SenderObj.Add("DelegateTo", "");
            SenderObj.Add("Delegated", "");
            SenderObj.Add("ActionDescription", comment);
            SenderObj.Add("ActionTo", "");
            SendMail(0, 8, "iptal edildi", "Cancelled", false, CurrentWFContext, SenderObj, LoginObject, WfInstanceId, AssignedUser, CurrentActionTaskInstance, ToList, CurrentWfIns);

            #endregion Form İptal edildikten sonra gerekli mailing yapılıyor

            #region Akış Reddedildiğinde Haberdar Edilecek Kullanıcıların listesi

            List<long> AcceptUserList = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastAcceptLoginList(WfInstanceId);
            for (int i = 0; i < AcceptUserList.Count; i++)
            {
                FLogin ToListItemRed = WFRepository<FLogin>.GetEntity(AcceptUserList[i]);
                ToList.Add(ToListItemRed);
                SenderObj.Add("DelegateTo", "");
                SenderObj.Add("Delegated", "");
                SenderObj.Add("ActionDescription", comment);
                SenderObj.Add("ActionTo", "");
                SenderObj.Add("WfAssigner", Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(AcceptUserList[i]));
                SendMail(0, 995, "iptal edildi", "Cancelled", false, CurrentWFContext, SenderObj, LoginObject, WfInstanceId, AssignedUser, CurrentActionTaskInstance, ToList, CurrentWfIns);
            }

            #endregion Akış Reddedildiğinde Haberdar Edilecek Kullanıcıların listesi

            #region History Kaydı Oluşturuluyor

            WfHistoryExec(WorkflowHistoryActionType.CANCEL, CurrentActionTaskInstance, WfInstanceId, LoginObject, comment);

            #endregion History Kaydı Oluşturuluyor
        }

        private static void WfHistoryExec(WorkflowHistoryActionType wfHistory, FWfActionTaskInstance CurrentActionTaskInstance, long WfInstanceId, FLogin LoginObject, string comment)
        {
            #region History Kaydı Atılıyor

            long _AssignToId = AssignToId(LoginObject.LoginId, WfInstanceId);
            if (CurrentActionTaskInstance == null)
            {
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(WfInstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);
                WorkflowHistoryWorker.Execute(ActionInstance, LoginObject.LoginId, _AssignToId, wfHistory, comment);
            }
            else
            {
                WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, LoginObject.LoginId, _AssignToId, wfHistory, comment);
            }

            #endregion History Kaydı Atılıyor
        }

        private static void CancelEndFlow(FWfWorkflowInstance CurrentWfIns)
        {
            long yillikIzinAkisId = 0, odemeTalepAkisId = 0, masrafFormuAkisId=0;
            yillikIzinAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(yillikIzinAkisSayfasi));
            odemeTalepAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(odemeTalepAkisSayfasi));
            masrafFormuAkisId = ConvertionHelper.ConvertValue<long>(FormInformationHelper.GetdefinitionId(masrafFormuAkisSayfasi));

            if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == yillikIzinAkisId)
                YillikIzinHelper.CancelEndFlow(CurrentWfIns);
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == odemeTalepAkisId)
                PaymentRequestHelper.CancelEndFlow(CurrentWfIns);
            else if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == masrafFormuAkisId)
                MasrafFormuHelper.CancelEndFlow(CurrentWfIns);
        }

        private static void SendMail(long ActionToLoginId, long MailTemplateId, string Action, string ActionEng, bool IsSave, WFContext CurrentWFContext, ContextObject SenderObj, FLogin LoginObject, long InstanceId, FLogin AssignedUser, FWfActionTaskInstance CurrentActionTaskInstance, List<FLogin> ToList, FWfWorkflowInstance CurrentWfIns)
        {
            if (ActionToLoginId > 0)
            {
                FLogin ActionTo = WFRepository<FLogin>.GetEntity(ActionToLoginId);
                CurrentWFContext.Parameters.AddOrChangeItem("ActionTo", Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(ActionTo.LoginId));
            }
            foreach (var item in SenderObj)
            {
                CurrentWFContext.Parameters.AddOrChangeItem(item.Key, item.Value);
            }
            CurrentWFContext.Parameters.AddOrChangeItem("Action", Action);
            CurrentWFContext.Parameters.AddOrChangeItem("ActionEng", ActionEng);

            CurrentWFContext.Parameters.AddOrChangeItem("ActionDate", DateTime.Now.ToString());
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(LoginObject.LoginId));

            FWfWorkflowDef WfDef = WFRepository<FWfWorkflowDef>.GetEntity(CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            CurrentWFContext.Parameters.AddOrChangeItem("WorkflowName", WfDef.Name);
            CurrentWFContext.Parameters.AddOrChangeItem("WfOwner", WfDataHelpers.GetLoginNameSurname(CurrentWfIns.OwnerLogin.LoginId));

            if (!CurrentWFContext.Parameters.ContainsKey("Onay")) CurrentWFContext.Parameters.AddOrChangeItem("Onay", "Yes");
            if (!CurrentWFContext.Parameters.ContainsKey("LastUpdatedBy")) CurrentWFContext.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(LoginObject, AssignedUser));
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentLoginType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");

            if (CurrentActionTaskInstance == null)
            {
                //FWfActionTaskInstance
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);

                MailHelper.SendMail(MailTemplateId, ActionInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
            }
            else
            {
                MailHelper.SendMail(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(CurrentActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);
            }

            if (IsSave) CurrentWFContext.Save();
            SenderObj.Clear();
            ToList.Clear();
        }

        private static long AssignToId(long LoginId, long InstanceId)
        {
            if (AssignToLoginIdCheck(LoginId, InstanceId))
            {
                return LoginId;
            }
            else
            {
                return Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(InstanceId);
            }
        }
        private static bool AssignToLoginIdCheck(long LoginId, long InstanceId)
        {
            return AssignToIdListLong(InstanceId).Contains(LoginId);
        }
        private static List<long> AssignToIdListLong(long InstanceId)
        {
            return Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(InstanceId);
        }
        private static List<long> GetAssignToIdListLong(long InstanceId)
        {
            return Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(InstanceId);
        }
        public static bool CheckIsFlowAdmin(long senderFLoginId, long wfWorkflowDefId)
        {
            return WorkflowAdminHelpers.IsWfAdmin(senderFLoginId, wfWorkflowDefId);
        }
        public static bool CheckIsSystemAdmin(long senderFLoginId)
        {
            return WorkflowAdminHelpers.IsSystemAdmin(senderFLoginId);
        }
        public static FLogin FlowAdmin(long senderFLoginId, long wfWorkflowDefId)
        {
            FLogin sonuc = null;
            if (CheckIsFlowAdmin(senderFLoginId, wfWorkflowDefId))
            {
                sonuc = (FLogin)WFRepository<FLogin>.GetEntity(senderFLoginId);
            }

            return sonuc;
        }
        public static void FlowAdminOperationChecking(FlowAdminOprObject Objs, long senderFLoginId, long wfWorkflowDefId)
        {
            long RealUserId = Objs.RealUserId;
            long DelegateUserId = Objs.DelegateUserId;
            long FlowAdminUserId = senderFLoginId;
            DateTime StartDate = Objs.OperationStartTime;
            DateTime EndDate = DateTime.Now;
            if (CheckIsFlowAdmin(senderFLoginId, wfWorkflowDefId) && FlowAdmin(senderFLoginId, wfWorkflowDefId)?.LoginId != RealUserId)
            {
                if (DelegateUserId > 0)
                {
                    /// Delegasyonu Parçalıyoruz
                    Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites.DelegationObject dlgReq = new Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites.DelegationObject(DelegateUserId);
                    WorkflowDelegationHelper.EndDelegation(RealUserId, DelegateUserId, wfWorkflowDefId, StartDate);
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        FlowAdminUserId,
                        wfWorkflowDefId,
                        "Akış Admini Delegasyonu",
                        StartDate,
                        EndDate);
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        DelegateUserId,
                        wfWorkflowDefId,
                        dlgReq.DelegationComment + "(Akış Admini işlemi Sonrası devam ettirildi.)",
                        EndDate,
                        dlgReq.EndTime);
                }
                else
                {
                    /// Akış Admini için Delegasyon üretiyoruz
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        FlowAdminUserId,
                        wfWorkflowDefId,
                        "Akış Admini Delegasyonu oluşturuldu",
                        StartDate,
                        EndDate);
                }
            }
        }
    }
}