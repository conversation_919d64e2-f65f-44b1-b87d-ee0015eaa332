﻿using Digiturk.Workflow.Digiflow.Api.Common;
using Digiturk.Workflow.Digiflow.Api.Common.DataTransferObjects.Inbox;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace Digiturk.Workflow.Digiflow.Api.Controllers
{
    public class InboxController : ApiController
    {
        public void Test()
        {
            //List<long> AssignedLoginList= Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(InstanceId);
            //LoginDto LoginDtoObject= Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDetailsOfLogin(loginId);
        }


        [HttpGet]
        [Route("api/GetInboxList")]
        public IEnumerable<DTOInbox> GetInboxList(long UserId)
        {
            List<DTOInbox> InboxList = new List<DTOInbox>();

            // Inbox Çekiyor
            DataTable dtInboxGridView = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetRealInbox("TASKINBOX", UserId);

            // Delegasyonu Çekiyor
            //dtInboxGridView = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetDelegeInbox("", UserId);

            // Yoruma Gönderilen Çekiyor
            //dtInboxGridView = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetCommentInbox("TASKCOMMENT", UserId);

            for (int i = 0; i < dtInboxGridView.Rows.Count; i++)
            {
                DTOInbox dto = new DTOInbox();
                dto.FlowName = dtInboxGridView.Rows[i]["FLOWNAME"].ToString();
                dto.FlowDesc = dtInboxGridView.Rows[i]["FLOWDESC"].ToString();
                dto.Atanan = (decimal)dtInboxGridView.Rows[i]["ATANAN"];
                dto.StateName= dtInboxGridView.Rows[i]["STATENAME"].ToString();
                dto.StateDesc = dtInboxGridView.Rows[i]["STATEDESC"].ToString();
                dto.WfInsId = (decimal)dtInboxGridView.Rows[i]["WFINSID"];
                dto.WfInstanceDef = dtInboxGridView.Rows[i]["WFINSTANCEDEF"].ToString();
                dto.WfOwner = dtInboxGridView.Rows[i]["WFOWNER"].ToString();
                dto.Bolum = dtInboxGridView.Rows[i]["BOLUM"].ToString();
                dto.WfWorkflowDefId = (decimal)dtInboxGridView.Rows[i]["WF_WORKFLOW_DEF_ID"];
                dto.LastLoginId = (decimal)dtInboxGridView.Rows[i]["LASTLOGINID"];
                dto.WfLastModifiedBy = dtInboxGridView.Rows[i]["WFLASTMODIFIEDBY"].ToString();
                dto.WfLastModifiedByNom = dtInboxGridView.Rows[i]["WFLASTMODIFIEDBY_NOM"].ToString();
                dto.Detail = dtInboxGridView.Rows[i]["DETAIL"].ToString();
                dto.Amount = dtInboxGridView.Rows[i]["AMOUNT"].ToString() == string.Empty ? 0 : Convert.ToDecimal(dtInboxGridView.Rows[i]["AMOUNT"]);
                dto.Currency = dtInboxGridView.Rows[i]["CURRENCY"].ToString();
                dto.Amounttl = (decimal)dtInboxGridView.Rows[i]["AMOUNTTL"];
                dto.WfInstanceLink = dtInboxGridView.Rows[i]["WFINSTANCELINK"].ToString();
                dto.MWfInstanceLink = dtInboxGridView.Rows[i]["MWFINSTANCELINK"].ToString();
                dto.WfDate = dtInboxGridView.Rows[i]["WFDATE"].ToString(); //Date Time
                dto.TopluOnayDurum = dtInboxGridView.Rows[i]["TOPLUONAYDURUM"].ToString();
                dto.WfWorkflowEntity = dtInboxGridView.Rows[i]["WF_WORKFLOW_ENTITY"].ToString();
                dto.WfWorkflowEntityValue = dtInboxGridView.Rows[i]["WF_WORKFLOW_ENTITY_VALUE"].ToString();

                //InboxList.Add(new DTOInbox() { StateName = dtInboxGridView.Rows[i][}]})
                InboxList.Add(dto);
            }
            return InboxList;
        }

        [HttpGet]
        [Route("api/GetDelegeInboxList")]
        public IEnumerable<DTOInbox> GetDelegeInboxList(long UserId)
        {
            List<DTOInbox> InboxList = new List<DTOInbox>();
            // Delegasyonu Çekiyor
            DataTable dtdelegationInboxGridView = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetDelegeInbox("", UserId);


            for (int i = 0; i < dtdelegationInboxGridView.Rows.Count; i++)
            {
                DTOInbox dto = new DTOInbox();
                dto.FlowName = dtdelegationInboxGridView.Rows[i]["FLOWNAME"].ToString();
                dto.FlowDesc = dtdelegationInboxGridView.Rows[i]["FLOWDESC"].ToString();
                dto.Atanan = (decimal)dtdelegationInboxGridView.Rows[i]["ATANAN"];
                dto.StateName = dtdelegationInboxGridView.Rows[i]["STATENAME"].ToString();
                dto.StateDesc = dtdelegationInboxGridView.Rows[i]["STATEDESC"].ToString();
                dto.WfInsId = (decimal)dtdelegationInboxGridView.Rows[i]["WFINSID"];
                dto.WfInstanceDef = dtdelegationInboxGridView.Rows[i]["WFINSTANCEDEF"].ToString();
                dto.WfOwner = dtdelegationInboxGridView.Rows[i]["WFOWNER"].ToString();
                dto.Bolum = dtdelegationInboxGridView.Rows[i]["BOLUM"].ToString();
                dto.WfWorkflowDefId = (decimal)dtdelegationInboxGridView.Rows[i]["WF_WORKFLOW_DEF_ID"];
                dto.LastLoginId = (decimal)dtdelegationInboxGridView.Rows[i]["LASTLOGINID"];
                dto.WfLastModifiedBy = dtdelegationInboxGridView.Rows[i]["WFLASTMODIFIEDBY"].ToString();
                dto.WfLastModifiedByNom = dtdelegationInboxGridView.Rows[i]["WFLASTMODIFIEDBY_NOM"].ToString();
                dto.Detail = dtdelegationInboxGridView.Rows[i]["DETAIL"].ToString();
                dto.Amount = dtdelegationInboxGridView.Rows[i]["AMOUNT"].ToString() == string.Empty ? 0 : (decimal)dtdelegationInboxGridView.Rows[i]["AMOUNT"];
                dto.Currency = dtdelegationInboxGridView.Rows[i]["CURRENCY"].ToString();
                dto.Amounttl = (decimal)dtdelegationInboxGridView.Rows[i]["AMOUNTTL"];
                dto.WfInstanceLink = dtdelegationInboxGridView.Rows[i]["WFINSTANCELINK"].ToString();
                dto.MWfInstanceLink = dtdelegationInboxGridView.Rows[i]["MWFINSTANCELINK"].ToString();
                dto.WfDate = dtdelegationInboxGridView.Rows[i]["WFDATE"].ToString(); //Date Time
                dto.TopluOnayDurum = dtdelegationInboxGridView.Rows[i]["TOPLUONAYDURUM"].ToString();
                dto.WfWorkflowEntity = dtdelegationInboxGridView.Rows[i]["WF_WORKFLOW_ENTITY"].ToString();
                dto.WfWorkflowEntityValue = dtdelegationInboxGridView.Rows[i]["WF_WORKFLOW_ENTITY_VALUE"].ToString();

                //InboxList.Add(new DTOInbox() { StateName = dtInboxGridView.Rows[i][}]})
                InboxList.Add(dto);
            }
            return InboxList;
        }


        [HttpGet]
        [Route("api/GetHistory")]
        public IEnumerable<DTOHistory> GetHistory(long WfInstanceId)
        {
            List<DTOHistory> HistoryList = new List<DTOHistory>();
            DataTable dtbHistory = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetWorkFlowHistoryC(WfInstanceId);
            for (int i = 0; i < dtbHistory.Rows.Count; i++)
            {
                DTOHistory dto = new DTOHistory();
                dto.State= dtbHistory.Rows[i]["STATE"].ToString();
                dto.Color = dtbHistory.Rows[i]["COLORS"].ToString();
                dto.Users = dtbHistory.Rows[i]["USERS"].ToString();
                dto.Action = dtbHistory.Rows[i]["ACTION"].ToString();
                dto.Dates = (DateTime)dtbHistory.Rows[i]["DATES"];
                dto.Commends = dtbHistory.Rows[i]["COMMENTS"].ToString();
                //public string State { get; set; } //STATE
                //public string Color { get; set; } //COLORS
                //public string Users { get; set; } //USERS
                //public string Action { get; set; } //ACTION
                //public DateTime Dates { get; set; } //DATES
                //public string Commends { get; set; } //COMMENTS
                HistoryList.Add(dto);
            }
            return HistoryList;
        }

        [HttpGet]
        [Route("api/GetYorumList")]
        public IEnumerable<DTOInbox> GetYorumList(long UserId)
        {
            List<DTOInbox> InboxList = new List<DTOInbox>();

            // Yoruma Gönderilen Çekiyor
            DataTable dtInboxGridView = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetCommentInbox("TASKCOMMENT", UserId);

            for (int i = 0; i < dtInboxGridView.Rows.Count; i++)
            {
                DTOInbox dto = new DTOInbox();
                dto.FlowName = dtInboxGridView.Rows[i]["FLOWNAME"].ToString();
                dto.FlowDesc = dtInboxGridView.Rows[i]["FLOWDESC"].ToString();
                dto.Atanan = (decimal)dtInboxGridView.Rows[i]["ATANAN"];
                dto.StateName = dtInboxGridView.Rows[i]["STATENAME"].ToString();
                dto.StateDesc = dtInboxGridView.Rows[i]["STATEDESC"].ToString();
                dto.WfInsId = (decimal)dtInboxGridView.Rows[i]["WFINSID"];
                dto.WfInstanceDef = dtInboxGridView.Rows[i]["WFINSTANCEDEF"].ToString();
                dto.WfOwner = dtInboxGridView.Rows[i]["WFOWNER"].ToString();
                dto.Bolum = dtInboxGridView.Rows[i]["BOLUM"].ToString();
                dto.WfWorkflowDefId = (decimal)dtInboxGridView.Rows[i]["WF_WORKFLOW_DEF_ID"];
                dto.LastLoginId = (decimal)dtInboxGridView.Rows[i]["LASTLOGINID"];
                dto.WfLastModifiedBy = dtInboxGridView.Rows[i]["WFLASTMODIFIEDBY"].ToString();
                dto.WfLastModifiedByNom = dtInboxGridView.Rows[i]["WFLASTMODIFIEDBY_NOM"].ToString();
                dto.Detail = dtInboxGridView.Rows[i]["DETAIL"].ToString();
                dto.Amount = dtInboxGridView.Rows[i]["AMOUNT"].ToString() == string.Empty ? 0 : (decimal)dtInboxGridView.Rows[i]["AMOUNT"];
                dto.Currency = dtInboxGridView.Rows[i]["CURRENCY"].ToString();
                dto.Amounttl = (decimal)dtInboxGridView.Rows[i]["AMOUNTTL"];
                dto.WfInstanceLink = dtInboxGridView.Rows[i]["WFINSTANCELINK"].ToString();
                dto.MWfInstanceLink = dtInboxGridView.Rows[i]["MWFINSTANCELINK"].ToString();
                dto.WfDate = dtInboxGridView.Rows[i]["WFDATE"].ToString(); //Date Time
                dto.TopluOnayDurum = dtInboxGridView.Rows[i]["TOPLUONAYDURUM"].ToString();
                dto.WfWorkflowEntity = dtInboxGridView.Rows[i]["WF_WORKFLOW_ENTITY"].ToString();
                dto.WfWorkflowEntityValue = dtInboxGridView.Rows[i]["WF_WORKFLOW_ENTITY_VALUE"].ToString();

                //InboxList.Add(new DTOInbox() { StateName = dtInboxGridView.Rows[i][}]})
                InboxList.Add(dto);
            }
            return InboxList;
        }


        [Route("api/GetDataTableType")]
        public IEnumerable<DTODataTableType> GetDataTableType()
        {
            DataTable dtInboxGridView = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetRealInbox("TASKINBOX", 1763);
            List<DTODataTableType> lst = new List<DTODataTableType>();
            //lst.Add(new DTODataTableType() { FieldName = "aa", FieldType = "aa" });
            foreach (var item in dtInboxGridView.Columns)
            {
                lst.Add(new DTODataTableType() { FieldType = ((DataColumn)item).DataType.ToString(), FieldName = ((DataColumn)item).ColumnName });
            }
            return lst;
        }
    }
}
