﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true"
    CodeFile="WorkflowAdmins.aspx.cs" Inherits="WorkflowAdmins" %>

<%@ Register Assembly="DevExpress.Web.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxDataView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.v10.2, Version=10.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxPopupControl" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" width="100%" cellspacing="0" cellpadding="0">
        <tr>
            <td style="padding: 5px; font-weight: 700;" align="left" width="100%" valign="top">Akış Listesi
            </td>
        </tr>
        <tr>
            <td style="padding: 5px" align="left" width="100%" valign="top">
                <dx:ASPxComboBox ID="WorkFlowCombobox" runat="server" AutoPostBack="True" OnSelectedIndexChanged="WorkFlowCombobox_SelectedIndexChanged"
                    Width="250px">
                </dx:ASPxComboBox>
            </td>
        </tr>
        <tr>
            <td style="padding: 5px; font-weight: 700;" align="left" width="100%" valign="top">Yöneticiler
            </td>
        </tr>
        <tr>
            <td style="padding: 5px" align="left" width="100%" valign="top">
                <dx:ASPxGridView ID="ManagersGridView" runat="server" AutoGenerateColumns="False"
                    Width="100%"
                    OnPageIndexChanged="ManagersGridViewPageIndexChanging"
                    OnBeforeColumnSortingGrouping="ManagersGridViewPageIndexChanging"
                    OnCustomColumnGroup="ManagersGridViewPageIndexChanging"
                    OnCustomGroupDisplayText="ManagersGridViewPageIndexChanging"
                    OnFilterControlCustomValueDisplayText="ManagersGridViewPageIndexChanging"
                    OnFilterControlParseValue="ManagersGridViewPageIndexChanging"
                    OnFilterControlOperationVisibility="ManagersGridViewPageIndexChanging"
                    OnHtmlDataCellPrepared="ManagersGridView_HtmlDataCellPrepared"
                    OnRowDeleting="ManagersGridView_RowDeleting"
                    KeyFieldName="WF_OF_ADMIN_ID"
                    OnRowCommand="ManagersGridView_RowCommand">
                    <Columns>
                        <dx:GridViewDataTextColumn Caption="Yönetici" FieldName="FULLNAME" ShowInCustomizationForm="True"
                            VisibleIndex="1">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Kullanıcı Seviyesi" FieldName="ADMIN_LEVEL_TYPE_ID"
                            ShowInCustomizationForm="True" VisibleIndex="2">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="Aktif" FieldName="IS_ACTIVE" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataHyperLinkColumn Caption="Düzenle" FieldName="LOGIN_ID" VisibleIndex="4">
                            <PropertiesHyperLinkEdit NavigateUrlFormatString="AddUpdateWorkflowAdmin.aspx?AdminId={0}"
                                Text="Düzenle">
                            </PropertiesHyperLinkEdit>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataHyperLinkColumn>
                        <%--   <dx:GridViewCommandColumn Caption="Sil" ShowInCustomizationForm="True" VisibleIndex="4"
                            Name="Sil">
                            <DeleteButton Text="Sil" Visible="True">
                            </DeleteButton>
                             <ClearFilterButton Visible="True">
                             </ClearFilterButton>
                             <CellStyle HorizontalAlign="Center">
                             </CellStyle>
                        </dx:GridViewCommandColumn>--%>
                        <dx:GridViewDataButtonEditColumn Caption="Sil" VisibleIndex="6">
                            <DataItemTemplate>
                                <dx:ASPxButton ID="DeleteButton" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                    <ClientSideEvents Click="function(s, e) {
	 e.processOnServer = confirm('Kişinin Akış Yöneticiliğini silmek istediğinize emin misiniz?');}" />
                                </dx:ASPxButton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn>
                        <dx:GridViewDataTextColumn Caption="Sıra No" VisibleIndex="0" Width="40px">
                            <DataItemTemplate>
                                <%# Container.ItemIndex + 1 %>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                        <dx:GridViewDataTextColumn Caption="ID" FieldName="WF_OF_ADMIN_ID" ShowInCustomizationForm="True"
                            Visible="False" VisibleIndex="6">
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataTextColumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterRowMenu="True" ShowFilterBar="Auto" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Kişinin Akış Yöneticiliğini silmek istediğinize emin misiniz?"></SettingsText>
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
            </td>
        </tr>
    </table>
</asp:Content>