﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations;
using System;
using System.Data;

public partial class RaporTalepAdminPage : UnSecurePage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            Uygulama_Getir();
        }
    }

    /// <summary>
    /// Uygulama Listesini Listeye doldurur
    /// </summary>
    private void Uygulama_Getir()
    {
        Temizle();
        DataTable t = RaporTalepHelper.UygulamaListesi(rdpTuru.SelectedValue, drpTalepTipi.SelectedValue, true, true);
        drpUygulamalar.DataSource = t;
        drpUygulamalar.DataTextField = "STRUYGULAMA";
        drpUygulamalar.DataValueField = "ID";
        drpUygulamalar.DataBind();
    }

    protected void btnKaydet_Click(object sender, EventArgs e)
    {
        Kaydet();
    }

    /// <summary>
    /// Kayıt işlemleri yapılır.
    /// </summary>
    protected void Kaydet()
    {
        RaporTalepAdmin RA;
        if (HiddenField1.Value == "")
        {
            RA = new RaporTalepAdmin();
            RA.VersionId = 1;
        }
        else
        {
            RA = WFRepository<RaporTalepAdmin>.GetEntity(HiddenField1.Value);
            RA.VersionId = RA.VersionId + 1;
        }
        RA.STRTIPI = drpTalepTipi.SelectedValue;
        RA.STRGOSTER = Convert.ToInt32(chkGoster.Checked).ToString();
        RA.STRTURU = rdpTuru.SelectedValue;
        RA.STRUYGULAMA = txtUygulama.Text;
        RA.Created = DateTime.Now;
        RA.CreatedBy = FormInformationHelper.GetLoginId(((AdminPanel_AdminPanelMaster)Master).UserName);
        RA.LastUpdated = DateTime.Now;
        ActionHelpers.EntitySave(RA);
        Uygulama_Getir();
        ((AdminPanel_AdminPanelMaster)Master).ShowPopup(false, "Admin Sayfaları", "Kayıt Edildi", false, string.Empty);
    }

    /// <summary>
    /// Ekran Üzerindeki Kontrolleri Temizler
    /// </summary>
    protected void Temizle()
    {
        txtUygulama.Text = "";
        chkGoster.Checked = true;
        HiddenField1.Value = "";
    }

    protected void btnTemizle_Click(object sender, EventArgs e)
    {
        Temizle();
    }

    protected void btnSil_Click(object sender, EventArgs e)
    {
        Sil();
    }

    /// <summary>
    /// Kayıt Silme İşlemlerini yapar
    /// </summary>
    private void Sil()
    {
        if (HiddenField1.Value == "")
        {
            ((AdminPanel_AdminPanelMaster)Master).ShowPopup(false, "Hata", "Lütfen silinecek kayıt seçiniz.", true, string.Empty);
        }
        else
        {
            if (RaporTalepAdminHelper.Uygulama_Var(HiddenField1.Value, drpTalepTipi.SelectedValue))
            {
                ((AdminPanel_AdminPanelMaster)Master).ShowPopup(false, "Hata", "Bu uygulama daha önce ekranlarda kullanılmış.Silinemez !", true, string.Empty);
            }
            else
            {
                RaporTalepAdmin RA = WFRepository<RaporTalepAdmin>.GetEntity(HiddenField1.Value);
                ActionHelpers.EntityDelete(RA);
                Uygulama_Getir();
                RA = null;
                ((AdminPanel_AdminPanelMaster)Master).ShowPopup(false, "Admin Sayfaları", "Kayıt Silindi", false, string.Empty);
            }
        }
    }

    protected void rdpTuru_SelectedIndexChanged(object sender, EventArgs e)
    {
        Uygulama_Getir();
    }

    protected void drpUygulamalar_SelectedIndexChanged(object sender, EventArgs e)
    {
        Uygulama_esitle();
    }

    /// <summary>
    /// Seçilmiş Uygulamayı ekrana getirir
    /// </summary>
    private void Uygulama_esitle()
    {
        Temizle();
        HiddenField1.Value = drpUygulamalar.SelectedValue;
        if (HiddenField1.Value == "0")
        {
            return;
        }
        RaporTalepAdmin RA = WFRepository<RaporTalepAdmin>.GetEntity(HiddenField1.Value);
        drpTalepTipi.SelectedValue = RA.STRTIPI;
        chkGoster.Checked = Convert.ToBoolean(int.Parse(RA.STRGOSTER));
        rdpTuru.SelectedValue = RA.STRTURU;
        txtUygulama.Text = RA.STRUYGULAMA;
        RA = null;
    }

    protected void drpTalepTipi_SelectedIndexChanged(object sender, EventArgs e)
    {
        Uygulama_Getir();
    }
}