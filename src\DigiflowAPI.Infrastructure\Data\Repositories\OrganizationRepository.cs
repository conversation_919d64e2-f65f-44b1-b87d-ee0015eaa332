using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Domain.Entities.Organization;
using Microsoft.AspNetCore.Http;
using DigiflowAPI.Application.Interfaces.DataAccess;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    public class OrganizationRepository(IOracleDataAccessRepositoryFactory repositoryFactory, IGlobalHelpers globalHelpers, IHttpContextAccessor httpContextAccessor) : IOrganizationRepository
    {
        public string GetDepartment(long managerID)
        {
            if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
            {
                return WfDataHelpers.GetDeptAdi(managerID, "DEPS_EN");
            }
            else
            {
                return WfDataHelpers.GetDeptAdi(managerID, "BOLUM");
            }
        }

        public async Task<IEnumerable<DpHrDeps>> GetDepartmentAsync(long? id)
        {
            string sql = @"
                SELECT * FROM DT_WORKFLOW.DP_HR_DEPS
                WHERE DT_WORKFLOW.DP_HR_DEPS.ID = :BolumId
                ORDER BY DT_WORKFLOW.DP_HR_DEPS.BOLUM ASC";

            var parameters = new Dictionary<string, object>
            {
                {"BolumId", id}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<DpHrDeps>(sql, parameters);
        }

        public async Task<IEnumerable<DpHrDeps>> GetDepartmentSelectAsync(long? id)
        {
            string sql = @"
                SELECT
                    DT_WORKFLOW.DP_HR_DEPS.ID,
                    DT_WORKFLOW.DP_HR_DEPS.BOLUM,
                    DT_WORKFLOW.DP_HR_DEPS.DEPS_EN
                FROM DT_WORKFLOW.DP_HR_DEPS WHERE DT_WORKFLOW.DP_HR_DEPS.UST_BOLUM_ID = :UstBolumId
                ORDER BY DT_WORKFLOW.DP_HR_DEPS.BOLUM ASC";

            var parameters = new Dictionary<string, object>
            {
                {"UstBolumId", id}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<DpHrDeps>(sql, parameters);
        }

        public async Task<DpHrUsers?> GetDPHRUserByIdAsync(long? userId = null)
        {
            userId ??= await globalHelpers.GetUserId();
            string sql = @"SELECT * FROM DT_WORKFLOW.DP_HR_USERS WHERE F_LOGIN_ID = :UserId";

            var parameters = new Dictionary<string, object>
            {
                { "UserId", userId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteSingleQueryAsync<DpHrUsers>(sql, parameters);
        }

        public async Task<DepsPath?> GetDPHRUserDepsPathByIdAsync(long? userId = null)
        {
            userId ??= await globalHelpers.GetUserId();
            string sql = @"SELECT d.* FROM DT_WORKFLOW.DP_HR_USERS u INNER JOIN DT_WORKFLOW.DEPS_PATH d ON u.DEPT_ID = d.ID WHERE u.F_LOGIN_ID = :UserId";

            var parameters = new Dictionary<string, object>
            {
                { "UserId", userId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteSingleQueryAsync<DepsPath>(sql, parameters);
        }

        public async Task<(DepsPath? depsPath, List<DpHrDeps> departments, List<VwUserInformation> users)> GetOrganizationHierarchy(long? wfInstanceId = null)
        {
            long selectedUserId;

            if (!long.TryParse(httpContextAccessor.HttpContext?.Request.Headers["X-Login-Id"].ToString(), out selectedUserId))
            {
                if (wfInstanceId == null)
                {
                    selectedUserId = await globalHelpers.GetUserId();
                }
                else
                {
                    var frameworkRepository = repositoryFactory.Create("FrameworkConnection");
                    var wfIns = await frameworkRepository.GetEntityAsync<FWfWorkflowInstance>(wfInstanceId.Value);
                    selectedUserId = wfIns.OwnerLoginId ?? await globalHelpers.GetUserId();
                }
            }

            // Get user's department path
            var depsPath = await GetDPHRUserDepsPathByIdAsync(selectedUserId);

            // Get all departments (root level)
            var departments = (await GetSubDepartments(99)).ToList();

            // Get users from the user's department
            var user = await GetDPHRUserByIdAsync(selectedUserId);
            var users = new List<VwUserInformation>();

            if (user?.DeptId != null)
            {
                string usersSql = @"
                    SELECT v.* FROM DT_WORKFLOW.VW_USER_INFORMATION v
                    INNER JOIN DT_WORKFLOW.DP_HR_USERS u ON v.LOGIN_ID = u.F_LOGIN_ID
                    WHERE u.DEPT_ID = :DeptId AND v.IS_DELETED = 'N'";

                var usersParams = new Dictionary<string, object> { { "DeptId", user.DeptId } };
                var repository = repositoryFactory.Create("DT_WORKFLOW");
                users = (await repository.ExecuteQueryAsync<VwUserInformation>(usersSql, usersParams)).ToList();
            }

            return (depsPath, departments, users);
        }

        public async Task<IEnumerable<DpHrDeps>> GetSubDepartments(long departmentId)
        {
            string sql = "SELECT * FROM DT_WORKFLOW.DP_HR_DEPS WHERE DT_WORKFLOW.DP_HR_DEPS.UST_BOLUM_ID = :DepartmentId";
            var parameters = new Dictionary<string, object>
            {
                { "DepartmentId", departmentId }
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync<DpHrDeps>(sql, parameters);
        }


    }
}
