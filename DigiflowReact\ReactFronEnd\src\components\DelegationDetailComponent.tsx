import * as React from 'react'
import i18next from "i18next"
import { useTranslation } from "react-i18next"
import cookies from 'js-cookie'
import {useEffect, useState} from "react";
import {WDatePicker, WGrid, WList, WListItem, WListItemText, WTextField, WTypography, WDivider} from "@wface/components";
import axios from "axios";
import {getUser} from "../services/wface-helper";

function DelegationDetailComponent(props) {
    const { t } = useTranslation()
    const user = getUser()
    const [starterDate, setStarterDate] = useState(null)
    const [finisherDate, setFinisherDate] = useState(null)

    return(
        <WGrid container alignItems='center' direction="column" style={{marginBottom: 0,}}>
            <WTypography variant="body2" gutterBottom style={{fontWeight: 600}}>DELEGASYON</WTypography>
            <WTypography variant="body2" gutterBottom style={{fontWeight: 500}}>{user&&user.UserNameSurName}</WTypography>
            <WTypography variant="body2" gutterBottom style={{fontWeight: 400}}>TEKNOLOJİDEN SORUMLU GRUP BAŞKANLIĞI</WTypography>

            <h3> Props: {props.id} </h3>

            <WDivider />

            <WGrid style={{background: "#662e85", width: 1000, marginTop: 20}}>
                <h3 style={{textAlign: "center", color: "white", fontSize: 16,}}>Akışlar ve Detaylar</h3>
                <WGrid style={{background: "white", margin: 10}}>
                    <WList id="" component="nav" style={{backgroundColor: '#fff', margin: 0}}>
                        <WListItem>
                            <WListItemText primary="Trash" />
                        </WListItem>
                        <WListItem>
                            <WListItemText primary="Spam" />
                        </WListItem>
                    </WList>
                    <WDatePicker id="" label={t('date')} value={starterDate} onChange={value => setStarterDate(value)}/>
                    <WDatePicker id="" label={t('date')} style={{marginTop: 10,}} value={finisherDate} onChange={value => setFinisherDate(value)}/>
                    <WTextField label={t('description')} fullWidth={true}/>
                </WGrid>
            </WGrid>

        </WGrid>
    )
}

export default DelegationDetailComponent
