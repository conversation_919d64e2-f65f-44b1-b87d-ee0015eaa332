﻿<hibernate-configuration  xmlns="urn:nhibernate-configuration-2.2">
  <session-factory name="NHibernate.Test">
    <property name="connection.driver_class">NHibernate.Driver.OracleDataClientDriver</property>
    <property name="connection.connection_string">Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1521))(CONNECT_DATA=(SID=FLOWDEV)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR;Pooling=true;Statement Cache Size=10</property>
    <property name="show_sql">true</property>
    <property name="dialect">NHibernate.Dialect.Oracle10gDialect</property>
    <property name="query.substitutions">true 1, false 0, yes 'Y', no 'N'</property>
    <property name="proxyfactory.factory_class">NHibernate.ByteCode.LinFu.ProxyFactoryFactory, NHibernate.ByteCode.LinFu</property>
    <property name="show_sql">true</property>
    <property name="format_sql">true</property>

    <mapping assembly="Digiturk.WorkFlow.Entities" />
    <mapping assembly="Digiturk.Common.Entities" />
    <mapping assembly="Digiturk.Workflow.Digiflow.Entities" />
    <mapping assembly="Digiturk.Workflow.Digiflow.DataAccessLayer" />
    <event type="pre-update">
      <listener class="Digiturk.Workflow.Repository.AuditEventListener, Digiturk.Workflow.Repository" />
    </event>
    <event type="pre-insert">
      <listener class="Digiturk.Workflow.Repository.AuditEventListener, Digiturk.Workflow.Repository" />
    </event>
    <!--<event type="delete">
      <listener class="Digiturk.Workflow.Repository.SoftDeleteEventListener, Digiturk.Workflow.Repository" />
    </event>-->
  </session-factory>
</hibernate-configuration>