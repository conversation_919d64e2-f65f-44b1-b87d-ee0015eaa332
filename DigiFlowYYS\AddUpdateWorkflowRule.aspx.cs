﻿using DevExpress.Web.ASPxEditors;
using DevExpress.Web.ASPxGridView;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Web.UI;

public partial class AddUpdateWorkflowRule : YYSSecurePage
{
    /// <summary>
    /// İş Akış kurallarının oluşturulduğu düzenlendiği ekran
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        ((MasterPage)Master).ShowMenu(true);
        ((MasterPage)Master).PageTitle = "Akış Kuralı Tanımlama";

        if (!Page.IsPostBack)
        {
            #region İş Akış kuralı tanımlaması için gerekli olan tüm comboboxlar doldurulur

            long IsAdmin = 0;

            if (Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAndFlowAdmin.ToString() || Convert.ToString((YYSAdminType)Session[AdminUser]) == YYSAdminType.SystemAdmin.ToString())
            {
                IsAdmin = 1;
            }

            WorkfowListASPxComboBox = Common.FillComboboxWorkFlow("Name", "WorkflowDefId", WorkfowListASPxComboBox, WorkflowHelper.GetWorkflowsOfAdmin(UserInformation.LoginObject.LoginId, IsAdmin), "İş Akışı Seçiniz", "0");

            if (Request.QueryString["RuleId"] != null)
            {
                ((MasterPage)Master).PageTitle = "Akış Kuralı Düzenleme";
                DeleteButton.Visible = true;
                DeleteButton.Enabled = true;
                string wfId = Request.QueryString["RuleId"];
                Session["UpdatingWorkFlowRule"] = wfId;
                ActionAuthorization wfRule = ActionAuthorizationHelper.GetByWorkflowRuleID(ConvertionHelper.ConvertValue<long>(wfId));

                for (int i = 0; i < WorkfowListASPxComboBox.Items.Count; i++)
                {
                    if (WorkfowListASPxComboBox.Items[i].Value.ToString() == wfRule.WfDefId.ToString())
                    {
                        WorkfowListASPxComboBox.Items[i].Selected = true;
                        break;
                    }
                }
                WorkfowListASPxComboBox_SelectedIndexChanged(null, null);
                IsActiveCheckBox.Checked = ConvertionHelper.ConvertValue<Boolean>(wfRule.IsActive);
                // select state
                for (int i = 0; i < StateDropDownList.Items.Count; i++)
                {
                    if (StateDropDownList.Items[i].Value.ToString() == wfRule.StateDefId.ToString())
                    {
                        StateDropDownList.Items[i].Selected = true;
                        break;
                    }
                }
                StateDropDownList_SelectedIndexChanged(null, null);
                // select action
                for (int i = 0; i < ActionTypeDropDownList.Items.Count; i++)
                {
                    if (ActionTypeDropDownList.Items[i].Value.ToString() == wfRule.ActionId.ToString())
                    {
                        ActionTypeDropDownList.Items[i].Selected = true;
                        break;
                    }
                }
                ActionTypeDropDownList_SelectedIndexChanged(null, null);
                for (int i = 0; i < FromDropDownList.Items.Count; i++)
                {
                    if (FromDropDownList.Items[i].Value.ToString() == wfRule.SourceId.ToString())
                    {
                        FromDropDownList.Items[i].Selected = true;
                        break;
                    }
                }

                for (int i = 0; i < TargetDropDownList.Items.Count; i++)
                {
                    if (TargetDropDownList.Items[i].Value.ToString() == wfRule.ToGroupId.ToString())
                    {
                        TargetDropDownList.Items[i].Selected = true;
                        break;
                    }
                }

                foreach (ListEditItem item in WorkfowListASPxComboBox.Items)
                {
                    if (item.Value.ToString() == wfId)
                    {
                        item.Selected = true;
                        WorkfowListASPxComboBox.Enabled = false;

                        StateDropDownList.Enabled = true;
                        FromDropDownList.Enabled = true;
                        ActionTypeDropDownList.Enabled = true;

                        IsActiveCheckBox.Enabled = true;
                        SaveButton.Enabled = true;
                        DeleteButton.Visible = true;
                        DeleteButton.Enabled = true;

                        break;
                    }
                }
            }

            #endregion İş Akış kuralı tanımlaması için gerekli olan tüm comboboxlar doldurulur
        }
        //Sayfaya
        if (Page.IsCallback)
        {
            #region Kurallar Doldurulur

            if (Session["WfDefId"] != null && Session["WorkFlowRulesGridView"] != null)
            {
                // List<ActionAuthorization> items = ActionAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(Session["WfDefId"]));
                WorkFlowRulesGridView.DataSource = (DataTable)Session["WorkFlowRulesGridView"];
                WorkFlowRulesGridView.DataBind();
            }
            else
            {
                List<ActionAuthorization> items = ActionAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
                WorkFlowRulesGridView.DataSource = ConvertToDataTable(items);
                WorkFlowRulesGridView.DataBind();
            }

            #endregion Kurallar Doldurulur
        }
    }

    /// <summary>
    /// İş akışına göre state leri, kullanıcıları doldurur
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkfowListASPxComboBox_SelectedIndexChanged(object sender, EventArgs e)
    {
        #region Seçilen iş akışına göre state ler ve kullanıcılar doldurulur

        if (WorkfowListASPxComboBox.SelectedItem == null)
        {
            return;
        }

        bool enable = WorkfowListASPxComboBox.SelectedItem != null;
        StateDropDownList.Enabled = enable;
        FromDropDownList.Enabled = enable;
        TargetDropDownList.Enabled = enable;
        ActionTypeDropDownList.Enabled = enable;
        IsActiveCheckBox.Enabled = enable;
        SaveButton.Enabled = enable;

        if (enable)
        {
            long WfDefId = ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value);
            List<LogicalGroup> lgList = LogicalGroupHelper.GetAllByWorkflowId(WfDefId);
            FromDropDownList = Common.FillComboboxLogicalGroup("Name", "RequestId", FromDropDownList, lgList, "Mantıksal Grup Seçiniz", "0");
            TargetDropDownList = Common.FillComboboxLogicalGroup("Name", "RequestId", TargetDropDownList, lgList, "Mantıksal Grup Seçiniz", "0");
            StateDropDownList = Common.FillComboboxWorkFlowState("Name", "WorkflowStateId", StateDropDownList, WorkflowHelper.GetStates(WfDefId), "Adım Seçiniz", "0");
        }

        #endregion Seçilen iş akışına göre state ler ve kullanıcılar doldurulur

        #region O akışın kuralları listelenir

        List<ActionAuthorization> items = ActionAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
        DataTable dtRules = ConvertToDataTable(items);
        WorkFlowRulesGridView.DataSource = dtRules;
        Session["WorkFlowRulesGridView"] = dtRules;
        WorkFlowRulesGridView.DataBind();
        Session["WfDefId"] = WorkfowListASPxComboBox.SelectedItem.Value;

        #endregion O akışın kuralları listelenir
    }

    /// <summary>
    /// İş kuralı kaydetmeyi sağlar
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void SaveButton_Click(object sender, EventArgs e)
    {
        try
        {
            if (WorkfowListASPxComboBox.SelectedIndex == 0)
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Kural kaydetmek için bir akış seçmeniz gerekli.", true, string.Empty);
                return;
            }
            if (StateDropDownList.SelectedIndex == 0)
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Akış aşaması alanı boş bırakılamaz.", true, string.Empty);
                return;
            }

            if (FromDropDownList.SelectedIndex == 0)
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Kim alanı boş bırakılamaz.", true, string.Empty);
                return;
            }
            if (TargetDropDownList.SelectedIndex == 0 && TargetDropDownList.Enabled)
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Hedef alanı boş bırakılamaz.", true, string.Empty);
                return;
            }

            if (ActionTypeDropDownList.SelectedIndex == 0)
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "İşlem türü alanı boş bırakılamaz.", true, string.Empty);
                return;
            }
            ActionAuthorization rule = new ActionAuthorization();
            if (Session["UpdatingWorkFlowRule"] != null)
            {
                long ruleId = ConvertionHelper.ConvertValue<long>(Session["UpdatingWorkFlowRule"]);
                rule = ActionAuthorizationHelper.GetByWorkflowRuleID(ruleId);
                if (rule == null)
                {
                    rule = new ActionAuthorization();
                }
            }
            else
            {
                rule = new ActionAuthorization();
                rule.WfDefId = ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value);
            }
            long ToGroupId = 0;
            if (TargetDropDownList.Enabled)
            {
                long.TryParse(TargetDropDownList.SelectedItem.Value.ToString(), out ToGroupId);
            }
            rule.ToGroupId = ToGroupId;
            rule.Created = DateTime.Now;
            rule.CreatedBy = UserInformation.LoginObject.LoginId;
            rule.LastUpdated = DateTime.Now;
            rule.LastUpdatedBy = UserInformation.LoginObject.LoginId;
            rule.StateDefId = ConvertionHelper.ConvertValue<long>(StateDropDownList.SelectedItem.Value);
            rule.IsActive = ConvertionHelper.ConvertValue<long>(IsActiveCheckBox.Checked);
            rule.SourceId = ConvertionHelper.ConvertValue<long>(FromDropDownList.SelectedItem.Value.ToString());
            rule.ActionId = ConvertionHelper.ConvertValue<long>(ActionTypeDropDownList.SelectedItem.Value);

            if (Session["UpdatingWorkFlowRule"] == null)
            {
                ActionAuthorization ac = ActionAuthorizationHelper.IsDefined(rule);
                if (ac == null)
                {
                    ActionAuthorizationHelper.AddNewWorkflowRule(rule);
                    ((MasterPage)Master).ShowPopup(false, "Kural Tanımlandı", "Kural başarıyla oluşturuldu.", false, string.Empty);
                }
                else
                {
                    rule.RequestId = ac.RequestId;
                    ActionAuthorizationHelper.Update(rule);
                    ((MasterPage)Master).ShowPopup(false, "Kural Güncellendi", "Kural başarıyla güncellendi.", false, string.Empty);
                }
            }
            else
            {
                ActionAuthorizationHelper.Update(rule);
                ((MasterPage)Master).ShowPopup(false, "Kural Güncellendi", "Kural başarıyla güncellendi.", false, string.Empty);
                Session["UpdatingWorkFlowRule"] = null;
            }
            //Grid tekrar doldurulur
            FillGrid();
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowPopup(false, "Hata", "İşlem sırasında bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.Message);
        }
    }

    /// <summary>
    /// Ekrandaki iş kuralını silmemizi sağlar
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DeleteButton_Click(object sender, EventArgs e)
    {
        try
        {
            if (Session["UpdatingWorkFlowRule"] != null)
            {
                long ruleId = ConvertionHelper.ConvertValue<long>(Session["UpdatingWorkFlowRule"]);
                ActionAuthorizationHelper.DeleteWorkflowRule(ruleId);
                ((MasterPage)Master).ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
                DeleteButton.Visible = false;
                // SaveButton.Enabled = false;
                Session["UpdatingWorkFlowRule"] = null;
                FillGrid();
                Clear();
            }
            else
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Bu kuralı tanımlı olup olmadığı belirlenemediğinden silinemedi, Listeden silmeyi deneyiniz", true, "");
            }
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowPopup(false, "Hata", "İşlem sırasında bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.Message);
        }
    }

    /// <summary>
    /// Eğer action Yönlendirme ve Yoruma Gönderme değil ise kime alanını pasif hale getirir
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void ActionTypeDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        //Except Forward and SendToComment, TargetDropDownList must be disabled
        if (ActionTypeHelper.GetActionTypeName(ConvertionHelper.ConvertValue<long>(ActionTypeDropDownList.SelectedItem.Value)).IsToGroup == 1)
        {
            TargetDropDownList.Enabled = true;
            TargetDropDownList.SelectedIndex = 0;
        }
        else
        {
            TargetDropDownList.SelectedIndex = -1;
            TargetDropDownList.Enabled = false;
        }
    }

    /// <summary>
    /// State(Adım) ın durumuna göre action ları getirmemizi sağlar
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void StateDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (StateDropDownList.SelectedIndex != 0)
        {
            string StateDef = WorkflowHelper.GetState(ConvertionHelper.ConvertValue<long>(StateDropDownList.SelectedItem.Value)).StateType;
            ActionTypeDropDownList = Common.FillCombobox("ACTION_TYPE_NAME", "ACTION_TYPE_ID", ActionTypeDropDownList, ActionTypeHelper.GetActionTypes(StateDef), "Aksiyon Seçiniz", "0");
        }
    }

    #region Inner Functions

    /// <summary>
    /// İş akışına göre grid doldurulur
    /// </summary>
    private void FillGrid()
    {
        List<ActionAuthorization> items = ActionAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
        DataTable dtRules = ConvertToDataTable(items);
        WorkFlowRulesGridView.DataSource = dtRules;
        Session["WorkFlowRulesGridView"] = dtRules;
        WorkFlowRulesGridView.DataBind();
    }

    /// <summary>
    /// Grid teki int değerlerin isim karşılıklarını bulup değiştirir
    /// </summary>
    /// <param name="items">Action authorization nesneleri</param>
    /// <returns></returns>
    private DataTable ConvertToDataTable(List<ActionAuthorization> items)
    {



        DataTable dt = new DataTable();
        dt.Columns.Add("Id");
        dt.Columns.Add("State");
        dt.Columns.Add("Source");
        dt.Columns.Add("Action");
        dt.Columns.Add("ToGroup");
        dt.Columns.Add("Active");
        dt.AcceptChanges();

        foreach (ActionAuthorization item in items)
        {
            try
            {
                DataRow r = dt.NewRow();
                r["Id"] = item.RequestId;
                r["State"] = WorkflowHelper.GetState(item.StateDefId).Name;
                r["Source"] = ConvertUser(item.SourceId);
                r["Action"] = ConvertActionType(item.ActionId);

                if (item.ToGroupId != 0)
                {
                    r["ToGroup"] = ConvertUser(item.ToGroupId);
                }

                r["Active"] = ConvertIsActive(ConvertionHelper.ConvertValue<Boolean>(item.IsActive));

                dt.Rows.Add(r);
            }
            catch (Exception)
            {

                throw;
            }
        }

        dt.AcceptChanges();
        return dt;


    }

    /// <summary>
    /// Grid teki Int olan isActive değerini Aktif veya Pasif olarak değiştirir
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private string ConvertIsActive(bool val)
    {
        return val ? "Evet" : "Hayır";
    }

    /// <summary>
    /// Grid teki int olan ActionId değerini Action adı olarak değiştirir
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private string ConvertActionType(long val)
    {
        string ret = string.Empty;

        var lg = ActionTypeHelper.GetActionTypeName(val);
        ret = lg.ActionTypeName;
        return ret;
    }

    /// <summary>
    /// Grid teki loginId değerinin karşılığı olan isme çevirir
    /// </summary>
    /// <param name="val"></param>
    /// <returns></returns>
    private string ConvertUser(long val)
    {
        string ret = string.Empty;

        var lg = LogicalGroupHelper.Get(val);
        ret = lg.Name;

        return ret;
    }

    /// <summary>
    /// Combobox lardaki değerleri ilk değerine getiren fonksiyonu çağırır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Clear_Click(object sender, EventArgs e)
    {
        Clear();
    }

    /// <summary>
    /// Combobox lardaki değerleri ilk değerine getirir
    /// </summary>
    private void Clear()
    {
        StateDropDownList.SelectedIndex = 0;
        FromDropDownList.SelectedIndex = 0;
        ActionTypeDropDownList.SelectedIndex = 0;
        TargetDropDownList.SelectedIndex = 0;
    }

    #endregion Inner Functions

    /// <summary>
    /// Silme işlemi
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowRulesGridView_RowDeleting(object sender, DevExpress.Web.Data.ASPxDataDeletingEventArgs e)
    {
        // long ruleId = ConvertionHelper.ConvertValue<long>(e.Values[5]);
        // ASPxGridView gridView = (ASPxGridView)sender;

        // ActionAuthorizationHelper.DeleteWorkflowRule(ruleId);
        // ((MasterPage)Master).ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
        //// DeleteButton.Enabled = false;
        // // SaveButton.Enabled = false;
        // FillGrid();
        // gridView.CancelEdit();
        // e.Cancel = true;
    }

    /// <summary>
    /// Düzenle butonuna basıldığı zaman üstteki combobox ları doldurur
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowRulesGridView_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
    {
        string ruleId = e.KeyValue.ToString();
        Session["DeletingWorkFlowRule"] = ruleId;
        Session["UpdatingWorkFlowRule"] = ruleId;
        DeleteButton.Visible = true;
        ActionAuthorization wfRule = ActionAuthorizationHelper.GetByWorkflowRuleID(ConvertionHelper.ConvertValue<long>(ruleId));

        for (int i = 0; i < WorkfowListASPxComboBox.Items.Count; i++)
        {
            if (WorkfowListASPxComboBox.Items[i].Value.ToString() == wfRule.WfDefId.ToString())
            {
                WorkfowListASPxComboBox.Items[i].Selected = true;
                break;
            }
        }
        WorkfowListASPxComboBox_SelectedIndexChanged(null, null);
        IsActiveCheckBox.Checked = ConvertionHelper.ConvertValue<Boolean>(wfRule.IsActive);
        // select state
        for (int i = 0; i < StateDropDownList.Items.Count; i++)
        {
            if (StateDropDownList.Items[i].Value.ToString() == wfRule.StateDefId.ToString())
            {
                StateDropDownList.Items[i].Selected = true;
                break;
            }
        }
        StateDropDownList_SelectedIndexChanged(null, null);

        // select action
        for (int i = 0; i < ActionTypeDropDownList.Items.Count; i++)
        {
            if (ActionTypeDropDownList.Items[i].Value.ToString() == wfRule.ActionId.ToString())
            {
                ActionTypeDropDownList.Items[i].Selected = true;
                break;
            }
        }
        ActionTypeDropDownList_SelectedIndexChanged(null, null);
        for (int i = 0; i < FromDropDownList.Items.Count; i++)
        {
            if (FromDropDownList.Items[i].Value.ToString() == wfRule.SourceId.ToString())
            {
                FromDropDownList.Items[i].Selected = true;
                break;
            }
        }

        for (int i = 0; i < TargetDropDownList.Items.Count; i++)
        {
            if (TargetDropDownList.Items[i].Value.ToString() == wfRule.ToGroupId.ToString())
            {
                TargetDropDownList.Items[i].Selected = true;
                break;
            }
        }

        foreach (ListEditItem item in WorkfowListASPxComboBox.Items)
        {
            if (item.Value.ToString() == ruleId)
            {
                item.Selected = true;
                WorkfowListASPxComboBox.Enabled = false;

                StateDropDownList.Enabled = true;
                FromDropDownList.Enabled = true;
                ActionTypeDropDownList.Enabled = true;

                IsActiveCheckBox.Enabled = true;
                SaveButton.Enabled = true;
                DeleteButton.Visible = true;
                DeleteButton.Enabled = true;
                break;
            }
        }
    }

    /// <summary>
    /// Silme işlemi burada yapılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DeleteRuleButton_Click(object sender, EventArgs e)
    {
        long ruleId = ConvertionHelper.ConvertValue<long>(Session["DeletingWorkFlowRule"]);
        ActionAuthorizationHelper.DeleteWorkflowRule(ruleId);
        ((MasterPage)Master).ShowPopup(false, "Kural Silindi", "Kural silindi.", false, string.Empty);
        DeleteButton.Visible = false;
        FillGrid();
        Session["UpdatingWorkFlowRule"] = null;
        Clear();
    }

    /// <summary>
    /// Sayfalamada datayı daha hızlı getirmek için kullanılır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void WorkFlowRulesGridViewPageIndexChanging(object sender, EventArgs e)
    {
        DataTable dtInboxGridView = new DataTable();
        if (Session["WorkFlowRulesGridView"] != null)
        {
            dtInboxGridView = (DataTable)Session["WorkFlowRulesGridView"];
        }
        else
        {
            List<ActionAuthorization> items = ActionAuthorizationHelper.GetByWorkflowId(ConvertionHelper.ConvertValue<long>(WorkfowListASPxComboBox.SelectedItem.Value));
            dtInboxGridView = ConvertToDataTable(items);
            WorkFlowRulesGridView.DataSource = dtInboxGridView;
            Session["WorkFlowRulesGridView"] = dtInboxGridView;
        }
        WorkFlowRulesGridView.DataSource = dtInboxGridView;
        WorkFlowRulesGridView.DataBind();
        dtInboxGridView.Dispose();
        dtInboxGridView = null;
    }
}