﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System.Collections.Generic;
using System.Data;

/// <summary>
/// Mantıksal gruplar için kullanılan ve kullanılacak olan fonksiyonları barındırır
/// </summary>
public class LogicalGroupHelper
{

    public static string GetFlowName(long FlowDefId)
    {
        string query = "Select * from FRAMEWORK.F_WF_WORKFLOW_DEF where WF_WORKFLOW_DEF_ID="+FlowDefId+"";
        DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);
        string result = "";
        if (dt != null && dt.Rows.Count>0)
        {
            result = dt.Rows[0]["NAME"].ToString();
        }
        return result;
    }
    /// <summary>
    /// Returns all Logical Groups in SQL Server.
    /// </summary>
    /// <returns></returns>
    public static List<LogicalGroup> GetAdWithlogicalGroups()
    {
        List<LogicalGroup> ret = new List<LogicalGroup>();
        string query = "Select * from DT_WORKFLOW.YYS_LOGICAL_GROUPS Where AD_GROUP IS NOT NULL Order By DT_WORKFLOW.YYS_LOGICAL_GROUPS.LOGICAL_GROUP_ID DESC";
        DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);

        if (dt != null)
        {
            foreach (DataRow row in dt.Rows)
            {
                LogicalGroup lg = ConvertDataRow(row);

                if (!ret.Contains(lg))
                {
                    ret.Add(lg);
                }
            }
        }

        return ret;
    }

    /// <summary>
    /// Returns all Logical Groups in SQL Server.
    /// </summary>
    /// <returns></returns>
    public static List<LogicalGroup> GetAlllogicalGroups()
    {
        List<LogicalGroup> ret = new List<LogicalGroup>();
        string query = "select * from DT_WORKFLOW.YYS_LOGICAL_GROUPS";
        DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);

        if (dt != null)
        {
            foreach (DataRow row in dt.Rows)
            {
                LogicalGroup lg = ConvertDataRow(row);

                if (!ret.Contains(lg))
                {
                    ret.Add(lg);
                }
            }
        }

        return ret;
    }

    /// <summary>
    /// Belirtilen iş akışında bu mantıksal grup var mı yok mu kontrolü yapılır
    /// </summary>
    /// <param name="name"></param>
    /// <param name="workFlowId"></param>
    /// <returns></returns>
    public static bool IsSameGroupExistsInSameWorkflow(string name, long workFlowId)
    {
        bool retVal = false;

        string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUPS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUPS.WF_DEF_ID=:WF_DEF_ID AND DT_WORKFLOW.YYS_LOGICAL_GROUPS.NAME=:NAME";
        OracleParameter[] p = new OracleParameter[2];
        p[0] = new OracleParameter("WF_DEF_ID", workFlowId);
        p[1] = new OracleParameter("NAME", name);
        DataTable dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

        if (dt.Rows.Count > 1)
        {
            retVal = true;
        }
        return retVal;
    }

    /// <summary>
    /// Belirtilen id ile eşleşen logical group değerini döner
    /// </summary>
    /// <param name="logicalGroupId">Logical Group Id</param>
    /// <returns>Belirtilen id ile eşleşen logical group değerini döner</returns>
    public static LogicalGroup Get(long logicalGroupId)
    {
        string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUPS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUPS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID ";
        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
        dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
        if (dt != null && dt.Rows.Count == 0)
        {
            return null;
        }

        return ConvertDataRow(dt.Rows[0]);
    }

    /// <summary>
    /// Belirtilen id ile eşleşen logical group değerini döner
    /// </summary>
    /// <param name="logicalGroupId">Logical Group Id</param>
    /// <returns>Belirtilen id ile eşleşen logical group değerini döner</returns>
    public static List<LogicalGroup> GetAllByWorkflowId(long workflowId)
    {
        string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUPS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUPS.WF_DEF_ID=:WF_DEF_ID order by DT_WORKFLOW.YYS_LOGICAL_GROUPS.NAME";
        DataTable dt = new DataTable();
        List<LogicalGroup> allLg = new List<LogicalGroup>();

        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("WF_DEF_ID", workflowId);
        dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

        if (dt != null && dt.Rows.Count > 0)
        {
            foreach (DataRow dr in dt.Rows)
            {
                LogicalGroup lg = ConvertDataRow(dr);

                if (!allLg.Contains(lg))
                {
                    allLg.Add(lg);
                }
            }
        }
        return allLg;
    }

    /// <summary>
    /// Verilen datarow'daki değerleri kullanarak bir LogicalGroup nesnesi oluşturur
    /// </summary>
    /// <param name="row"></param>
    /// <returns>WorkflowAdmin nesnesinde kullanılacak değerlerin bulunduğu datarow</returns>
    public static LogicalGroup ConvertDataRow(DataRow row)
    {
        LogicalGroup lg = new LogicalGroup();
        lg.RequestId = ConvertionHelper.ConvertValue<long>(row["LOGICAL_GROUP_ID"]);
        lg.WfDefId = ConvertionHelper.ConvertValue<long>(row["WF_DEF_ID"]);
        lg.Name = row["NAME"].ToString();
        lg.Description = row["DESCRIPTION"].ToString();
        lg.AdDomainName = row["AD_DOMAIN"].ToString();
        lg.AdGroupName = row["AD_GROUP"].ToString();
        lg.CanBeDeleted = ConvertionHelper.ConvertValue<long>(row["CAN_BE_DELETED"].ToString());
        lg.IsOnePersonGroup = ConvertionHelper.ConvertValue<long>(row["IS_ONE_PERSON_GROUP"].ToString());
        return lg;
    }

    /// <summary>
    /// Bir tane yeni Mantıksal Grup tanımlamaya yarar
    /// </summary>
    /// <param name="workflowId"></param>
    /// <param name="lg"></param>
    /// <returns></returns>
    public static string AddNewLogicalGroup(LogicalGroup lg)
    {
        string query = @"INSERT INTO DT_WORKFLOW.YYS_LOGICAL_GROUPS (
    WF_DEF_ID, NAME,DESCRIPTION, CAN_BE_DELETED, IS_ONE_PERSON_GROUP,CREATED, CREATED_BY, LAST_UPDATED, LAST_UPDATED_BY)
VALUES (:WF_DEF_ID,:NAME,:DESCRIPTION,:CAN_BE_DELETED,:IS_ONE_PERSON_GROUP,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY) returning LOGICAL_GROUP_ID into :LOGICAL_GROUP_ID";
        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[10];
        p[0] = new OracleParameter("WF_DEF_ID", lg.WfDefId);
        p[1] = new OracleParameter("NAME", lg.Name);
        p[2] = new OracleParameter("DESCRIPTION", lg.Description);
        p[3] = new OracleParameter("CAN_BE_DELETED", lg.CanBeDeleted);
        p[4] = new OracleParameter("IS_ONE_PERSON_GROUP", lg.IsOnePersonGroup);
        p[5] = new OracleParameter("CREATED", lg.Created);
        p[6] = new OracleParameter("CREATED_BY", lg.CreatedBy);
        p[7] = new OracleParameter("LAST_UPDATED", lg.LastUpdated);
        p[8] = new OracleParameter("LAST_UPDATED_BY", lg.LastUpdatedBy);
        p[9] = new OracleParameter("LOGICAL_GROUP_ID", OracleDbType.Int64);
        p[9].Direction = ParameterDirection.Output;

        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection, ExecuteNonQueryType.ReturnTheIdentity);
    }

    /// <summary>
    /// Mantıksal Grup Düzeltme işlemi yapar
    /// </summary>
    /// <param name="lg">LogicalGroup nesnesi</param>
    /// <returns>Başarılı Kayıt Sayısını döner</returns>
    public static int UpdateLogicalGroup(LogicalGroup lg)
    {
        string query = @"UPDATE DT_WORKFLOW.YYS_LOGICAL_GROUPS
SET
       WF_DEF_ID           = :WF_DEF_ID,
       NAME                = :NAME,
       DESCRIPTION         = :DESCRIPTION,
       CAN_BE_DELETED      = :CAN_BE_DELETED,
       IS_ONE_PERSON_GROUP = :IS_ONE_PERSON_GROUP,
       AD_DOMAIN = :AD_DOMAIN,
       AD_GROUP = :AD_GROUP,
       LAST_UPDATED        = :LAST_UPDATED,
       LAST_UPDATED_BY     = :LAST_UPDATED_BY
       WHERE  LOGICAL_GROUP_ID    = :LOGICAL_GROUP_ID";
        DataTable dt = new DataTable();
        OracleParameter[] p = new OracleParameter[10];

        p[0] = new OracleParameter("WF_DEF_ID", lg.WfDefId);
        p[1] = new OracleParameter("NAME", lg.Name.Trim());
        p[2] = new OracleParameter("DESCRIPTION", lg.Description.Trim());
        p[3] = new OracleParameter("CAN_BE_DELETED", lg.CanBeDeleted);
        p[4] = new OracleParameter("IS_ONE_PERSON_GROUP", lg.IsOnePersonGroup);
        p[5] = new OracleParameter("AD_DOMAIN", lg.AdDomainName);
        p[6] = new OracleParameter("AD_GROUP", lg.AdGroupName);
        p[7] = new OracleParameter("LAST_UPDATED", lg.LastUpdated);
        p[8] = new OracleParameter("LAST_UPDATED_BY", lg.LastUpdatedBy);
        p[9] = new OracleParameter("LOGICAL_GROUP_ID", lg.RequestId);

        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }

    /// <summary>
    /// Belirtilen id ile eşleşen logical group kaydını siler
    /// </summary>
    /// <param name="logicalGroupId">LogicalGroupId değeri</param>
    /// <returns>Etkilenen satır sayısı</returns>
    public static int DeleteLogicalGroup(long logicalGroupId)
    {
        string query = @"DELETE FROM DT_WORKFLOW.YYS_LOGICAL_GROUPS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUPS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID";
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
        return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
    }

    /// <summary>
    /// Bu mantıksal grubun bağlı olduğu herhangi bir kural var mı yok mu kontrolünün yapıldığı yer
    /// </summary>
    /// <param name="logicalGroupId">Mantıksal grup Id</param>
    /// <returns>Kural var ise true yok ise false döner</returns>
    public static bool IsExistActionAuthorizationRule(long logicalGroupId)
    {
        string query = @"SELECT * FROM DT_WORKFLOW.YYS_ACTION_AUTHORIZATION WHERE DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.SOURCE_ID=:TO_GROUP_ID OR DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.TO_GROUP_ID=:TO_GROUP_ID ";
        OracleParameter[] p = new OracleParameter[1];
        p[0] = new OracleParameter("TO_GROUP_ID", logicalGroupId);
        DataTable dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
        if (dt.Rows.Count > 0)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
}