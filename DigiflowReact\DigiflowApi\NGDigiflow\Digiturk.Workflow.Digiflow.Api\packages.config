﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net45" />
  <package id="bootstrap" version="3.4.1" targetFramework="net45" />
  <package id="jQuery" version="3.4.1" targetFramework="net45" />
  <package id="Microsoft.AspNet.Cors" version="5.3.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.2" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi" version="5.3.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Client" version="6.0.0" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.3.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.3.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.3.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.3.0" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net452" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net45" />
  <package id="Modernizr" version="2.8.3" targetFramework="net45" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net45" />
  <package id="Newtonsoft.Json.Bson" version="1.0.2" targetFramework="net45" />
  <package id="Swashbuckle" version="5.6.0" targetFramework="net452" />
  <package id="Swashbuckle.Core" version="5.6.0" targetFramework="net452" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net45" />
  <package id="System.Memory" version="4.5.5" targetFramework="net45" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net45" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net45" />
  <package id="WebActivatorEx" version="2.0" targetFramework="net452" />
  <package id="WebGrease" version="1.6.0" targetFramework="net45" />
</packages>