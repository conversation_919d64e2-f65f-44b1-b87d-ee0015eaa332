﻿using System;
using System.Web.UI;
using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System.Data;
using System.Web.UI.WebControls;
using Digiturk.Workflow.Digiflow.Authorization;
using Digiturk.Workflow.Digiflow.CoreHelpers;


public partial class UserControls_OrganizationTreeWebUserControl2 : System.Web.UI.UserControl
{
    #region Field and Properties

    // Yönlendirme panelinden geliyor ise aynı hiyerarşik seviyede olan kişileri göstereceğiz
    private bool _yonlendirPaneli = false;

    public bool yonlendirPaneli
    {
        get { return _yonlendirPaneli; }
        set { _yonlendirPaneli = value; }
    }


    // yoruma gönderme panelinden geliyor ise aynı hiyerarşik seviyede olan kişileri göstereceğiz
    private bool _yorumaGonderPaneli = false;

    public bool yorumaGonderPaneli
    {
        get { return _yorumaGonderPaneli; }
        set { _yorumaGonderPaneli = value; }
    }


    /// <summary>
    /// DP_HR_DEPSID Gets
    /// </summary>
    public string HRDeptId
    {
        get
        {
            string DeptId = "99";
            if (SubTeamValue5 != "0" && !string.IsNullOrEmpty(SubTeamValue5))
            {
                DeptId = SubTeamValue5;
            }
            else
            if (SubTeamValue4 != "0" && !string.IsNullOrEmpty(SubTeamValue4))
            {
                DeptId = SubTeamValue4;
            }
            else
            if (SubTeamValue3 != "0" && !string.IsNullOrEmpty(SubTeamValue3))
            {
                DeptId = SubTeamValue3;
            }
            else
            if (SubTeamValue2 != "0" && !string.IsNullOrEmpty(SubTeamValue2))
            {
                DeptId = SubTeamValue2;
            }
            else
            if (SubTeamValue1 != "0" && !string.IsNullOrEmpty(SubTeamValue1))
            {
                DeptId = SubTeamValue1;
            }
            else
            if (TeamValue != "0" && !string.IsNullOrEmpty(TeamValue))
            {
                DeptId = TeamValue;
            }
            else if (this.UnitValue != "0" && !string.IsNullOrEmpty(UnitValue))
            {
                DeptId = UnitValue;
            }
            else if (DivisionValue != "0" && !string.IsNullOrEmpty(DivisionValue))
            {
                DeptId = DivisionValue;
            }
            else if (DepartmentId != "0" && !string.IsNullOrEmpty(DepartmentId))
            {
                DeptId = DepartmentId;
            }
            return DeptId;
        }
    }

    /// <summary>
    ///
    /// </summary>
    public bool ByPassDepartmentBinding { get; set; }

    /// <summary>
    ///
    /// </summary>
    public bool ValuesSet { get; set; }

    /// <summary>
    ///
    /// </summary>
    public FLogin ManagerInstance { get; set; }

    /// <summary>
    /// Id of selected department
    /// </summary>
    public string DepartmentId
    {
        get
        {
            try
            {
                return DepartmentDropDownList.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Name of selected department
    /// </summary>
    public string DepartmentName
    {
        get
        {
            try
            {
                return DepartmentDropDownList.SelectedItem.Text.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Id of selected division
    /// </summary>
    public string DivisionValue
    {
        get
        {
            try
            {
                return DivisionDropDownList.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Name of selected division
    /// </summary>
    public string DivisionName
    {
        get
        {
            try
            {
                return DivisionDropDownList.SelectedItem.Text.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Id of selected unit
    /// </summary>
    public string UnitValue
    {
        get
        {
            try
            {
                return UnitDropDownList.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Name of selected unit
    /// </summary>
    public string UnitName
    {
        get
        {
            try
            {
                return UnitDropDownList.SelectedItem.Text.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Id of selected team
    /// </summary>
    public string TeamValue
    {
        get
        {
            try
            {
                return TeamDropDownList.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    public string SubTeamValue5
    {
        get
        {
            try
            {
                return SubTeamDropDownList5.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    public string SubTeamValue4
    {
        get
        {
            try
            {
                return SubTeamDropDownList4.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    public string SubTeamValue3
    {
        get
        {
            try
            {
                return SubTeamDropDownList3.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    public string SubTeamValue2
    {
        get
        {
            try
            {
                return SubTeamDropDownList2.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    public string SubTeamValue1
    {
        get
        {
            try
            {
                return SubTeamDropDownList1.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }





    /// <summary>
    /// Name of selected team
    /// </summary>
    public string TeamName
    {
        get
        {
            try
            {
                return TeamDropDownList.SelectedItem.Text.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Name of manager
    /// </summary>
    public string ManagerName
    {
        get
        {
            return ManagerLabel.Text;
        }
    }

    /// <summary>
    /// Set the title of user control
    /// </summary>
    public string Title
    {
        set
        {
            TitleLabel.Text = value;
        }
    }

    /// <summary>
    /// Change the visibility of user panel
    /// </summary>
    public bool UserPanelVisible
    {
        get { return UserPanel.Visible; }
        set { OrganizationUpdatePanel.Update(); UserPanel.Visible = value; OrganizationUpdatePanel.Update(); }
    }

    /// <summary>
    /// Change the visibility of manager panel
    /// </summary>
    public bool ManagerPanelVisible
    {
        get { return ManagerPanel.Visible; }
        set { OrganizationUpdatePanel.Update(); ManagerPanel.Visible = value; OrganizationUpdatePanel.Update(); }
    }

    /// <summary>
    /// Id of selected user
    /// </summary>
    public string UserId
    {
        get
        {
            if (UserPanel.Visible)
            {
                return UserDropDownList.SelectedItem.Value;
            }

            return string.Empty;
        }
    }

    /// <summary>
    /// Name of manager
    /// </summary>
    public string Manager
    {
        get
        {
            if (ManagerPanel.Visible)
            {
                return ManagerLabel.Text;
            }

            return string.Empty;
        }
    }

    /// <summary>
    /// Kullanıcı dropdown kontrolünü kontrol eden validation kontrolünü yönetmek için kullanılır.
    /// </summary>
    public bool EnableUserDropdownValidation
    {
        get { return UserDropdownRequiredFieldValidator.Enabled; }
        set { UserDropdownRequiredFieldValidator.Enabled = value; }
    }

    /// <summary>
    /// Kullanıcının UserDropdowndan çıkartmak istediği user seçilerek çıkartılır.
    /// </summary>
    /// <param name="LoginId"></param>
    public void RemoveOwnUser(long LoginId)
    {
        for (int i = 0; i < UserDropDownList.Items.Count; i++)
        {
            if (UserDropDownList.Items[i].Value == LoginId.ToString())
            {
                UserDropDownList.Items.RemoveAt(i);
            }
        }
    }

    /// <summary>
    /// Dropdowndan çıkartılacak kullanıcıyı işaretler
    /// </summary>
    public long RemoveUser
    {
        get
        {
            if (Session["RemoveUser"] != null)
                return long.Parse(Session["RemoveUser"].ToString());
            return 0;
        }
        set
        {
            Session["RemoveUser"] = value.ToString();
        }
    }

    #endregion Field and Properties

    #region Events

    /// <summary>
    /// Department Dropdownlist Eventi
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DepartmentDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        OrganizationUpdatePanel.Update();
        OrganizationTreePanel.Enabled = false;

        UpdateDepartmentCombo();
        GetManager();

        if (UserPanel.Visible)
        {
            UpdateLoginsCombo();
        }
        OrganizationTreePanel.Enabled = true;
        OrganizationUpdatePanel.Update();
    }

    /// <summary>
    /// Division Dropdownlist Eventi
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DivisionDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        OrganizationUpdatePanel.Update();
        OrganizationTreePanel.Enabled = false;

        UpdateDivisionCombo();
        GetManager();
        UpdateLoginsCombo();
        OrganizationTreePanel.Enabled = true;
        OrganizationUpdatePanel.Update();
    }

    /// <summary>
    /// UnitDropdownList Event i
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void UnitDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        OrganizationUpdatePanel.Update();
        OrganizationTreePanel.Enabled = false;

        UpdateUnitCombo();
        GetManager();
        UpdateLoginsCombo();
        OrganizationTreePanel.Enabled = true;
        OrganizationUpdatePanel.Update();
    }

    /// <summary>
    /// Team DropdownListEvent i
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void TeamDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        OrganizationUpdatePanel.Update();
        OrganizationTreePanel.Enabled = false;
        string drpId = (sender as DropDownList).ID;
        switch (drpId)
        {
            case "TeamDropDownList":
                UpdateTeamCombo();
                break;
            case "SubTeamDropDownList1":
                UpdateSubTeamCombo1();
                break;
            case "SubTeamDropDownList2":
                UpdateSubTeamCombo2();
                break;
            case "SubTeamDropDownList3":
                UpdateSubTeamCombo3();
                break;
            case "SubTeamDropDownList4":
                UpdateSubTeamCombo4();
                break;
            //case "SubTeamDropDownList5":
            //    UpdateTeamCombo();
            //    break;
            default:
                break;
        }
        //  UpdateTeamCombo();
        GetManager();
        UpdateLoginsCombo();
        OrganizationTreePanel.Enabled = true;
        OrganizationUpdatePanel.Update();
    }


    //protected void SubTeamTeamDropDownList1_SelectedIndexChanged(object sender, EventArgs e)
    //{
    //    OrganizationUpdatePanel.Update();
    //    OrganizationTreePanel.Enabled = false;

    //    GetManager();
    //    UpdateLoginsCombo();
    //    OrganizationTreePanel.Enabled = true;
    //    OrganizationUpdatePanel.Update();
    //}


    //usercontrol sayfasından ana sayfaya event gönderimi
    public delegate void ChildControlDelegate(string LoginId);

    public event ChildControlDelegate GetDataFromChild;

    /// <summary>
    /// UserDropDownList Event i
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void UserDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (GetDataFromChild != null)
        {
            GetDataFromChild(UserDropDownList.SelectedValue.ToString());
        }
    }

    /// <summary>
    /// Page Load
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsCallback)
        {
            if (!Page.IsPostBack)
            {
                if (!ByPassDepartmentBinding)
                {
                    DepartmentDropDownList.Enabled = true;
                    DepartmentDropDownList.DataSource = WfDataHelpers.GetDeparmentList();
                    DepartmentDropDownList.DataTextField = "LOGIN_GROUP_NAME";
                    DepartmentDropDownList.DataValueField = "LOGIN_GROUP_ID";
                    DepartmentDropDownList.DataBind();
                    DepartmentDropDownList.SelectedIndex = -1;
                    DivisionDropDownList.Items.Clear();
                    UnitDropDownList.Items.Clear();
                    TeamDropDownList.Items.Clear();
                    SubTeamDropDownList1.Items.Clear();
                    SubTeamDropDownList2.Items.Clear();
                    SubTeamDropDownList3.Items.Clear();
                    SubTeamDropDownList4.Items.Clear();
                    SubTeamDropDownList5.Items.Clear();

                    Session["RemoveUser"] = null;
                }
                UserPanel.Visible = UserPanelVisible;
                ManagerPanel.Visible = ManagerPanelVisible;
            }
        }
        if (yonlendirPaneli) // Yönlendir panelinden geldiyse aynı üste sahip kişiler getirilecek.
        {
            DepartmentDropDownList.Enabled = false;
            DivisionDropDownList.Enabled = false;
            UnitDropDownList.Enabled = false;
            TeamDropDownList.Enabled = false;
            SubTeamDropDownList1.Enabled = false;
            SubTeamDropDownList2.Enabled = false;
            SubTeamDropDownList3.Enabled = false;
            SubTeamDropDownList4.Enabled = false;
            SubTeamDropDownList5.Enabled = false;
            OrganizationUpdatePanel.Update();
        }
    }

    #endregion Events

    #region Public Methodlar

    /// <summary>
    /// Kullanıcı Set Etmek için Kullanılır
    /// </summary>
    /// <param name="LoginId"></param>
    /// <param name="IsControlEnable"></param>
    public void SetUser(long LoginId, bool IsControlEnable)
    {
        //LoginDto=WorkflowLoginHelper
        ManagerPanelVisible = false;
        UserPanelVisible = true;
        ByPassDepartmentBinding = true;
        LoginDto LoginObj = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDetailsOfLogin(LoginId);
        SetValues(LoginObj.DepartmentId, LoginObj.DivisionId, LoginObj.UnitId, LoginObj.TeamId, "", false, LoginObj.LoginId, true, true, LoginObj.SubTeam_1_Id, LoginObj.SubTeam_2_Id, LoginObj.SubTeam_3_Id, LoginObj.SubTeam_4_Id, LoginObj.SubTeam_5_Id);
        LoginObj = null;
        SetControlStatus(IsControlEnable, IsControlEnable, IsControlEnable, IsControlEnable, IsControlEnable, IsControlEnable, IsControlEnable, IsControlEnable, IsControlEnable, IsControlEnable);
        
    }

    /// <summary>
    /// Sets page mode
    /// </summary>
    /// <param name="readOnly">Is the control will be loaded in read-only mode</param>
    public void SetPageMode(bool readOnly)
    {
        DepartmentDropDownList.Enabled = !readOnly;
        DivisionDropDownList.Enabled = !readOnly;
        UnitDropDownList.Enabled = !readOnly;
        TeamDropDownList.Enabled = !readOnly;
        SubTeamDropDownList1.Enabled = !readOnly;
        SubTeamDropDownList2.Enabled = !readOnly;
        SubTeamDropDownList3.Enabled = !readOnly;
        SubTeamDropDownList4.Enabled = !readOnly;
        SubTeamDropDownList5.Enabled = !readOnly;
        if (UserPanel.Visible)
        {
            UserDropDownList.Enabled = !readOnly;
        }
       
        OrganizationUpdatePanel.Update();
    }

    /// <summary>
    /// Sets values for controls
    /// </summary>
    /// <param name="department">Name of department</param>
    /// <param name="division">Name of division</param>
    /// <param name="unit">Name of unit</param>
    /// <param name="team">Name of team</param>
    /// <param name="manager">Name of manager</param>
    /// <param name="managerVisible">Manager is visible</param>
    /// <param name="employee">Name of employee</param>
    /// <param name="employeeVisible">Employee is visible</param>
    public void SetValues(long department, long division, long unit, long team, string manager, bool managerVisible, long employee, bool employeeVisible, bool allowAssignmentToOwnDivision, long subTeam1, long subTeam2, long subTeam3, long subTeam4, long subTeam5)
    {
        if (department == 0)
        {
            department = division;
            division = unit;
            unit = team;
            team = 0;
            subTeam1 = 0;
            subTeam2 = 0;
            subTeam3 = 0;
            subTeam4 = 0;
            subTeam5 = 0;
        }
        if (subTeam1 == 0)
            SubTeamPanel1.Visible = false;

        if (subTeam2 == 0)
            SubTeamPanel2.Visible = false;

        if (subTeam3 == 0)
            SubTeamPanel3.Visible = false;

        if (subTeam4 == 0)
            SubTeamPanel4.Visible = false;

        if (subTeam5 == 0)
            SubTeamPanel5.Visible = false;

        ValuesSet = true;
        DepartmentDropDownList.Items.Clear();
        DepartmentDropDownList.Enabled = true;
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            DepartmentDropDownList.DataSource = WfDataHelpers.GetDeparmentListEng();
        }
        else
        {
            DepartmentDropDownList.DataSource = WfDataHelpers.GetDeparmentList();
        }
        DepartmentDropDownList.DataTextField = "LOGIN_GROUP_NAME";
        DepartmentDropDownList.DataValueField = "LOGIN_GROUP_ID";
        DepartmentDropDownList.DataBind();
        DepartmentDropDownList.SelectedIndex = -1;
        DivisionDropDownList.Items.Clear();
        UnitDropDownList.Items.Clear();
        TeamDropDownList.Items.Clear();
        SubTeamDropDownList1.Items.Clear();
        SubTeamDropDownList2.Items.Clear();
        SubTeamDropDownList3.Items.Clear();
        SubTeamDropDownList4.Items.Clear();
        SubTeamDropDownList5.Items.Clear();

        if (department != 0)
        {
            try
            {
                DepartmentDropDownList.SelectedValue = department.ToString();
            }
            catch (Exception ex)
            {
            }

            DepartmentDropDownList.Enabled = false;
            UpdateDepartmentCombo();
        }

        if (division != 0 && department != 0)
        {
            try
            {
                DivisionDropDownList.SelectedValue = division.ToString();
            }
            catch (Exception ex)
            {
            }
            DivisionDropDownList.Enabled = false;
            UpdateDivisionCombo();
        }

        if (unit != 0 && division != 0 && department != 0)
        {
            try
            {
                //CC de patlıyor
                UnitDropDownList.SelectedValue = unit.ToString();
            }
            catch (Exception)
            { }

            UnitDropDownList.Enabled = false;
            UpdateUnitCombo();
        }

        if (team != 0 && unit != 0 && division != 0 && department != 0)
        {
            try
            {
                //ccde patlayabiliyor
                TeamDropDownList.SelectedValue = team.ToString();
            }
            catch (Exception) { }
            TeamDropDownList.Enabled = false;
            UpdateTeamCombo();
        }

        if (subTeam1 != 0 && team != 0 && unit != 0 && division != 0 && department != 0)
        {
            try
            {
                //ccde patlayabiliyor
                SubTeamDropDownList1.SelectedValue = subTeam1.ToString();
                // SubTeamPanel1.Visible = true;
            }
            catch (Exception) { }

            TeamDropDownList.Enabled = false;
            UpdateSubTeamCombo1();

        }

        if (subTeam2 != 0 && subTeam1 != 0 && team != 0 && unit != 0 && division != 0 && department != 0)
        {
            try
            {
                //ccde patlayabiliyor
                SubTeamDropDownList2.SelectedValue = subTeam2.ToString();
                //SubTeamPanel2.Visible = true;
            }
            catch (Exception) { }
            UpdateSubTeamCombo2();
        }


        if (subTeam3 != 0 && subTeam2 != 0 && subTeam1 != 0 && team != 0 && unit != 0 && division != 0 && department != 0)
        {
            try
            {
                //ccde patlayabiliyor
                SubTeamDropDownList3.SelectedValue = subTeam3.ToString();
                //SubTeamPanel4.Visible = true;
            }
            catch (Exception) { }
            UpdateSubTeamCombo3();
        }

        if (subTeam4 != 0 && subTeam3 != 0 && subTeam2 != 0 && subTeam1 != 0 && team != 0 && unit != 0 && division != 0 && department != 0)
        {
            try
            {
                //ccde patlayabiliyor
                SubTeamDropDownList4.SelectedValue = subTeam4.ToString();
                //SubTeamPanel5.Visible = true;
            }
            catch (Exception) { }
            UpdateSubTeamCombo4();
        }









        if (managerVisible)
        {
            ManagerLabel.Text = manager;
        }
        if (employeeVisible)
        {
            UserPanel.Visible = true;
            UserDropDownList.Enabled = true;
            UserDropDownList.Visible = true;
            UpdateLoginsCombo();
            if (employee != 0)
            {
                try
                {
                    UserDropDownList.SelectedValue = employee.ToString();
                }
                catch (Exception ex)
                {
                }

                if (UserDropDownList.SelectedValue != employee.ToString())
                {
                    ListItem obje = new ListItem();
                    obje.Text = WfDataHelpers.GetLoginNameSurname(employee);
                    obje.Value = employee.ToString();
                    UserDropDownList.SelectedIndex = -1;

                    UserDropDownList.Items.Add(obje);
                    UserDropDownList.Enabled = false;
                    UserDropDownList.SelectedIndex = UserDropDownList.Items.Count - 1;
                }
            }
        }
        OrganizationUpdatePanel.Update();

        // kullanıcı yetki seviyesine göre yapılabilecek seçimler güncelleniyor
        if (allowAssignmentToOwnDivision)
        {
            DepartmentDropDownList.Enabled = true;
            DivisionDropDownList.Enabled = true;
            UnitDropDownList.Enabled = true;
            TeamDropDownList.Enabled = true;
            SubTeamDropDownList1.Enabled = true;
            SubTeamDropDownList2.Enabled = true;
            SubTeamDropDownList3.Enabled = true;
            SubTeamDropDownList4.Enabled = true;
            SubTeamDropDownList5.Enabled = true;
            if (department > 0)
            {
                DepartmentDropDownList.Enabled = false;
            }
            if (division > 0)
            {
                DivisionDropDownList.Enabled = false;
            }
            if (unit > 0)
            {
                UnitDropDownList.Enabled = false;
            }
            if (team > 0)
            {
                TeamDropDownList.Enabled = false;
            }
            if (subTeam1 > 0)
            {
                SubTeamDropDownList1.Enabled = false;
            }
            if (subTeam2 > 0)
            {
                SubTeamDropDownList2.Enabled = false;
            }
            if (subTeam3 > 0)
            {
                SubTeamDropDownList3.Enabled = false;
            }
            if (subTeam4 > 0)
            {
                SubTeamDropDownList4.Enabled = false;
            }
            if (subTeam5 > 0)
            {
                SubTeamDropDownList5.Enabled = false;
            }

        }
    }

    /// <summary>
    /// Manages control accesibility
    /// </summary>
    /// <param name="departmentDropDownEnabled">Status of department dropdown</param>
    /// <param name="divisionDropDownEnabled">Status of division dropdown</param>
    /// <param name="unitDropDownEnabled">Status of unit dropdown</param>
    /// <param name="teamDropDownEnabled">Status of team dropdown</param>
    /// <param name="userDropDownEnabled">Status of user dropdown</param>
    /// <param name="subTeamDropDownEnabled1">Status of subTeam1 dropdown</param>
    /// <param name="subTeamDropDownEnabled2">Status of subTeam2 dropdown</param>
    /// <param name="subTeamDropDownEnabled3">Status of subTeam3 dropdown</param>
    /// <param name="subTeamDropDownEnabled4">Status of subTeam4 dropdown</param>
    /// <param name="subTeamDropDownEnabled5">Status of subTeam5 dropdown</param>
    public void SetControlStatus(bool departmentDropDownEnabled, bool divisionDropDownEnabled, bool unitDropDownEnabled, bool teamDropDownEnabled, bool userDropDownEnabled, bool subTeamDropDownEnabled1, bool subTeamDropDownEnabled2, bool subTeamDropDownEnabled3, bool subTeamDropDownEnabled4, bool subTeamDropDownEnabled5)
    {
        DepartmentDropDownList.Enabled = departmentDropDownEnabled;
        DivisionDropDownList.Enabled = divisionDropDownEnabled;
        UnitDropDownList.Enabled = unitDropDownEnabled;
        TeamDropDownList.Enabled = teamDropDownEnabled;
        UserDropDownList.Enabled = userDropDownEnabled;
        SubTeamDropDownList1.Enabled = subTeamDropDownEnabled1;
        SubTeamDropDownList2.Enabled = subTeamDropDownEnabled2;
        SubTeamDropDownList3.Enabled = subTeamDropDownEnabled3;
        SubTeamDropDownList4.Enabled = subTeamDropDownEnabled4;
        SubTeamDropDownList5.Enabled = subTeamDropDownEnabled5;
       
        OrganizationUpdatePanel.Update();
    }

    /// <summary>
    /// Sets controls visibility
    /// </summary>
    /// <param name="departmentDropDownVisible">Department DropDown Visibility</param>
    /// <param name="divisionDreopDownVisible">Division DropDown Visibility</param>
    /// <param name="unitDropDownVisible">Unit DropDown Visibility</param>
    /// <param name="teamDropDownVisible">Team DropDown Visibility</param>
    /// <param name="userDropDownVisible">Userlist DropDown Visibility</param>
    /// <param name="managerLabelVisible">Manage Labels Visibility</param>
    public void SetControlsVisibility(bool departmentDropDownVisible, bool divisionDreopDownVisible, bool unitDropDownVisible, bool teamDropDownVisible, bool userDropDownVisible, bool managerLabelVisible, bool subteamDropDown1Visible, bool subteamDropDown2Visible, bool subteamDropDown3Visible, bool subteamDropDown4Visible, bool subteamDropDown5Visible)
    {
        DepartmentPanel.Visible = departmentDropDownVisible;
        DivisionPanel.Visible = divisionDreopDownVisible;
        UnitPanel.Visible = unitDropDownVisible;
        TeamPanel.Visible = teamDropDownVisible;
        SubTeamPanel1.Visible = subteamDropDown1Visible;
        SubTeamPanel2.Visible = subteamDropDown2Visible;
        SubTeamPanel3.Visible = subteamDropDown3Visible;
        SubTeamPanel4.Visible = subteamDropDown4Visible;
        SubTeamPanel5.Visible = subteamDropDown5Visible;

        UserPanel.Visible = userDropDownVisible;
        ManagerPanel.Visible = managerLabelVisible;
       
        OrganizationUpdatePanel.Update();
    }

    

    private string deptGetir(long managerID)
    {
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            return WfDataHelpers.GetDeptAdi(managerID, "DEPS_EN");
        }
        else
        {
            return WfDataHelpers.GetDeptAdi(managerID, "BOLUM");
        }
    }

    /// <summary>
    /// Update the division dropdown
    /// </summary>
    public void UpdateDivisionCombo()
    {
        UnitDropDownList.Items.Clear();
        UnitDropDownList.Enabled = false;
        TeamDropDownList.Items.Clear();
        TeamDropDownList.Enabled = false;

        if (DivisionDropDownList.SelectedValue == "")
        {
            return;
        }

        UnitDropDownList.Enabled = true;

        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            UnitDropDownList.DataSource = WfDataHelpers.GetUnitListEng(Convert.ToInt64(DivisionDropDownList.SelectedValue));
        }
        else
        {
            UnitDropDownList.DataSource = WfDataHelpers.GetUnitList(Convert.ToInt64(DivisionDropDownList.SelectedValue));
        }
        UnitDropDownList.DataTextField = "LOGIN_GROUP_NAME";
        UnitDropDownList.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            UnitDropDownList.DataBind();
        }
        catch (Exception)
        {
        }

        GetManager();
    }

    /// <summary>
    /// Update the unit dropdown
    /// </summary>
    public void UpdateUnitCombo()
    {
        TeamDropDownList.Items.Clear();
        TeamDropDownList.Enabled = false;

        if (UnitDropDownList.SelectedValue == "")
        {
            return;
        }

        TeamDropDownList.Enabled = true;
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            TeamDropDownList.DataSource = WfDataHelpers.GetTeamsEng(Convert.ToInt64(UnitDropDownList.SelectedValue));
        }
        else
        {
            TeamDropDownList.DataSource = WfDataHelpers.GetTeams(Convert.ToInt64(UnitDropDownList.SelectedValue));
        }
        TeamDropDownList.DataTextField = "LOGIN_GROUP_NAME";
        TeamDropDownList.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            TeamDropDownList.DataBind();
        }
        catch (Exception)
        { }

        GetManager();
    }



    public void UpdateTeamCombo()
    {
        SubTeamDropDownList1.Items.Clear();
        SubTeamDropDownList1.Enabled = false;

        if (TeamDropDownList.SelectedValue == "")
        {
            return;
        }

        SubTeamDropDownList1.Enabled = true;
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            SubTeamDropDownList1.DataSource = WfDataHelpers.GetTeamsEng(Convert.ToInt64(TeamDropDownList.SelectedValue));
        }
        else
        {
            SubTeamDropDownList1.DataSource = WfDataHelpers.GetTeams(Convert.ToInt64(TeamDropDownList.SelectedValue));
        }
        SubTeamDropDownList1.DataTextField = "LOGIN_GROUP_NAME";
        SubTeamDropDownList1.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            SubTeamDropDownList1.DataBind();
        }
        catch (Exception)
        { }

        GetManager();
    }

    public void UpdateSubTeamCombo1()
    {
        SubTeamDropDownList2.Items.Clear();
        SubTeamDropDownList2.Enabled = false;

        if (SubTeamDropDownList1.SelectedValue == "")
        {
            return;
        }

        SubTeamDropDownList2.Enabled = true;
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            SubTeamDropDownList2.DataSource = WfDataHelpers.GetTeamsEng(Convert.ToInt64(SubTeamDropDownList1.SelectedValue));
        }
        else
        {
            SubTeamDropDownList2.DataSource = WfDataHelpers.GetTeams(Convert.ToInt64(SubTeamDropDownList1.SelectedValue));
        }
        SubTeamDropDownList2.DataTextField = "LOGIN_GROUP_NAME";
        SubTeamDropDownList2.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            SubTeamDropDownList2.DataBind();
        }
        catch (Exception)
        { }

        GetManager();
    }
    public void UpdateSubTeamCombo2()
    {
        SubTeamDropDownList3.Items.Clear();
        SubTeamDropDownList3.Enabled = false;

        if (SubTeamDropDownList2.SelectedValue == "")
        {
            return;
        }

        SubTeamDropDownList3.Enabled = true;
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            SubTeamDropDownList3.DataSource = WfDataHelpers.GetTeamsEng(Convert.ToInt64(SubTeamDropDownList2.SelectedValue));
        }
        else
        {
            SubTeamDropDownList3.DataSource = WfDataHelpers.GetTeams(Convert.ToInt64(SubTeamDropDownList2.SelectedValue));
        }
        SubTeamDropDownList3.DataTextField = "LOGIN_GROUP_NAME";
        SubTeamDropDownList3.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            SubTeamDropDownList3.DataBind();
        }
        catch (Exception)
        { }

        GetManager();
    }

    public void UpdateSubTeamCombo3()
    {
        SubTeamDropDownList4.Items.Clear();
        SubTeamDropDownList4.Enabled = false;

        if (SubTeamDropDownList3.SelectedValue == "")
        {
            return;
        }

        SubTeamDropDownList4.Enabled = true;
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            SubTeamDropDownList4.DataSource = WfDataHelpers.GetTeamsEng(Convert.ToInt64(SubTeamDropDownList3.SelectedValue));
        }
        else
        {
            SubTeamDropDownList4.DataSource = WfDataHelpers.GetTeams(Convert.ToInt64(SubTeamDropDownList3.SelectedValue));
        }
        SubTeamDropDownList4.DataTextField = "LOGIN_GROUP_NAME";
        SubTeamDropDownList4.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            SubTeamDropDownList4.DataBind();
        }
        catch (Exception)
        { }

        GetManager();
    }

    public void UpdateSubTeamCombo4()
    {
        SubTeamDropDownList5.Items.Clear();
        SubTeamDropDownList5.Enabled = false;

        if (SubTeamDropDownList4.SelectedValue == "")
        {
            return;
        }

        SubTeamDropDownList5.Enabled = true;
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            SubTeamDropDownList5.DataSource = WfDataHelpers.GetTeamsEng(Convert.ToInt64(SubTeamDropDownList4.SelectedValue));
        }
        else
        {
            SubTeamDropDownList5.DataSource = WfDataHelpers.GetTeams(Convert.ToInt64(SubTeamDropDownList4.SelectedValue));
        }
        SubTeamDropDownList5.DataTextField = "LOGIN_GROUP_NAME";
        SubTeamDropDownList5.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            SubTeamDropDownList4.DataBind();
        }
        catch (Exception)
        { }

        GetManager();
    }





    /// <summary>
    /// Update the department dropdown
    /// </summary>
    public void UpdateDepartmentCombo()
    {
        DivisionDropDownList.Items.Clear();
        DivisionDropDownList.Enabled = false;
        UnitDropDownList.Items.Clear();
        UnitDropDownList.Enabled = false;
        TeamDropDownList.Items.Clear();
        TeamDropDownList.Enabled = false;
        SubTeamDropDownList1.Items.Clear();
        SubTeamDropDownList1.Enabled = false;
        SubTeamDropDownList2.Items.Clear();
        SubTeamDropDownList2.Enabled = false;
        SubTeamDropDownList3.Items.Clear();
        SubTeamDropDownList3.Enabled = false;
        SubTeamDropDownList4.Items.Clear();
        SubTeamDropDownList4.Enabled = false;
        SubTeamDropDownList5.Items.Clear();
        SubTeamDropDownList5.Enabled = false;

        DivisionDropDownList.Enabled = true;
        DataTable dt = new DataTable();
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            dt = WfDataHelpers.GetDivisionListEng(Convert.ToInt64(DepartmentDropDownList.SelectedValue));
        }
        else
        {
            dt = WfDataHelpers.GetDivisionList(Convert.ToInt64(DepartmentDropDownList.SelectedValue));
        }

        DivisionDropDownList.DataSource = dt;

        DivisionDropDownList.DataTextField = "LOGIN_GROUP_NAME";
        DivisionDropDownList.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            DivisionDropDownList.DataBind();
        }
        catch (Exception)
        {
        }

        GetManager();
    }

    /// <summary>
    /// Update the manager label
    /// </summary>
    protected void GetManager()
    {
        if (this.ManagerPanelVisible)
        {
            long wfHelperGetManagerOf = 0;
            try
            {
                using (UnitOfWork.Start())
                {
                    var wfHelper = new WorkflowLoginHelper();
                    if (SubTeamDropDownList5.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(SubTeamDropDownList5.SelectedValue));
                    }
                    else if (SubTeamDropDownList4.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(SubTeamDropDownList4.SelectedValue));
                    }
                    else if (SubTeamDropDownList3.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(SubTeamDropDownList3.SelectedValue));
                    }
                    else if (SubTeamDropDownList2.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(SubTeamDropDownList2.SelectedValue));
                    }
                    else if (SubTeamDropDownList1.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(SubTeamDropDownList1.SelectedValue));
                    }
                    else if (TeamDropDownList.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(TeamDropDownList.SelectedValue));
                    }
                    else if (UnitDropDownList.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(UnitDropDownList.SelectedValue));
                    }
                    else if (DivisionDropDownList.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(DivisionDropDownList.SelectedValue));
                    }
                    else if (DepartmentDropDownList.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(DepartmentDropDownList.SelectedValue));
                    }
                    else
                    {
                        if (this.Page is Digiturk.Workflow.Digiflow.WebCore.SecurePage)
                        {
                            wfHelperGetManagerOf = wfHelper.GetManager(((Digiturk.Workflow.Digiflow.WebCore.SecurePage)this.Page).UserInformation.LoginObject.LoginId);
                            ManagerInstance = WFRepository<FLogin>.GetEntity(wfHelperGetManagerOf);
                        }
                    }
                }
            }
            catch
            {
                wfHelperGetManagerOf = 0;
                ManagerInstance = null;
            }

            if (ManagerInstance != null)
            {
                ManagerLabel.Text = WfDataHelpers.GetLoginNameSurname(ManagerInstance.LoginId);
            }
            else if (wfHelperGetManagerOf > 0)
            {
                ManagerLabel.Text = WfDataHelpers.GetLoginNameSurname(wfHelperGetManagerOf);
            }
            else
            {
                ManagerLabel.Text = string.Empty;
            }
        }
    }

    /// <summary>
    /// Update the users dropdown
    /// </summary>
    public void UpdateLoginsCombo()
    {
        if (!UserPanel.Visible)
        {
            return;
        }
        DataTable dtPersonnel = null;
        if (yonlendirPaneli) // Yönlendir panelinden geldiyse aynı üste sahip kişiler getirilecek.
        {
            dtPersonnel = WfDataHelpers.GetForwardPersonel(((Digiturk.Workflow.Digiflow.WebCore.SecurePage)this.Page).UserInformation.LoginObject.DomainUserName);
        }
        else if (yorumaGonderPaneli)
        {
            #region Satınalma akışında GMY onayı adımında yoruma gönderme açıldığında talep sahibi ve gmy nin listeye gelmesi için 

            if (((Digiturk.Workflow.Digiflow.WebCore.WorkFlowPage)this.Page).CurrentStateDef != null)
            {
                if (((Digiturk.Workflow.Digiflow.WebCore.WorkFlowPage)this.Page).CurrentStateDef.WfStateDefId == ConvertionHelper.ConvertValue<int>(Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDefinitionHelper.StateDefIDBul("Satinalma_CeoState")))
                {
                    DepartmentPanel.Visible = false;
                    DivisionPanel.Visible = false;
                    UnitPanel.Visible = false;
                    TeamPanel.Visible = false;
                    SubTeamPanel1.Visible = false;
                    SubTeamPanel2.Visible = false;
                    SubTeamPanel3.Visible = false;
                    SubTeamPanel4.Visible = false;
                    SubTeamPanel5.Visible = false;
                    OrganizationUpdatePanel.Update();
                    dtPersonnel = WfDataHelpers.GetCommentPersonel(((Digiturk.Workflow.Digiflow.WebCore.WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowInstanceId);
                }
            }
            #endregion
        }
        else
        {
            dtPersonnel = WfDataHelpers.GetPersonel(long.Parse(HRDeptId));
        }
        if (dtPersonnel == null)
        {
            dtPersonnel = WfDataHelpers.GetPersonel(long.Parse(HRDeptId));
        }



        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US" && dtPersonnel.Rows[0][1].ToString() == " ---Seçiniz---")
            dtPersonnel.Rows[0][1] = " ---Choose---";

        dtPersonnel.DefaultView.Sort = "LOGINNAME Asc";
        UserDropDownList.DataSource = dtPersonnel;
        UserDropDownList.DataTextField = "LoginName";
        UserDropDownList.DataValueField = "LoginId";
        try
        {
            UserDropDownList.DataBind();
        }
        catch (Exception ex)
        {
        }

        if (RemoveUser > 0) RemoveOwnUser(RemoveUser);

        if (yonlendirPaneli)
        {
            RemoveOwnUser(((Digiturk.Workflow.Digiflow.WebCore.SecurePage)this.Page).UserInformation.LoginObject.LoginId);
        }
        dtPersonnel = null;
        GetManager();
    }

    #endregion Public Methodlar
}
/*
public partial class UserControls_OrganizationTreeWebUserControl2 : System.Web.UI.UserControl
{

    #region Field and Properties
    // Yönlendirme panelinden geliyor ise aynı hiyerarşik seviyede olan kişileri göstereceğiz
    private bool _yonlendirPaneli = false;
    public bool yonlendirPaneli 
    {
        get { return _yonlendirPaneli; }
        set { _yonlendirPaneli = value; }

    }
    /// <summary>
    /// DP_HR_DEPSID Gets
    /// </summary>
    public string HRDeptId
    {
        get
        {
            string DeptId = "99";
            if (TeamValue != "0" && !string.IsNullOrEmpty(TeamValue))
            {
                DeptId = TeamValue;
            }
            else if (this.UnitValue != "0" && !string.IsNullOrEmpty(UnitValue))
            {
                DeptId = UnitValue;
            }
            else if (DivisionValue != "0" && !string.IsNullOrEmpty(DivisionValue))
            {
                DeptId = DivisionValue;
            }
            else if (DepartmentId != "0" && !string.IsNullOrEmpty(DepartmentId))
            {
                DeptId = DepartmentId;
            }
            return DeptId;
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public bool ByPassDepartmentBinding { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public bool ValuesSet { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public FLogin ManagerInstance { get; set; }
    /// <summary>
    /// Id of selected department
    /// </summary>
    public string DepartmentId
    {
        get
        {
            try
            {
                return DepartmentDropDownList.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    /// <summary>
    /// Name of selected department
    /// </summary>
    public string DepartmentName
    {
        get
        {
            try
            {
                return DepartmentDropDownList.SelectedItem.Text.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    /// <summary>
    /// Id of selected division
    /// </summary>
    public string DivisionValue
    {
        get
        {
            try
            {
                return DivisionDropDownList.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    /// <summary>
    /// Name of selected division
    /// </summary>
    public string DivisionName
    {
        get
        {
            try
            {
                return DivisionDropDownList.SelectedItem.Text.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    /// <summary>
    /// Id of selected unit
    /// </summary>
    public string UnitValue
    {
        get
        {
            try
            {
                return UnitDropDownList.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    /// <summary>
    /// Name of selected unit
    /// </summary>
    public string UnitName
    {
        get
        {
            try
            {
                return UnitDropDownList.SelectedItem.Text.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    /// <summary>
    /// Id of selected team
    /// </summary>
    public string TeamValue
    {
        get
        {
            try
            {
                return TeamDropDownList.SelectedItem.Value.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    /// <summary>
    /// Name of selected team
    /// </summary>
    public string TeamName
    {
        get
        {
            try
            {
                return TeamDropDownList.SelectedItem.Text.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
    }
    /// <summary>
    /// Name of manager
    /// </summary>
    public string ManagerName
    {
        get
        {
            return ManagerLabel.Text;
        }
    }
    /// <summary>
    /// Set the title of user control
    /// </summary>
    public string Title
    {
        set
        {
            TitleLabel.Text = value;
        }
    }
    /// <summary>
    /// Change the visibility of user panel
    /// </summary>
    public bool UserPanelVisible
    {

        get { return UserPanel.Visible; }
        set { OrganizationUpdatePanel.Update(); UserPanel.Visible = value; OrganizationUpdatePanel.Update(); }
    }
    /// <summary>
    /// Change the visibility of manager panel
    /// </summary>
    public bool ManagerPanelVisible
    {
        get { return ManagerPanel.Visible; }
        set { OrganizationUpdatePanel.Update(); ManagerPanel.Visible = value; OrganizationUpdatePanel.Update(); }
    }
    /// <summary>
    /// Id of selected user
    /// </summary>
    public string UserId
    {
        get
        {
            if (UserPanel.Visible)
            {
                return UserDropDownList.SelectedItem.Value;
            }

            return string.Empty;
        }
    }
    /// <summary>
    /// Name of manager
    /// </summary>
    public string Manager
    {
        get
        {
            if (ManagerPanel.Visible)
            {
                return ManagerLabel.Text;
            }

            return string.Empty;
        }
    }
    /// <summary>
    /// Kullanıcı dropdown kontrolünü kontrol eden validation kontrolünü yönetmek için kullanılır. 
    /// </summary>
    public bool EnableUserDropdownValidation
    {
        get { return UserDropdownRequiredFieldValidator.Enabled; }
        set { UserDropdownRequiredFieldValidator.Enabled = value; }
    }
    /// <summary>
    /// Kullanıcının UserDropdowndan çıkartmak istediği user seçilerek çıkartılır.
    /// </summary>
    /// <param name="LoginId"></param>
    public void RemoveOwnUser(long LoginId)
    {
        for (int i = 0; i < UserDropDownList.Items.Count; i++)
        {
            if (UserDropDownList.Items[i].Value == LoginId.ToString())
            {
                UserDropDownList.Items.RemoveAt(i);
            }
        }
    }
    /// <summary>
    /// Dropdowndan çıkartılacak kullanıcıyı işaretler
    /// </summary>
    public long RemoveUser
    {
        get
        {
            if (Session["RemoveUser"] != null)
                return long.Parse(Session["RemoveUser"].ToString());
            return 0;
        }
        set
        {
            Session["RemoveUser"] = value.ToString();
        }
    }
    #endregion


    #region Events
    /// <summary>
    /// Department Dropdownlist Eventi
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DepartmentDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        OrganizationUpdatePanel.Update();
        OrganizationTreePanel.Enabled = false;

        UpdateDepartmentCombo();
        GetManager();

        if (UserPanel.Visible)
        {
            UpdateLoginsCombo();
        }
        OrganizationTreePanel.Enabled = true;
        OrganizationUpdatePanel.Update();
    }
    /// <summary>
    /// Division Dropdownlist Eventi
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DivisionDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        OrganizationUpdatePanel.Update();
        OrganizationTreePanel.Enabled = false;

        UpdateDivisionCombo();
        GetManager();
        UpdateLoginsCombo();
        OrganizationTreePanel.Enabled = true;
        OrganizationUpdatePanel.Update();
    }
    /// <summary>
    /// UnitDropdownList Event i 
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void UnitDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        OrganizationUpdatePanel.Update();
        OrganizationTreePanel.Enabled = false;

        UpdateUnitCombo();
        GetManager();
        UpdateLoginsCombo();
        OrganizationTreePanel.Enabled = true;
        OrganizationUpdatePanel.Update();
    }
    /// <summary>
    /// Team DropdownListEvent i
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void TeamDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        OrganizationUpdatePanel.Update();
        OrganizationTreePanel.Enabled = false;

        GetManager();
        UpdateLoginsCombo();
        OrganizationTreePanel.Enabled = true;
        OrganizationUpdatePanel.Update();
    }
   
    //usercontrol sayfasından ana sayfaya event gönderimi
    public delegate void ChildControlDelegate(string LoginId);
    public event ChildControlDelegate GetDataFromChild;
     
    /// <summary>
    /// UserDropDownList Event i 
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void UserDropDownList_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (GetDataFromChild != null)
        {
            GetDataFromChild(UserDropDownList.SelectedValue.ToString());
        }
        
    }


    /// <summary>
    /// Page Load
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsCallback)
        {
            if (!Page.IsPostBack)
            {
                if (!ByPassDepartmentBinding)
                {
                    DepartmentDropDownList.Enabled = true;
                    DepartmentDropDownList.DataSource = WfDataHelpers.GetDeparmentList();
                    DepartmentDropDownList.DataTextField = "LOGIN_GROUP_NAME";
                    DepartmentDropDownList.DataValueField = "LOGIN_GROUP_ID";
                    DepartmentDropDownList.DataBind();
                    DepartmentDropDownList.SelectedIndex = -1;
                    DivisionDropDownList.Items.Clear();
                    UnitDropDownList.Items.Clear();
                    TeamDropDownList.Items.Clear();
                    Session["RemoveUser"] = null;
                }
                UserPanel.Visible = UserPanelVisible;
                ManagerPanel.Visible = ManagerPanelVisible;
            }
        }
        if (yonlendirPaneli) // Yönlendir panelinden geldiyse aynı üste sahip kişiler getirilecek.
        {
            DepartmentDropDownList.Enabled = false;
            DivisionDropDownList.Enabled = false;
            UnitDropDownList.Enabled = false;
            TeamDropDownList.Enabled = false;
            OrganizationUpdatePanel.Update();
        }
    }
    #endregion

    #region Public Methodlar
    /// <summary>
    /// Kullanıcı Set Etmek için Kullanılır
    /// </summary>
    /// <param name="LoginId"></param>
    /// <param name="IsControlEnable"></param>
    public void SetUser(long LoginId, bool IsControlEnable)
    { 
        //LoginDto=WorkflowLoginHelper 
        ManagerPanelVisible = false;
        UserPanelVisible = true;
        ByPassDepartmentBinding = true;
        LoginDto LoginObj = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDetailsOfLogin(LoginId);
        SetValues(LoginObj.DepartmentId, LoginObj.DivisionId, LoginObj.UnitId, LoginObj.TeamId, "", false, LoginObj.LoginId, true, true);
        SetControlStatus(IsControlEnable, IsControlEnable, IsControlEnable, IsControlEnable, IsControlEnable);
    }
    /// <summary>
    /// Sets page mode
    /// </summary>
    /// <param name="readOnly">Is the control will be loaded in read-only mode</param>
    public void SetPageMode(bool readOnly)
    {
        DepartmentDropDownList.Enabled = !readOnly;
        DivisionDropDownList.Enabled = !readOnly;
        UnitDropDownList.Enabled = !readOnly;
        TeamDropDownList.Enabled = !readOnly;

        if (UserPanel.Visible)
        {
            UserDropDownList.Enabled = !readOnly;
        }
        OrganizationUpdatePanel.Update();
    }
    /// <summary>
    /// Sets values for controls
    /// </summary>
    /// <param name="department">Name of department</param>
    /// <param name="division">Name of division</param>
    /// <param name="unit">Name of unit</param>
    /// <param name="team">Name of team</param>
    /// <param name="manager">Name of manager</param>
    /// <param name="managerVisible">Manager is visible</param>
    /// <param name="employee">Name of employee</param>
    /// <param name="employeeVisible">Employee is visible</param>
    public void SetValues(long department, long division, long unit, long team, string manager, bool managerVisible, long employee, bool employeeVisible, bool allowAssignmentToOwnDivision)
    {
        if (department == 0)
        {
            department = division;
            division = unit;
            unit = team;
            team = 0;
        }
        ValuesSet = true;
        DepartmentDropDownList.Items.Clear();
        DepartmentDropDownList.Enabled = true;
        //DepartmentDropDownList.DataSource = WfDataHelpers.GetDeparmentList();
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            DepartmentDropDownList.DataSource = WfDataHelpers.GetDeparmentListEng();
        }
        else
        {
            DepartmentDropDownList.DataSource = WfDataHelpers.GetDeparmentList();
        }

        DepartmentDropDownList.DataTextField = "LOGIN_GROUP_NAME";
        DepartmentDropDownList.DataValueField = "LOGIN_GROUP_ID";
        DepartmentDropDownList.DataBind();
        DepartmentDropDownList.SelectedIndex = -1;
        DivisionDropDownList.Items.Clear();
        UnitDropDownList.Items.Clear();
        TeamDropDownList.Items.Clear();
        if (department != 0)
        {            
            DepartmentDropDownList.SelectedValue = department.ToString();            
            DepartmentDropDownList.Enabled = false;
            UpdateDepartmentCombo();
        }
        if (division != 0 && department != 0)
        {
            DivisionDropDownList.SelectedValue = division.ToString();
            DivisionDropDownList.Enabled = false;
            UpdateDivisionCombo();
        }
        if (unit != 0 && division != 0 && department != 0)
        {
            try
            {
                //CC de patlıyor
                UnitDropDownList.SelectedValue = unit.ToString();
            }
            catch (Exception)
            {}
            
            UnitDropDownList.Enabled = false;
            UpdateUnitCombo();
        }
        if (team != 0 && unit != 0 && division != 0 && department != 0)
        {
            try
            {
                //ccde patlayabiliyor
                TeamDropDownList.SelectedValue = team.ToString();
            }catch (Exception){}
            
        }
        if (managerVisible)
        {
            ManagerLabel.Text = manager;
        }
        if (employeeVisible)
        {
            UserPanel.Visible = true;
            UserDropDownList.Enabled = true;
            UserDropDownList.Visible = true;
            UpdateLoginsCombo();
            if (employee != 0)
            {
                UserDropDownList.SelectedValue = employee.ToString();
                if ( UserDropDownList.SelectedValue != employee.ToString())
                {
                    ListItem  obje = new ListItem();
                    obje.Text = WfDataHelpers.GetLoginNameSurname(employee);
                    obje.Value = employee.ToString();
                    UserDropDownList.SelectedIndex = -1;
                  
                    UserDropDownList.Items.Add(obje);
                    UserDropDownList.Enabled = false;
                    UserDropDownList.SelectedIndex = UserDropDownList.Items.Count - 1;

                }
            }
        }
        OrganizationUpdatePanel.Update();

        // kullanıcı yetki seviyesine göre yapılabilecek seçimler güncelleniyor
        if (allowAssignmentToOwnDivision)
        {
            DepartmentDropDownList.Enabled = true;
            DivisionDropDownList.Enabled = true;
            UnitDropDownList.Enabled = true;
            TeamDropDownList.Enabled = true;
            if (department > 0)
            {
                DepartmentDropDownList.Enabled = false;

            }
            if (division > 0)
            {
                DivisionDropDownList.Enabled = false;
            }
            if (unit > 0)
            {
                UnitDropDownList.Enabled = false;
            }
            if (team > 0)
            {
                TeamDropDownList.Enabled = false;
            }
        }
    }
    /// <summary>
    /// Manages control accesibility
    /// </summary>
    /// <param name="departmentDropDownEnabled">Status of department dropdown</param>
    /// <param name="divisionDropDownEnabled">Status of division dropdown</param>
    /// <param name="unitDropDownEnabled">Status of unit dropdown</param>
    /// <param name="teamDropDownEnabled">Status of team dropdown</param>
    /// <param name="userDropDownEnabled">Status of user dropdown</param>
    public void SetControlStatus(bool departmentDropDownEnabled, bool divisionDropDownEnabled, bool unitDropDownEnabled, bool teamDropDownEnabled, bool userDropDownEnabled)
    {
        DepartmentDropDownList.Enabled = departmentDropDownEnabled;
        DivisionDropDownList.Enabled = divisionDropDownEnabled;
        UnitDropDownList.Enabled = unitDropDownEnabled;
        TeamDropDownList.Enabled = teamDropDownEnabled;
        UserDropDownList.Enabled = userDropDownEnabled;
        OrganizationUpdatePanel.Update();
    }
    /// <summary>
    /// Sets controls visibility
    /// </summary>
    /// <param name="departmentDropDownVisible">Department DropDown Visibility</param>
    /// <param name="divisionDreopDownVisible">Division DropDown Visibility</param>
    /// <param name="unitDropDownVisible">Unit DropDown Visibility</param>
    /// <param name="teamDropDownVisible">Team DropDown Visibility</param>
    /// <param name="userDropDownVisible">Userlist DropDown Visibility</param>
    /// <param name="managerLabelVisible">Manage Labels Visibility</param>
    public void SetControlsVisibility(bool departmentDropDownVisible, bool divisionDreopDownVisible, bool unitDropDownVisible, bool teamDropDownVisible, bool userDropDownVisible, bool managerLabelVisible)
    {
        DepartmentPanel.Visible = departmentDropDownVisible;
        DivisionPanel.Visible = divisionDreopDownVisible;
        UnitPanel.Visible = unitDropDownVisible;
        TeamPanel.Visible = teamDropDownVisible;
        UserPanel.Visible = userDropDownVisible;
        ManagerPanel.Visible = managerLabelVisible;
        OrganizationUpdatePanel.Update();
    }
    /// <summary>
    /// Update the division dropdown
    /// </summary>
    public void UpdateDivisionCombo()
    {
        UnitDropDownList.Items.Clear();
        UnitDropDownList.Enabled = false;
        TeamDropDownList.Items.Clear();
        TeamDropDownList.Enabled = false;

        if (DivisionDropDownList.SelectedValue == "")
        {
            return;
        }

        UnitDropDownList.Enabled = true;


        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {
            UnitDropDownList.DataSource = WfDataHelpers.GetUnitListEng(Convert.ToInt64(DivisionDropDownList.SelectedValue));
        }
        else
        {
            UnitDropDownList.DataSource = WfDataHelpers.GetUnitList(Convert.ToInt64(DivisionDropDownList.SelectedValue));
        }

        //UnitDropDownList.DataSource = WfDataHelpers.GetUnitList(Convert.ToInt64(DivisionDropDownList.SelectedValue));
        UnitDropDownList.DataTextField = "LOGIN_GROUP_NAME";
        UnitDropDownList.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            UnitDropDownList.DataBind();
        }
        catch (Exception)
        {
        }
        
        GetManager();
    }
    /// <summary>
    /// Update the unit dropdown
    /// </summary>
    public void UpdateUnitCombo()
    {
        TeamDropDownList.Items.Clear();
        TeamDropDownList.Enabled = false;

        if (UnitDropDownList.SelectedValue == "")
        {
            return;
        }

        TeamDropDownList.Enabled = true;
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {

            TeamDropDownList.DataSource = WfDataHelpers.GetTeamsEng(Convert.ToInt64(UnitDropDownList.SelectedValue));
        }
        else
        {
            TeamDropDownList.DataSource = WfDataHelpers.GetTeams(Convert.ToInt64(UnitDropDownList.SelectedValue));
        }

        //TeamDropDownList.DataSource = WfDataHelpers.GetTeams(Convert.ToInt64(UnitDropDownList.SelectedValue));
        TeamDropDownList.DataTextField = "LOGIN_GROUP_NAME";
        TeamDropDownList.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            TeamDropDownList.DataBind();
        }
        catch (Exception)
        {}
        
        GetManager();
    }
    /// <summary>
    /// Update the department dropdown
    /// </summary>
    public void UpdateDepartmentCombo()
    {
        DivisionDropDownList.Items.Clear();
        DivisionDropDownList.Enabled = false;
        UnitDropDownList.Items.Clear();
        UnitDropDownList.Enabled = false;
        TeamDropDownList.Items.Clear();
        TeamDropDownList.Enabled = false;
        DivisionDropDownList.Enabled = true;
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US")
        {

            DivisionDropDownList.DataSource = WfDataHelpers.GetDivisionListEng(Convert.ToInt64(DepartmentDropDownList.SelectedValue));
        }
        else
        {
            DivisionDropDownList.DataSource = WfDataHelpers.GetDivisionList(Convert.ToInt64(DepartmentDropDownList.SelectedValue));
        }
        //DivisionDropDownList.DataSource = WfDataHelpers.GetDivisionList(Convert.ToInt64(DepartmentDropDownList.SelectedValue));
        DivisionDropDownList.DataTextField = "LOGIN_GROUP_NAME";
        DivisionDropDownList.DataValueField = "LOGIN_GROUP_ID";
        try
        {
            DivisionDropDownList.DataBind();
        }
        catch (Exception)
        {
        }
        
        GetManager();
    }
    /// <summary>
    /// Update the manager label
    /// </summary>
    protected void GetManager()
    {
        if (this.ManagerPanelVisible)
        {
            long wfHelperGetManagerOf = 0;
            try
            {
                using (UnitOfWork.Start())
                {
                    var wfHelper = new WorkflowLoginHelper();
                    if (TeamDropDownList.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(TeamDropDownList.SelectedValue));
                    }
                    else if (UnitDropDownList.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(UnitDropDownList.SelectedValue));
                    }
                    else if (DivisionDropDownList.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(DivisionDropDownList.SelectedValue));
                    }
                    else if (DepartmentDropDownList.SelectedIndex > 0)
                    {
                        wfHelperGetManagerOf = wfHelper.GetManagerOf(Convert.ToInt64(DepartmentDropDownList.SelectedValue));
                    }
                    else
                    {
                        if (this.Page is Digiturk.Workflow.Digiflow.WebCore.SecurePage)
                        {
                            wfHelperGetManagerOf = wfHelper.GetManager(((Digiturk.Workflow.Digiflow.WebCore.SecurePage)this.Page).UserInformation.LoginObject.LoginId);
                            ManagerInstance = WFRepository<FLogin>.GetEntity(wfHelperGetManagerOf);
                        }
                    }
                }
            }
            catch
            {
                wfHelperGetManagerOf = 0;
                ManagerInstance = null;
            }


            if (ManagerInstance != null)
            {
                ManagerLabel.Text = WfDataHelpers.GetLoginNameSurname(ManagerInstance.LoginId);
            }
            else if (wfHelperGetManagerOf > 0)
            {
                ManagerLabel.Text = WfDataHelpers.GetLoginNameSurname(wfHelperGetManagerOf);
            }
            else
            {
                ManagerLabel.Text = string.Empty;
            }
        }

    }
    /// <summary>
    /// Update the users dropdown
    /// </summary>
    public void UpdateLoginsCombo()
    {
       
        if (!UserPanel.Visible)
        {
            return;
        }
        DataTable dtPersonnel=null;
        if (yonlendirPaneli) // Yönlendir panelinden geldiyse aynı üste sahip kişiler getirilecek.
        {
            dtPersonnel = WfDataHelpers.GetForwardPersonel(((Digiturk.Workflow.Digiflow.WebCore.SecurePage)this.Page).UserInformation.LoginObject.DomainUserName);
        }
        else
        {
            dtPersonnel = WfDataHelpers.GetPersonel(long.Parse(HRDeptId));

        }
        if (System.Globalization.CultureInfo.CurrentUICulture.Name == "en-US" && dtPersonnel.Rows[0][1].ToString() == " ---Seçiniz---")
            dtPersonnel.Rows[0][1] = " ---Choose---";

        dtPersonnel.DefaultView.Sort = "LOGINNAME Asc";
        UserDropDownList.DataSource = dtPersonnel;
        UserDropDownList.DataTextField = "LoginName";
        UserDropDownList.DataValueField = "LoginId";
        UserDropDownList.DataBind();
        if (RemoveUser > 0) RemoveOwnUser(RemoveUser);
        
        if (yonlendirPaneli)
        {
            RemoveOwnUser(((Digiturk.Workflow.Digiflow.WebCore.SecurePage)this.Page).UserInformation.LoginObject.LoginId);
        }
        GetManager();
    }
    #endregion
}
*/
