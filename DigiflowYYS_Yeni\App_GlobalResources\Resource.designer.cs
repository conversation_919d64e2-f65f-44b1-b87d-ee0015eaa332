//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "14.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Resource", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alt Birim 1.
        /// </summary>
        internal static string Alt_Birim1 {
            get {
                return ResourceManager.GetString("Alt_Birim1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alt Birim 2.
        /// </summary>
        internal static string Alt_Birim2 {
            get {
                return ResourceManager.GetString("Alt_Birim2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alt Birim 3.
        /// </summary>
        internal static string Alt_Birim3 {
            get {
                return ResourceManager.GetString("Alt_Birim3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alt Birim 4.
        /// </summary>
        internal static string Alt_Birim4 {
            get {
                return ResourceManager.GetString("Alt_Birim4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alt Birim 5.
        /// </summary>
        internal static string Alt_Birim5 {
            get {
                return ResourceManager.GetString("Alt_Birim5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1. Finans Onayı Yurt Dışı.
        /// </summary>
        internal static string HistoryState_1FinansOnayıYurtDışı {
            get {
                return ResourceManager.GetString("HistoryState_1FinansOnayıYurtDışı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1. Finans Onayı Yurtiçi.
        /// </summary>
        internal static string HistoryState_1FinansOnayıYurtiçi {
            get {
                return ResourceManager.GetString("HistoryState_1FinansOnayıYurtiçi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1. Kontrat Yönetimi Onayı.
        /// </summary>
        internal static string HistoryState_1KontratYönetimiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_1KontratYönetimiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2. Finans Onayı Yurt Dışı.
        /// </summary>
        internal static string HistoryState_2FinansOnayıYurtDışı {
            get {
                return ResourceManager.GetString("HistoryState_2FinansOnayıYurtDışı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2. Finans Onayı Yurt İçi.
        /// </summary>
        internal static string HistoryState_2FinansOnayıYurtİçi {
            get {
                return ResourceManager.GetString("HistoryState_2FinansOnayıYurtİçi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2.Giriş Adımı.
        /// </summary>
        internal static string HistoryState_2GirişAdımı {
            get {
                return ResourceManager.GetString("HistoryState_2GirişAdımı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2. Kontrat Yönetimi Onayı.
        /// </summary>
        internal static string HistoryState_2KontratYönetimiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_2KontratYönetimiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2. Üst Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_2ÜstYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_2ÜstYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 3. Kontrat Yönetimi Onayı.
        /// </summary>
        internal static string HistoryState_3KontratYönetimiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_3KontratYönetimiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acc Oluşturma.
        /// </summary>
        internal static string HistoryState_AccOluşturma {
            get {
                return ResourceManager.GetString("HistoryState_AccOluşturma", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Akış Atlatma Talebi.
        /// </summary>
        internal static string HistoryState_AkışAtlatmaTalebi {
            get {
                return ResourceManager.GetString("HistoryState_AkışAtlatmaTalebi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Analiz Aşaması.
        /// </summary>
        internal static string HistoryState_AnalizAşaması {
            get {
                return ResourceManager.GetString("HistoryState_AnalizAşaması", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ara Departman Onayı.
        /// </summary>
        internal static string HistoryState_AraDepartmanOnayı {
            get {
                return ResourceManager.GetString("HistoryState_AraDepartmanOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ara Departman Yönetici Onayı.
        /// </summary>
        internal static string HistoryState_AraDepartmanYöneticiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_AraDepartmanYöneticiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ara Departman Yöneticisi.
        /// </summary>
        internal static string HistoryState_AraDepartmanYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_AraDepartmanYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ara Departman Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_AraDepartmanYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_AraDepartmanYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ara Onay.
        /// </summary>
        internal static string HistoryState_AraOnay {
            get {
                return ResourceManager.GetString("HistoryState_AraOnay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ara Onay 1.
        /// </summary>
        internal static string HistoryState_AraOnay1 {
            get {
                return ResourceManager.GetString("HistoryState_AraOnay1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ara Onay 2.
        /// </summary>
        internal static string HistoryState_AraOnay2 {
            get {
                return ResourceManager.GetString("HistoryState_AraOnay2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ara State.
        /// </summary>
        internal static string HistoryState_AraState {
            get {
                return ResourceManager.GetString("HistoryState_AraState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Araç Adımı.
        /// </summary>
        internal static string HistoryState_AraçAdımı {
            get {
                return ResourceManager.GetString("HistoryState_AraçAdımı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avans KRM Yetkili Onayı.
        /// </summary>
        internal static string HistoryState_AvansKRMYetkiliOnayı {
            get {
                return ResourceManager.GetString("HistoryState_AvansKRMYetkiliOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avans Yetkili Onayı.
        /// </summary>
        internal static string HistoryState_AvansYetkiliOnayı {
            get {
                return ResourceManager.GetString("HistoryState_AvansYetkiliOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avans Yetki Onayı.
        /// </summary>
        internal static string HistoryState_AvansYetkiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_AvansYetkiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barter Yetkili Onayı.
        /// </summary>
        internal static string HistoryState_BarterYetkiliOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BarterYetkiliOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Batı Ulusal Bölge Müdürü Onayı.
        /// </summary>
        internal static string HistoryState_BatıUlusalBölgeMüdürüOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BatıUlusalBölgeMüdürüOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Batı Ulusal Bölge Satış.
        /// </summary>
        internal static string HistoryState_BatıUlusalBölgeSatış {
            get {
                return ResourceManager.GetString("HistoryState_BatıUlusalBölgeSatış", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birim Müdürü Onayı.
        /// </summary>
        internal static string HistoryState_BirimMüdürüOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BirimMüdürüOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birim Onayı.
        /// </summary>
        internal static string HistoryState_BirimOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BirimOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birim Yönetici Onayı.
        /// </summary>
        internal static string HistoryState_BirimYöneticiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BirimYöneticiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birim Yöneticisi.
        /// </summary>
        internal static string HistoryState_BirimYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_BirimYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birim Yöneticisi Onay.
        /// </summary>
        internal static string HistoryState_BirimYöneticisiOnay {
            get {
                return ResourceManager.GetString("HistoryState_BirimYöneticisiOnay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birim Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_BirimYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BirimYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broadcast Software Tanımlama ve Onayı.
        /// </summary>
        internal static string HistoryState_BroadcastSoftwareTanımlamaveOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BroadcastSoftwareTanımlamaveOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bölge Müdürü Onayı.
        /// </summary>
        internal static string HistoryState_BölgeMüdürüOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BölgeMüdürüOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bölge Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_BölgeYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BölgeYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bölüm Yönetici Onayı.
        /// </summary>
        internal static string HistoryState_BölümYöneticiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BölümYöneticiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bölüm Yöneticisi.
        /// </summary>
        internal static string HistoryState_BölümYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_BölümYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bölüm Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_BölümYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_BölümYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bütçe Yöneticisi.
        /// </summary>
        internal static string HistoryState_BütçeYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_BütçeYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Conditional Access Tanımlama ve Onayı.
        /// </summary>
        internal static string HistoryState_ConditionalAccessTanımlamaveOnayı {
            get {
                return ResourceManager.GetString("HistoryState_ConditionalAccessTanımlamaveOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRM Grubu Onayı.
        /// </summary>
        internal static string HistoryState_CRMGrubuOnayı {
            get {
                return ResourceManager.GetString("HistoryState_CRMGrubuOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delegasyon Talebi Cevap.
        /// </summary>
        internal static string HistoryState_DelegasyonTalebiCevap {
            get {
                return ResourceManager.GetString("HistoryState_DelegasyonTalebiCevap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delegasyon Talep.
        /// </summary>
        internal static string HistoryState_DelegasyonTalep {
            get {
                return ResourceManager.GetString("HistoryState_DelegasyonTalep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Departman Onayı.
        /// </summary>
        internal static string HistoryState_DepartmanOnayı {
            get {
                return ResourceManager.GetString("HistoryState_DepartmanOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Departman Yönetici Onayı.
        /// </summary>
        internal static string HistoryState_DepartmanYöneticiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_DepartmanYöneticiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Departman Yöneticisi.
        /// </summary>
        internal static string HistoryState_DepartmanYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_DepartmanYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Departman Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_DepartmanYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_DepartmanYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Değerlendirme Tamamlandı.
        /// </summary>
        internal static string HistoryState_DeğerlendirmeTamamlandı {
            get {
                return ResourceManager.GetString("HistoryState_DeğerlendirmeTamamlandı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Digiturk Finans Onayı.
        /// </summary>
        internal static string HistoryState_DigiturkFinansOnayı {
            get {
                return ResourceManager.GetString("HistoryState_DigiturkFinansOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diğer Bilgi Toplama.
        /// </summary>
        internal static string HistoryState_DiğerBilgiToplama {
            get {
                return ResourceManager.GetString("HistoryState_DiğerBilgiToplama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Doğu Ulusal Bölge Müdürü Onayı.
        /// </summary>
        internal static string HistoryState_DoğuUlusalBölgeMüdürüOnayı {
            get {
                return ResourceManager.GetString("HistoryState_DoğuUlusalBölgeMüdürüOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Doğu Ulusal Bölge Satış.
        /// </summary>
        internal static string HistoryState_DoğuUlusalBölgeSatış {
            get {
                return ResourceManager.GetString("HistoryState_DoğuUlusalBölgeSatış", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dts Onayı.
        /// </summary>
        internal static string HistoryState_DtsOnayı {
            get {
                return ResourceManager.GetString("HistoryState_DtsOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DTS Ready Onayı.
        /// </summary>
        internal static string HistoryState_DTSReadyOnayı {
            get {
                return ResourceManager.GetString("HistoryState_DTSReadyOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dublaj Arşiv Tanımlam ve Onayı.
        /// </summary>
        internal static string HistoryState_DublajArşivTanımlamveOnayı {
            get {
                return ResourceManager.GetString("HistoryState_DublajArşivTanımlamveOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzeltme.
        /// </summary>
        internal static string HistoryState_Düzeltme {
            get {
                return ResourceManager.GetString("HistoryState_Düzeltme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Egitim Sonrası Değerlendirme Süreci.
        /// </summary>
        internal static string HistoryState_EgitimSonrasıDeğerlendirmeSüreci {
            get {
                return ResourceManager.GetString("HistoryState_EgitimSonrasıDeğerlendirmeSüreci", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ekip Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_EkipYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_EkipYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ek Satınalma Sahibi Onayı.
        /// </summary>
        internal static string HistoryState_EkSatınalmaSahibiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_EkSatınalmaSahibiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ek İhtiyaçların Düzenlenmesi.
        /// </summary>
        internal static string HistoryState_EkİhtiyaçlarınDüzenlenmesi {
            get {
                return ResourceManager.GetString("HistoryState_EkİhtiyaçlarınDüzenlenmesi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fatura Düzenleme.
        /// </summary>
        internal static string HistoryState_FaturaDüzenleme {
            get {
                return ResourceManager.GetString("HistoryState_FaturaDüzenleme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fatura Düzenleme(Jüpiter).
        /// </summary>
        internal static string HistoryState_FaturaDüzenlemeJüpiter {
            get {
                return ResourceManager.GetString("HistoryState_FaturaDüzenlemeJüpiter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fatura Düzenleme(Pluton).
        /// </summary>
        internal static string HistoryState_FaturaDüzenlemePluton {
            get {
                return ResourceManager.GetString("HistoryState_FaturaDüzenlemePluton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fatura Düzenlendi.
        /// </summary>
        internal static string HistoryState_FaturaDüzenlendi {
            get {
                return ResourceManager.GetString("HistoryState_FaturaDüzenlendi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finans Bütçe.
        /// </summary>
        internal static string HistoryState_FinansBütçe {
            get {
                return ResourceManager.GetString("HistoryState_FinansBütçe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finans Bütçe Yöneticisi.
        /// </summary>
        internal static string HistoryState_FinansBütçeYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_FinansBütçeYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finans Genel Müdür Yardımcısı Onayı.
        /// </summary>
        internal static string HistoryState_FinansGenelMüdürYardımcısıOnayı {
            get {
                return ResourceManager.GetString("HistoryState_FinansGenelMüdürYardımcısıOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finans GMY.
        /// </summary>
        internal static string HistoryState_FinansGMY {
            get {
                return ResourceManager.GetString("HistoryState_FinansGMY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finans GMY Onayı.
        /// </summary>
        internal static string HistoryState_FinansGMYOnayı {
            get {
                return ResourceManager.GetString("HistoryState_FinansGMYOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finans Kontrol Onayı.
        /// </summary>
        internal static string HistoryState_FinansKontrolOnayı {
            get {
                return ResourceManager.GetString("HistoryState_FinansKontrolOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finans Kontrolü Onayı.
        /// </summary>
        internal static string HistoryState_FinansKontrolüOnayı {
            get {
                return ResourceManager.GetString("HistoryState_FinansKontrolüOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finans Yetkili Onayı.
        /// </summary>
        internal static string HistoryState_FinansYetkiliOnayı {
            get {
                return ResourceManager.GetString("HistoryState_FinansYetkiliOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Firma Bekleme Onay.
        /// </summary>
        internal static string HistoryState_FirmaBeklemeOnay {
            get {
                return ResourceManager.GetString("HistoryState_FirmaBeklemeOnay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Firma Onayı.
        /// </summary>
        internal static string HistoryState_FirmaOnayı {
            get {
                return ResourceManager.GetString("HistoryState_FirmaOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form Kontrol ve Onayı.
        /// </summary>
        internal static string HistoryState_FormKontrolveOnayı {
            get {
                return ResourceManager.GetString("HistoryState_FormKontrolveOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geliştirme Aşaması.
        /// </summary>
        internal static string HistoryState_GeliştirmeAşaması {
            get {
                return ResourceManager.GetString("HistoryState_GeliştirmeAşaması", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Genel Müdür Onayı.
        /// </summary>
        internal static string HistoryState_GenelMüdürOnayı {
            get {
                return ResourceManager.GetString("HistoryState_GenelMüdürOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Genel Müdür Yardımcısı Onayı.
        /// </summary>
        internal static string HistoryState_GenelMüdürYardımcısıOnayı {
            get {
                return ResourceManager.GetString("HistoryState_GenelMüdürYardımcısıOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GM Onayı.
        /// </summary>
        internal static string HistoryState_GMOnayı {
            get {
                return ResourceManager.GetString("HistoryState_GMOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GM onayı.
        /// </summary>
        internal static string HistoryState_GMonayı1 {
            get {
                return ResourceManager.GetString("HistoryState_GMonayı1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GMY Onayı.
        /// </summary>
        internal static string HistoryState_GMYOnayı {
            get {
                return ResourceManager.GetString("HistoryState_GMYOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GSM Talebi- Talep Oluşturma.
        /// </summary>
        internal static string HistoryState_GSMTalebiTalepOluşturma {
            get {
                return ResourceManager.GetString("HistoryState_GSMTalebiTalepOluşturma", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Görüntüleme Talebi.
        /// </summary>
        internal static string HistoryState_GörüntülemeTalebi {
            get {
                return ResourceManager.GetString("HistoryState_GörüntülemeTalebi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hazine Onayı.
        /// </summary>
        internal static string HistoryState_HazineOnayı {
            get {
                return ResourceManager.GetString("HistoryState_HazineOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hazine onayı.
        /// </summary>
        internal static string HistoryState_Hazineonayı1 {
            get {
                return ResourceManager.GetString("HistoryState_Hazineonayı1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hazine Onayı 2.
        /// </summary>
        internal static string HistoryState_HazineOnayı2 {
            get {
                return ResourceManager.GetString("HistoryState_HazineOnayı2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hazine Üst Onayı.
        /// </summary>
        internal static string HistoryState_HazineÜstOnayı {
            get {
                return ResourceManager.GetString("HistoryState_HazineÜstOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Head End Bütçe Departman Yöneticisi.
        /// </summary>
        internal static string HistoryState_HeadEndBütçeDepartmanYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_HeadEndBütçeDepartmanYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Head End Bütçe GMY.
        /// </summary>
        internal static string HistoryState_HeadEndBütçeGMY {
            get {
                return ResourceManager.GetString("HistoryState_HeadEndBütçeGMY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Head End Bütçe Yöneticisi.
        /// </summary>
        internal static string HistoryState_HeadEndBütçeYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_HeadEndBütçeYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hukuk Departmanı - Hukuku Çalışanı.
        /// </summary>
        internal static string HistoryState_HukukDepartmanıHukukuÇalışanı {
            get {
                return ResourceManager.GetString("HistoryState_HukukDepartmanıHukukuÇalışanı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hukuk GMY Onayı.
        /// </summary>
        internal static string HistoryState_HukukGMYOnayı {
            get {
                return ResourceManager.GetString("HistoryState_HukukGMYOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hukuk Müdürü Onayı.
        /// </summary>
        internal static string HistoryState_HukukMüdürüOnayı {
            get {
                return ResourceManager.GetString("HistoryState_HukukMüdürüOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hukuk Müdürü Yönlendirme.
        /// </summary>
        internal static string HistoryState_HukukMüdürüYönlendirme {
            get {
                return ResourceManager.GetString("HistoryState_HukukMüdürüYönlendirme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IK Bölüm Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_IKBölümYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_IKBölümYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IK Onayı.
        /// </summary>
        internal static string HistoryState_IKOnayı {
            get {
                return ResourceManager.GetString("HistoryState_IKOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IK Operasyon Onayı.
        /// </summary>
        internal static string HistoryState_IKOperasyonOnayı {
            get {
                return ResourceManager.GetString("HistoryState_IKOperasyonOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IK-Operasyon Onayı.
        /// </summary>
        internal static string HistoryState_IKOperasyonOnayı1 {
            get {
                return ResourceManager.GetString("HistoryState_IKOperasyonOnayı1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IK Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_IKYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_IKYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ilgili Onayı.
        /// </summary>
        internal static string HistoryState_IlgiliOnayı {
            get {
                return ResourceManager.GetString("HistoryState_IlgiliOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ilk Onay Grubu Onayi.
        /// </summary>
        internal static string HistoryState_IlkOnayGrubuOnayi {
            get {
                return ResourceManager.GetString("HistoryState_IlkOnayGrubuOnayi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IS Onayı.
        /// </summary>
        internal static string HistoryState_ISOnayı {
            get {
                return ResourceManager.GetString("HistoryState_ISOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kanal Tipi Spor ise.
        /// </summary>
        internal static string HistoryState_KanalTipiSporise {
            get {
                return ResourceManager.GetString("HistoryState_KanalTipiSporise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kanal Tipi Ulusal ise.
        /// </summary>
        internal static string HistoryState_KanalTipiUlusalise {
            get {
                return ResourceManager.GetString("HistoryState_KanalTipiUlusalise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kartvizit Basım Adımı.
        /// </summary>
        internal static string HistoryState_KartvizitBasımAdımı {
            get {
                return ResourceManager.GetString("HistoryState_KartvizitBasımAdımı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kontrol Grubu Onayı.
        /// </summary>
        internal static string HistoryState_KontrolGrubuOnayı {
            get {
                return ResourceManager.GetString("HistoryState_KontrolGrubuOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kontrol Onayı.
        /// </summary>
        internal static string HistoryState_KontrolOnayı {
            get {
                return ResourceManager.GetString("HistoryState_KontrolOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KRM Operasyon Onayı.
        /// </summary>
        internal static string HistoryState_KRMOperasyonOnayı {
            get {
                return ResourceManager.GetString("HistoryState_KRMOperasyonOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kırtasiye Yetkilisi.
        /// </summary>
        internal static string HistoryState_KırtasiyeYetkilisi {
            get {
                return ResourceManager.GetString("HistoryState_KırtasiyeYetkilisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LIG TV BUTCE-1.
        /// </summary>
        internal static string HistoryState_LIGTVBUTCE1 {
            get {
                return ResourceManager.GetString("HistoryState_LIGTVBUTCE1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LIG TV BUTCE-2.
        /// </summary>
        internal static string HistoryState_LIGTVBUTCE2 {
            get {
                return ResourceManager.GetString("HistoryState_LIGTVBUTCE2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lig TV Operasyon Onayı.
        /// </summary>
        internal static string HistoryState_LigTVOperasyonOnayı {
            get {
                return ResourceManager.GetString("HistoryState_LigTVOperasyonOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lig TV Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_LigTVYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_LigTVYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maintenance Tanımlama ve Onayı.
        /// </summary>
        internal static string HistoryState_MaintenanceTanımlamaveOnayı {
            get {
                return ResourceManager.GetString("HistoryState_MaintenanceTanımlamaveOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maliyet ve Kıymetli Evraklar Muhasebe Onayı.
        /// </summary>
        internal static string HistoryState_MaliyetveKıymetliEvraklarMuhasebeOnayı {
            get {
                return ResourceManager.GetString("HistoryState_MaliyetveKıymetliEvraklarMuhasebeOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MCR Tanımlama ve Onayı.
        /// </summary>
        internal static string HistoryState_MCRTanımlamaveOnayı {
            get {
                return ResourceManager.GetString("HistoryState_MCRTanımlamaveOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Muhasebe Onayı.
        /// </summary>
        internal static string HistoryState_MuhasebeOnayı {
            get {
                return ResourceManager.GetString("HistoryState_MuhasebeOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Onay Kontrol.
        /// </summary>
        internal static string HistoryState_OnayKontrol {
            get {
                return ResourceManager.GetString("HistoryState_OnayKontrol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organizasyonel Gelişim Onayı.
        /// </summary>
        internal static string HistoryState_OrganizasyonelGelişimOnayı {
            get {
                return ResourceManager.GetString("HistoryState_OrganizasyonelGelişimOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organizasyonel Gelişim Onayı 2.
        /// </summary>
        internal static string HistoryState_OrganizasyonelGelişimOnayı2 {
            get {
                return ResourceManager.GetString("HistoryState_OrganizasyonelGelişimOnayı2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organizasyonel Gelişim Onayı 3.
        /// </summary>
        internal static string HistoryState_OrganizasyonelGelişimOnayı3 {
            get {
                return ResourceManager.GetString("HistoryState_OrganizasyonelGelişimOnayı3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organizasyonel Gelişim Onayı 4.
        /// </summary>
        internal static string HistoryState_OrganizasyonelGelişimOnayı4 {
            get {
                return ResourceManager.GetString("HistoryState_OrganizasyonelGelişimOnayı4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Otomatik Onay.
        /// </summary>
        internal static string HistoryState_OtomatikOnay {
            get {
                return ResourceManager.GetString("HistoryState_OtomatikOnay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pazarlama Değerlendirme.
        /// </summary>
        internal static string HistoryState_PazarlamaDeğerlendirme {
            get {
                return ResourceManager.GetString("HistoryState_PazarlamaDeğerlendirme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pazarlama GMY Onayı.
        /// </summary>
        internal static string HistoryState_PazarlamaGMYOnayı {
            get {
                return ResourceManager.GetString("HistoryState_PazarlamaGMYOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personel Düzeltme.
        /// </summary>
        internal static string HistoryState_PersonelDüzeltme {
            get {
                return ResourceManager.GetString("HistoryState_PersonelDüzeltme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rapor Kontrol Adımı.
        /// </summary>
        internal static string HistoryState_RaporKontrolAdımı {
            get {
                return ResourceManager.GetString("HistoryState_RaporKontrolAdımı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regülasyon Onayi.
        /// </summary>
        internal static string HistoryState_RegülasyonOnayi {
            get {
                return ResourceManager.GetString("HistoryState_RegülasyonOnayi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Satınalma Sahibi Onayı.
        /// </summary>
        internal static string HistoryState_SatınalmaSahibiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_SatınalmaSahibiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Satış Bütçe Yöneticisi.
        /// </summary>
        internal static string HistoryState_SatışBütçeYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_SatışBütçeYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Satış Genel Müdür Onayı.
        /// </summary>
        internal static string HistoryState_SatışGenelMüdürOnayı {
            get {
                return ResourceManager.GetString("HistoryState_SatışGenelMüdürOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Satış Yetkili Onayı.
        /// </summary>
        internal static string HistoryState_SatışYetkiliOnayı {
            get {
                return ResourceManager.GetString("HistoryState_SatışYetkiliOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sipariş Aşaması.
        /// </summary>
        internal static string HistoryState_SiparişAşaması {
            get {
                return ResourceManager.GetString("HistoryState_SiparişAşaması", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sistem Onayı.
        /// </summary>
        internal static string HistoryState_SistemOnayı {
            get {
                return ResourceManager.GetString("HistoryState_SistemOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRBC Yetkilisi Onayı.
        /// </summary>
        internal static string HistoryState_SRBCYetkilisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_SRBCYetkilisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Takım Yönetici Onayı.
        /// </summary>
        internal static string HistoryState_TakımYöneticiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TakımYöneticiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Takım Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_TakımYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TakımYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Başlat.
        /// </summary>
        internal static string HistoryState_TalepBaşlat {
            get {
                return ResourceManager.GetString("HistoryState_TalepBaşlat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Başlatma.
        /// </summary>
        internal static string HistoryState_TalepBaşlatma {
            get {
                return ResourceManager.GetString("HistoryState_TalepBaşlatma", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Oluştur.
        /// </summary>
        internal static string HistoryState_TalepOluştur {
            get {
                return ResourceManager.GetString("HistoryState_TalepOluştur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Oluşturana Düzeltme.
        /// </summary>
        internal static string HistoryState_TalepOluşturanaDüzeltme {
            get {
                return ResourceManager.GetString("HistoryState_TalepOluşturanaDüzeltme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Oluşturan Düzeltme.
        /// </summary>
        internal static string HistoryState_TalepOluşturanDüzeltme {
            get {
                return ResourceManager.GetString("HistoryState_TalepOluşturanDüzeltme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Oluşturma.
        /// </summary>
        internal static string HistoryState_TalepOluşturma {
            get {
                return ResourceManager.GetString("HistoryState_TalepOluşturma", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Onay.
        /// </summary>
        internal static string HistoryState_TalepOnay {
            get {
                return ResourceManager.GetString("HistoryState_TalepOnay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Onaylandı.
        /// </summary>
        internal static string HistoryState_TalepOnaylandı {
            get {
                return ResourceManager.GetString("HistoryState_TalepOnaylandı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep OnaylandıXXX.
        /// </summary>
        internal static string HistoryState_TalepOnaylandıXXX {
            get {
                return ResourceManager.GetString("HistoryState_TalepOnaylandıXXX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Onay - Satın Alındı.
        /// </summary>
        internal static string HistoryState_TalepOnaySatınAlındı {
            get {
                return ResourceManager.GetString("HistoryState_TalepOnaySatınAlındı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Otomatik Onay.
        /// </summary>
        internal static string HistoryState_TalepOtomatikOnay {
            get {
                return ResourceManager.GetString("HistoryState_TalepOtomatikOnay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Red.
        /// </summary>
        internal static string HistoryState_TalepRed {
            get {
                return ResourceManager.GetString("HistoryState_TalepRed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Reddedildi.
        /// </summary>
        internal static string HistoryState_TalepReddedildi {
            get {
                return ResourceManager.GetString("HistoryState_TalepReddedildi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Sahibi Düzeltme.
        /// </summary>
        internal static string HistoryState_TalepSahibiDüzeltme {
            get {
                return ResourceManager.GetString("HistoryState_TalepSahibiDüzeltme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Sahibi Giriş Onayı.
        /// </summary>
        internal static string HistoryState_TalepSahibiGirişOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TalepSahibiGirişOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Sahibi Gmy Onayı.
        /// </summary>
        internal static string HistoryState_TalepSahibiGmyOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TalepSahibiGmyOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Sahibi GMY Onayı.
        /// </summary>
        internal static string HistoryState_TalepSahibiGMYOnayı1 {
            get {
                return ResourceManager.GetString("HistoryState_TalepSahibiGMYOnayı1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Sahibi Onayı.
        /// </summary>
        internal static string HistoryState_TalepSahibiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TalepSahibiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teklif Aşaması.
        /// </summary>
        internal static string HistoryState_TeklifAşaması {
            get {
                return ResourceManager.GetString("HistoryState_TeklifAşaması", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teknik Servis Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_TeknikServisYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TeknikServisYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Teknik Servis Yönlendirme Onayı.
        /// </summary>
        internal static string HistoryState_TeknikServisYönlendirmeOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TeknikServisYönlendirmeOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test Aşaması.
        /// </summary>
        internal static string HistoryState_TestAşaması {
            get {
                return ResourceManager.GetString("HistoryState_TestAşaması", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ticari Satış Onayı.
        /// </summary>
        internal static string HistoryState_TicariSatışOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TicariSatışOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transmission Tanımlama ve Onayı -1-.
        /// </summary>
        internal static string HistoryState_TransmissionTanımlamaveOnayı1 {
            get {
                return ResourceManager.GetString("HistoryState_TransmissionTanımlamaveOnayı1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transmission Tanımlama ve Onayı -2-.
        /// </summary>
        internal static string HistoryState_TransmissionTanımlamaveOnayı2 {
            get {
                return ResourceManager.GetString("HistoryState_TransmissionTanımlamaveOnayı2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turkmax Bütçe Yöneticisi.
        /// </summary>
        internal static string HistoryState_TurkmaxBütçeYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_TurkmaxBütçeYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TURKMAX Finans Onayı.
        /// </summary>
        internal static string HistoryState_TURKMAXFinansOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TURKMAXFinansOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TURKMAX Operasyon Onayı.
        /// </summary>
        internal static string HistoryState_TURKMAXOperasyonOnayı {
            get {
                return ResourceManager.GetString("HistoryState_TURKMAXOperasyonOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ulusal Bölge Müdürü Onayı.
        /// </summary>
        internal static string HistoryState_UlusalBölgeMüdürüOnayı {
            get {
                return ResourceManager.GetString("HistoryState_UlusalBölgeMüdürüOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ulusal Satış GMY Onayı.
        /// </summary>
        internal static string HistoryState_UlusalSatışGMYOnayı {
            get {
                return ResourceManager.GetString("HistoryState_UlusalSatışGMYOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ulusal Satış Yönetici Onayı.
        /// </summary>
        internal static string HistoryState_UlusalSatışYöneticiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_UlusalSatışYöneticiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yardım Masası.
        /// </summary>
        internal static string HistoryState_YardımMasası {
            get {
                return ResourceManager.GetString("HistoryState_YardımMasası", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yardım Masası 1. Adım.
        /// </summary>
        internal static string HistoryState_YardımMasası1Adım {
            get {
                return ResourceManager.GetString("HistoryState_YardımMasası1Adım", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yardım Masası 1.Adım Onayı.
        /// </summary>
        internal static string HistoryState_YardımMasası1AdımOnayı {
            get {
                return ResourceManager.GetString("HistoryState_YardımMasası1AdımOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yardım Masası 2. Adım.
        /// </summary>
        internal static string HistoryState_YardımMasası2Adım {
            get {
                return ResourceManager.GetString("HistoryState_YardımMasası2Adım", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili Grubu Onayı.
        /// </summary>
        internal static string HistoryState_YetkiliGrubuOnayı {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliGrubuOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili Onayı.
        /// </summary>
        internal static string HistoryState_YetkiliOnayı {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili Onayı 1.
        /// </summary>
        internal static string HistoryState_YetkiliOnayı1 {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliOnayı1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili Onayı (Kullanilmiyor).
        /// </summary>
        internal static string HistoryState_YetkiliOnayıKullanilmiyor {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliOnayıKullanilmiyor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili Onayı Yurt Dışı.
        /// </summary>
        internal static string HistoryState_YetkiliOnayıYurtDışı {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliOnayıYurtDışı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili Onayı Yurt İçi.
        /// </summary>
        internal static string HistoryState_YetkiliOnayıYurtİçi {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliOnayıYurtİçi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili yönetici Giriş Onayı.
        /// </summary>
        internal static string HistoryState_YetkiliyöneticiGirişOnayı {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliyöneticiGirişOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili Yönetici Onayı.
        /// </summary>
        internal static string HistoryState_YetkiliYöneticiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliYöneticiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili Yönetici Onayı 2.
        /// </summary>
        internal static string HistoryState_YetkiliYöneticiOnayı2 {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliYöneticiOnayı2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkili Yönetici Onayı 3.
        /// </summary>
        internal static string HistoryState_YetkiliYöneticiOnayı3 {
            get {
                return ResourceManager.GetString("HistoryState_YetkiliYöneticiOnayı3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetki Onay.
        /// </summary>
        internal static string HistoryState_YetkiOnay {
            get {
                return ResourceManager.GetString("HistoryState_YetkiOnay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yonetici Grubu.
        /// </summary>
        internal static string HistoryState_YoneticiGrubu {
            get {
                return ResourceManager.GetString("HistoryState_YoneticiGrubu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to YTS Talep Kapatma İşlemi.
        /// </summary>
        internal static string HistoryState_YTSTalepKapatmaİşlemi {
            get {
                return ResourceManager.GetString("HistoryState_YTSTalepKapatmaİşlemi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yurtdışı Finans Onayı.
        /// </summary>
        internal static string HistoryState_YurtdışıFinansOnayı {
            get {
                return ResourceManager.GetString("HistoryState_YurtdışıFinansOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yurtdışı Satış Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_YurtdışıSatışYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_YurtdışıSatışYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yönetici Değerlendirme.
        /// </summary>
        internal static string HistoryState_YöneticiDeğerlendirme {
            get {
                return ResourceManager.GetString("HistoryState_YöneticiDeğerlendirme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yönetici Onayı.
        /// </summary>
        internal static string HistoryState_YöneticiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_YöneticiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zincir Mağaza Yetkili.
        /// </summary>
        internal static string HistoryState_ZincirMağazaYetkili {
            get {
                return ResourceManager.GetString("HistoryState_ZincirMağazaYetkili", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Çalışan Onayı.
        /// </summary>
        internal static string HistoryState_ÇalışanOnayı {
            get {
                return ResourceManager.GetString("HistoryState_ÇalışanOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Özellikleri Tanımlama ve Onayı.
        /// </summary>
        internal static string HistoryState_ÖzellikleriTanımlamaveOnayı {
            get {
                return ResourceManager.GetString("HistoryState_ÖzellikleriTanımlamaveOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Geliştirme Ve Oper. GMY Onayı.
        /// </summary>
        internal static string HistoryState_ÜrünGeliştirmeVeOperGMYOnayı {
            get {
                return ResourceManager.GetString("HistoryState_ÜrünGeliştirmeVeOperGMYOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ürün Geliştirme Ve Oper.GMY Onayı.
        /// </summary>
        internal static string HistoryState_ÜrünGeliştirmeVeOperGMYOnayı1 {
            get {
                return ResourceManager.GetString("HistoryState_ÜrünGeliştirmeVeOperGMYOnayı1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Üst Yöneticisi Onay.
        /// </summary>
        internal static string HistoryState_ÜstYöneticisiOnay {
            get {
                return ResourceManager.GetString("HistoryState_ÜstYöneticisiOnay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Üst Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_ÜstYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_ÜstYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İnsan Kaynakları Adımı.
        /// </summary>
        internal static string HistoryState_İnsanKaynaklarıAdımı {
            get {
                return ResourceManager.GetString("HistoryState_İnsanKaynaklarıAdımı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İnsan Kaynakları Yetkilsi.
        /// </summary>
        internal static string HistoryState_İnsanKaynaklarıYetkilsi {
            get {
                return ResourceManager.GetString("HistoryState_İnsanKaynaklarıYetkilsi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İçerik Bütçe Yöneticisi.
        /// </summary>
        internal static string HistoryState_İçerikBütçeYöneticisi {
            get {
                return ResourceManager.GetString("HistoryState_İçerikBütçeYöneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İçerik Bütçe Yöneticisi-2.
        /// </summary>
        internal static string HistoryState_İçerikBütçeYöneticisi2 {
            get {
                return ResourceManager.GetString("HistoryState_İçerikBütçeYöneticisi2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İç Hizmetler 2. Yönetici Onayı.
        /// </summary>
        internal static string HistoryState_İçHizmetler2YöneticiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_İçHizmetler2YöneticiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İç Hizmetler Birim Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_İçHizmetlerBirimYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_İçHizmetlerBirimYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İç Hizmetler Bölüm Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_İçHizmetlerBölümYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_İçHizmetlerBölümYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İç Hizmetler Genel Müdür Yardımcısı Onayı.
        /// </summary>
        internal static string HistoryState_İçHizmetlerGenelMüdürYardımcısıOnayı {
            get {
                return ResourceManager.GetString("HistoryState_İçHizmetlerGenelMüdürYardımcısıOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İç Hizmetler GMY Onayı.
        /// </summary>
        internal static string HistoryState_İçHizmetlerGMYOnayı {
            get {
                return ResourceManager.GetString("HistoryState_İçHizmetlerGMYOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İç Hizmetler Yöneticisi Onayı.
        /// </summary>
        internal static string HistoryState_İçHizmetlerYöneticisiOnayı {
            get {
                return ResourceManager.GetString("HistoryState_İçHizmetlerYöneticisiOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşe Giriş İşlemleri.
        /// </summary>
        internal static string HistoryState_İşeGirişİşlemleri {
            get {
                return ResourceManager.GetString("HistoryState_İşeGirişİşlemleri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İş Geliştirme ve Strateji GMY Onayı.
        /// </summary>
        internal static string HistoryState_İşGeliştirmeveStratejiGMYOnayı {
            get {
                return ResourceManager.GetString("HistoryState_İşGeliştirmeveStratejiGMYOnayı", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçilen akışlar onaylandı..
        /// </summary>
        internal static string inbox_akislar_onaylandi {
            get {
                return ResourceManager.GetString("inbox_akislar_onaylandi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Multiple Workflow is Approved.
        /// </summary>
        internal static string inbox_akislar_toplu_onay {
            get {
                return ResourceManager.GetString("inbox_akislar_toplu_onay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçili akış bulunamadı.
        /// </summary>
        internal static string inbox_akis_yok {
            get {
                return ResourceManager.GetString("inbox_akis_yok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inbox oluşturulurken bir hata oluştu..
        /// </summary>
        internal static string inbox_hata_olustu {
            get {
                return ResourceManager.GetString("inbox_hata_olustu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yıllık İzin Talep Formu Akış Yöneticileri (CİHAN BAYRAKTAR,NUR BAŞEĞMEZER,ŞENİZ ÖZYAPAR).
        /// </summary>
        internal static string izin_admin {
            get {
                return ResourceManager.GetString("izin_admin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzin Bakiye Süresi (Gün).
        /// </summary>
        internal static string izin_bakiye {
            get {
                return ResourceManager.GetString("izin_bakiye", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzin Başlangıç Tarihi  .
        /// </summary>
        internal static string izin_basla {
            get {
                return ResourceManager.GetString("izin_basla", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen Başlangıç Tarihini Giriniz.
        /// </summary>
        internal static string izin_basla_uyari {
            get {
                return ResourceManager.GetString("izin_basla_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzin Bitiş Tarihi  .
        /// </summary>
        internal static string izin_bitis {
            get {
                return ResourceManager.GetString("izin_bitis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen Bitiş Tarihini Giriniz.
        /// </summary>
        internal static string izin_bitis_uyari {
            get {
                return ResourceManager.GetString("izin_bitis_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tüm Onaylarla İzinden Dönünce Bizzat ilgilenmek istemekteyim.İznim Sürecince onayımı gerektirecek herhangi bir akış delege etmek istemiyorum..
        /// </summary>
        internal static string izin_delegasyon {
            get {
                return ResourceManager.GetString("izin_delegasyon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu izin talebini yapabilmek için belirlediğiniz tarihler arasında bir delegasyonunuz bulunmalıdır. Tüm onaylarla izin dönüşü bizzat ilgilenmek istiyorsanız aşağıdaki kırmızı kutucuğu işaretleyiniz..
        /// </summary>
        internal static string izin_delegasyon_uyari {
            get {
                return ResourceManager.GetString("izin_delegasyon_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzin Süresince Onayınıza gidecek hangi akışları kimlere delege etmek istersiniz?.
        /// </summary>
        internal static string izin_delege {
            get {
                return ResourceManager.GetString("izin_delege", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen Tıklayınız.
        /// </summary>
        internal static string izin_delege_form {
            get {
                return ResourceManager.GetString("izin_delege_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçim yapmamanız durumunda kurumsal süreçlerde aksaklık yaşanacaktır..
        /// </summary>
        internal static string izin_delege_uyari {
            get {
                return ResourceManager.GetString("izin_delege_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gün.
        /// </summary>
        internal static string izin_gun {
            get {
                return ResourceManager.GetString("izin_gun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzin kayıt bilgileriniz bulunamadı, lütfen sicil numaranız ve izin bilgilerinizin uyuşması için Insan Kaynakları ile iletişime geçiniz..
        /// </summary>
        internal static string izin_kayit_bulunamadi {
            get {
                return ResourceManager.GetString("izin_kayit_bulunamadi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzin Nedeni.
        /// </summary>
        internal static string izin_nedeni {
            get {
                return ResourceManager.GetString("izin_nedeni", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yıllık İzin Prosedürü.
        /// </summary>
        internal static string izin_prosedur {
            get {
                return ResourceManager.GetString("izin_prosedur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzin Süresi ( Gün ) .
        /// </summary>
        internal static string izin_sure {
            get {
                return ResourceManager.GetString("izin_sure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İzin Türü.
        /// </summary>
        internal static string izin_turu {
            get {
                return ResourceManager.GetString("izin_turu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yarım Gün.
        /// </summary>
        internal static string izin_yarim_gun {
            get {
                return ResourceManager.GetString("izin_yarim_gun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama alanına maximum 4000 karakter girebilirsiniz.
        /// </summary>
        internal static string main_4000_sinir {
            get {
                return ResourceManager.GetString("main_4000_sinir", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama.
        /// </summary>
        internal static string Main_aciklama {
            get {
                return ResourceManager.GetString("Main_aciklama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Description.
        /// </summary>
        internal static string main_aciklama_degistir {
            get {
                return ResourceManager.GetString("main_aciklama_degistir", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama Düzeltme.
        /// </summary>
        internal static string Main_aciklama_duzeltme {
            get {
                return ResourceManager.GetString("Main_aciklama_duzeltme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen bir açıklama giriniz.
        /// </summary>
        internal static string Main_Aciklama_Giriniz {
            get {
                return ResourceManager.GetString("Main_Aciklama_Giriniz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama&lt;br&gt;Öneri.
        /// </summary>
        internal static string Main_aciklama_oneri {
            get {
                return ResourceManager.GetString("Main_aciklama_oneri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktivite Tipi.
        /// </summary>
        internal static string Main_Activity_Type {
            get {
                return ResourceManager.GetString("Main_Activity_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçili Akışları Onayla.
        /// </summary>
        internal static string main_akislari_onayla {
            get {
                return ResourceManager.GetString("main_akislari_onayla", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Akışı Bitir.
        /// </summary>
        internal static string main_akis_bitir {
            get {
                return ResourceManager.GetString("main_akis_bitir", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Akış Yöneticisi.
        /// </summary>
        internal static string main_akis_yoneticisi {
            get {
                return ResourceManager.GetString("main_akis_yoneticisi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Şu anda &lt;b&gt;akış yöneticisi&lt;/b&gt; olarak işlem yapmaktasınız..
        /// </summary>
        internal static string main_akis_yoneticisi_bildirim {
            get {
                return ResourceManager.GetString("main_akis_yoneticisi_bildirim", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aranacak kelime....
        /// </summary>
        internal static string main_arama_sozcugu {
            get {
                return ResourceManager.GetString("main_arama_sozcugu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen Askıya alma tarihini giriniz..
        /// </summary>
        internal static string main_askiya_alma_tarihiri_gir {
            get {
                return ResourceManager.GetString("main_askiya_alma_tarihiri_gir", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bugüne ait askıya alma tarihi girilemez..
        /// </summary>
        internal static string main_askiya_alma_uyari {
            get {
                return ResourceManager.GetString("main_askiya_alma_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarihine kadar askıya al.
        /// </summary>
        internal static string Main_askiya_al_aciklama {
            get {
                return ResourceManager.GetString("Main_askiya_al_aciklama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Askıya Al.
        /// </summary>
        internal static string Main_Askıya_al {
            get {
                return ResourceManager.GetString("Main_Askıya_al", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beklet / Devam Et.
        /// </summary>
        internal static string Main_Askıya_al_devam {
            get {
                return ResourceManager.GetString("Main_Askıya_al_devam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date.
        /// </summary>
        internal static string main_basla_tarih {
            get {
                return ResourceManager.GetString("main_basla_tarih", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem başarıyla beklemeye alındı..
        /// </summary>
        internal static string main_bekle_bilgi {
            get {
                return ResourceManager.GetString("main_bekle_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bilgi.
        /// </summary>
        internal static string main_bilgi {
            get {
                return ResourceManager.GetString("main_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Date.
        /// </summary>
        internal static string main_bitis_tarih {
            get {
                return ResourceManager.GetString("main_bitis_tarih", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aktif Delegasyonlar.
        /// </summary>
        internal static string main_combo_aktif_delegasyonlar {
            get {
                return ResourceManager.GetString("main_combo_aktif_delegasyonlar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Devam Eden İş Akışları.
        /// </summary>
        internal static string main_combo_devam_eden {
            get {
                return ResourceManager.GetString("main_combo_devam_eden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Durdurulan İş Akışları.
        /// </summary>
        internal static string main_combo_durdurulan {
            get {
                return ResourceManager.GetString("main_combo_durdurulan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geçmiş Delegasyonlar.
        /// </summary>
        internal static string main_combo_gecmis_delegasyonlar {
            get {
                return ResourceManager.GetString("main_combo_gecmis_delegasyonlar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İptal Edilen İş Akışları.
        /// </summary>
        internal static string main_combo_iptal {
            get {
                return ResourceManager.GetString("main_combo_iptal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Onaylanan İş Akışları.
        /// </summary>
        internal static string main_combo_onaylanan {
            get {
                return ResourceManager.GetString("main_combo_onaylanan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reddedilen İş Akışları.
        /// </summary>
        internal static string main_combo_reddedilen {
            get {
                return ResourceManager.GetString("main_combo_reddedilen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tamamlanan İş Akışları.
        /// </summary>
        internal static string main_combo_tamamlanan {
            get {
                return ResourceManager.GetString("main_combo_tamamlanan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tüm İş Akışları.
        /// </summary>
        internal static string main_combo_tum {
            get {
                return ResourceManager.GetString("main_combo_tum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebi yoruma gönderilmiştir..
        /// </summary>
        internal static string main_comment_bilgi {
            get {
                return ResourceManager.GetString("main_comment_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bana yoruma gönderilmiş iş akışları.
        /// </summary>
        internal static string main_comment_inbox {
            get {
                return ResourceManager.GetString("main_comment_inbox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem Yoruma Gönderildi..
        /// </summary>
        internal static string main_comment_islem {
            get {
                return ResourceManager.GetString("main_comment_islem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delegasyon İptali.
        /// </summary>
        internal static string Main_Delegasyon_Iptal {
            get {
                return ResourceManager.GetString("Main_Delegasyon_Iptal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bana delege edilmiş iş akışları.
        /// </summary>
        internal static string main_delege_inbox {
            get {
                return ResourceManager.GetString("main_delege_inbox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yetkilerinizi delege ettiğiniz için bu akış üzerinde işlem yapamazsınız.
        /// </summary>
        internal static string main_delege_uyari {
            get {
                return ResourceManager.GetString("main_delege_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Devam Ettir.
        /// </summary>
        internal static string Main_Devam_Et {
            get {
                return ResourceManager.GetString("Main_Devam_Et", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;İş akışını devam ettirmek istediğinizden emin misiniz?&apos;,&apos;vG1&apos;).
        /// </summary>
        internal static string Main_devam_Soru {
            get {
                return ResourceManager.GetString("Main_devam_Soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Doküman alani bos gecilememektedir, kontrol edip yeniden deneyiniz..
        /// </summary>
        internal static string Main_Dokuman_Uyari {
            get {
                return ResourceManager.GetString("Main_Dokuman_Uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzeltme Talebi.
        /// </summary>
        internal static string main_duzeltme_talebi {
            get {
                return ResourceManager.GetString("main_duzeltme_talebi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Düzenle.
        /// </summary>
        internal static string Main_edit {
            get {
                return ResourceManager.GetString("Main_edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu akış için sistemde bekleyen emir mevcuttur.İşlem yapmak için bir süre sonra lütfen tekrar deneyiniz..
        /// </summary>
        internal static string main_emir_var_uyari {
            get {
                return ResourceManager.GetString("main_emir_var_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu akış için sistemde bekleyen emir mevcuttur.Lütfen ekranı yenileyerek (F5) tekrar deneyiniz..
        /// </summary>
        internal static string main_emir_var_uyari1 {
            get {
                return ResourceManager.GetString("main_emir_var_uyari1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtre.
        /// </summary>
        internal static string main_filtre {
            get {
                return ResourceManager.GetString("main_filtre", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebi sonlandırılmıştır.
        /// </summary>
        internal static string main_finalize_bilgi {
            get {
                return ResourceManager.GetString("main_finalize_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebi yönlendirilmiştir..
        /// </summary>
        internal static string main_forward_bilgi {
            get {
                return ResourceManager.GetString("main_forward_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen geçerli bir tarih formatı (gg.aa.yyyy) giriniz..
        /// </summary>
        internal static string main_gecerli_tarih_formati {
            get {
                return ResourceManager.GetString("main_gecerli_tarih_formati", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geçmiş tarihe askıya alma işlemi yapılamaz..
        /// </summary>
        internal static string main_gecmis_askiya_alma {
            get {
                return ResourceManager.GetString("main_gecmis_askiya_alma", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geri Al.
        /// </summary>
        internal static string Main_Geri_al {
            get {
                return ResourceManager.GetString("Main_Geri_al", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem Geri Alındı.
        /// </summary>
        internal static string main_geri_alindi_bilgi {
            get {
                return ResourceManager.GetString("main_geri_alindi_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;İş akışını geri almak istediğinize emin misiniz ? &apos;,&apos;vG6&apos;).
        /// </summary>
        internal static string Main_Geri_al_soru {
            get {
                return ResourceManager.GetString("Main_Geri_al_soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;Talebi geri göndermek istediğinize emin misiniz ?&apos;,&apos;vG30&apos;).
        /// </summary>
        internal static string Main_Geri_Soru {
            get {
                return ResourceManager.GetString("Main_Geri_Soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Görevlendirilecek Personel.
        /// </summary>
        internal static string Main_Gorev_Personeli {
            get {
                return ResourceManager.GetString("Main_Gorev_Personeli", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Görüntüle.
        /// </summary>
        internal static string Main_goruntule {
            get {
                return ResourceManager.GetString("Main_goruntule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Görüntüleme Türü.
        /// </summary>
        internal static string main_goruntuleme_turu {
            get {
                return ResourceManager.GetString("main_goruntuleme_turu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tümü.
        /// </summary>
        internal static string main_grid_all {
            get {
                return ResourceManager.GetString("main_grid_all", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Atanan.
        /// </summary>
        internal static string main_grid_atanan {
            get {
                return ResourceManager.GetString("main_grid_atanan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Akış Atlatma Talebi Yap.
        /// </summary>
        internal static string main_grid_atlat {
            get {
                return ResourceManager.GetString("main_grid_atlat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geri.
        /// </summary>
        internal static string main_grid_back {
            get {
                return ResourceManager.GetString("main_grid_back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Temizle.
        /// </summary>
        internal static string main_grid_clear {
            get {
                return ResourceManager.GetString("main_grid_clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Devamı Sonraki Sayfada.
        /// </summary>
        internal static string main_grid_devami {
            get {
                return ResourceManager.GetString("main_grid_devami", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İlk.
        /// </summary>
        internal static string main_grid_first {
            get {
                return ResourceManager.GetString("main_grid_first", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Açıklama.
        /// </summary>
        internal static string main_grid_flow_aciklama {
            get {
                return ResourceManager.GetString("main_grid_flow_aciklama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem.
        /// </summary>
        internal static string main_grid_flow_action {
            get {
                return ResourceManager.GetString("main_grid_flow_action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birim.
        /// </summary>
        internal static string main_grid_flow_birim {
            get {
                return ResourceManager.GetString("main_grid_flow_birim", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gönderen.
        /// </summary>
        internal static string main_grid_flow_forwarder {
            get {
                return ResourceManager.GetString("main_grid_flow_forwarder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İş Akışı Adı.
        /// </summary>
        internal static string main_grid_flow_name {
            get {
                return ResourceManager.GetString("main_grid_flow_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Workflow No.
        /// </summary>
        internal static string main_grid_flow_no {
            get {
                return ResourceManager.GetString("main_grid_flow_no", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Başlatan.
        /// </summary>
        internal static string main_grid_flow_owner {
            get {
                return ResourceManager.GetString("main_grid_flow_owner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adım.
        /// </summary>
        internal static string main_grid_flow_state {
            get {
                return ResourceManager.GetString("main_grid_flow_state", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarih.
        /// </summary>
        internal static string main_grid_flow_tarih {
            get {
                return ResourceManager.GetString("main_grid_flow_tarih", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tutar.
        /// </summary>
        internal static string main_grid_flow_tutar {
            get {
                return ResourceManager.GetString("main_grid_flow_tutar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gruplamak istediğiniz alanları buraya sürükleyin.
        /// </summary>
        internal static string main_grid_group {
            get {
                return ResourceManager.GetString("main_grid_group", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hatırlat.
        /// </summary>
        internal static string main_grid_hatirlat {
            get {
                return ResourceManager.GetString("main_grid_hatirlat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Akış Kopyala.
        /// </summary>
        internal static string main_grid_kopyala {
            get {
                return ResourceManager.GetString("main_grid_kopyala", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Son.
        /// </summary>
        internal static string main_grid_last {
            get {
                return ResourceManager.GetString("main_grid_last", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Son Yapılan İşlem.
        /// </summary>
        internal static string main_grid_last_action {
            get {
                return ResourceManager.GetString("main_grid_last_action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Son İşlem Yapan.
        /// </summary>
        internal static string main_grid_last_modified {
            get {
                return ResourceManager.GetString("main_grid_last_modified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İleri.
        /// </summary>
        internal static string main_grid_next {
            get {
                return ResourceManager.GetString("main_grid_next", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Herhangi bir kayıt bulunmamaktadır.
        /// </summary>
        internal static string main_grid_no_row {
            get {
                return ResourceManager.GetString("main_grid_no_row", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Güncelle Sayfasına Geri Dön.
        /// </summary>
        internal static string main_guncelle_don {
            get {
                return ResourceManager.GetString("main_guncelle_don", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Görüntüleme İptali.
        /// </summary>
        internal static string Main_Görüntüleme_Iptal {
            get {
                return ResourceManager.GetString("Main_Görüntüleme_Iptal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hata.
        /// </summary>
        internal static string main_hata {
            get {
                return ResourceManager.GetString("main_hata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem Geçmişi.
        /// </summary>
        internal static string Main_History {
            get {
                return ResourceManager.GetString("Main_History", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen Internet Explorer tarayıcısı ile işlemlerinizi yapınız !.
        /// </summary>
        internal static string main_IE_uyari {
            get {
                return ResourceManager.GetString("main_IE_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Üzerimdeki İşlemler.
        /// </summary>
        internal static string Main_Inbox {
            get {
                return ResourceManager.GetString("Main_Inbox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bana atanmış iş akışları.
        /// </summary>
        internal static string main_inbox_header {
            get {
                return ResourceManager.GetString("main_inbox_header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İptal Et.
        /// </summary>
        internal static string Main_Iptal {
            get {
                return ResourceManager.GetString("Main_Iptal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to talebi başarıyla iptal edilmiştir..
        /// </summary>
        internal static string main_iptal_bilgi {
            get {
                return ResourceManager.GetString("main_iptal_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebi iptal edilmiştir..
        /// </summary>
        internal static string main_iptal_bilgi1 {
            get {
                return ResourceManager.GetString("main_iptal_bilgi1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;İş akışını iptal etmek istediğinizden emin misiniz ?&apos;,&apos;vG4&apos;).
        /// </summary>
        internal static string Main_iptal_soru {
            get {
                return ResourceManager.GetString("Main_iptal_soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem.
        /// </summary>
        internal static string Main_islem {
            get {
                return ResourceManager.GetString("Main_islem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem sırasında bir hata ile karşılaşıldı. Lütfen yeniden deneyin..
        /// </summary>
        internal static string main_islem_hata_bilgi {
            get {
                return ResourceManager.GetString("main_islem_hata_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem Listesi.
        /// </summary>
        internal static string Main_islem_listesi {
            get {
                return ResourceManager.GetString("Main_islem_listesi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kapat.
        /// </summary>
        internal static string main_kapat {
            get {
                return ResourceManager.GetString("main_kapat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lütfen bekleyiniz.
        /// </summary>
        internal static string Main_Lutfen_Bekleyiniz {
            get {
                return ResourceManager.GetString("Main_Lutfen_Bekleyiniz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hata - Lütfen yorum giriniz..
        /// </summary>
        internal static string main_lutfen_yorum_gririniz {
            get {
                return ResourceManager.GetString("main_lutfen_yorum_gririniz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Onay.
        /// </summary>
        internal static string main_onay {
            get {
                return ResourceManager.GetString("main_onay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Onayla.
        /// </summary>
        internal static string Main_Onayla {
            get {
                return ResourceManager.GetString("Main_Onayla", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Onay / Red.
        /// </summary>
        internal static string Main_Onay_Red {
            get {
                return ResourceManager.GetString("Main_Onay_Red", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;İş akışını onaylamak istediğinizden emin misiniz?&apos;,&apos;vG30&apos;).
        /// </summary>
        internal static string Main_Onay_Soru {
            get {
                return ResourceManager.GetString("Main_Onay_Soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Şartlı Onay / Onay seçeneklerinden birini seçmediniz, Lütfen kontrol edip yeniden deneyiniz..
        /// </summary>
        internal static string Main_Onay_Uyari {
            get {
                return ResourceManager.GetString("Main_Onay_Uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reddet.
        /// </summary>
        internal static string Main_Reddet {
            get {
                return ResourceManager.GetString("Main_Reddet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep reddedilmiştir.
        /// </summary>
        internal static string main_reddet_bilgi {
            get {
                return ResourceManager.GetString("main_reddet_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reddetmek için açıklama alanına en fazla 200 karakter girilebilir..
        /// </summary>
        internal static string main_red_karakter_siniri {
            get {
                return ResourceManager.GetString("main_red_karakter_siniri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;İş akışını reddetmek istediğinizden emin misiniz?&apos;,&apos;vG31&apos;).
        /// </summary>
        internal static string Main_Red_Onay {
            get {
                return ResourceManager.GetString("Main_Red_Onay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İş akışını reddetmek istediğinizden emin misiniz?.
        /// </summary>
        internal static string main_red_soru {
            get {
                return ResourceManager.GetString("main_red_soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İşlem başarıyla devam ettirildi..
        /// </summary>
        internal static string main_resume_bilgi {
            get {
                return ResourceManager.GetString("main_resume_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebi devam ettirilmiştir.
        /// </summary>
        internal static string main_resume_bilgi1 {
            get {
                return ResourceManager.GetString("main_resume_bilgi1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Şartlı Onay.
        /// </summary>
        internal static string main_sartli_onay {
            get {
                return ResourceManager.GetString("main_sartli_onay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Seçiniz.
        /// </summary>
        internal static string Main_Seciniz {
            get {
                return ResourceManager.GetString("Main_Seciniz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebine geri alınmıştır..
        /// </summary>
        internal static string main_sendback_bilgi {
            get {
                return ResourceManager.GetString("main_sendback_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebi talep eden kişiye geri gönderilmiştir..
        /// </summary>
        internal static string main_sendowner_bilgi {
            get {
                return ResourceManager.GetString("main_sendowner_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sonlandır.
        /// </summary>
        internal static string Main_sonlandir_delegasyon {
            get {
                return ResourceManager.GetString("Main_sonlandir_delegasyon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adım Adı.
        /// </summary>
        internal static string Main_state {
            get {
                return ResourceManager.GetString("Main_state", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu akış, artık ekranı açtığınız adımda değil.Lütfen ekranı yenileyerek (F5) tekrar deneyiniz..
        /// </summary>
        internal static string main_state_uyusmuyor_uyari {
            get {
                return ResourceManager.GetString("main_state_uyusmuyor_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Askıda Bekleyenler.
        /// </summary>
        internal static string Main_Suspended {
            get {
                return ResourceManager.GetString("Main_Suspended", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebi beklemeye alınmıştır..
        /// </summary>
        internal static string main_suspend_bilgi {
            get {
                return ResourceManager.GetString("main_suspend_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;İş akışını askıya almak istediğinize emin misiniz ?&apos;,&apos;vG3&apos;).
        /// </summary>
        internal static string Main_Suspend_Soru {
            get {
                return ResourceManager.GetString("Main_Suspend_Soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebiniz başarıyla oluşturuldu.
        /// </summary>
        internal static string main_talepolustu_bilgi {
            get {
                return ResourceManager.GetString("main_talepolustu_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Oluştur.
        /// </summary>
        internal static string Main_Talep_Olustur {
            get {
                return ResourceManager.GetString("Main_Talep_Olustur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;İş akışını başlatmak istediğinize istediğinize emin misiniz ?&apos;,&apos;vG0&apos;).
        /// </summary>
        internal static string Main_Talep_Olustur_Soru {
            get {
                return ResourceManager.GetString("Main_Talep_Olustur_Soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hedef Kullanıcı.
        /// </summary>
        internal static string main_target {
            get {
                return ResourceManager.GetString("main_target", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarih.
        /// </summary>
        internal static string Main_tarih {
            get {
                return ResourceManager.GetString("Main_tarih", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarih formatı gg.aa.yyyy olmalı.
        /// </summary>
        internal static string main_Tarihformat_bilgi {
            get {
                return ResourceManager.GetString("main_Tarihformat_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tarih formatı gg.aa.yyyy olmalı.
        /// </summary>
        internal static string Main_tarih_format {
            get {
                return ResourceManager.GetString("Main_tarih_format", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Girilen tarih bugünden büyük olmalıdır..
        /// </summary>
        internal static string Main_tarih_format_aralik {
            get {
                return ResourceManager.GetString("Main_tarih_format_aralik", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu işlem Toplam  xxx  sn sürmüştür..
        /// </summary>
        internal static string main_toplam_saniye_bilgi {
            get {
                return ResourceManager.GetString("main_toplam_saniye_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ***Toplu Onay Fonksiyonu belirli akışlar için geçerlidir..
        /// </summary>
        internal static string main_toplu_onay_bilgi {
            get {
                return ResourceManager.GetString("main_toplu_onay_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı.
        /// </summary>
        internal static string Main_user {
            get {
                return ResourceManager.GetString("Main_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hata - Lütfen yönlendirilecek kullanıcıyı seçiniz..
        /// </summary>
        internal static string main_user_seciniz {
            get {
                return ResourceManager.GetString("main_user_seciniz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uyarı.
        /// </summary>
        internal static string Main_Uyari {
            get {
                return ResourceManager.GetString("Main_Uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to İş akış Yönetimi.
        /// </summary>
        internal static string Main_WF_Admin {
            get {
                return ResourceManager.GetString("Main_WF_Admin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yazdır ve Kapat.
        /// </summary>
        internal static string main_yazdir_kapat {
            get {
                return ResourceManager.GetString("main_yazdir_kapat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bu akışa erişim yetkiniz bulunmamaktadır..
        /// </summary>
        internal static string main_yetki_uyari {
            get {
                return ResourceManager.GetString("main_yetki_uyari", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yönlendir.
        /// </summary>
        internal static string Main_Yonlendir {
            get {
                return ResourceManager.GetString("Main_Yonlendir", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;Akışı yönlendirmek istediğinize emin misiniz ?&apos;,&apos;vG1&apos;).
        /// </summary>
        internal static string Main_yonlendir_soru {
            get {
                return ResourceManager.GetString("Main_yonlendir_soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Akışı yönlendirmek istediğinize emin misiniz ?.
        /// </summary>
        internal static string Main_yonlendir_soru_mobile {
            get {
                return ResourceManager.GetString("Main_yonlendir_soru_mobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yoruma Gönder.
        /// </summary>
        internal static string Main_Yoruma_Gonder {
            get {
                return ResourceManager.GetString("Main_Yoruma_Gonder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;Talebi yoruma göndermek istediğinize emin misiniz ?&apos;,&apos;vG2&apos;).
        /// </summary>
        internal static string main_yoruma_gonder_soru {
            get {
                return ResourceManager.GetString("main_yoruma_gonder_soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talebine yorum  eklenmiştir..
        /// </summary>
        internal static string main_yorumekle_bilgi {
            get {
                return ResourceManager.GetString("main_yorumekle_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yorum Düzeltme.
        /// </summary>
        internal static string main_yorum_duzeltme {
            get {
                return ResourceManager.GetString("main_yorum_duzeltme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yorum Ekle.
        /// </summary>
        internal static string Main_Yorum_Ekle {
            get {
                return ResourceManager.GetString("Main_Yorum_Ekle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yorumunuz başarıyla kaydedildi..
        /// </summary>
        internal static string main_yorum_eklendi_bilgi {
            get {
                return ResourceManager.GetString("main_yorum_eklendi_bilgi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return ConfirmSubmitValGroup(&apos;İş akışına yorum girmek istediğinize emin misiniz ? &apos;,&apos;vG5&apos;).
        /// </summary>
        internal static string Main_yorum_soru {
            get {
                return ResourceManager.GetString("Main_yorum_soru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Talep Sahibi.
        /// </summary>
        internal static string Mobil_Talep_Sahibi {
            get {
                return ResourceManager.GetString("Mobil_Talep_Sahibi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to javascript:return confirm(&apos;Toplu onaylamak istediğinize emin misiniz ?&apos;);.
        /// </summary>
        internal static string topluonaysoru {
            get {
                return ResourceManager.GetString("topluonaysoru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birim.
        /// </summary>
        internal static string Uc_Birim {
            get {
                return ResourceManager.GetString("Uc_Birim", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bölüm.
        /// </summary>
        internal static string Uc_Bolum {
            get {
                return ResourceManager.GetString("Uc_Bolum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Departman.
        /// </summary>
        internal static string Uc_Departman {
            get {
                return ResourceManager.GetString("Uc_Departman", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı.
        /// </summary>
        internal static string Uc_Kullanici {
            get {
                return ResourceManager.GetString("Uc_Kullanici", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kullanıcı alanı boş bırakılamaz..
        /// </summary>
        internal static string Uc_KullaniciHata {
            get {
                return ResourceManager.GetString("Uc_KullaniciHata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Takım.
        /// </summary>
        internal static string Uc_Takim {
            get {
                return ResourceManager.GetString("Uc_Takim", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yönetici.
        /// </summary>
        internal static string Uc_Yonetici {
            get {
                return ResourceManager.GetString("Uc_Yonetici", resourceCulture);
            }
        }
    }
}
