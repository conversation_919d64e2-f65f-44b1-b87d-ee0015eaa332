.dxWeb_rpHeaderTopLeftCorner_RedWine,
.dxWeb_rpHeaderTopRightCorner_RedWine,
.dxWeb_rpBottomLeftCorner_RedWine,
.dxWeb_rpBottomRightCorner_RedWine,
.dxWeb_rpTopLeftCorner_RedWine,
.dxWeb_rpTopRightCorner_RedWine,
.dxWeb_rpGroupBoxBottomLeftCorner_RedWine,
.dxWeb_rpGroupBoxBottomRightCorner_RedWine,
.dxWeb_rpGroupBoxTopLeftCorner_RedWine,
.dxWeb_rpGroupBoxTopRightCorner_RedWine,
.dxWeb_mHorizontalPopOut_RedWine,
.dxWeb_mVerticalPopOut_RedWine,
.dxWeb_mVerticalPopOutRtl_RedWine,
.dxWeb_mSubMenuItem_RedWine,
.dxWeb_mSubMenuItemChecked_RedWine,
.dxWeb_mScrollUp_RedWine,
.dxWeb_mScrollDown_RedWine,
.dxWeb_tcScrollLeft_RedWine,
.dxWeb_tcScrollRight_RedWine,
.dxWeb_tcScrollLeftHover_RedWine,
.dxWeb_tcScrollRightHover_RedWine,
.dxWeb_tcScrollLeftPressed_RedWine,
.dxWeb_tcScrollRightPressed_RedWine,
.dxWeb_tcScrollLeftDisabled_RedWine,
.dxWeb_tcScrollRightDisabled_RedWine,
.dxWeb_nbCollapse_RedWine,
.dxWeb_nbExpand_RedWine,
.dxWeb_splVSeparator_RedWine,
.dxWeb_splVSeparatorHover_RedWine,
.dxWeb_splHSeparator_RedWine,
.dxWeb_splHSeparatorHover_RedWine,
.dxWeb_splVCollapseBackwardButton_RedWine,
.dxWeb_splVCollapseBackwardButtonHover_RedWine,
.dxWeb_splHCollapseBackwardButton_RedWine,
.dxWeb_splHCollapseBackwardButtonHover_RedWine,
.dxWeb_splVCollapseForwardButton_RedWine,
.dxWeb_splVCollapseForwardButtonHover_RedWine,
.dxWeb_splHCollapseForwardButton_RedWine,
.dxWeb_splHCollapseForwardButtonHover_RedWine,
.dxWeb_pcCloseButton_RedWine,
.dxWeb_pcSizeGrip_RedWine,
.dxWeb_pcSizeGripRtl_RedWine,
.dxWeb_pAll_RedWine,
.dxWeb_pAllDisabled_RedWine,
.dxWeb_pPrev_RedWine,
.dxWeb_pPrevDisabled_RedWine,
.dxWeb_pNext_RedWine,
.dxWeb_pNextDisabled_RedWine,
.dxWeb_pLast_RedWine,
.dxWeb_pLastDisabled_RedWine,
.dxWeb_pFirst_RedWine,
.dxWeb_pFirstDisabled_RedWine,
.dxWeb_tiBackToTop_RedWine,
.dxWeb_smLevelBullet_RedWine,
.dxWeb_tvColBtn_RedWine,
.dxWeb_tvColBtnRtl_RedWine,
.dxWeb_tvExpBtn_RedWine,
.dxWeb_tvExpBtnRtl_RedWine,
.dxWeb_fmFolder_RedWine,
.dxWeb_fmFolderLocked_RedWine,
.dxWeb_fmCreateButton_RedWine,
.dxWeb_fmMoveButton_RedWine,
.dxWeb_fmRenameButton_RedWine,
.dxWeb_fmDeleteButton_RedWine,
.dxWeb_fmRefreshButton_RedWine,
.dxWeb_fmCreateButtonDisabled_RedWine,
.dxWeb_fmMoveButtonDisabled_RedWine,
.dxWeb_fmRenameButtonDisabled_RedWine,
.dxWeb_fmDeleteButtonDisabled_RedWine,
.dxWeb_fmRefreshButtonDisabled_RedWine {
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.RedWine.Web.sprite.png")%>');
    -background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.RedWine.Web.sprite.gif")%>'); /* for IE6 */
    background-repeat: no-repeat;
    background-color: transparent;
}

.dxWeb_rpHeaderTopLeftCorner_RedWine {
    background-position: -104px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_rpHeaderTopRightCorner_RedWine {
    background-position: -117px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_rpBottomLeftCorner_RedWine {
    background-position: -78px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_rpBottomRightCorner_RedWine {
    background-position: -91px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_rpTopLeftCorner_RedWine {
    background-position: -52px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_rpTopRightCorner_RedWine {
    background-position: -65px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_rpGroupBoxBottomLeftCorner_RedWine {
    background-position: -26px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_rpGroupBoxBottomRightCorner_RedWine {
    background-position: -39px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_rpGroupBoxTopLeftCorner_RedWine {
    background-position: 0px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_rpGroupBoxTopRightCorner_RedWine {
    background-position: -13px -88px;
    width: 5px;
    height: 5px;
}

.dxWeb_mHorizontalPopOut_RedWine {
    background-position: -92px -50px;
    width: 11px;
    height: 11px;
}

.dxWeb_mVerticalPopOut_RedWine {
    background-position: -105px -50px;
    width: 11px;
    height: 11px;
}

.dxWeb_mVerticalPopOutRtl_RedWine {
    background-position: -107px -62px;
    width: 11px;
    height: 11px;
}

.dxWeb_mSubMenuItem_RedWine {
    background-position: -48px -50px;
    width: 14px;
    height: 14px;
}

.dxWeb_mSubMenuItemChecked_RedWine {
    background-position: -70px -50px;
    width: 14px;
    height: 14px;
}

.dxWeb_mScrollUp_RedWine {
    background-position: -118px -50px;
    width: 7px;
    height: 4px;
}

.dxWeb_mScrollDown_RedWine {
    background-position: -118px -57px;
    width: 7px;
    height: 4px;
}

.dxWeb_tcScrollLeft_RedWine
{
	background-position: -147px 0;
	width: 21px;
	height: 21px;
}

.dxWeb_tcScrollRight_RedWine
{
	background-position: -169px 0;
	width: 21px;
	height: 21px;
}

.dxWeb_tcScrollLeftHover_RedWine
{
	background-position: -147px -22px;
	width: 21px;
	height: 21px;
}

.dxWeb_tcScrollRightHover_RedWine
{
	background-position: -169px -22px;
	width: 21px;
	height: 21px;
}

.dxWeb_tcScrollLeftPressed_RedWine
{
	background-position: -147px -44px;
	width: 21px;
	height: 21px;
}

.dxWeb_tcScrollRightPressed_RedWine
{
	background-position: -169px -44px;
	width: 21px;
	height: 21px;
}

.dxWeb_tcScrollLeftDisabled_RedWine
{
	background-position: -147px -66px;
	width: 21px;
	height: 21px;
}

.dxWeb_tcScrollRightDisabled_RedWine
{
	background-position: -169px -66px;
	width: 21px;
	height: 21px;
}

.dxWeb_nbCollapse_RedWine {
    background-position: -129px 0px;
    width: 17px;
    height: 17px;
}

.dxWeb_nbExpand_RedWine {
    background-position: -129px -23px;
    width: 17px;
    height: 17px;
}

.dxWeb_splVSeparator_RedWine {
    background-position: -92px -62px;
    width: 6px;
    height: 18px;
}

.dxWeb_splVSeparatorHover_RedWine {
    background-position: -99px -62px;
    width: 6px;
    height: 18px;
}

.dxWeb_splHSeparator_RedWine {
    background-position: -58px -74px;
    width: 18px;
    height: 6px;
}

.dxWeb_splHSeparatorHover_RedWine {
    background-position: -58px -81px;
    width: 18px;
    height: 6px;
}

.dxWeb_splVCollapseBackwardButton_RedWine {
    background-position: -30px -74px;
    width: 6px;
    height: 7px;
}

.dxWeb_splVCollapseBackwardButtonHover_RedWine {
    background-position: -37px -74px;
    width: 6px;
    height: 7px;
}

.dxWeb_splHCollapseBackwardButton_RedWine {
    background-position: 0px -74px;
    width: 7px;
    height: 6px;
}

.dxWeb_splHCollapseBackwardButtonHover_RedWine {
    background-position: 0px -81px;
    width: 7px;
    height: 6px;
}

.dxWeb_splVCollapseForwardButton_RedWine {
    background-position: -44px -74px;
    width: 6px;
    height: 7px;
}

.dxWeb_splVCollapseForwardButtonHover_RedWine {
    background-position: -51px -74px;
    width: 6px;
    height: 7px;
}

.dxWeb_splHCollapseForwardButton_RedWine {
    background-position: -15px -74px;
    width: 7px;
    height: 6px;
}

.dxWeb_splHCollapseForwardButtonHover_RedWine {
    background-position: -15px -81px;
    width: 7px;
    height: 6px;
}

.dxWeb_pcCloseButton_RedWine {
    background-position: 0px -50px;
    width: 19px;
    height: 19px;
}

.dxWeb_pcSizeGrip_RedWine {
    background-position: -24px -50px;
    width: 13px;
    height: 13px;
}

.dxWeb_pcSizeGripRtl_RedWine {
    background-position: -39px -95px;
    width: 13px;
    height: 13px;
}

.dxWeb_pAll_RedWine {
    background-position: 0px 0px;
    width: 24px;
    height: 19px;
}

.dxWeb_pAllDisabled_RedWine {
    background-position: 0px -25px;
    width: 24px;
    height: 19px;
}

.dxWeb_pPrev_RedWine {
    background-position: -105px 0px;
    width: 19px;
    height: 19px;
}

.dxWeb_pPrevDisabled_RedWine {
    background-position: -105px -25px;
    width: 19px;
    height: 19px;
}

.dxWeb_pNext_RedWine {
    background-position: -81px 0px;
    width: 19px;
    height: 19px;
}

.dxWeb_pNextDisabled_RedWine {
    background-position: -81px -25px;
    width: 19px;
    height: 19px;
}

.dxWeb_pLast_RedWine {
    background-position: -55px 0px;
    width: 23px;
    height: 19px;
}

.dxWeb_pLastDisabled_RedWine {
    background-position: -55px -25px;
    width: 23px;
    height: 19px;
}

.dxWeb_pFirst_RedWine {
    background-position: -29px 0px;
    width: 23px;
    height: 19px;
}

.dxWeb_pFirstDisabled_RedWine {
    background-position: -29px -25px;
    width: 23px;
    height: 19px;
}

.dxWeb_tiBackToTop_RedWine {
    background-position: -129px -50px;
    width: 13px;
    height: 13px;
}

.dxWeb_smLevelBullet_RedWine {
    background-position: -130px -66px;
    width: 5px;
    height: 5px;
}

.dxWeb_tvColBtn_RedWine,
.dxWeb_tvColBtnRtl_RedWine {
	background-position: -19px -95px;
    width: 17px;
    height: 17px;
}

.dxWeb_tvExpBtn_RedWine,
.dxWeb_tvExpBtnRtl_RedWine {
	background-position: 0px -95px;
    width: 17px;
    height: 17px;
}

.dxWeb_fmFolder_RedWine {
	background-position: 0px -113px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmFolderLocked_RedWine {
	background-position: -20px -113px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmCreateButton_RedWine {
	background-position: -40px -113px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmRenameButton_RedWine {
	background-position: -60px -113px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmMoveButton_RedWine {
	background-position: -80px -113px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmDeleteButton_RedWine {
	background-position: -100px -113px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmRefreshButton_RedWine {
	background-position: -120px -113px;
    width: 16px;
    height: 16px;
}
.dxWeb_fmCreateButtonDisabled_RedWine {
	background-position: -40px -137px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmRenameButtonDisabled_RedWine {
	background-position: -60px -137px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmMoveButtonDisabled_RedWine {
	background-position: -80px -137px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmDeleteButtonDisabled_RedWine {
	background-position: -100px -137px;
    width: 16px;
    height: 16px;
}

.dxWeb_fmRefreshButtonDisabled_RedWine {
	background-position: -120px -137px;
    width: 16px;
    height: 16px;
}