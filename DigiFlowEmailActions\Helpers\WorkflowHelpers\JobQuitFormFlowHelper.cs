﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.GenericMailHelper;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class JobQuitFormFlowHelper
    {
        internal static void RejectWorkFlow(Digiturk.Workflow.Common.WFContext CurrentWFContext, Digiturk.Workflow.Entities.FWfWorkflowInstance CurrentWfIns, FWfActionTaskInstance CurrentActionTaskInstance)
        {
            ActionType CurrentActionOwnerType = ActionType.ONWFREJECTFLOWUSERS;
            long TemplateMailId = Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.GetTemplateId(CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, CurrentActionOwnerType);
            JobQuitFormRequest RequestObject = WFRepository<Digiturk.Workflow.Digiflow.Entities.JobQuitFormRequest>.GetEntity(CurrentWfIns.EntityRefId);
            DataTable dtManagers = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.GetHierarchialManagersByLoginId(RequestObject.PersonnelId);
            string nameSurname = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(RequestObject.PersonnelId);
            CurrentWFContext.Parameters.AddOrChangeItem("LeavingPersonnel", nameSurname);
            foreach (DataRow row in dtManagers.Rows)
            {
                if (row["SEVIYE"].ToString() != "0")
                {
                    //Yönetici
                    long LoginId = ConvertionHelper.ConvertValue<long>(row["F_LOGIN_ID"].ToString());
                    if (RequestObject.PersonnelId != LoginId)
                    {
                        FLogin Login = WFRepository<FLogin>.GetEntity(LoginId);
                        Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailDirect(TemplateMailId, CurrentActionTaskInstance, CurrentWFContext, new Dictionary<string, string>(), new List<string>() { Login.Email }, new List<string>());
                    }
                }
            }
            Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailDirect(TemplateMailId, CurrentActionTaskInstance, CurrentWFContext, new Dictionary<string, string>(), new List<string>() { System.Configuration.ConfigurationManager.AppSettings["HizmetAlimEposta"] }, new List<string>());
        }
    }
}
