import * as React from 'react'
import { useEffect, useState } from 'react'
import {WGrid, WTable} from '@wface/components'
import { useTranslation } from "react-i18next"
import Api from "../services/http-service";

function HistoryComponent() {
    const { t } = useTranslation()
    const [history, setHistory] = useState([])

    const GetHistory = () => {
        Api.GetData("GetHistory?WfInstanceId=32726").then((result : [])  => {
            setHistory(result)
        })
    }

    useEffect(() => {
        GetHistory()
    }, [])

    return(
        <WGrid container alignItems='center' direction="column" style={{marginTop: 20, marginBottom: 0}}>
            <WGrid style={{background: "#662e85", width: 1000}}>
                <h3 style={{textAlign: "center", color: "white", fontSize: 16,}}>{t('history')}</h3>
                <WTable
                    id=""
                    columns={[
                        {title: "State", field: 'state'},
                        {title: 'User', field: 'user'},
                        {title: 'Operation', field: 'operation'},
                        {title: 'Date', field: 'date'},
                        {title: 'Description Proposal', field: 'description_proposal'},
                        {title: 'Edit Description', field: 'edit_description'},
                    ]}
                    data={history.map(repo => {
                        return {
                            id: repo.State,
                            user: repo.Users,
                            operation: repo.Action,
                            date: repo.Dates,
                            description_proposal: repo.Color,
                            edit_description: repo.Commends,
                        };
                    })}
                    title=""
                    options={{
                        filtering: false,
                        search: false,
                        sorting: false,
                        grouping: false,
                        draggable: false,
                        paging: false,
                    }}
                />
            </WGrid>
        </WGrid>
    )
}

export default HistoryComponent
