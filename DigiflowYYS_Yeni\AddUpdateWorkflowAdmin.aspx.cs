﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WebCore;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Digiflow.YYS.Core;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
namespace DigiflowYYS_Yeni
{

    public partial class AddUpdateWorkflowAdmin : YYSSecurePage
    {
        /// <summary>
        /// İş Akış Admin i nin eklenip düzenleneceği ekran
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                if (!Page.IsPostBack)
                {
                    if (AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAdmin || AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAndFlowAdmin)
                    {

                       this.Master.ShowMenu(true);
                       this.Master.PageTitle = "Yeni Akış Yöneticisi Tanımlama";

                        #region İş akışı ve kullanıcı  Combobox ları doldurulur

                        WorkflowListCheckBoxList.DataTextField = "NAME";
                        WorkflowListCheckBoxList.DataValueField = "WF_WORKFLOW_DEF_ID";
                        WorkflowListCheckBoxList.DataSource = WorkflowHelper.GetLiveWorkFlows();
                        WorkflowListCheckBoxList.DataBind();

                        DataTable dt = HrHelper.GetUsers();
                        UsersASPxComboBox = Digiturk.Workflow.Digiflow.YYS.Core.YYSCommon.FillDropDownList("NAME_SURNAME", "LOGIN_ID", UsersASPxComboBox, dt, "KULLANICI SEÇİNİZ", "0");

                        #endregion İş akışı ve kullanıcı  Combobox ları doldurulur

                        //Sayfaya düzenleme için geliniyor ise kullanıcının admin i olduğu iş akışları işaretlenir
                        if (Request.QueryString["AdminId"] != null)
                        {
                            #region Kullanıcının admin olduğu iş akışları işaretlenir

                           this.Master.PageTitle = "Akış Yöneticisi Düzenleme";

                            UserSelectionPanel.Visible = false;
                            UsersASPxComboBox.SelectedValue = Request.QueryString["AdminId"];
                            long adminId = ConvertionHelper.ConvertValue<long>(Request.QueryString["AdminId"]);
                            WorkFlowAdmin wfa = WorkflowAdminHelper.Get(adminId);
                            List<Digiturk.Workflow.Digiflow.Entities.Workflow> wf = WorkflowHelper.GetWorkflowsOfAdmin(adminId);
                            foreach (ListItem item in WorkflowListCheckBoxList.Items)
                            {
                                bool check = (from s in wf
                                              where s.WorkflowDefId == ConvertionHelper.ConvertValue<decimal>(item.Value.ToString())
                                              select s).FirstOrDefault() != null;
                                if (check)
                                {
                                    item.Selected = true;
                                }
                            }

                            NameASPxLabel.Text = WfDataHelpers.GetLoginNameSurname(wfa.LoginId);
                            IsSystemManagerASPxCheckBox.Checked = ConvertionHelper.ConvertValue<Boolean>(wfa.IsSysAdmin);
                            IsActiveASPxCheckBox.Checked = ConvertionHelper.ConvertValue<Boolean>(wfa.IsActive);

                            #endregion Kullanıcının admin olduğu iş akışları işaretlenir
                        }
                    }
                    else
                    {
                        Response.Redirect("ErrorPage.aspx", false);
                    }
                }
            }
            catch (Exception ex)
            {
               this.Master.ShowError("Hata", "Sayfa yüklenirken beklenmeyen bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", ex.Message);
            }
        }

        /// <summary>
        /// Kullanıcı değiştiği zaman onun iş akışlarını getirmemizi sağlar
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void UsersASPxComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            #region Kullanıcı seçimi değiştiği zaman o kullanıcının admin i olduğu akışlar var ise işaretler

            if (UsersASPxComboBox.SelectedIndex != 0)
            {
                NameASPxLabel.Text = "Tanımlanacak Akış Yöneticisi :" + UsersASPxComboBox.SelectedItem.Text;
                SaveASPxButton.Enabled = true;
                foreach (ListItem item in WorkflowListCheckBoxList.Items)
                {
                    item.Selected = false;
                }
                WorkFlowAdmin AdminRequestObj = new WorkFlowAdmin();
                AdminRequestObj = WorkflowAdminHelper.Get(ConvertionHelper.ConvertValue<long>(UsersASPxComboBox.SelectedItem.Value));
                if (AdminRequestObj != null)
                {
                    List<Digiturk.Workflow.Digiflow.Entities.Workflow> wf = WorkflowHelper.GetWorkflowsOfAdmin(ConvertionHelper.ConvertValue<long>(UsersASPxComboBox.SelectedItem.Value));
                    if (wf.Count != 0)
                    {
                        foreach (ListItem item in WorkflowListCheckBoxList.Items)
                        {
                            bool check = (from s in wf
                                          where s.WorkflowDefId == ConvertionHelper.ConvertValue<Decimal>(item.Value.ToString())
                                          select s).FirstOrDefault() != null;
                            if (check)
                            {
                                item.Selected = true;
                            }
                        }
                    }
                    IsSystemManagerASPxCheckBox.Checked = ConvertionHelper.ConvertValue<Boolean>(AdminRequestObj.IsSysAdmin);
                    IsActiveASPxCheckBox.Checked = ConvertionHelper.ConvertValue<Boolean>(AdminRequestObj.IsActive);
                }
                else
                {
                    IsSystemManagerASPxCheckBox.Checked = false;
                }
            }

            #endregion Kullanıcı seçimi değiştiği zaman o kullanıcının admin i olduğu akışlar var ise işaretler
        }

        /// <summary>
        /// Akış admini veya sistem admin i kaydetmemizi sağlar
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void SaveASPxButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (UsersASPxComboBox.SelectedIndex != 0)
                {
                    Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType AdminType = WorkFlowInformationHelper.GetFlowAdminType(UserInformation.LoginObject.LoginId);
                    if (AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAdmin || AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAndFlowAdmin)
                    {
                        #region Kullanıcını tipini belirliyoruz

                        long AdminLevelTypeId;
                        int i = 0;
                        foreach (ListItem item in WorkflowListCheckBoxList.Items)
                        {
                            if (item.Selected)
                            {
                                i++;
                            }
                        }
                        if (IsSystemManagerASPxCheckBox.Checked && i == 0)
                        {
                            AdminLevelTypeId = 3;//SystemAdmin
                        }
                        else if (IsSystemManagerASPxCheckBox.Checked && i > 0)
                        {
                            AdminLevelTypeId = 4;//SystemAndFlowAdmin
                        }
                        else if (!IsSystemManagerASPxCheckBox.Checked && i > 0)
                        {
                            AdminLevelTypeId = 2;//FlowAdmin
                        }
                        else
                        {
                            AdminLevelTypeId = 1;//None
                        }

                        #endregion Kullanıcını tipini belirliyoruz

                        long AssignmentId = 0;
                        Digiturk.Workflow.Entities.FWfAssignment Assignment = new Digiturk.Workflow.Entities.FWfAssignment();
                        //update işlemi
                        if (Request.QueryString["AdminId"] != null)
                        {
                            #region İş Akış admin i ve akışlarını düzenliyoruz

                            WorkFlowAdmin wfa = new WorkFlowAdmin(); ;
                            wfa = WorkflowAdminHelper.Get(ConvertionHelper.ConvertValue<long>(Request.QueryString["AdminId"]));
                            wfa.IsActive = ConvertionHelper.ConvertValue<long>(IsActiveASPxCheckBox.Checked);
                            wfa.IsSysAdmin = ConvertionHelper.ConvertValue<long>(IsSystemManagerASPxCheckBox.Checked);
                            wfa.LastUpdated = DateTime.Now;
                            wfa.LastUpdatedBy = UserInformation.LoginObject.LoginId;
                            wfa.AdminLevelTypeId = AdminLevelTypeId;

                            try
                            {
                                WorkflowAdminHelper.SendMailToUsers(AdminLevelTypeId, wfa, "Update", WorkflowListCheckBoxList, UserInformation.LoginObject.LoginId);
                            }
                            catch (Exception ex)
                            {
                                this.Master.ShowPopup(false, "Mail Yollanamadı ", "Hata : " + ex.Message, true, "");
                            }

                            WorkflowAdminHelper.Update(wfa);

                            //Admin in tüm iş akışlarını siler
                            WorkflowAdminHelper.RevokePermissions(wfa.LoginId);
                            List<int> wfList = new List<int>();

                            #region İş akışlarını kaydeder

                            foreach (ListItem item in WorkflowListCheckBoxList.Items)
                            {
                                if (item.Selected)
                                {
                                    WorkflowAdminWorkflow wfawf = new WorkflowAdminWorkflow();
                                    wfawf.WfDefId = ConvertionHelper.ConvertValue<long>(item.Value);
                                    wfawf.AdminId = ConvertionHelper.ConvertValue<long>(wfa.LoginId);
                                    wfawf.Created = DateTime.Now;
                                    wfawf.CreatedBy = UserInformation.LoginObject.LoginId;
                                    wfawf.LastUpdated = DateTime.Now;
                                    wfawf.LastUpdatedBy = UserInformation.LoginObject.LoginId;
                                    WorkflowAdminHelper.AddWfOfAdmin(wfawf);
                                    Assignment = WorkflowAdminHelper.GetAssignment(UsersASPxComboBox.SelectedItem.Value.ToString(), item.Value, "WFMODIFY");
                                    if (!WorkflowAdminHelper.IsAssignmentExist(Assignment, out AssignmentId))
                                    {
                                        Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfAssignment>.SaveEntity(Assignment);
                                    }
                                }
                                else
                                {
                                    Assignment = WorkflowAdminHelper.GetAssignment(UsersASPxComboBox.SelectedItem.Value.ToString(), item.Value, "WFMODIFY");
                                    if (WorkflowAdminHelper.IsAssignmentExist(Assignment, out AssignmentId))
                                    {
                                        WorkflowAdminHelper.DeleteAssignment(AssignmentId);
                                    }
                                }
                            }

                            this.Master.ShowPopup(false, "İşlem Tamamlandı", "Akış yöneticisi başarıyla güncellendi.", false, "");

                            //SaveASPxButton.Enabled = false;

                            #endregion İş akışlarını kaydeder

                            #endregion İş Akış admin i ve akışlarını düzenliyoruz
                        }
                        else
                        {
                            WorkFlowAdmin AdminRequestObj = new WorkFlowAdmin();
                            AdminRequestObj = WorkflowAdminHelper.Get(ConvertionHelper.ConvertValue<long>(UsersASPxComboBox.SelectedItem.Value));

                            //Yeni Admin kaydetme
                            if (AdminRequestObj == null)
                            {
                                #region Kullanıcı eğer veritabanında admin olarak tanımlı değil ise

                                AdminRequestObj = new WorkFlowAdmin();
                                AdminRequestObj.LoginId = ConvertionHelper.ConvertValue<long>(UsersASPxComboBox.SelectedItem.Value);
                                HrUser h = HrHelper.Get(ConvertionHelper.ConvertValue<long>(UsersASPxComboBox.SelectedItem.Value));
                                // AdminRequestObj.Email = h.Email;
                                // AdminRequestObj.FullName = h.FullName;
                                // AdminRequestObj.Sid = h.DomainUserSid;

                                AdminRequestObj.Created = DateTime.Now;
                                AdminRequestObj.IsActive = ConvertionHelper.ConvertValue<long>(IsActiveASPxCheckBox.Checked);
                                AdminRequestObj.IsSysAdmin = ConvertionHelper.ConvertValue<long>(IsSystemManagerASPxCheckBox.Checked);
                                AdminRequestObj.CreatedBy = UserInformation.LoginObject.LoginId;
                                AdminRequestObj.AdminLevelTypeId = AdminLevelTypeId;

                                try
                                {
                                    WorkflowAdminHelper.SendMailToUsers(AdminLevelTypeId, AdminRequestObj, "Insert", WorkflowListCheckBoxList, UserInformation.LoginObject.LoginId);
                                }
                                catch (Exception ex)
                                {
                                    this.Master.ShowPopup(false, "Mail Yollanamadı ", "Hata : " + ex.Message, true, "");
                                }

                                WorkflowAdminHelper.AddNewWorkFlowAdmin(AdminRequestObj);

                                List<int> wfList = new List<int>();
                                foreach (ListItem item in WorkflowListCheckBoxList.Items)
                                {
                                    if (item.Selected)
                                    {
                                        WorkflowAdminWorkflow wfawf = new WorkflowAdminWorkflow();
                                        wfawf.WfDefId = ConvertionHelper.ConvertValue<long>(item.Value);
                                        wfawf.AdminId = ConvertionHelper.ConvertValue<long>(UsersASPxComboBox.SelectedItem.Value);
                                        wfawf.Created = DateTime.Now;
                                        wfawf.CreatedBy = UserInformation.LoginObject.LoginId;
                                        wfawf.LastUpdated = DateTime.Now;
                                        wfawf.LastUpdatedBy = UserInformation.LoginObject.LoginId;
                                        WorkflowAdminHelper.AddWfOfAdmin(wfawf);
                                        Assignment = WorkflowAdminHelper.GetAssignment(UsersASPxComboBox.SelectedItem.Value.ToString(), item.Value, "WFMODIFY");
                                        if (!WorkflowAdminHelper.IsAssignmentExist(Assignment, out AssignmentId))
                                        {
                                            Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfAssignment>.SaveEntity(Assignment);
                                        }
                                    }
                                    else
                                    {
                                        Assignment = WorkflowAdminHelper.GetAssignment(UsersASPxComboBox.SelectedItem.Value.ToString(), item.Value, "WFMODIFY");
                                        if (WorkflowAdminHelper.IsAssignmentExist(Assignment, out AssignmentId))
                                        {
                                            WorkflowAdminHelper.DeleteAssignment(AssignmentId);
                                        }
                                    }
                                }

                                this.Master.ShowPopup(false, "İşlem Tamamlandı", "Yeni akış yöneticisi başarıyla tanımlandı.", false, "");

                                //   SaveASPxButton.EnableClientSideAPI = false;

                                #endregion Kullanıcı eğer veritabanında admin olarak tanımlı değil ise
                            }
                            else
                            {
                                #region Kullanıcı eğer veritabanında admin olarak tanımlı ise

                                AdminRequestObj = WorkflowAdminHelper.Get(ConvertionHelper.ConvertValue<long>(UsersASPxComboBox.SelectedItem.Value));
                                AdminRequestObj.LastUpdated = DateTime.Now;
                                AdminRequestObj.IsActive = ConvertionHelper.ConvertValue<long>(IsActiveASPxCheckBox.Checked);
                                AdminRequestObj.IsSysAdmin = ConvertionHelper.ConvertValue<long>(IsSystemManagerASPxCheckBox.Checked);
                                AdminRequestObj.AdminLevelTypeId = AdminLevelTypeId;
                                AdminRequestObj.LastUpdatedBy = UserInformation.LoginObject.LoginId;

                                try
                                {
                                    WorkflowAdminHelper.SendMailToUsers(AdminLevelTypeId, AdminRequestObj, "Update", WorkflowListCheckBoxList, UserInformation.LoginObject.LoginId);
                                }
                                catch (Exception ex)
                                {
                                    this.Master.ShowPopup(false, "Mail Yollanamadı ", "Hata : " + ex.Message, true, "");
                                }
                                WorkflowAdminHelper.Update(AdminRequestObj);

                                //Delete the Workflows that the user was admin of
                                WorkflowAdminHelper.RevokePermissions(AdminRequestObj.LoginId);

                                List<int> wfList = new List<int>();
                                foreach (ListItem item in WorkflowListCheckBoxList.Items)
                                {
                                    if (item.Selected)
                                    {
                                        WorkflowAdminWorkflow wfawf = new WorkflowAdminWorkflow();
                                        wfawf.WfDefId = ConvertionHelper.ConvertValue<long>(item.Value);
                                        wfawf.AdminId = AdminRequestObj.LoginId;
                                        wfawf.Created = DateTime.Now;
                                        wfawf.CreatedBy = UserInformation.LoginObject.LoginId;
                                        wfawf.LastUpdated = DateTime.Now;
                                        wfawf.LastUpdatedBy = UserInformation.LoginObject.LoginId;
                                        WorkflowAdminHelper.AddWfOfAdmin(wfawf);
                                        Assignment = WorkflowAdminHelper.GetAssignment(wfawf.AdminId.ToString(), item.Value, "WFMODIFY");
                                        if (!WorkflowAdminHelper.IsAssignmentExist(Assignment, out AssignmentId))
                                        {
                                            Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfAssignment>.SaveEntity(Assignment);
                                        }
                                    }
                                    else
                                    {
                                        Assignment = WorkflowAdminHelper.GetAssignment(AdminRequestObj.LoginId.ToString(), item.Value, "WFMODIFY");
                                        if (WorkflowAdminHelper.IsAssignmentExist(Assignment, out AssignmentId))
                                        {
                                            WorkflowAdminHelper.DeleteAssignment(AssignmentId);
                                        }
                                    }
                                }

                                this.Master.ShowPopup(false, "İşlem Tamamlandı", "Akış yöneticisi başarıyla güncellendi.", false, "");

                                // SaveASPxButton.Enabled = false;

                                #endregion Kullanıcı eğer veritabanında admin olarak tanımlı ise
                            }
                        }
                    }
                    else
                    {
                        this.Master.ShowPopup(false, "Akış Yöneticisi Tanımlama Hata ", "Sistem Yöneticisi olmadan Akış yöneticisi tanımı yapılamaz", true, "");
                    }
                }
                else
                {
                    this.Master.ShowPopup(false, "Lütfen listeden kullanıcı seçiniz", "Kullanıcı seçilmeden kaydetme yapılamaz", true, "");
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// İş akışları listesindeki elemanların tümünü seçer
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void lbTumunuSec_Click(object sender, EventArgs e)
        {
            foreach (ListItem item in WorkflowListCheckBoxList.Items)
            {
                item.Selected = true;
            }
        }

        /// <summary>
        /// İş akışları listesindeki elemanların tümündeki seçimi kaldırır
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void lbTumunuKaldir_Click(object sender, EventArgs e)
        {
            foreach (ListItem item in WorkflowListCheckBoxList.Items)
            {
                item.Selected = false;
            }
        }
    }
}