.dxPivotGrid_pgCollapsedButton_RedWine,
.dxPivotGrid_pgExpandedButton_RedWine,
.dxPivotGrid_pgSortDownButton_RedWine,
.dxPivotGrid_pgSortUpButton_RedWine,
.dxPivotGrid_pgFilterResizer_RedWine,
.dxPivotGrid_pgFilterButton_RedWine,
.dxPivotGrid_pgFilterButtonActive_RedWine,
.dxPivotGrid_pgCustomizationFormCloseButton_RedWine,
.dxPivotGrid_pgDragArrowDown_RedWine,
.dxPivotGrid_pgDragArrowUp_RedWine,
.dxPivotGrid_pgDragHideField_RedWine,
.dxPivotGrid_pgDataHeaders_RedWine,
.dxPivotGrid_pgGroupSeparator_RedWine,
.dxPivotGrid_pgSortByColumn_RedWine,
.dxPivotGrid_pgPrefilterButton_RedWine {
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.RedWine.PivotGrid.sprite.png")%>');
    -background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.RedWine.PivotGrid.sprite.gif")%>'); /* for IE6 */
    background-repeat: no-repeat;
    background-color: transparent;
}

.dxPivotGrid_pgCollapsedButton_RedWine {
    background-position: 0px 0px;
    width: 15px;
    height: 15px;
}

.dxPivotGrid_pgExpandedButton_RedWine {
    background-position: 0px -18px;
    width: 15px;
    height: 15px;
}

.dxPivotGrid_pgSortDownButton_RedWine {
    background-position: -36px 0px;
    width: 11px;
    height: 15px;
}

.dxPivotGrid_pgSortUpButton_RedWine {
    background-position: -36px -18px;
    width: 11px;
    height: 15px;
}

.dxPivotGrid_pgFilterResizer_RedWine {
    background-position: -69px -37px;
    width: 13px;
    height: 13px;
}

.dxPivotGrid_pgFilterButton_RedWine {
    background-position: 0px -36px;
    width: 19px;
    height: 19px;
}

.dxPivotGrid_pgFilterButtonActive_RedWine {
    background-position: -23px -36px;
    width: 19px;
    height: 19px;
}

.dxPivotGrid_pgCustomizationFormCloseButton_RedWine {
    background-position: -46px -39px;
    width: 19px;
    height: 19px;
}

.dxPivotGrid_pgDragArrowDown_RedWine {
    background-position: -17px 0px;
    width: 11px;
    height: 9px;
}

.dxPivotGrid_pgDragArrowUp_RedWine {
    background-position: -17px -17px;
    width: 11px;
    height: 9px;
}

.dxPivotGrid_pgDragHideField_RedWine {
    background-position: -75px 0px;
    width: 22px;
    height: 22px;
}

.dxPivotGrid_pgDataHeaders_RedWine {
    background-position: -53px -19px;
    width: 12px;
    height: 12px;
}

.dxPivotGrid_pgGroupSeparator_RedWine {
    background-position: -105px -21px;
    width: 5px;
    height: 1px;
}

.dxPivotGrid_pgSortByColumn_RedWine {
    background-position: -53px 0px;
    width: 15px;
    height: 14px;
}

.dxPivotGrid_pgPrefilterButton_RedWine {
    background-position: -105px 0px;
    width: 13px;
    height: 13px;
}
.dxPivotGrid_FLButton_RedWine,
.dxPivotGrid_FLStackedDefault_RedWine,
.dxPivotGrid_FLStackedSideBySide_RedWine,
.dxPivotGrid_FLTopPanelOnly_RedWine,
.dxPivotGrid_FLBottomPanelOnly2by2_RedWine,
.dxPivotGrid_FLBottomPanelOnly1by4_RedWine,
.dxPivotGrid_FLFieldList_RedWine,
.dxPivotGrid_FLFilterAreaHeaders_RedWine,
.dxPivotGrid_FLColumnAreaHeaders_RedWine,
.dxPivotGrid_FLRowAreaHeaders_RedWine,
.dxPivotGrid_FLDataAreaHeaders_RedWine,
.dxPivotGrid_pgDragArrowLeft_RedWine,
.dxPivotGrid_pgDragArrowRight_RedWine
 {
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Aqua.PivotGrid.FLsprite.png")%>');
    -background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Aqua.PivotGrid.FLsprite.gif")%>'); /* for IE6 */
    background-repeat: no-repeat;
    background-color: transparent;
}
.dxpgFLTextDiv_RedWine img.dxPivotGrid_FLFilterAreaHeaders_RedWine,
.dxpgFLTextDiv_RedWine img.dxPivotGrid_FLColumnAreaHeaders_RedWine,
.dxpgFLTextDiv_RedWine img.dxPivotGrid_FLRowAreaHeaders_RedWine,
.dxpgFLTextDiv_RedWine img.dxPivotGrid_FLDataAreaHeaders_RedWine,
.dxpgFLTextDiv_RedWine img.dxPivotGrid_FLFieldList_RedWine
{
    height:16px;
    width:16px;
}
.dxPivotGrid_FLButton_RedWine
{
    display:block;
    height:16px;
    width:16px
}
.dxPivotGrid_FLStackedDefault_RedWine,
.dxPivotGrid_FLStackedSideBySide_RedWine,
.dxPivotGrid_FLTopPanelOnly_RedWine,
.dxPivotGrid_FLBottomPanelOnly2by2_RedWine,
.dxPivotGrid_FLBottomPanelOnly1by4_RedWine
{
    height:32px;
    width:32px;
    margin-left:-6px;
}
.dxPivotGrid_pgDragArrowLeft_RedWine,
.dxPivotGrid_pgDragArrowRight_RedWine
{
    height:11px;
    width:9px;
}
.dxPivotGrid_pgDragArrowLeft_RedWine
{
  background-position: -96px -11px;
}
.dxPivotGrid_pgDragArrowRight_RedWine
{
 background-position: -96px -0px;
}
.dxPivotGrid_FLButton_RedWine
{
    background-position: -96px -32px;
}
.dxPivotGrid_FLStackedDefault_RedWine
{
    background-position: -32px 0px;
}
.dxPivotGrid_FLStackedSideBySide_RedWine
{
    background-position: 0px -32px;
}
.dxPivotGrid_FLTopPanelOnly_RedWine
{
    background-position: 0px 0px;
}
.dxPivotGrid_FLBottomPanelOnly2by2_RedWine
{
    background-position: -32px -32px;
}
.dxPivotGrid_FLBottomPanelOnly1by4_RedWine
{
    background-position: -64px 0px;
}
.dxPivotGrid_FLFieldList_RedWine
{
    background-position: -64px -32px;
}
.dxPivotGrid_FLFilterAreaHeaders_RedWine
{
    background-position: -80px -48px;
}
.dxPivotGrid_FLColumnAreaHeaders_RedWine
{
    background-position: -64px -48px;
}
.dxPivotGrid_FLRowAreaHeaders_RedWine
{
    background-position: -96px -48px;
}
.dxPivotGrid_FLDataAreaHeaders_RedWine
{
    background-position: -80px -32px;
}