﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Actions.AssigmentClass;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;

namespace Digiturk.Workflow.Digiflow.Actions
{
    /// <summary>
    /// Delegasyon işleminin Sonunda delegasyonu sağlamak için deamon tarafından çalıştırılır
    /// </summary>
    public class DelegationAssigment : WorkFlowEndState, IActionClass, IWorkflowDiagramAssignmentHelper
    {
        private List<string> ListNameSurname = new List<string>();
        /// <summary>
        /// Delegasyon işleminin Sonunda delegasyonu sağlamak için deamon tarafından çalıştırılır
        /// </summary>
        /// <param name="wfContext"></param>
        /// <param name="actionInstance"></param>
        /// <returns></returns>
        public bool Execute(WFContext wfContext, FWfActionInstance actionInstance)
        {
            #region EskiKod
            /*
             using (UnitOfWork.Start())
            {
                if (wfContext.Parameters.ContainsKey("Onay") && wfContext.Parameters["Onay"].ToString() == "Yes")
                {
                    #region Delegation Assigment

                    DelegationRequest dlgReq = WFRepository<DelegationRequest>.GetEntity(actionInstance.WfStateInstance.WfWorkflowInstance.EntityRefId);
                    // foreach ile yazdığım alanlar girilecek
                    string[] WfDefIds = dlgReq.WorkFlowIds.Split(',');
                    for (int i = 0; i < WfDefIds.Length; i++)
                    {
                        WorkflowDelegationHelper.StartDelegation(dlgReq.OwnerLoginId, dlgReq.DelegatedLoginId, Convert.ToInt64(WfDefIds[i]), dlgReq.DelegationComment, dlgReq.StartTime, dlgReq.EndTime);
                    }
                    Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(dlgReq.DelegatedLoginId, "LOGIN", dlgReq.OwnerLoginId, "WFVIEW");
                    System.Threading.Thread.Sleep(1000); // History loglama fix, daemon tarafında olduğundan problem çıkarmayacaktır.
                    dlgReq = null;

                    #endregion Delegation Assigment
                }
            }
            return base.Execute(wfContext, actionInstance);
            */
            #endregion
            return DoExecution(wfContext, actionInstance, null, true);
        }
        private bool DoExecution(WFContext wfContext, FWfActionInstance actionInstance, FWfWorkflowInstance wfInstance, bool doExecution)
        {
            using (UnitOfWork.Start())
            {
                if (wfContext.Parameters.ContainsKey("Onay") && wfContext.Parameters["Onay"].ToString() == "Yes")
                {
                    #region Delegation Assigment

                    DelegationRequest dlgReq = WFRepository<DelegationRequest>.GetEntity(actionInstance != null ? actionInstance.WfStateInstance.WfWorkflowInstance.EntityRefId : wfInstance.EntityRefId);
                    if (doExecution)
                    {
                        // foreach ile yazdığım alanlar girilecek
                        string[] WfDefIds = dlgReq.WorkFlowIds.Split(',');
                        for (int i = 0; i < WfDefIds.Length; i++)
                        {
                            WorkflowDelegationHelper.StartDelegation(dlgReq.OwnerLoginId, dlgReq.DelegatedLoginId, Convert.ToInt64(WfDefIds[i]), dlgReq.DelegationComment, dlgReq.StartTime, dlgReq.EndTime);
                        }
                        Digiturk.Workflow.Digiflow.AssignmentBase.AssignmentBase.Assign(dlgReq.DelegatedLoginId, "LOGIN", dlgReq.OwnerLoginId, "WFVIEW");
                        System.Threading.Thread.Sleep(1000); // History loglama fix, daemon tarafında olduğundan problem çıkarmayacaktır.
                    }
                    else
                    {
                        long NewAssignLoginId = dlgReq.DelegatedLoginId;
                        if (NewAssignLoginId > 0)
                        {
                            string nameSurname = Digiflow.Authorization.WflowDataHelpers.GetLoginNameSurname(NewAssignLoginId);
                            ListNameSurname.Add(nameSurname);
                        }
                        ListNameSurname.Sort();
                    }
                    dlgReq = null;

                    #endregion Delegation Assigment
                }
            }
            if (doExecution)
                return base.Execute(wfContext, actionInstance);
            else
                return true;
        }

        public List<string> GetAssignmentListNames(WFContext wfContext, FWfWorkflowInstance wfInstance, long WfStateDefId)
        {
            DoExecution(wfContext, null, wfInstance, false);
            return ListNameSurname;
        }
    }
}