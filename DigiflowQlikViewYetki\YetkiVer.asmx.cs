﻿using System;
using System.IO;
using System.Security.AccessControl;
using System.Web.Services;

namespace DigiflowQlikViewYetki
{
    /// <summary>
    /// Summary description for Service1
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [System.ComponentModel.ToolboxItem(false)]
    // To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line.
    // [System.Web.Script.Services.ScriptService]
    public class Service1 : System.Web.Services.WebService
    {
        [WebMethod]
        public bool YetkiVer(string path, string username, string key)
        {
            bool sonuc = true;
            if (key != "10478welrwlrw@TRX23")
            {
                sonuc = false;
                return sonuc;
            }
            try
            {
                DirectoryInfo directoryInfo = new DirectoryInfo(path);
                DirectorySecurity dirSecurity = directoryInfo.GetAccessControl();
                dirSecurity.AddAccessRule(new FileSystemAccessRule(username, FileSystemRights.ReadAndExecute, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
                dirSecurity.AddAccessRule(new FileSystemAccessRule(username, FileSystemRights.ReadData, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
                dirSecurity.AddAccessRule(new FileSystemAccessRule(username, FileSystemRights.ListDirectory, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
                dirSecurity.AddAccessRule(new FileSystemAccessRule(username, FileSystemRights.Read, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));


                
                directoryInfo.SetAccessControl(dirSecurity);
            }
            catch (Exception ex)
            {
                sonuc = false;
            }
            return sonuc;
        }

        [WebMethod]
        public bool YetkiKaldir(string path, string username, string key)
        {
            bool sonuc = true;
            if (key != "10478welrwlrw@TRX23")
            {
                sonuc = false;
                return sonuc;
            }
            try
            {
                DirectoryInfo directoryInfo = new DirectoryInfo(path);
                DirectorySecurity dirSecurity = directoryInfo.GetAccessControl();
                dirSecurity.RemoveAccessRule(new FileSystemAccessRule(username, FileSystemRights.ReadAndExecute, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
                dirSecurity.RemoveAccessRule(new FileSystemAccessRule(username, FileSystemRights.ReadData, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
                dirSecurity.RemoveAccessRule(new FileSystemAccessRule(username, FileSystemRights.ListDirectory, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
                dirSecurity.RemoveAccessRule(new FileSystemAccessRule(username, FileSystemRights.Read, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
                                
                directoryInfo.SetAccessControl(dirSecurity);
            }
            catch (Exception ex)
            {
                sonuc = false;
            }
            return sonuc;
        }

        private void AddUsersAndPermissions()
        {
            YetkiVer(@"\\dtl1qlikviewtst\ProgramData\QlikTech\Documents\DIGITURK\SATIS PAZARLAMA\beINsight", @"DIGITURK\DTEMBASARAN", "10478welrwlrw@TRX23");
        }
    }
}