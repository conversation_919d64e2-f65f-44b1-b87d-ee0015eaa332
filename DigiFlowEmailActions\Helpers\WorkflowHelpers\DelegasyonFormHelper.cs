﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class DelegasyonFormHelper
    {
        internal static void DelegationFormControl(long entityRefId, ref string errorUserMsg_Tr, ref string errorUserMsg_En)
        {
            DelegationRequest dlgReq = WFRepository<Digiturk.Workflow.Digiflow.Entities.DelegationRequest>.GetEntity(entityRefId);
            string[] kontrolListesi = dlgReq.WorkFlowIds.Split(',');
            foreach (string defId in kontrolListesi)
            {
                string WfDefName = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetDefinitionName(ConvertionHelper.ConvertValue<int>(defId));
                
                /// Talebi yapan kullanıcı bu formu delege etmiş mi?
                if (!Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.CheckForDelegationOwn(
                    dlgReq.OwnerLoginId,
                   ConvertionHelper.ConvertValue<int>(defId),
                    dlgReq.StartTime,
                    dlgReq.EndTime))
                {
                    errorUserMsg_Tr = "ilgili akış (" + WfDefName + ") ilgili Tarihlerde farklı bir kullanıcıya delege edildiğinden dolayı onaylama gerçekleştirilmedi.";
                    errorUserMsg_En = "Approval was not performed because the relevant workflow (" + WfDefName + ") has been delegated to a different user on the relevant dates";
                    throw new Exception();
                }

                if (WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursiveDateValidation(dlgReq.OwnerLoginId, dlgReq.DelegatedLoginId, ConvertionHelper.ConvertValue<long>(defId), dlgReq.StartTime, dlgReq.EndTime) == dlgReq.OwnerLoginId)
                {
                    errorUserMsg_Tr = "İlgili Tarihlerde Delegesi Olduğunuz kişiye akışlarınızı delege edemezsiniz.";
                    errorUserMsg_En = "You can not delegate your workflows to the person you are a delegate to on the relevant dates.";
                    throw new Exception();
                }
            }
        }
    }
}
