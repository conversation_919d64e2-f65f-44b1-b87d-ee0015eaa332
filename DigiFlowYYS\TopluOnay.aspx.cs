﻿using DevExpress.Web.ASPxGridView;
using Digiturk.Workflow.Digiflow.WebCore;
using System;
using System.Data;
using System.Web.UI;

public partial class TopluOnay : YYSSecurePage
{
    private DataTable dtable = new DataTable();

    protected void Page_Load(object sender, EventArgs e)
    {
        try
        {
            if (!Page.IsPostBack)
            {
                if (AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAdmin || AdminType == Digiturk.Workflow.Digiflow.WorkFlowHelpers.YYSAdminType.SystemAndFlowAdmin)
                {
                    ((MasterPage)Master).ShowMenu(true);
                    ((MasterPage)Master).PageTitle = "Toplu Onay Ekranı";

                    #region İş akışı ve kullanıcı  Combobox ları doldurulur

                    DataTable dtbl = WorkflowHelper.GetLiveWorkFlows();
                    drpIsAkislari = Common.FillDropDownList("NAME", "WF_WORKFLOW_DEF_ID", drp<PERSON>s<PERSON><PERSON><PERSON><PERSON>, dtbl, "İŞ AKIŞI SEÇİNİZ", "0");

                    DataTable dt = HrHelper.GetUsers();
                    UsersASPxComboBox = Common.FillDropDownList("NAME_SURNAME", "LOGIN_ID", UsersASPxComboBox, dt, "KULLANICI SEÇİNİZ", "0");

                    #endregion İş akışı ve kullanıcı  Combobox ları doldurulur

                    FillGrid();
                }
                else
                {
                    Response.Redirect("Error.aspx", false);
                }
            }
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowError("Hata", "Sayfa yüklenirken beklenmeyen bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", ex.Message);
        }
        if (IsCallback)
        {
            FillGrid();
        }
    }

    protected void btnKaydet_Click(object sender, EventArgs e)
    {
        Kaydet();
    }

    /// <summary>
    /// Verilmiş değerleri kaydeder
    /// WF_DF_MULTIPLE_APPROVAL_ENTITY tablosunda bir iş akışı kayıtlıysa,bir daha kaydetmemenin kontrolünü ve
    /// WF_DF_MULTIPLE_APPROVAL_DEF tablosuna login id'si ve iş akışı aynı olan kayıtları kaydetmeme kontrolü yapılıyor.
    /// </summary>
    private void Kaydet()
    {
        DataTable dt = TopluOnayHelper.GetGrid();
        DataTable dtbl = TopluOnayHelper.ControlToEntityTable();

        long loginId = 0;
        long wfWorkflowDefId = 0;
        bool isTrue = false;
        bool a = false;

        foreach (DataRow item in dtbl.Rows)
        {
            wfWorkflowDefId = Convert.ToInt64(drpIsAkislari.SelectedValue);
            if (wfWorkflowDefId == Convert.ToInt64(item["WF_WORKFLOW_DEF_ID"]))
            {
                a = true;
                break;
            }
        }
        if (!a)
        {
            TopluOnayHelper.EntityKayitEt(Convert.ToInt64(drpIsAkislari.SelectedValue), txtAlanAdi.Text);
        }

        foreach (DataRow items in dt.Rows)
        {
            loginId = Convert.ToInt64(UsersASPxComboBox.SelectedValue);
            wfWorkflowDefId = Convert.ToInt64(drpIsAkislari.SelectedValue);

            if (loginId == Convert.ToInt64(items["LOGIN_ID"]) && wfWorkflowDefId == Convert.ToInt64(items["WF_WORKFLOW_DEF_ID"]))
            {
                isTrue = true;

                break;
            }
        }

        if (isTrue)
        {
            ((MasterPage)Master).ShowPopup(false, "Belirttiğiniz kullanıcı adı ve iş akışı zaten mevcut", "Kayıt Eklenemedi.", false, string.Empty);
        }
        else
        {
            TopluOnayHelper.KayitEt(Convert.ToInt64(UsersASPxComboBox.SelectedValue), Convert.ToInt64(drpIsAkislari.SelectedValue), txtAlanAdi.Text, txtAlanDegeri.Text);
            FillGrid();
            ((MasterPage)Master).ShowPopup(false, "Kayıt Başarıyla Eklendi", "Kayıt Eklendi.", false, string.Empty);
        }
    }

    /// <summary>
    /// Silmek için Id yi buradan alınır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void gvLogicalMembers_RowCommand(object sender, ASPxGridViewRowCommandEventArgs e)
    {
        Session["KeyIndex"] = e.VisibleIndex;
    }

    /// <summary>
    /// Grid üzerindeki özel tanımlanan Sil butonu çalışır
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    protected void DeleteRuleButton_Click(object sender, EventArgs e)
    {
        try
        {
            long loginId = 0;
            long defId = 0;
            int index = Convert.ToInt32(Session["KeyIndex"].ToString());

            loginId = Convert.ToInt64(WorkFlowRulesGridView.GetRowValues(index, "LOGIN_ID"));
            defId = Convert.ToInt64(WorkFlowRulesGridView.GetRowValues(index, "WF_WORKFLOW_DEF_ID"));

            if (Session["KeyIndex"] != null)
            {
                DeleteRow(loginId, defId);
                ((MasterPage)Master).ShowPopup(false, "Satır Silindi", "Satır silindi.", false, string.Empty);

                Session["KeyIndex"] = null;
                FillGrid();
            }
            else
            {
                ((MasterPage)Master).ShowPopup(false, "Hata", "Seçili satır silinemedi!", true, "");
            }
        }
        catch (Exception ex)
        {
            ((MasterPage)Master).ShowPopup(false, "Hata", "İşlem sırasında bir hata ile karşılaşıldı. Lütfen yeniden deneyin.", true, ex.Message);
        }
    }

    /// <summary>
    /// Sayfayı ilk kez açarken Grid dolduruluyor
    /// </summary>
    public void FillGrid()
    {
        WorkFlowRulesGridView.DataSource = TopluOnayHelper.GetGrid();
        WorkFlowRulesGridView.DataBind();
    }

    public void DeleteRow(long loginId, long defId)
    {
        TopluOnayHelper.Delete(loginId, defId);
    }
}