using DigiflowAPI.Application.DTOs.HelperDTOs;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Interfaces.DataAccess;

namespace DigiflowAPI.Infrastructure.Services
{
    public class LogicalGroupService(ILogicalGroupRepository logicalGroupRepository, IOracleDataAccessRepositoryFactory repositoryFactory) : ILogicalGroupService
    {
        public async Task<IEnumerable<MemberInfoDto>> GetPersonelList(long logicalGroupId)
        {
            // Implement the method directly since it's not in the interface
            string sql = @"
                SELECT
                    m.FULLNAME,
                    m.EMAIL,
                    m.LOGIN_ID,
                    t.DESCRIPTION,
                    m.CONTENT
                FROM DT_WORKFLOW.YYS_LOGICAL_GROUPS g
                LEFT JOIN DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS m ON g.LOGICAL_GROUP_ID = m.LOGICAL_GROUP_ID
                LEFT JOIN DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBER_TYPES t ON m.LOGICAL_GROUP_MEMBER_TYPE_ID = t.LOGICAL_GROUP_MEMBER_TYPE_ID
                WHERE m.LOGIN_ID > -1 AND g.LOGICAL_GROUP_ID = :LogicalGroupId";

            var parameters = new Dictionary<string, object>
            {
                {"LogicalGroupId", logicalGroupId}
            };

            var repository = repositoryFactory.Create("DT_WORKFLOW");
            return await repository.ExecuteQueryAsync(sql, reader => new MemberInfoDto
            {
                Fullname = reader["FULLNAME"].ToString(),
                Email = reader["EMAIL"].ToString(),
                LoginId = Convert.ToInt32(reader["LOGIN_ID"]),
                Description = reader["DESCRIPTION"].ToString(),
                Content = reader["CONTENT"].ToString()
            }, parameters);
        }

        public async Task<bool> IsExistLogicalGroup(long logicalGroupId, long userId)
        {
            return await logicalGroupRepository.IsExistLogicalGroup(logicalGroupId, userId);
        }

        public async Task<int> GetLogicalGroupID(string logicalGroupName)
        {
            return await logicalGroupRepository.GetLogicalGroupID(logicalGroupName);
        }

        public async Task<bool> IsDefExistLogicalGroup(long logicalGroupId, long defId, string isAdmin)
        {
            return await logicalGroupRepository.IsDefExistLogicalGroup(logicalGroupId, defId, isAdmin);
        }
    }
}
