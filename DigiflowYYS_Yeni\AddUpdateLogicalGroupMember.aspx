﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site1.Master" AutoEventWireup="true" CodeBehind="AddUpdateLogicalGroupMember.aspx.cs" Inherits="DigiflowYYS_Yeni.AddUpdateLogicalGroupMember" %>
<%@ MasterType VirtualPath="~/Site1.Master" %>
<%@ Register Assembly="DevExpress.Web.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxPopupControl" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v12.1" Namespace="DevExpress.Web.ASPxEditors"
    TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v12.1" Namespace="DevExpress.Web.ASPxGridView"
    TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.v12.1" Namespace="DevExpress.Web.ASPxPopupControl"
    TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v12.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <style type="text/css">
        .style1 {
            width: 100%;
        }

        .style3 {
            width: 153px;
        }

        .style4 {
            width: 138px;
        }

        .style5 {
            width: 11%;
        }
    </style>

    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="js/select2.min.js"></script>
    <link href="css/select2.min.css" rel="stylesheet" />
    <script type="text/javascript">    
        function pageLoad() {
            debugger
            $(".drpSelect").select2();
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table border="0" cellpadding="0" cellspacing="0" width="70%" align="center">
        <asp:Panel ID="WorkflowPanel" runat="server" Visible="true">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <table class="style1">
                        <tr>
                            <td class="style3">
                                <b>Akış Listesi</b>
                            </td>
                            <td>
                                <%--<dx:ASPxComboBox ID="WorkFlowCombobox" runat="server" AutoPostBack="True" Width="350px"
                                    OnSelectedIndexChanged="WorkFlowCombobox_SelectedIndexChanged" ValueType="System.Int64">
                                </dx:ASPxComboBox>--%>

                                <asp:DropDownList ID="WorkFlowCombobox1" runat="server" AutoPostBack="True" Width="350px"
                                    OnSelectedIndexChanged="WorkFlowCombobox1_SelectedIndexChanged" CssClass="drpSelect" ValueType="System.Int64">                                    
                                </asp:DropDownList>
                            </td>
                        </tr>
                    </table>
                    &nbsp;
                </td>
            </tr>
        </asp:Panel>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <table class="style1">
                    <tr>
                        <td class="style3">
                            <b>Mantıksal Grup İsmi </b>
                        </td>
                        <td>
                            <%--<dx:ASPxComboBox ID="ASPxComboBoxLogicalGroup" runat="server" OnSelectedIndexChanged="ASPxComboBoxLogicalGroup_SelectedIndexChanged"
                                ValueType="System.Int64" Width="350px" AutoPostBack="True"></dx:ASPxComboBox>--%>
                            <asp:DropDownList ID="ASPxComboBoxLogicalGroup1" runat="server" OnSelectedIndexChanged="ASPxComboBoxLogicalGroup1_SelectedIndexChanged"
                                ValueType="System.Int64" Width="350px" CssClass="drpSelect" AutoPostBack="True">                                                                
                            </asp:DropDownList>
                            
                        </td>
                    </tr>
                    <tr>
                        <td class="style3"></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="style3">
                            <b>Mantıksal Grup Açıklama </b>
                        </td>
                        <td>
                            <asp:TextBox Enabled="false" ID="txtDescription" runat="server" TextMode="MultiLine" Style="font-family: Microsoft Sans Serif, Sans-Serif;"
                                Height="70px" Width="350px"></asp:TextBox>
                        </td>
                    </tr>
                </table>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <table class="ui-accordion">
                    <tr>
                        <td class="style4">
                            <b>Kullanıcı Tipi</b>
                        </td>
                    </tr>
                    <tr>
                        <td class="style4">
                            <asp:RadioButtonList ID="rblMemberType" runat="server" AutoPostBack="True" OnSelectedIndexChanged="rblMemberType_SelectedIndexChanged"
                                Height="22px" Width="282px">
                            </asp:RadioButtonList>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <asp:Panel ID="UserSelectionPanel" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Kullanıcılar</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <asp:DropDownList ID="UsersASPxComboBox" CssClass="drpSelect" runat="server" Width="250px">
                    </asp:DropDownList>
                    <%-- <asp:LinkButton ID="lnkAdd" runat="server" OnClick="LinkButtonFlogin_click">Ekle</asp:LinkButton>--%>
                </td>
            </tr>
            <br />
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:aspxbutton ID="lnkAdd" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                        CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                        Text="Ekle" OnClick="LinkButtonFlogin_click">
                    </dx:aspxbutton>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="AllUserSelectionPanel" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Tüm Kullanıcılar</b>
                </td>
            </tr>
            <br />
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:aspxbutton ID="ButtonAllUsers" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                        CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                        Text="Ekle" OnClick="LinkButtonAllUsers_click">
                    </dx:aspxbutton>
                    <%-- <asp:LinkButton ID="lnkAllUsers" runat="server" OnClick="LinkButtonAllUsers_click">Ekle</asp:LinkButton>--%>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="OutsourceUserPanel" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Ad - Soyad</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:aspxtextbox ID="txtFullName" runat="server" Width="350px" MaxLength="1024">
                    </dx:aspxtextbox>
                    <asp:RequiredFieldValidator ID="rqFullName" runat="server" ControlToValidate="txtFullName"
                        ErrorMessage="Lütfen isim bilgisi yazın." ValidationGroup="2"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>E - Posta</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    
                    <dx:aspxtextbox ID="txtEmail" runat="server" Width="350px" MaxLength="1024">
                    </dx:aspxtextbox>
                    <asp:RequiredFieldValidator ID="rqEmail" runat="server" ControlToValidate="txtEmail"
                        ErrorMessage="Lütfen bir Email adresi yazın." ValidationGroup="2"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="revEmail" runat="server" ErrorMessage="Lütfen geçerli bir Email adresi yazın."
                        ValidationGroup="2" ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                        ControlToValidate="txtEmail"></asp:RegularExpressionValidator>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:aspxbutton ID="ButtonOutsource" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                        CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                        Text="Ekle" OnClick="LinkButtonOutsource_click" CausesValidation="true" ValidationGroup="2">
                    </dx:aspxbutton>
                    <%-- <asp:LinkButton ID="LinkButtonOutsource" runat="server" OnClick="LinkButtonOutsource_click"
                        CausesValidation="true" ValidationGroup="2">Ekle</asp:LinkButton>--%>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="ParameterPanel" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>İçerik</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:aspxtextbox ID="txtContent" runat="server" Width="350px" MaxLength="1024">
                    </dx:aspxtextbox>
                    <asp:RequiredFieldValidator ID="rqContent" runat="server" ControlToValidate="txtContent"
                        ErrorMessage="Lütfen parametre giriniz" ValidationGroup="3"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>Açıklama</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:aspxtextbox ID="txtDescriptions" runat="server" Width="350px" MaxLength="1024">
                    </dx:aspxtextbox>
                    <asp:RequiredFieldValidator ID="rqDescriptions" runat="server" ControlToValidate="txtDescriptions"
                        ErrorMessage="Lütfen açıklama giriniz" ValidationGroup="3"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:aspxbutton ID="LinkParameter" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                        CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                        Text="Ekle" OnClick="LinkParameter_click" CausesValidation="true" ValidationGroup="2">
                    </dx:aspxbutton>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="PnlAdMembers" runat="server" Visible="false">
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <b>AD Grupları</b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <table>
                        <tr>
                                <td> Domain </td>
                                <td> : </td>
                                <td><asp:DropDownList ID="DrpDomain" runat="server" Width="250px" OnTextChanged="DrpDomain_TextChanged" AutoPostBack="true"> 
                                        <asp:ListItem Value="0">---Seçiniz---</asp:ListItem>
                                        <asp:ListItem Value="DIGITURK">DIGITURK</asp:ListItem>
                                        <asp:ListItem Value="DIGITURKCC">DIGITURKCC</asp:ListItem>
                                    </asp:DropDownList> </td>
                                <td>AD Grubu </td>
                                <td> : </td>
                                <td><asp:DropDownList ID="DrpGrupList" runat="server" Width="250px"> 
                                        <asp:ListItem Value="0">---Seçiniz---</asp:ListItem>    
                                    </asp:DropDownList> 

                                </td>
                                <td> 
                                    <dx:aspxbutton ID="BtnAdEkle" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                                    CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                                    Text="Ekle" OnClick="BtnAdEkle_Click">
                                </dx:aspxbutton>
                                </td>
                        </tr>
                    </table>
                </td>
            </tr>
             <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%"><br />
                    <b> Yaptığınız Değişiklikleri Kaydetmek İçin <u>Kaydet</u> Botonuna Basmalısınız </b><br />
                    <br />
                    <b>YYS Mantıksal Gruba Ait AD Grupları</b>
                </td>
            </tr>
             <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                     <dx:ASPxGridView ID="GrdAdLogicalGroupMap" runat="server" AutoGenerateColumns="False" OnRowCommand="GrdAdLogicalGroupMap_RowCommand"
                    Width="100%" KeyFieldName="ADGROUPNAME">
                    <Columns>
                        <dx:gridviewdatatextcolumn Caption="Akışın Adı" FieldName="FLOWNAME" ShowInCustomizationForm="True"
                            VisibleIndex="1">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Mantıksal Grubun Adı" FieldName="LOGICALGROUPNAME" ShowInCustomizationForm="True"
                            VisibleIndex="2">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Domain" FieldName="DOMAIN" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="AD Grubunun Adı" FieldName="ADGROUPNAME" ShowInCustomizationForm="True"
                            VisibleIndex="4">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:GridViewDataButtonEditColumn  Caption="Sil" VisibleIndex="7" FieldName="RequestId">
                            <DataItemTemplate>
                                <dx:aspxbutton ID="DeleteGroupButton" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteGroupButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                    <ClientSideEvents Click="function(s, e) {
	 e.processOnServer = confirm('Ad Grubu silmek istediğinizden emin misiniz ?');}" />
                                </dx:aspxbutton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn >
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" AllowSelectSingleRowOnly="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterRowMenu="True" ShowFilterBar="Auto" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Mantıksal üyeyi bu gruptan silmek istediğinize emin misiniz?"></SettingsText>
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
                </td>
            </tr>
             <tr>
                <td align="right" style="padding: 5px" valign="top" width="100%">
                    <br />
                    <b> Yaptığınız Değişiklikleri Kaydetmek İçin <u>Kaydet</u> Botonuna Basmalısınız </b><br />
                    <br />
                       <dx:aspxbutton ID="BtnSearchUserList" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                            CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                            Text="Sorgula" OnClick="BtnSearchUserList_Click">
                        </dx:aspxbutton>
                </td>
            </tr>
             <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                  <b>      Mantıksal Grupların Üyeleri   </b>
                </td>
            </tr>
            <tr>
                <td align="left" style="padding: 5px" valign="top" width="100%">
                    <dx:ASPxGridView ID="GrdAdMembersUsers" runat="server" AutoGenerateColumns="False"
                    Width="100%" KeyFieldName="UserName">
                    <Columns>
                        <dx:gridviewdatatextcolumn Caption="Kullanıcı Adı" FieldName="UserName" ShowInCustomizationForm="True"
                            VisibleIndex="2">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Görünen Adı" FieldName="DisplayName" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Login Id" FieldName="Flogin" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Sonuç Kontrol" FieldName="CheckResult" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" AllowSelectSingleRowOnly="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterRowMenu="True" ShowFilterBar="Auto" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Mantıksal üyeyi bu gruptan silmek istediğinize emin misiniz?"></SettingsText>
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
                </td>
            </tr>
            <tr>
                <td align="center"> 
                    <br />
                        <dx:aspxbutton ID="BtnAktar" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                            CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                            Text="Kaydet" OnClick="BtnAktar_Click">
                        </dx:aspxbutton>
                    <br />
                    <b> Kaydetme İşlemi İle Ekranda Seçmiş olduğunuz Mantıksal Gruplar ve Kullanıcılar kaydedilir. </b>
                    <br />
                    <dx:aspxbutton ID="BtnJobs" runat="server" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                            CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                            Text="Tüm AD Gruplarını Güncelle" OnClick="BtnJobs_Click" Visible="false">
                        </dx:aspxbutton>
                </td>
            </tr>
        </asp:Panel>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                <b>Grup Üyeleri</b>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">&nbsp;
                <asp:Label ID="lblMessage" runat="server" Text="" ForeColor="Red" Font-Bold="true"></asp:Label>
            </td>
        </tr>
        <tr>
            <td align="left" style="padding: 5px" valign="top" width="100%">
                
                <dx:ASPxGridView ID="gvLogicalMembers" runat="server" AutoGenerateColumns="False"
                    Width="100%" KeyFieldName="RequestId" OnRowCommand="gvLogicalMembers_RowCommand"
                    OnRowDeleting="gvLogicalMembers_RowDeleting" OnHtmlDataCellPrepared="gvLogicalMembers_HtmlDataCellPrepared">
                    <Columns>
                        
                        <dx:gridviewdatatextcolumn Caption="ID" FieldName="RequestId" VisibleIndex="1">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Parametre Adı" FieldName="Content" ShowInCustomizationForm="True"
                            VisibleIndex="1">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Açıklama" FieldName="Description" ShowInCustomizationForm="True"
                            VisibleIndex="2">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="LoginId" FieldName="LoginId" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Ad Soyad" FieldName="FullName" ShowInCustomizationForm="True"
                            VisibleIndex="4">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Email" FieldName="Email" ShowInCustomizationForm="True"
                            VisibleIndex="5">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Mantıksal Grup Tipi" FieldName="LogicalGroupMemberTypeId"
                            ShowInCustomizationForm="True" VisibleIndex="6">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <%--   <dx:GridViewCommandColumn Caption="Sil" ShowInCustomizationForm="True" VisibleIndex="7"
                            Name="Sil">
                            <DeleteButton Text="Sil" Visible="True">
                            </DeleteButton>
                            <ClearFilterButton Visible="True">
                            </ClearFilterButton>
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewCommandColumn>--%>
                        <dx:GridViewDataButtonEditColumn  Caption="Sil" VisibleIndex="7" FieldName="RequestId">
                            <DataItemTemplate>
                                <dx:aspxbutton ID="DeleteButton" runat="server" CssFilePath="~/App_Themes/Office2010Blue/{0}/styles.css"
                                    CssPostfix="Office2010Blue" OnClick="DeleteButton_Click" SpriteCssFilePath="~/App_Themes/Office2010Blue/{0}/sprite.css"
                                    Text="Sil">
                                    <ClientSideEvents Click="function(s, e) {
	 e.processOnServer = confirm('Mantıksal grup üyesini silmek istediğinizden emin misiniz ?');}" />
                                </dx:aspxbutton>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:GridViewDataButtonEditColumn >
                        <dx:gridviewdatatextcolumn Caption="Sıra No" VisibleIndex="0" Width="40px">
                            <DataItemTemplate>
                                <%# Container.ItemIndex + 1 %>
                            </DataItemTemplate>
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" AllowSelectSingleRowOnly="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterRowMenu="True" ShowFilterBar="Auto" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Mantıksal üyeyi bu gruptan silmek istediğinize emin misiniz?"></SettingsText>
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
                <table align="center" class="style5">
                    <tr>
                        <td align="left">
                            <br />
                            <dx:aspxbutton ID="LinkButtonRemoveAll" runat="server" OnClick="LinkButtonRemoveAll_Click"
                                Visible="False" Text="Tümünü Sil" CssFilePath="~/App_Themes/Office2010Silver/{0}/styles.css"
                                CssPostfix="Office2010Silver" SpriteCssFilePath="~/App_Themes/Office2010Silver/{0}/sprite.css"
                                Width="100px">
                            </dx:aspxbutton>
                        </td>
                    </tr>
                </table>
                &nbsp;
            </td>
        </tr>
        <tr>
            <td> <b>YYS Mantıksal Gruba Ait <b> Kayıtlı </b> AD Grupları</b> </td>
        </tr>
        <tr>
            <td>
                <dx:ASPxGridView ID="GrdYYSAdGroupMember" runat="server" AutoGenerateColumns="False"
                    Width="100%" KeyFieldName="AdGroupName">
                    <Columns>
                        <dx:gridviewdatatextcolumn Caption="Akışın Adı" FieldName="FLOWNAME" ShowInCustomizationForm="True"
                            VisibleIndex="1">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Mantıksal Grubun Adı" FieldName="LOGICALGROUPNAME" ShowInCustomizationForm="True"
                            VisibleIndex="2">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="Domain" FieldName="DOMAIN" ShowInCustomizationForm="True"
                            VisibleIndex="3">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                        <dx:gridviewdatatextcolumn Caption="AD Grubunun Adı" FieldName="ADGROUPNAME" ShowInCustomizationForm="True"
                            VisibleIndex="4">
                            <HeaderStyle HorizontalAlign="Center" />
                            <CellStyle HorizontalAlign="Center">
                            </CellStyle>
                        </dx:gridviewdatatextcolumn>
                    </Columns>
                    <SettingsBehavior ConfirmDelete="True" AllowSelectSingleRowOnly="True" />
                    <Settings ShowFilterRow="True" ShowGroupPanel="True" ShowFilterRowMenu="True" ShowFilterBar="Auto" />
                    <SettingsText GroupContinuedOnNextPage="(Devamı sonraki sayfada)" GroupPanel="Gruplamak istediğiniz alanları buraya sürükleyin."
                        FilterBarClear="Temizle" EmptyDataRow="Herhangi bir kayıt bulunmamaktadır" ConfirmDelete="Mantıksal üyeyi bu gruptan silmek istediğinize emin misiniz?"></SettingsText>
                    <SettingsPager ShowDefaultImages="false" CurrentPageNumberFormat="{0}">
                        <AllButton Text="Tümü">
                        </AllButton>
                        <NextPageButton Text="İleri >">
                        </NextPageButton>
                        <PrevPageButton Text="< Geri">
                        </PrevPageButton>
                        <FirstPageButton Text="<< İlk Sayfa">
                        </FirstPageButton>
                        <LastPageButton Text="Son Sayfa >>">
                        </LastPageButton>
                        <Summary Text="Sayfa" />
                    </SettingsPager>
                    <SettingsLoadingPanel Text="Yükleniyor" />
                </dx:ASPxGridView>
            </td>
        </tr>
        <tr>
            <td align="center" style="padding: 5px" valign="top" width="100%">&nbsp;
            </td>
        </tr>
    </table>
</asp:Content>