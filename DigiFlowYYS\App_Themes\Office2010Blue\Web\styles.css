﻿/* -- ASPxCallbackPanel -- */
.dxcpLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxcpLoadingPanel_Office2010Blue td.dx,
.dxcpLoadingPanelWithContent_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxcpLoadingPanelWithContent_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
	border: 1px solid #859ebf;
	background: White;
}
.dxcpLoadingDiv_Office2010Blue
{
	background: Gray;
	opacity: 0.01;
	filter:progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=1);
}
/* Disabled */
.dxcpDisabled_Office2010Blue
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxCloudControl -- */
.dxccControl_Office2010Blue
{
	font-family: Verdana;
	text-decoration:none;
	color: #6a88a1;
	background: White;
	border-style: none;
}
.dxccControl_Office2010Blue a:hover
{
    text-decoration:underline!important;
}
.dxccControl_Office2010Blue a
{
	text-decoration:none!important;
	color: #6a88a1;
}
/* Disabled */
.dxccDisabled_Office2010Blue
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxDataView -- */
.dxdvControl_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxdvControl_Office2010Blue td.dxdvCtrl
{
	padding: 0;
}
.dxdvLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxdvLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxdvContent_Office2010Blue
{
    background: White;
    padding: 21px;
    border-style: solid;
    border-color: #859ebf;
    border-width: 1px 0;
}
.dxdvItem_Office2010Blue,
.dxdvFlowItem_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	border: solid 1px #bdcbdf;
	background: #ebf3fc;
	padding: 20px;
	height: 180px; /*if IE*/
	height: expression("154px");
}
.dxdvFlowItem_Office2010Blue
{
	float: left;
	overflow: hidden;
}
.dxdvFlowItemsContainer_Office2010Blue
{
}
.dxdvEmptyItem_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	text-align: left;
	vertical-align: top;
	padding: 20px;
	height: 180px;
	/*if IE*/
	height:expression("154px");
}
.dxdvPagerPanel_Office2010Blue
{
    padding: 4px 9px;
    background: #e4effa;
}
.dxdvEmptyData_Office2010Blue
{
    color: #1e395b;
    padding: 12px 40px;
}
/* Disabled */
.dxdvDisabled_Office2010Blue
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxHeadline -- */
.dxhlControl_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
}
.dxhlContent_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
	padding-left: 2px;
}
.dxhlContent_Office2010Blue a
{
	color: #498bc2;
	text-decoration: none;
}
.dxhlContent_Office2010Blue a:hover
{
    text-decoration: underline;
}

.dxhlDate_Office2010Blue
{
    font: 11pt Verdana;
	color: #498bc2;
	white-space: nowrap;
}
.dxhlHeader_Office2010Blue
{
	font: 11pt Verdana;
	color: #498bc2;
	padding: 1px 1px 6px;
}

.dxhlDate_Office2010Blue a,
.dxhlHeader_Office2010Blue a
{
	font: 11pt Verdana;
	color: #498bc2;
	text-decoration: none;
}
.dxhlDate_Office2010Blue a:hover,
.dxhlHeader_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxhlDate_Office2010Blue a:visited,
.dxhlHeader_Office2010Blue a:visited
{
    color: #8467b2;
	text-decoration: none;
}

.dxhlDateHeader_Office2010Blue
{
	font: 10pt Verdana;
	color: #498bc2;
	font-weight: normal;
}
.dxhlLeftPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: Gray;
}
.dxhlRightPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
}
.dxhlDateLeftPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: Gray;
	padding-bottom: 2px;
	white-space: nowrap;
}
.dxhlDateRightPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: Gray;
	white-space: nowrap;
}
.dxhlTail_Office2010Blue
{
    font: 8pt Verdana;
    color: #498bc2;
}
.dxhlTailDiv_Office2010Blue
{
	font: 8pt Verdana;
	color: #498bc2;
}
.dxhlTailDiv_Office2010Blue a
{
	font: 8pt Verdana;
	color: #498bc2;
	text-decoration: none;
}
.dxhlTailDiv_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxhlTailDiv_Office2010Blue a:visited
{
    color: #8467b2;
	text-decoration: none;
}
.dxhlContent_Office2010Blue a.dxhl
{
	color: #498bc2;
	text-decoration: none;
}
.dxhlContent_Office2010Blue a.dxhl:hover
{
    text-decoration: underline;
}
.dxhlContent_Office2010Blue a.dxhl:visited
{
    color: #8467b2;
    text-decoration: none;
}
/* Disabled */
.dxhlDisabled_Office2010Blue,
.dxhlDisabled_Office2010Blue a,
.dxhlDisabled_Office2010Blue a:hover
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxLoadingPanel -- */
.dxlpLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxlpLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxlpLoadingDiv_Office2010Blue
{
	background-color: Gray;
	opacity: 0.7;
	filter:progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=70);
}
/* -- ASPxMenu -- */
.dxmControl_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxmControl_Office2010Blue a,
.dxmMenu_Office2010Blue a,
.dxmVerticalMenu_Office2010Blue a,
.dxmSubMenu_Office2010Blue a
{
	color: #1e395b;
	text-decoration: none;
}
.dxmLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxmLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxmMenu_Office2010Blue,
.dxmVerticalMenu_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background-color: #dae5f2;
	border: 1px solid #8ba0bc;
	padding: 0;
}
.dxmMenu_Office2010Blue
{
    background: #dae5f2 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemBack.png")%>') repeat-x left top;
}

.dxmMenuGutter_Office2010Blue,
.dxmMenuRtlGutter_Office2010Blue
{
}

.dxmMenuSeparator_Office2010Blue .dx,
.dxmMenuFullHeightSeparator_Office2010Blue .dx
{
	font-size: 0;
	line-height: 0;
	overflow: hidden;
	width: 1px;
	height: 1px;
}
.dxmMenuSeparator_Office2010Blue,
.dxmMenuFullHeightSeparator_Office2010Blue
{
	width: 1px;
}

.dxmMenuSeparator_Office2010Blue .dx,
.dxmMenuFullHeightSeparator_Office2010Blue
{
    background: #a9bdd8;
	width: 1px;
}
.dxmMenuSeparator_Office2010Blue .dx
{
	height: 21px;
}
.dxmMenuFullHeightSeparator_Office2010Blue
{
	display: none;
}
.dxmMenuVerticalSeparator_Office2010Blue
{
    background: #c5cfdd;
	width: 100%;
	height: 1px;
}

.dxmMenuItem_Office2010Blue,
.dxmMenuItemWithImage_Office2010Blue,
.dxmMenuItemWithPopOutImage_Office2010Blue,
.dxmMenuItemWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuItem_Office2010Blue,
.dxmVerticalMenuItemWithImage_Office2010Blue,
.dxmVerticalMenuItemWithPopOutImage_Office2010Blue,
.dxmVerticalMenuItemWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuRtlItem_Office2010Blue,
.dxmVerticalMenuRtlItemWithImage_Office2010Blue,
.dxmVerticalMenuRtlItemWithPopOutImage_Office2010Blue,
.dxmVerticalMenuRtlItemWithImageWithPopOutImage_Office2010Blue,
.dxmMenuLargeItem_Office2010Blue,
.dxmMenuLargeItemWithImage_Office2010Blue,
.dxmMenuLargeItemWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItem_Office2010Blue,
.dxmVerticalMenuLargeItemWithImage_Office2010Blue,
.dxmVerticalMenuLargeItemWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItem_Office2010Blue,
.dxmVerticalMenuLargeRtlItemWithImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemWithImageWithPopOutImage_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background: #dae5f2 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemBack.png")%>') repeat-x left top;
	white-space: nowrap;
}
.dxmMenuItem_Office2010Blue,
.dxmMenuItemWithImage_Office2010Blue
{
	padding: 4px 12px;
}
.dxmMenuItemWithPopOutImage_Office2010Blue,
.dxmMenuItemWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 14px;
}
.dxmVerticalMenuItem_Office2010Blue
{
	padding: 4px 19px 4px 8px;
}
.dxmVerticalMenuRtlItem_Office2010Blue
{
	padding: 4px 8px 4px 19px;
}
.dxmVerticalMenuItemWithImage_Office2010Blue
{
	padding: 4px 19px 4px 8px;
}
.dxmVerticalMenuRtlItemWithImage_Office2010Blue
{
	padding: 4px 8px 4px 19px;
}
.dxmVerticalMenuItemWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuRtlItemWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmVerticalMenuItemWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuRtlItemWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmMenuLargeItem_Office2010Blue,
.dxmMenuLargeItemWithImage_Office2010Blue
{
	padding: 4px 12px;
}
.dxmMenuLargeItemWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 11px;
}
.dxmVerticalMenuLargeItem_Office2010Blue,
.dxmVerticalMenuLargeItemWithImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmVerticalMenuLargeRtlItem_Office2010Blue,
.dxmVerticalMenuLargeRtlItemWithImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmVerticalMenuLargeItemWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuLargeRtlItemWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmMenuItemDropDownButton_Office2010Blue,
.dxmMenuLargeItemDropDownButton_Office2010Blue
{
    padding: 0 7px 0 8px;
}
.dxmMenuRtlItemDropDownButton_Office2010Blue,
.dxmMenuLargeRtlItemDropDownButton_Office2010Blue
{
    padding: 0 8px 0 7px;
}
.dxmVerticalMenuItemDropDownButton_Office2010Blue,
.dxmVerticalMenuLargeItemDropDownButton_Office2010Blue
{
	padding: 0 7px 0 8px;
}
.dxmVerticalMenuRtlItemDropDownButton_Office2010Blue,
.dxmVerticalMenuLargeRtlItemDropDownButton_Office2010Blue
{
	padding: 0 8px 0 7px;
}
.dxmMenuItemSelected_Office2010Blue,
.dxmMenuItemSelectedWithImage_Office2010Blue,
.dxmMenuItemSelectedWithPopOutImage_Office2010Blue,
.dxmMenuItemSelectedWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuItemSelected_Office2010Blue,
.dxmVerticalMenuItemSelectedWithImage_Office2010Blue,
.dxmVerticalMenuItemSelectedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuItemSelectedWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuRtlItemSelected_Office2010Blue,
.dxmVerticalMenuRtlItemSelectedWithImage_Office2010Blue,
.dxmVerticalMenuRtlItemSelectedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuRtlItemSelectedWithImageWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemSelected_Office2010Blue,
.dxmMenuLargeItemSelectedWithImage_Office2010Blue,
.dxmMenuLargeItemSelectedWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemSelectedWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemSelected_Office2010Blue,
.dxmVerticalMenuLargeItemWithImageSelected_Office2010Blue,
.dxmVerticalMenuLargeItemSelectedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemSelectedWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemSelected_Office2010Blue,
.dxmVerticalMenuLargeRtlItemWithImageSelected_Office2010Blue,
.dxmVerticalMenuLargeRtlItemSelectedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemSelectedWithImageWithPopOutImage_Office2010Blue
{
	background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemSBack.png")%>') repeat-x left top;
}
.dxmMenuItemSelected_Office2010Blue,
.dxmMenuItemSelectedWithImage_Office2010Blue
{
	padding: 4px 12px;
}
.dxmMenuItemSelectedWithPopOutImage_Office2010Blue,
.dxmMenuItemSelectedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 14px;
}
.dxmVerticalMenuItemSelected_Office2010Blue
{
	padding: 4px 19px 4px 8px;
}
.dxmVerticalMenuRtlItemSelected_Office2010Blue
{
	padding: 4px 8px 4px 19px;
}
.dxmVerticalMenuItemSelectedWithImage_Office2010Blue
{
	padding: 4px 19px 4px 8px;
}
.dxmVerticalMenuRtlItemSelectedWithImage_Office2010Blue
{
	padding: 4px 8px 4px 19px;
}
.dxmVerticalMenuItemSelectedWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuRtlItemSelectedWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmVerticalMenuItemSelectedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuRtlItemSelectedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmMenuLargeItemSelected_Office2010Blue,
.dxmMenuLargeItemSelectedWithImage_Office2010Blue
{
	padding: 4px 12px;
}
.dxmMenuLargeItemSelectedWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemSelectedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 11px;
}
.dxmVerticalMenuLargeItemSelected_Office2010Blue,
.dxmVerticalMenuLargeItemSelectedWithImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmVerticalMenuLargeRtlItemSelected_Office2010Blue,
.dxmVerticalMenuLargeRtlItemSelectedWithImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmVerticalMenuLargeItemSelectedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemSelectedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuLargeRtlItemSelectedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemSelectedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmMenuItemDropDownButtonSelected_Office2010Blue,
.dxmMenuLargeItemDropDownButtonSelected_Office2010Blue
{
    padding: 0 7px;
	border-left: 1px solid #c2762b;
}
.dxmMenuRtlItemDropDownButtonSelected_Office2010Blue,
.dxmMenuLargeRtlItemDropDownButtonSelected_Office2010Blue
{
    padding: 0 7px;
	border-right: 1px solid #c2762b;
}
.dxmVerticalMenuItemDropDownButtonSelected_Office2010Blue,
.dxmVerticalMenuLargeItemDropDownButtonSelected_Office2010Blue
{
	padding: 0 7px;
	border-left: 1px solid #c2762b;
}
.dxmVerticalMenuRtlItemDropDownButtonSelected_Office2010Blue,
.dxmVerticalMenuLargeRtlItemDropDownButtonSelected_Office2010Blue
{
	padding: 0 7px;
	border-right: 1px solid #c2762b;
}
.dxmMenuItemChecked_Office2010Blue,
.dxmMenuItemCheckedWithImage_Office2010Blue,
.dxmMenuItemCheckedWithPopOutImage_Office2010Blue,
.dxmMenuItemCheckedWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuItemChecked_Office2010Blue,
.dxmVerticalMenuItemCheckedWithImage_Office2010Blue,
.dxmVerticalMenuItemCheckedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuItemCheckedWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuRtlItemChecked_Office2010Blue,
.dxmVerticalMenuRtlItemCheckedWithImage_Office2010Blue,
.dxmVerticalMenuRtlItemCheckedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuRtlItemCheckedWithImageWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemChecked_Office2010Blue,
.dxmMenuLargeItemCheckedWithImage_Office2010Blue,
.dxmMenuLargeItemCheckedWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemCheckedWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemChecked_Office2010Blue,
.dxmVerticalMenuLargeItemWithImageChecked_Office2010Blue,
.dxmVerticalMenuLargeItemCheckedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemCheckedWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemChecked_Office2010Blue,
.dxmVerticalMenuLargeRtlItemWithImageChecked_Office2010Blue,
.dxmVerticalMenuLargeRtlItemCheckedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemCheckedWithImageWithPopOutImage_Office2010Blue
{
	background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemSBack.png")%>') repeat-x left top;
}
.dxmMenuItemChecked_Office2010Blue,
.dxmMenuItemCheckedWithImage_Office2010Blue
{
	padding: 4px 12px;
}
.dxmMenuItemCheckedWithPopOutImage_Office2010Blue,
.dxmMenuItemCheckedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 14px;
}
.dxmVerticalMenuItemChecked_Office2010Blue
{
	padding: 4px 19px 4px 8px;
}
.dxmVerticalMenuRtlItemChecked_Office2010Blue
{
	padding: 4px 8px 4px 19px;
}
.dxmVerticalMenuItemCheckedWithImage_Office2010Blue
{
	padding: 4px 19px 4px 8px;
}
.dxmVerticalMenuRtlItemCheckedWithImage_Office2010Blue
{
	padding: 4px 8px 4px 19px;
}
.dxmVerticalMenuItemCheckedWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuRtlItemCheckedWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmVerticalMenuItemCheckedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuRtlItemCheckedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmMenuLargeItemChecked_Office2010Blue,
.dxmMenuLargeItemCheckedWithImage_Office2010Blue
{
	padding: 4px 12px;
}
.dxmMenuLargeItemCheckedWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemCheckedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 11px;
}
.dxmVerticalMenuLargeItemChecked_Office2010Blue,
.dxmVerticalMenuLargeItemCheckedWithImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmVerticalMenuLargeRtlItemChecked_Office2010Blue,
.dxmVerticalMenuLargeRtlItemCheckedWithImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmVerticalMenuLargeItemCheckedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemCheckedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuLargeRtlItemCheckedWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemCheckedWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmMenuItemDropDownButtonChecked_Office2010Blue,
.dxmMenuLargeItemDropDownButtonChecked_Office2010Blue
{
    padding: 0 7px;
	border-left: 1px solid #c2762b;
}
.dxmMenuRtlItemDropDownButtonChecked_Office2010Blue,
.dxmMenuLargeRtlItemDropDownButtonChecked_Office2010Blue
{
    padding: 0 7px;
	border-right: 1px solid #c2762b;
}
.dxmVerticalMenuItemDropDownButtonChecked_Office2010Blue,
.dxmVerticalMenuLargeItemDropDownButtonChecked_Office2010Blue
{
	padding: 0 7px;
	border-left: 1px solid #c2762b;
}
.dxmVerticalMenuRtlItemDropDownButtonChecked_Office2010Blue,
.dxmVerticalMenuLargeRtlItemDropDownButtonChecked_Office2010Blue
{
	padding: 0 7px;
	border-right: 1px solid #c2762b;
}
.dxmMenuItemHover_Office2010Blue,
.dxmMenuItemHoverWithImage_Office2010Blue,
.dxmMenuItemHoverWithPopOutImage_Office2010Blue,
.dxmMenuItemHoverWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuItemHover_Office2010Blue,
.dxmVerticalMenuItemHoverWithImage_Office2010Blue,
.dxmVerticalMenuItemHoverWithPopOutImage_Office2010Blue,
.dxmVerticalMenuItemHoverWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuRtlItemHover_Office2010Blue,
.dxmVerticalMenuRtlItemHoverWithImage_Office2010Blue,
.dxmVerticalMenuRtlItemHoverWithPopOutImage_Office2010Blue,
.dxmVerticalMenuRtlItemHoverWithImageWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemHover_Office2010Blue,
.dxmMenuLargeItemHoverWithImage_Office2010Blue,
.dxmMenuLargeItemHoverWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemHover_Office2010Blue,
.dxmVerticalMenuLargeItemHoverWithImage_Office2010Blue,
.dxmVerticalMenuLargeItemHoverWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemHover_Office2010Blue,
.dxmVerticalMenuLargeRtlItemHoverWithImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemHoverWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemHoverWithImageWithPopOutImage_Office2010Blue
{
	background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemHBack.png")%>') repeat-x left top;
}
.dxmMenuItemHover_Office2010Blue,
.dxmMenuItemHoverWithImage_Office2010Blue
{
	padding: 4px 12px;
}
.dxmMenuItemHoverWithPopOutImage_Office2010Blue,
.dxmMenuItemHoverWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 14px;
}
.dxmVerticalMenuItemHover_Office2010Blue
{
	padding: 4px 19px 4px 8px;
}
.dxmVerticalMenuRtlItemHover_Office2010Blue
{
	padding: 4px 8px 4px 19px;
}
.dxmVerticalMenuItemHoverWithImage_Office2010Blue
{
	padding: 4px 19px 4px 8px;
}
.dxmVerticalMenuRtlItemHoverWithImage_Office2010Blue
{
	padding: 4px 8px 4px 19px;
}
.dxmVerticalMenuItemHoverWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuRtlItemHoverWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmVerticalMenuItemHoverWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuRtlItemHoverWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmMenuLargeItemHover_Office2010Blue,
.dxmMenuLargeItemHoverWithImage_Office2010Blue
{
	padding: 4px 12px;
}
.dxmMenuLargeItemHoverWithPopOutImage_Office2010Blue,
.dxmMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 11px;
}
.dxmVerticalMenuLargeItemHover_Office2010Blue,
.dxmVerticalMenuLargeItemHoverWithImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmVerticalMenuLargeRtlItemHover_Office2010Blue,
.dxmVerticalMenuLargeRtlItemHoverWithImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmVerticalMenuLargeItemHoverWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 9px 4px 8px;
}
.dxmVerticalMenuLargeRtlItemHoverWithPopOutImage_Office2010Blue,
.dxmVerticalMenuLargeRtlItemHoverWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px 4px 9px;
}
.dxmMenuItemDropDownButtonHover_Office2010Blue,
.dxmMenuLargeItemDropDownButtonHover_Office2010Blue
{
    padding: 0 7px;
	border-left: 1px solid #eecf71;
}
.dxmMenuRtlItemDropDownButtonHover_Office2010Blue,
.dxmMenuLargeRtlItemDropDownButtonHover_Office2010Blue
{
    padding: 0 7px;
    border-right: 1px solid #eecf71;
}
.dxmVerticalMenuItemDropDownButtonHover_Office2010Blue,
.dxmVerticalMenuLargeItemDropDownButtonHover_Office2010Blue
{
    padding: 0 7px;
	border-left: 1px solid #eecf71;
}
.dxmVerticalMenuRtlItemDropDownButtonHover_Office2010Blue,
.dxmVerticalMenuLargeRtlItemDropDownButtonHover_Office2010Blue
{
    padding: 0 7px;
    border-right: 1px solid #eecf71;
}
.dxmSubMenu_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
	background-color: White;
	border: 1px solid #a7abb0;
	padding: 1px;
}
.dxmSubMenuGutter_Office2010Blue
{
	background: White url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemGutterBack.png")%>') repeat-y right top;
}
.dxmSubMenuRtlGutter_Office2010Blue
{
    background: White url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemRtlGutterBack.png")%>') repeat-y left top;
}
.dxmSubMenuSeparator_Office2010Blue
{
    background: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemSepBack.gif")%>') repeat-x left top;
	width: 100%;
	height: 1px;
}
.dxmSubMenuItem_Office2010Blue,
.dxmSubMenuItemWithImage_Office2010Blue,
.dxmSubMenuItemWithPopOutImage_Office2010Blue,
.dxmSubMenuItemWithImageWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItem_Office2010Blue,
.dxmSubMenuRtlItemWithImage_Office2010Blue,
.dxmSubMenuRtlItemWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItemWithImageWithPopOutImage_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
	white-space: nowrap;
}
.dxmSubMenuItem_Office2010Blue,
.dxmSubMenuItemWithImage_Office2010Blue
{
	padding: 4px 19px 4px 8px;
}
.dxmSubMenuRtlItem_Office2010Blue,
.dxmSubMenuRtlItemWithImage_Office2010Blue
{
	padding: 4px 8px 4px 19px;
}
.dxmSubMenuItemWithPopOutImage_Office2010Blue,
.dxmSubMenuItemWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmSubMenuRtlItemWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItemWithImageWithPopOutImage_Office2010Blue
{
	padding: 4px 8px;
}
.dxmSubMenuItemDropDownButton_Office2010Blue
{
    padding: 0 8px;
}
.dxmSubMenuRtlItemDropDownButton_Office2010Blue
{
    padding: 0 8px;
}
.dxmSubMenuItemSelected_Office2010Blue,
.dxmSubMenuItemSelectedWithImage_Office2010Blue,
.dxmSubMenuItemSelectedWithPopOutImage_Office2010Blue,
.dxmSubMenuItemSelectedWithImageWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItemSelected_Office2010Blue,
.dxmSubMenuRtlItemSelectedWithImage_Office2010Blue,
.dxmSubMenuRtlItemSelectedWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItemSelectedWithImageWithPopOutImage_Office2010Blue
{
    background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemSBack.png")%>') repeat-x left top;
	border: 1px solid #c2762b;
}
.dxmSubMenuItemSelected_Office2010Blue,
.dxmSubMenuItemSelectedWithImage_Office2010Blue
{
	padding: 3px 18px 3px 7px;
}
.dxmSubMenuRtlItemSelected_Office2010Blue,
.dxmSubMenuRtlItemSelectedWithImage_Office2010Blue
{
	padding: 3px 7px 3px 18px;
}
.dxmSubMenuItemSelectedWithPopOutImage_Office2010Blue,
.dxmSubMenuItemSelectedWithImageWithPopOutImage_Office2010Blue
{
	padding: 3px 7px;
}
.dxmSubMenuRtlItemSelectedWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItemSelectedWithImageWithPopOutImage_Office2010Blue
{
	padding: 3px 7px;
}
.dxmSubMenuItemDropDownButtonSelected_Office2010Blue
{
	padding: 0 7px;
}
.dxmSubMenuRtlItemDropDownButtonSelected_Office2010Blue
{
	padding: 0 7px;
}
.dxmSubMenuItemChecked_Office2010Blue,
.dxmSubMenuItemCheckedWithImage_Office2010Blue,
.dxmSubMenuItemCheckedWithPopOutImage_Office2010Blue,
.dxmSubMenuItemCheckedWithImageWithPopOutImage_Office2010Blue
.dxmSubMenuRtlItemChecked_Office2010Blue,
.dxmSubMenuRtlItemCheckedWithImage_Office2010Blue,
.dxmSubMenuRtlItemCheckedWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItemCheckedWithImageWithPopOutImage_Office2010Blue
{
}
.dxmSubMenuItemDropDownButtonChecked_Office2010Blue
{
}
.dxmSubMenuRtlItemDropDownButtonChecked_Office2010Blue
{
}
.dxmSubMenuItemHover_Office2010Blue,
.dxmSubMenuItemHoverWithImage_Office2010Blue,
.dxmSubMenuItemHoverWithPopOutImage_Office2010Blue,
.dxmSubMenuItemHoverWithImageWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItemHover_Office2010Blue,
.dxmSubMenuRtlItemHoverWithImage_Office2010Blue,
.dxmSubMenuRtlItemHoverWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItemHoverWithImageWithPopOutImage_Office2010Blue
{
	background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemHBack.png")%>') repeat-x left top;
	border: 1px solid #f2ca58;
}
.dxmSubMenuItemHover_Office2010Blue,
.dxmSubMenuItemHoverWithImage_Office2010Blue
{
	padding: 3px 18px 3px 7px;
}
.dxmSubMenuRtlItemHover_Office2010Blue,
.dxmSubMenuRtlItemHoverWithImage_Office2010Blue
{
	padding: 3px 7px 3px 18px;
}
.dxmSubMenuItemHoverWithPopOutImage_Office2010Blue,
.dxmSubMenuItemHoverWithImageWithPopOutImage_Office2010Blue
{
	padding: 3px 7px;
}
.dxmSubMenuRtlItemHoverWithPopOutImage_Office2010Blue,
.dxmSubMenuRtlItemHoverWithImageWithPopOutImage_Office2010Blue
{
	padding: 3px 7px;
}
.dxmSubMenuItemDropDownButtonHover_Office2010Blue
{
	padding: 0 7px;
	border: 1px solid #f2ca58;
}
.dxmSubMenuRtlItemDropDownButtonHover_Office2010Blue
{
	padding: 0 7px;
	border: 1px solid #f2ca58;
}
.dxmSubMenuBorderCorrector_Office2010Blue
{
    position: absolute;
    border: 0px;
    padding: 0px;
}

.dxmMenuItemSpacing_Office2010Blue,
.dxmMenuLargeItemSpacing_Office2010Blue,
.dxmMenuItemSeparatorSpacing_Office2010Blue,
.dxmMenuLargeItemSeparatorSpacing_Office2010Blue
{
	display: none;
}
.dxmVerticalMenuItemSpacing_Office2010Blue,
.dxmVerticalMenuItemSeparatorSpacing_Office2010Blue,
.dxmVerticalMenuLargeItemSpacing_Office2010Blue,
.dxmVerticalMenuLargeItemSeparatorSpacing_Office2010Blue
{
	display: none;
}
.dxmSubMenuItemSpacing_Office2010Blue,
.dxmSubMenuItemSeparatorSpacing_Office2010Blue
{
	height: 1px;
}

.dxmMenuItemLeftImageSpacing_Office2010Blue
{
	padding-right: 4px;
}
.dxmMenuItemRightImageSpacing_Office2010Blue
{
	padding-left: 4px;
}
.dxmVerticalMenuItemLeftImageSpacing_Office2010Blue,
.dxmVerticalMenuItemRightImageSpacing_Office2010Blue,
.dxmSubMenuItemImageSpacing_Office2010Blue
{
	width: 1px;
	padding-left: 0px !important;
	padding-right: 0px !important;
	border-left-width: 0px !important;
	border-right-width: 0px !important;
}
.dxmVerticalMenuItemLeftImageSpacing_Office2010Blue div,
.dxmVerticalMenuItemRightImageSpacing_Office2010Blue div
{
	width: 4px;
	height: 1px;
}
.dxmMenuItemTopImageSpacing_Office2010Blue,
.dxmVerticalMenuItemTopImageSpacing_Office2010Blue
{
	margin-bottom: 4px;
}
.dxmMenuItemBottomImageSpacing_Office2010Blue,
.dxmVerticalMenuItemBottomImageSpacing_Office2010Blue
{
	margin-top: 4px;
}
.dxmSubMenuItemImageSpacing_Office2010Blue div
{
	width: 9px;
	height: 1px;
}
/* Scroll elements */
.dxmScrollUpButton_Office2010Blue,
.dxmScrollDownButton_Office2010Blue
{
    border: 1px solid #8ba0bc;
    background: #dae4f3 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mScrollBack.png")%>') repeat-x left top;
    cursor: pointer;
    font-size: 0px;
    padding: 2px;
    text-align: center;
}
.dxmScrollUpButton_Office2010Blue
{
    margin-bottom: 1px;
}
.dxmScrollDownButton_Office2010Blue
{
    margin-top: 1px;
}
.dxmScrollButtonHover_Office2010Blue
{
    border: 1px solid #eac656;
    background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mScrollHBack.png")%>') repeat-x left top;
}
.dxmScrollButtonPressed_Office2010Blue
{
    border: 1px solid #c28737;
    background: #fddd81;
}
.dxmScrollButtonDisabled_Office2010Blue
{
    cursor: default;
}
.dxmScrollArea_Office2010Blue
{
    overflow: hidden;
    position: relative;
}

/* Disabled */
.dxmDisabled_Office2010Blue
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxMenu Toolbar mode -- */
/*                             */
td.dxmtb.dxmMenu_Office2010Blue
{
    padding: 1px;
}
.dxmtb .dxmMenuItem_Office2010Blue,
.dxmtb .dxmMenuItemWithImage_Office2010Blue,
.dxmtb .dxmMenuItemWithPopOutImage_Office2010Blue,
.dxmtb .dxmMenuItemWithImageWithPopOutImage_Office2010Blue
{
    padding: 4px 5px;
}
.dxmtb .dxmMenuItemSelected_Office2010Blue,
.dxmtb .dxmMenuItemChecked_Office2010Blue,
.dxmtb .dxmMenuItemSelectedWithImage_Office2010Blue,
.dxmtb .dxmMenuItemCheckedWithImage_Office2010Blue,
.dxmtb .dxmMenuItemSelectedWithPopOutImage_Office2010Blue,
.dxmtb .dxmMenuItemCheckedWithPopOutImage_Office2010Blue,
.dxmtb .dxmMenuItemSelectedWithImageWithPopOutImage_Office2010Blue,
.dxmtb .dxmMenuItemCheckedWithImageWithPopOutImage_Office2010Blue
{
    padding: 3px 4px;
    border: 1px solid #d3a752;
}
.dxmtb .dxmMenuItemHover_Office2010Blue,
.dxmtb .dxmMenuItemHoverWithImage_Office2010Blue,
.dxmtb .dxmMenuItemHoverWithPopOutImage_Office2010Blue,
.dxmtb .dxmMenuItemHoverWithImageWithPopOutImage_Office2010Blue
{
    padding: 3px 4px;
    border: 1px solid #eccf72;
}
.dxmtb .dxmMenuItemHoverWithImage_Office2010Blue.dxmMenuItemLeftImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemSelectedWithImage_Office2010Blue.dxmMenuItemLeftImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemCheckedWithImage_Office2010Blue.dxmMenuItemLeftImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemHoverWithImageWithPopOutImage_Office2010Blue.dxmMenuItemLeftImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemSelectedWithImageWithPopOutImage_Office2010Blue.dxmMenuItemLeftImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemCheckedWithImageWithPopOutImage_Office2010Blue.dxmMenuItemLeftImageSpacing_Office2010Blue
{
    padding-right: 5px;
}
.dxmtb .dxmMenuItemHoverWithImage_Office2010Blue.dxmMenuItemRightImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemSelectedWithImage_Office2010Blue.dxmMenuItemRightImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemCheckedWithImage_Office2010Blue.dxmMenuItemRightImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemHoverWithImageWithPopOutImage_Office2010Blue.dxmMenuItemRightImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemSelectedWithImageWithPopOutImage_Office2010Blue.dxmMenuItemRightImageSpacing_Office2010Blue,
.dxmtb .dxmMenuItemCheckedWithImageWithPopOutImage_Office2010Blue.dxmMenuItemRightImageSpacing_Office2010Blue
{
    padding-left: 5px;
}
.dxmtb .dxmMenuItemDropDownButton_Office2010Blue,
.dxmtb .dxmMenuRtlItemDropDownButton_Office2010Blue
{
    padding: 0 4px;
}
.dxmtb .dxmMenuItemDropDownButtonSelected_Office2010Blue,
.dxmtb .dxmMenuRtlItemDropDownButtonSelected_Office2010Blue,
.dxmtb .dxmMenuItemDropDownButtonChecked_Office2010Blue,
.dxmtb .dxmMenuRtlItemDropDownButtonChecked_Office2010Blue,
.dxmtb .dxmMenuItemDropDownButtonHover_Office2010Blue,
.dxmtb .dxmMenuRtlItemDropDownButtonHover_Office2010Blue
{
    padding: 0 3px;
}
.dxmtb .dxmMenuSeparator_Office2010Blue .dx
{
    height: 16px;
}
.dxmtb .dxmMenuItemSpacing_Office2010Blue,
.dxmtb .dxmMenuItemSeparatorSpacing_Office2010Blue
{
    width: 1px;
    display: block;
}

/*                     */
/* -- ASPxMenu Lite -- */
/*                     */
.dxm-rtl
{
	direction: ltr;
}
.dxm-rtl .dxm-content
{
	direction: rtl;
}

.dxm-ltr .dxm-main,
.dxm-ltr .dxm-horizontal ul.dx
{
	float: left;
}
.dxm-rtl .dxm-main,
.dxm-rtl .dxm-horizontal ul.dx
{
	float: right;
}
.dxm-popup
{
	position: relative;
}
ul.dx
{
	list-style: none none outside;
	margin: 0;
	padding: 0;

	background-repeat: repeat-y;
	background-position: left top;
}
.dxm-rtl ul.dx
{
	background-position: right top;
}
.dxm-image,
.dxm-pImage
{
	border-width: 0px;
	vertical-align: top;
}
.dxm-popOut,
.dxm-spacing,
.dxm-separator,
.dxm-separator b
{
	font-size: 0px;
	line-height: 0px;
	display: block;
}

.dxm-ltr .dxm-horizontal .dxm-item,
.dxm-ltr .dxm-horizontal .dxm-spacing,
.dxm-ltr .dxm-horizontal .dxm-separator,
.dxm-ltr .dxm-content
{
    float: left;
}
.dxm-rtl .dxm-horizontal .dxm-item,
.dxm-rtl .dxm-horizontal .dxm-spacing,
.dxm-rtl .dxm-horizontal .dxm-separator,
.dxm-rtl .dxm-content
{
    float: right;
}

.dxm-vertical .dxm-image-r .dxm-popOut
{
	float: left;
}
.dxm-vertical .dxm-image-l .dxm-popOut
{
	float: right;
}

.dxm-ltr .dxm-horizontal .dxm-popOut
{
    float: left;
}
.dxm-ltr .dxm-vertical .dxm-image-t .dxm-popOut,
.dxm-ltr .dxm-vertical .dxm-image-b .dxm-popOut,
.dxm-ltr .dxm-popup .dxm-popOut
{
	float: right;
}

.dxm-rtl .dxm-horizontal .dxm-popOut
{
    float: right;
}
.dxm-rtl .dxm-vertical .dxm-image-t .dxm-popOut,
.dxm-rtl .dxm-vertical .dxm-image-b .dxm-popOut,
.dxm-rtl .dxm-popup .dxm-popOut
{
	float: left;
}

.dxm-ie7 .dxm-vertical ul.dx,
.dxm-ie7 .dxm-popup ul.dx
{
	height: 1%;
}
.dxm-ie7 .dxm-vertical .dxm-item,
.dxm-ie7 .dxm-popup .dxm-item
{
	margin-bottom: -2px;
}
.dxm-ie7 .dxm-vertical .dxm-spacing,
.dxm-ie7 .dxm-popup .dxm-spacing
{
	margin-bottom: -1px;
}
.dxm-ie7 .dxm-vertical .dxm-item,
.dxm-ie7 .dxm-vertical .dxm-spacing,
.dxm-ie7 .dxm-vertical .dxm-separator,
.dxm-ie7 .dxm-popup .dxm-item,
.dxm-ie7 .dxm-popup .dxm-spacing,
.dxm-ie7 .dxm-popup .dxm-separator
{
	zoom: 1;
}
.dxm-vertical .dxm-separator b,
.dxm-popup .dxm-separator b
{
	margin: 0px auto;
}
.dxm-ie7 .dxm-vertical .dxm-separator b,
.dxm-ie7 .dxm-popup .dxm-separator b
{
	margin: 0px;
}
.dxm-ie7 .dxm-vertical .dxm-separator,
.dxm-ie7 .dxm-popup .dxm-separator
{
	text-align: center;
}
/* Horizontal align = Center */
.dxm-haCenter {
    padding-left: 0px !important;
    padding-right: 0px !important;
    overflow: hidden;
}
.dxm-haCenter .dxm-haWrapper,
.dxm-haCenter .dxm-content {
    position: relative;
}
.dxm-ltr .dxm-image-l .dxm-haCenter .dxm-haWrapper,
.dxm-ltr .dxm-image-t .dxm-haCenter .dxm-haWrapper,
.dxm-ltr .dxm-image-b .dxm-haCenter .dxm-haWrapper {
    float: left;
    left: 50%;
}
.dxm-rtl .dxm-image-l .dxm-haCenter .dxm-haWrapper,
.dxm-rtl .dxm-image-t .dxm-haCenter .dxm-haWrapper,
.dxm-rtl .dxm-image-b .dxm-haCenter .dxm-haWrapper {
    float: right;
    right: 50%;
}
.dxm-ltr .dxm-image-l .dxm-haCenter .dxm-content,
.dxm-ltr .dxm-image-t .dxm-haCenter .dxm-content,
.dxm-ltr .dxm-image-b .dxm-haCenter .dxm-content {
    left: -50%;
}
.dxm-rtl .dxm-image-l .dxm-haCenter .dxm-content,
.dxm-rtl .dxm-image-t .dxm-haCenter .dxm-content,
.dxm-rtl .dxm-image-b .dxm-haCenter .dxm-content {
    right: -50%;
}
.dxm-ltr .dxm-image-r .dxm-haCenter .dxm-haWrapper {
    float: right;
    right: 50%;
}
.dxm-rtl .dxm-image-r .dxm-haCenter .dxm-haWrapper {
    float: left;
    left: 50%;
}
.dxm-ltr .dxm-image-r .dxm-haCenter .dxm-content {
    right: -50%;
}
.dxm-rtl .dxm-image-r .dxm-haCenter .dxm-content {
    left: -50%;
}

/* Appearance */
.dxmLite_Office2010Blue .dxm-main
{
	background: #dae5f2 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemBack.png")%>') repeat-x left top;
	border: 1px solid #8ba0bc;
	padding: 0;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-item
{
    background: #dae5f2 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemBack.png")%>') repeat-x left top;
}

.dxmLite_Office2010Blue .dxm-vertical
{
	width: 150px;
	padding: 0;
}

.dxmLite_Office2010Blue .dxm-popup
{
	background-color: White;
	border: 1px solid #a7abb0;
	padding: 1px;
}

.dxmBC
{
	background-color: white;
}

.dxmLite_Office2010Blue ul.dx
{
	font: 8pt Verdana;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-gutter
{
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemGutterBack.png")%>');
}
.dxm-rtl.dxmLite_Office2010Blue .dxm-popup .dxm-gutter
{
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemRtlGutterBack.png")%>');
}

.dxmLite_Office2010Blue .dxm-item
{
	cursor: default;
}

.dxmLite_Office2010Blue .dxm-image-t .dxm-item,
.dxmLite_Office2010Blue .dxm-image-b .dxm-item,
.dxmLite_Office2010Blue .dxm-content
{
	text-align: center;
	white-space: nowrap;
}

.dxmLite_Office2010Blue,
.dxmLite_Office2010Blue .dxm-content a.dx
{
	color: #1e395b;
}
.dxmLite_Office2010Blue .dxm-disabled,
.dxmLite_Office2010Blue .dxm-disabled .dxm-content a.dx
{
	color: #b2b7bd;
}

.dxmLite_Office2010Blue .dxm-content a.dx
{
	text-decoration: none;
}

.dxmLite_Office2010Blue .dxm-item
{
    border-width: 0px;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-item
{
	border-width: 1px;
}
.dxm-ltr.dxmLite_Office2010Blue .dxm-popOut,
.dxm-rtl.dxmLite_Office2010Blue .dxm-image-l .dxm-popOut
{
    border-width: 0 0 0 1px;
}
.dxm-ltr.dxmLite_Office2010Blue .dxm-image-r .dxm-popOut,
.dxm-rtl.dxmLite_Office2010Blue .dxm-popOut
{
	border-width: 0 1px 0 0;
}
.dxmLite_Office2010Blue .dxm-item,
.dxmLite_Office2010Blue .dxm-popOut
{
	border-color: Transparent;
	border-style: solid;
}

/* Checked, Selected, Hovered */
.dxmLite_Office2010Blue .dxm-popup .dxm-selected,
.dxmLite_Office2010Blue .dxm-dropDownMode.dxm-selected .dxm-popOut,
.dxmLite_Office2010Blue .dxm-main .dxm-dropDownMode.dxm-checked .dxm-popOut
{
    border-color: #c2762b;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-hovered
{
    border-width: 1px;
    padding: 0;
}

.dxmLite_Office2010Blue .dxm-popup .dxm-hovered,
.dxmLite_Office2010Blue .dxm-main .dxm-dropDownMode.dxm-hovered .dxm-popOut,
.dxmLite_Office2010Blue .dxm-popup .dxm-dropDownMode.dxm-hovered .dxm-popOut
{
	border-color: #f2ca58;
}
.dxmLite_Office2010Blue .dxm-main .dxm-checked,
.dxmLite_Office2010Blue .dxm-main .dxm-selected
{
	background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemSBack.png")%>') repeat-x left top;
}
.dxmLite_Office2010Blue .dxm-main .dxm-hovered
{
	background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemHBack.png")%>') repeat-x left top;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-selected
{
	background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemSBack.png")%>') repeat-x left top;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-hovered
{
	background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemHBack.png")%>') repeat-x left top;
}

/* Content */
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-l .dxm-content,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-r .dxm-content
{
	padding: 4px 12px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t .dxm-content,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b .dxm-content
{
	padding: 4px 12px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-l .dxm-subMenu .dxm-content,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t .dxm-subMenu .dxm-content,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b .dxm-subMenu .dxm-content
{
	padding-right: 9px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-l .dxm-dropDownMode .dxm-content
{
	padding-right: 9px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-r .dxm-subMenu .dxm-content
{
	padding-left: 9px;
	padding-right: 12px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-r .dxm-dropDownMode .dxm-content
{
	padding-left: 10px;
	padding-right: 11px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t .dxm-dropDownMode .dxm-content,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b .dxm-dropDownMode .dxm-content
{
	padding-right: 10px;
}

.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-content,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-content
{
    padding: 4px 19px 4px 8px;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-content
{
	padding: 3px 12px 3px 8px;
}
.dxm-rtl.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-content,
.dxm-rtl.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-content
{
    padding: 4px 8px 4px 12px;
}
.dxm-rtl.dxmLite_Office2010Blue .dxm-popup .dxm-content
{
	padding: 3px 8px 3px 12px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-noSubMenu .dxm-content,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-subMenu .dxm-content,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-dropDownMode .dxm-content
{
	padding-right: 5px;
	padding-left: 7px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t .dxm-content,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b .dxm-content
{
	padding: 6px 10px;
}

/* Image */
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-l .dxm-image,
.dxm-ltr.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-l .dxm-hasText .dxm-image
{
	margin-right: 4px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-r .dxm-image,
.dxm-ltr.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-r .dxm-hasText .dxm-image
{
	margin-left: 4px;
}
.dxmLite_Office2010Blue .dxm-image-t .dxm-image
{
	margin-bottom: 4px;
}
.dxmLite_Office2010Blue .dxm-image-b .dxm-image
{
	margin-top: 4px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-image
{
	margin-right: 7px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-image
{
	margin-left: 7px;
}
.dxm-ltr.dxmLite_Office2010Blue .dxm-popup .dxm-image
{
	margin-right: 24px;
}
.dxm-rtl.dxmLite_Office2010Blue .dxm-popup .dxm-image
{
	margin-left: 24px;
}

/* Image replacement */
.dxm-ltr.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l.dxm-noImages .dxm-content,
.dxm-ltr.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r.dxm-noImages .dxm-content
{
	padding-left: 7px;
}
.dxm-rtl.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l.dxm-noImages .dxm-content,
.dxm-rtl.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r.dxm-noImages .dxm-content
{
	padding-right: 7px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-noImage
{
	padding-left: 20px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-noImage.dxm-hovered
{
	padding-left: 20px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-noImage
{
	padding-right: 20px;
}
.dxm-ltr.dxmLite_Office2010Blue .dxm-popup .dxm-gutter.dxm-noImages .dxm-item,
.dxm-ltr.dxmLite_Office2010Blue .dxm-popup .dxm-noImage
{
	padding-left: 37px;
}
.dxm-rtl.dxmLite_Office2010Blue .dxm-popup .dxm-gutter.dxm-noImages .dxm-item,
.dxm-rtl.dxmLite_Office2010Blue .dxm-popup .dxm-noImage
{
	padding-right: 37px;
}

/* PopOut */
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-l .dxm-popOut,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-r .dxm-popOut,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-popOut,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t .dxm-noImage .dxm-popOut,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-popOut,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b .dxm-noImage .dxm-popOut
{
    padding-top: 9px;
    padding-bottom: 8px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t .dxm-popOut,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b .dxm-popOut
{
    padding-top: 26px;
    padding-bottom: 26px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-l .dxm-popOut
{
    padding-right: 9px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-r .dxm-popOut
{
	padding-left: 9px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t .dxm-popOut,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b .dxm-popOut
{
	padding-right: 11px;
}
.dxm-rtl.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t .dxm-popOut,
.dxm-rtl.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b .dxm-popOut
{
    padding-left: 8px;
}

.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-popOut,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-popOut
{
	padding-top: 7px;
	padding-bottom: 7px
}
.dxmLite_Office2010Blue .dxm-popup .dxm-popOut
{
    padding-top: 6px;
	padding-bottom: 6px
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t.dxm-noImages .dxm-popOut,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t .dxm-noImage .dxm-popOut,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b.dxm-noImages .dxm-popOut,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b .dxm-noImage .dxm-popOut
{
	padding-top: 9px;
	padding-bottom: 9px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t .dxm-popOut,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b .dxm-popOut
{
	padding-top: 27px;
	padding-bottom: 27px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-popOut,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-popOut,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t .dxm-popOut,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b .dxm-popOut
{
    padding-left: 8px;
    padding-right: 9px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-dropDownMode .dxm-popOut,
.dxmLite_Office2010Blue .dxm-vertical .dxm-dropDownMode .dxm-popOut
{
	padding-left: 7px;
	padding-right: 7px;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-popOut
{
	padding-left: 7px;
	padding-right: 7px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-popOut
{
    padding-left: 8px;
}

/* PopOut replacement */
.dxm-ltr.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t .dxm-noSubMenu,
.dxm-ltr.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b .dxm-noSubMenu,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-noSubMenu
{
    padding-right: 21px;
}
.dxm-ltr.dxmLite_Office2010Blue .dxm-popup .dxm-noSubMenu
{
    padding-right: 19px;
}

.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-noSubMenu,
.dxm-rtl.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t .dxm-noSubMenu,
.dxm-rtl.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b .dxm-noSubMenu,
.dxm-rtl.dxmLite_Office2010Blue .dxm-popup .dxm-noSubMenu
{
	padding-left: 16px;
}
/* Spacings */
.dxmLite_Office2010Blue .dxm-horizontal .dxm-spacing
{
	width: 2px;
	height: 1px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-spacing,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-spacing,
.dxmLite_Office2010Blue .dxm-popup .dxm-spacing
{
	height: 1px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t .dxm-spacing,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b .dxm-spacing
{
	height: 2px;
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-separator
{
	margin: 0;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-separator,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-separator,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t .dxm-separator,
.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b .dxm-separator
{
    margin: 0;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-separator
{
	margin: 1px;
}
.dxm-ie7.dxmLite_Office2010Blue .dxm-vertical .dxm-image-l .dxm-separator,
.dxm-ie7.dxmLite_Office2010Blue .dxm-vertical .dxm-image-r .dxm-separator,
.dxm-ie7.dxmLite_Office2010Blue .dxm-vertical .dxm-image-t .dxm-separator,
.dxm-ie7.dxmLite_Office2010Blue .dxm-vertical .dxm-image-b .dxm-separator
{
	margin-top: -3px;
}

/* Separator */
.dxmLite_Office2010Blue .dxm-horizontal .dxm-separator b
{
	background: #a9bdd8;
	height: 21px;
	width: 1px;
}
.dxmLite_Office2010Blue .dxm-vertical .dxm-separator b
{
    background: #c5cfdd;
    height: 1px;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-separator b
{
    background: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemSepBack.gif")%>') repeat-x left top;
	height: 1px;
}

.dxmLite_Office2010Blue .dxm-horizontal .dxm-separator b,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-separator b,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-separator b
{
}
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-t .dxm-separator b,
.dxmLite_Office2010Blue .dxm-horizontal .dxm-image-b .dxm-separator b
{
	margin-top: 18px;
}
.dxmLite_Office2010Blue .dxm-popup .dxm-gutter .dxm-separator
{
	padding-left: 40px;
}
/* Scroll elements */
.dxmLite_Office2010Blue .dxm-scrollUpBtn,
.dxmLite_Office2010Blue .dxm-scrollDownBtn
{
    border: 1px solid #8ba0bc;
    background: #dae4f3 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mScrollBack.png")%>') repeat-x left top;
    cursor: pointer;
    font-size: 0px;
    padding: 2px;
    text-align: center;
}
.dxmLite_Office2010Blue .dxm-scrollUpBtn
{
    margin-bottom: 1px;
}
.dxmLite_Office2010Blue .dxm-scrollDownBtn
{
    margin-top: 1px;
}
.dxmLite_Office2010Blue .dxm-scrollBtnHovered
{
    border: 1px solid #eac656;
    background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mScrollHBack.png")%>') repeat-x left top;
}
.dxmLite_Office2010Blue .dxm-scrollBtnPressed
{
    border: 1px solid #c28737;
    background: #fddd81;
}
.dxmLite_Office2010Blue .dxm-scrollBtnDisabled
{
    cursor: default;
}
.dxmLite_Office2010Blue .dxm-scrollArea
{
    overflow: hidden;
    position: relative;
}

/*                                  */
/* -- ASPxMenu Lite Toolbar mode -- */
/*                                  */
.dxmLite_Office2010Blue .dxm-main.dxmtb
{
    padding: 1px;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-item
{
	border-style: solid;
    border-width: 0;
    padding: 1px;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-item.dxm-hovered,
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-item.dxm-selected,
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-item.dxm-checked
{
    border-width: 1px;
    padding: 0;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-item.dxm-selected,
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-item.dxm-checked
{
    border-color: #d3a752;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-item.dxm-hovered
{
	border-color: #eccf72;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-l .dxm-content,
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-l .dxm-subMenu .dxm-content,
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-l .dxm-subMenu.dxm-noImage .dxm-content,
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-l .dxm-dropDownMode.dxm-noImage .dxm-content
{
	padding: 3px 4px;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-l.dxm-noImages .dxm-item .dxm-content,
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-l .dxm-noImage .dxm-content {
    padding: 6px 5px;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-l .dxm-popOut,
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-dropDownMode .dxm-popOut
{
    padding: 9px 3px;
}
.dxm-ltr.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-l .dxm-image
{
	margin-right: 0;
}
.dxm-rtl.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-image-r .dxm-image
{
	margin-left: 0;
}
.dxm-ltr.dxmLite_Office2010Blue .dxm-popup.dxmtb .dxm-image
{
	margin-right: 10px;
}
.dxm-rtl.dxmLite_Office2010Blue .dxm-popup.dxmtb .dxm-image
{
	margin-left: 10px;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-spacing
{
	width: 4px;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-separator
{
	margin: 0 1px;
}
.dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-separator b
{
	margin-top: 4px;
	height: 16px;
	width: 1px;
}

/* -- ASPxNavBar -- */
.dxnbControl_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxnbControl_Office2010Blue td.dxnbCtrl
{
    background-color: White;
}
.dxnbControl_Office2010Blue a
{
	color: #1e395b;
}
.dxnbLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxnbLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxnbGroupHeader_Office2010Blue,
.dxnbGroupHeaderCollapsed_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background: #d4e4f3 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.nbHBack.png")%>') repeat-x left top;
	border: 1px solid #8ba0bc;
	padding: 6px;
}
.dxnbGroupHeader_Office2010Blue table.dxnb,
.dxnbGroupHeaderCollapsed_Office2010Blue table.dxnb
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxnbGroupHeader_Office2010Blue td.dxnb,
.dxnbGroupHeaderCollapsed_Office2010Blue td.dxnb
{
	white-space: nowrap;
}
.dxnbGroupContent_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	border-style: none;
	padding: 1px 0 0;
}

.dxnbItem_Office2010Blue,
.dxnbLargeItem_Office2010Blue,
.dxnbBulletItem_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxnbItem_Office2010Blue
{
    padding: 6px 7px;
}
.dxnbLargeItem_Office2010Blue
{
    padding: 6px 7px;
}
.dxnbBulletItem_Office2010Blue
{
	margin: 5px 0;
}
.dxnbItemSelected_Office2010Blue,
.dxnbLargeItemSelected_Office2010Blue,
.dxnbBulletItemSelected_Office2010Blue
{
	background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.nbISBack.png")%>') repeat-x left top;
	border: 1px solid #c2762b;
}
.dxnbItemSelected_Office2010Blue
{
	padding: 5px 6px;
}
.dxnbLargeItemSelected_Office2010Blue
{
	padding: 5px 6px;
}
.dxnbItemHover_Office2010Blue,
.dxnbLargeItemHover_Office2010Blue,
.dxnbBulletItemHover_Office2010Blue
{
	background: #fcf6dd url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.nbIHBack.png")%>') repeat-x left top;
	border: 1px solid #eecf71;
}
.dxnbItemHover_Office2010Blue
{
	padding: 5px 6px;
}
.dxnbLargeItemHover_Office2010Blue
{
	padding: 5px 6px;
}
.dxnbGroupHeader_Office2010Blue,
.dxnbGroupHeaderCollapsed_Office2010Blue
{
    text-align: left;
}
.dxnbItem_Office2010Blue,
.dxnbItemHover_Office2010Blue,
.dxnbItemSelected_Office2010Blue,
.dxnbBulletItem_Office2010Blue,
.dxnbBulletItemHover_Office2010Blue,
.dxnbBulletItemSelected_Office2010Blue
{
    text-align: left;
}
.dxnbLargeItem_Office2010Blue,
.dxnbLargeItemHover_Office2010Blue,
.dxnbLargeItemSelected_Office2010Blue
{
    text-align: center;
}
.dxnbGroupHeaderHover_Office2010Blue
{
}
.dxnbGroupHeaderCollapsedHover_Office2010Blue
{
}
/* Spacings */
.dxnbGroupSpacing_Office2010Blue,
.dxnbItemSpacing_Office2010Blue
{
	width: 100%;
	height: 1px;
}
.dxnbControl_Office2010Blue .dxnbNoHeads .dxnbGroupSpacing_Office2010Blue
{
    height: 12px;
}
.dxnbImgCellLeft_Office2010Blue
{
	padding-right: 6px;
}
.dxnbImgCellRight_Office2010Blue
{
	padding-left: 6px;
}
.dxnbLargeItemImgTop_Office2010Blue
{
	margin-bottom: 5px;
}
.dxnbLargeItemImgBottom_Office2010Blue
{
	margin-top: 5px;
}
/* Disabled */
.dxnbDisabled_Office2010Blue,
.dxnbDisabled_Office2010Blue table.dxnb
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxNavBar Lite -- */
.dxnbLite_Office2010Blue
{
    font: 8pt Verdana;
    color: #1e395b;
    background-color: White;
	list-style: none none outside;
    margin: 0;
    padding: 0;
    float: left;
    width: 200px;
}
.dxnbLite_Office2010Blue a
{
	color: #1e395b;
}
.dxnbLite_Office2010Blue .dxnb-gr
{
	margin-bottom: 1px;
}
.dxnbLite_Office2010Blue.dxnb-noHeads .dxnb-gr
{
	margin-bottom: 12px;
}
.dxnbLite_Office2010Blue .dxnb-header,
.dxnbLite_Office2010Blue .dxnb-headerCollapsed
{
    font: 8pt Verdana;
	color: #1e395b;
	background: #d4e4f3 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.nbHBack.png")%>') repeat-x left top;
	border: 1px solid #8ba0bc;
	padding: 6px;
	overflow: hidden;
	cursor: pointer;
	clear: both;
}
.dxnbLite_Office2010Blue .dxnb-content
{
    list-style: none none outside;
    margin: 0;
    padding: 1px 0 0;
    overflow: hidden;
}

.dxnbLite_Office2010Blue .dxnb-item,
.dxnbLite_Office2010Blue .dxnb-large,
.dxnbLite_Office2010Blue .dxnb-bullet
{
	font: 8pt Verdana;
	color: #1e395b;
    clear: both;
    overflow: hidden;
    cursor: default;
}
.dxnbLite_Office2010Blue .dxnb-item,
.dxnbLite_Office2010Blue .dxnb-large,
.dxnbLite_Office2010Blue .dxnb-tmpl
{
	margin-bottom: 1px;
}
.dxnbLite_Office2010Blue .dxnb-item
{
	padding: 6px 7px;
}
.dxnbLite_Office2010Blue .dxnb-large
{
	padding: 6px 7px;
}
.dxnbLite_Office2010Blue .dxnb-bullet,
.dxnbLite_Office2010Blue .dxnb-bulletHover,
.dxnbLite_Office2010Blue .dxnb-bulletSelected
{
    padding: 0 5px;
    overflow: visible;
    margin: 5px 0;
}
.dxnbLite_Office2010Blue .dxnb-itemSelected,
.dxnbLite_Office2010Blue .dxnb-itemHover
{
	padding: 5px 6px;
}
.dxnbLite_Office2010Blue .dxnb-largeSelected,
.dxnbLite_Office2010Blue .dxnb-largeHover
{
	padding: 5px 6px;
}
.dxnbLite_Office2010Blue .dxnb-itemSelected,
.dxnbLite_Office2010Blue .dxnb-largeSelected
{
	background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.nbISBack.png")%>') repeat-x left top;
	border: 1px solid #c2762b;
}
.dxnbLite_Office2010Blue .dxnb-itemHover,
.dxnbLite_Office2010Blue .dxnb-largeHover
{
	background: #fcf6dd url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.nbIHBack.png")%>') repeat-x left top;
	border: 1px solid #eecf71;
}
.dxnbLite_Office2010Blue .dxnb-header,
.dxnbLite_Office2010Blue .dxnb-headerCollapsed,
.dxnbLite_Office2010Blue .dxnb-item,
.dxnbLite_Office2010Blue .dxnb-itemHover,
.dxnbLite_Office2010Blue .dxnb-itemSelected,
.dxnbLite_Office2010Blue .dxnb-bullet,
.dxnbLite_Office2010Blue .dxnb-bulletHover,
.dxnbLite_Office2010Blue .dxnb-bulletSelected
{
    text-align: left;
}
.dxnbLite_Office2010Blue .dxnb-large,
.dxnbLite_Office2010Blue .dxnb-largeHover,
.dxnbLite_Office2010Blue .dxnb-largeSelected
{
    text-align: center;
}

.dxnbLite_Office2010Blue .dxnb-headerHover
{
}
.dxnbLite_Office2010Blue .dxnb-headerCollapsedHover
{
}
.dxnbLite_Office2010Blue .dxnb-last,
.dxnbLite_Office2010Blue.dxnb-noHeads .dxnb-last
{
    margin-bottom: 0;
}
.dxnbLite_Office2010Blue .dxnb-btn,
.dxnbLite_Office2010Blue .dxnb-btnLeft,
.dxnbLite_Office2010Blue .dxnb-img
{
	border-width: 0;
}

.dxnbLite_Office2010Blue .dxnb-btn
{
	float: right;
	margin-left: 4px;
}
.dxnbLite_Office2010Blue .dxnb-btnLeft
{
	float: left;
	margin-right: 4px;
}
.dxnbLite_Office2010Blue .dxnb-img
{
	margin:0 6px 0 0;
	float: left;
}
.dxnbLite_Office2010Blue .dxnb-right .dxnb-item .dxnb-img,
.dxnbLite_Office2010Blue .dxnb-rtlHeader .dxnb-img
{
	float: right;
	margin: 0 0 0 6px;
}
.dxnbLite_Office2010Blue .dxnb-top .dxnb-large .dxnb-img
{
	margin-bottom: 5px;
}
.dxnbLite_Office2010Blue .dxnb-bottom .dxnb-large .dxnb-img
{
	margin-top: 5px;
}
.dxnbLite_Office2010Blue .dxnb-large .dxnb-img
{
    display: block;
    float: none;
    margin-left: auto;
    margin-right: auto;
}
.dxnbLiteDisabled_Office2010Blue,
.dxnbLite_Office2010Blue .dxnbLiteDisabled_Office2010Blue,
.dxnbLiteDisabled_Office2010Blue a,
.dxnbLiteDisabled_Office2010Blue .dxnb-item,
.dxnbLiteDisabled_Office2010Blue .dxnb-large,
.dxnbLiteDisabled_Office2010Blue .dxnb-bullet,
.dxnbLiteDisabled_Office2010Blue .dxnb-header,
.dxnbLiteDisabled_Office2010Blue .dxnb-headerCollapsed
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxNewsControl -- */
.dxncControl_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
}
.dxncControl_Office2010Blue td.dxncCtrl
{
	padding: 0;
}
.dxncLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxncLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxncContent_Office2010Blue
{
    background: White;
    border-style: solid;
    border-color: #859ebf;
    border-width: 1px 0;
    padding: 18px 53px 6px;
}
.dxncPagerPanel_Office2010Blue
{
	padding: 4px 9px;
	background: #e4effa;
}
.dxncItem_Office2010Blue
{
	font: 8pt Verdana;
	vertical-align: top;
	border-style: none;
	padding: 7px 0 9px;
}
.dxncEmptyItem_Office2010Blue
{
	font: 8pt Verdana;
	vertical-align: top;
	padding: 12px 12px 12px 14px;
}
.dxncBackToTop_Office2010Blue
{
	font: 8pt Verdana;
	color: #498bc2;
}
.dxncBackToTop_Office2010Blue a
{
	font: 8pt Verdana;
	color: #498bc2;
	text-decoration: none;
}
.dxncBackToTop_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxncBackToTop_Office2010Blue a:visited
{
	color: #8467b2;
	text-decoration: none;
}

/* Headline */
.dxncItemContent_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
	padding-left: 2px;
}
.dxncItemContent_Office2010Blue a
{
	color: #498bc2;
	text-decoration: none;
}
.dxncItemContent_Office2010Blue a:hover
{
	text-decoration: underline;
}
.dxncItemDate_Office2010Blue
{
	font: 11pt Verdana;
	color: #498bc2;
	padding-bottom: 1px;
}
.dxncItemHeader_Office2010Blue
{
	font: 11pt Verdana;
	color: #498bc2;
	padding: 1px 1px 6px;
}
.dxncItemHeader_Office2010Blue .dxncItemDate_Office2010Blue
{
	font: 10pt Verdana;
	color: Gray;
	font-weight: normal;
}
.dxncItemDate_Office2010Blue a,
.dxncItemHeader_Office2010Blue a
{
	font: 11pt Verdana;
	color: #498bc2;
	text-decoration: none;
}
.dxncItemDate_Office2010Blue a:hover,
.dxncItemHeader_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxncItemDate_Office2010Blue a:visited,
.dxncItemHeader_Office2010Blue a:visited
{
    color: #8467b2;
	text-decoration: none;
}
.dxncItemLeftPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: Gray;
}
.dxncItemRightPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
}
.dxncItemDateLeftPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: Gray;
	white-space: nowrap;
}
.dxncItemDateRightPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: Gray;
	white-space: nowrap;
}
.dxncItemTailDiv_Office2010Blue
{
	font: 8pt Verdana;
	color: #498bc2;
}
.dxncItemTailDiv_Office2010Blue a
{
	font: 8pt Verdana;
	color: #498bc2;
	text-decoration: none;
}
.dxncItemTailDiv_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxncItemTailDiv_Office2010Blue a:visited
{
    color: #8467b2;
	text-decoration: none;
}

.dxncItemContent_Office2010Blue a.dxhl
{
	color: #498bc2;
	text-decoration: none;
}
.dxncItemContent_Office2010Blue a.dxhl:hover
{
    text-decoration: underline;
}
.dxncItemContent_Office2010Blue a.dxhl:visited
{
    color: #8467b2;
    text-decoration: none;
}
.dxncEmptyData_Office2010Blue
{
    color: Gray;
}
/* Disabled */
.dxncDisabled_Office2010Blue,
.dxncDisabled_Office2010Blue a,
.dxncDisabled_Office2010Blue a:hover
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxPager -- */
.dxpControl_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxpControl_Office2010Blue td.dxpCtrl
{
    padding: 5px 2px;
}
.dxpControl_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxpButton_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	text-decoration: none;
	white-space: nowrap;
	text-align: center;
	vertical-align: middle;
}
.dxpButton_Office2010Blue a
{
	font: 8pt Verdana;
	color: #1e395b;
	text-decoration: none;
	white-space: nowrap;
}
.dxpDisabledButton_Office2010Blue
{
	font: 8pt Verdana;
	color: #b2b7bd;
	text-decoration: none;
}
.dxpPageNumber_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	text-decoration: none;
	text-align: center;
	vertical-align: middle;
	padding: 0 6px;
}
.dxpPageNumber_Office2010Blue a
{
	font: 8pt Verdana;
	color: #1e395b;
	text-decoration: none;
}
.dxpCurrentPageNumber_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background: #f7e7a6;
	text-decoration: none;
	padding: 4px;
	border: 1px solid #c08930;
	white-space: nowrap;
}
.dxpSummary_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	white-space: nowrap;
	text-align: center;
	vertical-align: middle;
	padding: 1px 4px;
}
.dxpSeparator_Office2010Blue
{
	background: #c9c9c9;
	padding: 5px 0;
}
/* Disabled */
.dxpDisabled_Office2010Blue
{
	color: #b2b7bd;
	border-color: #b2b7bd;
	cursor: default;
}

/* -- ASPxPager Lite -- */

.dxpLite_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	padding: 5px 2px;
	float: left;
}

.dxpLite_Office2010Blue .dxp-summary,
.dxpLite_Office2010Blue .dxp-sep,
.dxpLite_Office2010Blue .dxp-button,
.dxpLite_Office2010Blue .dxp-num,
.dxpLite_Office2010Blue .dxp-current,
.dxpLite_Office2010Blue .dxp-ellip
{
	display: block;
	float: left;
	margin-left: 2px;
	font-weight: normal;
}
.dxpLite_Office2010Blue .dxp-lead
{
	margin-left: 0 !important;
}

.dxpLite_Office2010Blue a
{
	color: #1e395b;
	text-decoration: none;
}
.dxpLite_Office2010Blue a:hover
{
	text-decoration: underline;
}

.dxpLite_Office2010Blue .dxp-button
{
	color: #1e395b;
	white-space: nowrap;
	text-align: center;
	cursor: pointer;
	text-decoration: none;
}
.dxpLite_Office2010Blue .dxp-button img
{
	border: 0;
	vertical-align: middle;
	text-decoration: none;
}
.dxpLite_Office2010Blue .dxp-wideButton
{
	padding: 0 5px;
}
.dxpLite_Office2010Blue .dxp-disabledButton
{
	text-decoration: none;
	color: #b2b7bd;
	cursor: default;
}

.dxpLite_Office2010Blue .dxp-num
{
	color: #1e395b;
	text-decoration: none;
	padding: 5px 6px 1px;
	cursor: pointer;
}

.dxpLite_Office2010Blue .dxp-current
{
	color: #1e395b;
	text-decoration: none;
	background: #f7e7a6;
	padding: 4px;
	cursor: text;
	border: 1px solid #c08930;
}

.dxpLite_Office2010Blue .dxp-summary,
.dxpLite_Office2010Blue .dxp-ellip
{
	white-space: nowrap;
	padding: 5px 4px 1px;
}

.dxpLite_Office2010Blue .dxp-sep
{
	background: #c9c9c9;
    width: 1px;
    height: 21px;
    margin-top: 1px !important;
	padding: 0;
}

.dxpLiteDisabled_Office2010Blue,
.dxpLiteDisabled_Office2010Blue a,
.dxpLiteDisabled_Office2010Blue .dxp-summary,
.dxpLiteDisabled_Office2010Blue .dxp-sep,
.dxpLiteDisabled_Office2010Blue .dxp-button,
.dxpLiteDisabled_Office2010Blue .dxp-num,
.dxpLiteDisabled_Office2010Blue .dxp-current,
.dxpLiteDisabled_Office2010Blue .dxp-ellip
{
	color: #b2b7bd;
	border-color: #b2b7bd;
	cursor: default;
}

/* -- ASPxPopupControl -- */
.dxpcControl_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background: White;
	border: 1px solid #909aa6;
}
.dxpcControl_Office2010Blue a
{
	color: #1e395b;
}

.dxpcCloseButton_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	padding: 1px 4px 0px 1px;
}
.dxpcCloseButtonHover_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxpcContent_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
	white-space: normal;
	padding: 9px 12px 10px;
	vertical-align:top;
}
.dxpcFooter_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background: #bbcee7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.pcFBack.png")%>') repeat-x left top;
	border-top: 1px solid #909aa6;
}
.dxpcFooter_Office2010Blue td.dxpc
{
	font: 8pt Verdana;
	color: #1e395b;
	white-space: nowrap;
	padding: 5px 12px 6px;
}
.dxpcHeader_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background: #bbcee6 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.pcHBack.png")%>') repeat-x left top;
	border-bottom: 1px solid #909aa6;
}
.dxpcHeader_Office2010Blue td.dxpc
{
	font: 8pt Verdana;
	color: #1e395b;
	white-space: nowrap;
	padding: 3px 0 4px 1px;
}
.dxpcModalBackground_Office2010Blue
{
	background-color: White;
	opacity: 0.7;
	filter:progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=70);
}
.dxpcLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxpcLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
	border: 1px solid #859ebf;
	background: White;
}
.dxpcLoadingDiv_Office2010Blue
{
	background: Gray;
	opacity: 0.01;
	filter:progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=1);
}
/* Disabled */
.dxpcDisabled_Office2010Blue
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxRoundPanel -- */
.dxrpControl_Office2010Blue td.dxrp,
.dxrpControlGB_Office2010Blue td.dxrp
{
	font-size: 8pt;
	font-family: Verdana;
	color: #1e395b;
}
/* Header */
.dxrpControl_Office2010Blue .dxrpHeader_Office2010Blue,
.dxrpControl_Office2010Blue .dxrpHeader_Office2010Blue td.dxrp,
.dxrpControlGB_Office2010Blue span.dxrpHeader_Office2010Blue
{
    font-size: 8pt;
	font-family: Verdana;
	color: #1e395b;
}
.dxrpControl_Office2010Blue .dxrpHeader_Office2010Blue
{
    background-color: #b2b7bd;
	border-bottom: 1px solid #bbc8d7;
}
.dxrpControl_Office2010Blue .dxrpHI,
.dxrpControl_Office2010Blue .dxrpHeader_Office2010Blue,
.dxrpControl_Office2010Blue .dxrpHeader_Office2010Blue td.dxrp
{
	vertical-align: top;
	white-space: nowrap;
}
/* Header image */
.dxrpControl_Office2010Blue .dxrpHI
{
    padding-right: 4px;
}
.dxrpControl_Office2010Blue .dxrpHIR
{
    padding-left: 4px;
}
/* Content */
.dxrpControl_Office2010Blue td.dxrpcontent,
.dxrpControlGB_Office2010Blue td.dxrpcontent
{
	vertical-align: top;
	color: Black;
}
.dxrpControl_Office2010Blue .dxrpcontent
{
    background: #eaf1fa;
}
/* Edges */
.dxrpControl_Office2010Blue .dxrpTE
{
    background-color: #dee8f5;
}
.dxrpControl_Office2010Blue .dxrpHLE,
.dxrpControl_Office2010Blue .dxrpHRE,
.dxrpControl_Office2010Blue .dxrpHeader_Office2010Blue
{
	background-color: #cdd9e8;
}
.dxrpControl_Office2010Blue .dxrpLE,
.dxrpControl_Office2010Blue .dxrpRE,
.dxrpControl_Office2010Blue .dxrpBE,
.dxrpControl_Office2010Blue .dxrpNHTE
{
	background-color: #eaf1fa;
}
.dxrpControl_Office2010Blue .dxrpTE,
.dxrpControl_Office2010Blue .dxrpNHTE,
.dxrpControlGB_Office2010Blue .dxrpNHTE
{
	border-top: 1px solid #8ba0bc;
}
.dxrpControl_Office2010Blue .dxrpHLE,
.dxrpControl_Office2010Blue .dxrpHRE
{
	border-bottom: 1px solid #bbc8d7;
}
.dxrpControl_Office2010Blue .dxrpLE,
.dxrpControl_Office2010Blue .dxrpHLE,
.dxrpControlGB_Office2010Blue .dxrpLE,
.dxrpControlGB_Office2010Blue .dxrpHLE
{
	border-left: 1px solid #8ba0bc;
}
.dxrpControl_Office2010Blue .dxrpRE,
.dxrpControl_Office2010Blue .dxrpHRE,
.dxrpControlGB_Office2010Blue .dxrpRE
{
	border-right: 1px solid #8ba0bc;
}
.dxrpControl_Office2010Blue .dxrpBE,
.dxrpControlGB_Office2010Blue .dxrpBE
{
	border-bottom: 1px solid #8ba0bc;
}
.dxrpControlGB_Office2010Blue .dxrpcontent,
.dxrpControlGB_Office2010Blue .dxrpHeader_Office2010Blue,
.dxrpControlGB_Office2010Blue .dxrpLE,
.dxrpControlGB_Office2010Blue .dxrpRE,
.dxrpControlGB_Office2010Blue .dxrpBE,
.dxrpControlGB_Office2010Blue .dxrpNHTE
{
	background-color: #eaf1fa;
}
.dxrpControl_Office2010Blue .dxrpHLE,
.dxrpControl_Office2010Blue .dxrpHRE,
.dxrpControl_Office2010Blue .dxrpHeader_Office2010Blue
{
	background: #cdd9e8 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.rpHBack.png")%>') repeat-x left top;
}

/* Disabled */
.dxrpDisabled_Office2010Blue,
.dxrpDisabled_Office2010Blue td.dxrp
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxSiteMapControl -- */
.dxsmControl_Office2010Blue a:hover
{
    text-decoration:underline !important;
}
.dxsmControl_Office2010Blue a:visited
{
    color: #8467b2 !important;
}

.dxsmControl_Office2010Blue
{
	color: #1e395b;
	background: White;
	font-family: Verdana;
	font-size: 11pt;
	border-style: none;
}
/* - Category Level - */
.dxsmCategoryLevel_Office2010Blue,
.dxsmCategoryLevel_Office2010Blue a
{
    color: #1e395b;
	background: White;
	font-family: Verdana;
	font-size: 11pt;
    text-decoration: none;
}
.dxsmCategoryLevel_Office2010Blue
{
    white-space: nowrap;
    padding: 0px 0px 5px 0px;
}
.dxsmCategoryLevel_Office2010Blue
{
    border-bottom: 1px solid #8ba0bc;
}
 /*flow layout*/
.dxsmLevelCategoryFlow_Office2010Blue,
.dxsmLevelCategoryFlow_Office2010Blue a
{
    color: #1e395b;
	font-family: Verdana;
	font-size: 11pt;
	text-decoration: underline;
}
/* - Level 0 - */
.dxsmLevel0_Office2010Blue,
.dxsmLevel0_Office2010Blue a,
.dxsmLevel0Categorized_Office2010Blue a,
.dxsmLevel0Categorized_Office2010Blue
{
    color: #1e395b;
	font-family: Verdana;
	font-size: 11pt;
    text-decoration: none;
}
.dxsmLevel0_Office2010Blue,
.dxsmLevel0Categorized_Office2010Blue
{
    white-space: nowrap;
    padding: 0px 0px 2px 0px;
}
.dxsmLevel0_Office2010Blue
{
    padding-bottom: 5px;
}
 /*flow layout*/
.dxsmLevel0Flow_Office2010Blue,
.dxsmLevel0Flow_Office2010Blue a,
.dxsmLevel0CategorizedFlow_Office2010Blue a,
.dxsmLevel0CategorizedFlow_Office2010Blue
{
    color: #1e395b;
	font-family: Verdana;
	font-size: 11pt;
    font-weight: bold;
	text-decoration: underline;
}
.dxsmLevel0Flow_Office2010Blue
{
    padding: 0;
}
.dxsmLevel0Flow_Office2010Blue
{
    text-decoration: none;
}

/* - Level 1 - */
.dxsmLevel1_Office2010Blue,
.dxsmLevel1_Office2010Blue a,
.dxsmLevel1Categorized_Office2010Blue a,
.dxsmLevel1Categorized_Office2010Blue
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
    text-decoration: none;
}
.dxsmLevel1_Office2010Blue,
.dxsmLevel1Categorized_Office2010Blue
{
    white-space: nowrap;
    padding-bottom: 3px;
}

/*flow layout*/
.dxsmLevel1Flow_Office2010Blue,
.dxsmLevel1Flow_Office2010Blue a,
.dxsmLevel1CategorizedFlow_Office2010Blue,
.dxsmLevel1CategorizedFlow_Office2010Blue a
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
	text-decoration: underline;
}
.dxsmLevel1Flow_Office2010Blue
{
    text-decoration: none;
    padding: 0;
}

/* - Level 2 - */
.dxsmLevel2_Office2010Blue,
.dxsmLevel2_Office2010Blue a,
.dxsmLevel2Categorized_Office2010Blue a,
.dxsmLevel2Categorized_Office2010Blue
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
    text-decoration: none;
}
.dxsmLevel2_Office2010Blue,
.dxsmLevel2Categorized_Office2010Blue
{
    white-space:nowrap;
    padding-bottom: 6px;
}
/*flow layout*/
.dxsmLevel2Flow_Office2010Blue,
.dxsmLevel2Flow_Office2010Blue a
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
	text-decoration:underline;
}
.dxsmLevel2Flow_Office2010Blue
{
    padding: 0;
}
/* - Level 3 - */
.dxsmLevel3_Office2010Blue,
.dxsmLevel3_Office2010Blue a
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
    text-decoration: none;
}
.dxsmLevel3_Office2010Blue
{
    white-space: nowrap;
    padding-bottom: 6px;
}
/*flow layout*/
.dxsmLevel3Flow_Office2010Blue,
.dxsmLevel3Flow_Office2010Blue a
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
	text-decoration: underline;
}
/* - Level 4 - */
.dxsmLevel4_Office2010Blue,
.dxsmLevel4_Office2010Blue a
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
    text-decoration: none;
}
.dxsmLevel4_Office2010Blue
{
    white-space: nowrap;
    padding-bottom: 6px;
}
/*flow layout*/
.dxsmLevel4Flow_Office2010Blue,
.dxsmLevel4Flow_Office2010Blue a
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
	text-decoration: underline;
}
.dxsmLevel4Flow_Office2010Blue
{
    padding: 0;
}
/* - Other Levels - */
.dxsmLevelOther_Office2010Blue,
.dxsmLevelOther_Office2010Blue a
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
    text-decoration: none;
}
.dxsmLevelOther_Office2010Blue
{
    white-space:nowrap;
    padding-bottom: 6px;
}
/*flow layout*/
.dxsmLevelOtherFlow_Office2010Blue,
.dxsmLevelOtherFlow_Office2010Blue a
{
    color: #498bc2;
    font-family: Verdana;
    font-size: 8pt;
	text-decoration: underline;
}
/* Disabled */
.dxsmDisabled_Office2010Blue
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxTabControl, ASPxPageControl -- */
.dxtcControl_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxtcLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxtcLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}

/* Tab Hyperlink*/
.dxtcTab_Office2010Blue a,
.dxtcTabWithTabPositionLeft_Office2010Blue a,
.dxtcTabWithTabPositionBottom_Office2010Blue a,
.dxtcTabWithTabPositionRight_Office2010Blue a,
.dxtcActiveTab_Office2010Blue a,
.dxtcActiveTabWithTabPositionBottom_Office2010Blue a,
.dxtcActiveTabWithTabPositionLeft_Office2010Blue a,
.dxtcActiveTabWithTabPositionRight_Office2010Blue a,
.dxtcTabHover_Office2010Blue a,
.dxtcTabHoverWithTabPositionBottom_Office2010Blue a,
.dxtcTabHoverWithTabPositionLeft_Office2010Blue a,
.dxtcTabHoverWithTabPositionRight_Office2010Blue a
{
	text-decoration: none;
	color: #1e395b;
}

.dxtcActiveTab_Office2010Blue,
.dxtcActiveTabWithTabPositionBottom_Office2010Blue,
.dxtcActiveTabWithTabPositionLeft_Office2010Blue,
.dxtcActiveTabWithTabPositionRight_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	border: solid 1px #859ebf;
	padding: 4px 12px 4px 12px;
	background-color: White;
	text-align: center;
}
.dxtcActiveTabWithTabPositionLeft_Office2010Blue,
.dxtcActiveTabWithTabPositionRight_Office2010Blue
{
	padding: 4px 13px 4px 12px;
}
/* Active Tab */
.dxtcActiveTab_Office2010Blue table.dxtc,
.dxtcActiveTabWithTabPositionBottom_Office2010Blue table.dxtc,
.dxtcActiveTabWithTabPositionLeft_Office2010Blue table.dxtc,
.dxtcActiveTabWithTabPositionRight_Office2010Blue table.dxtc
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxtcActiveTab_Office2010Blue td.dxtc,
.dxtcActiveTabWithTabPositionBottom_Office2010Blue td.dxtc,
.dxtcActiveTabWithTabPositionLeft_Office2010Blue td.dxtc,
.dxtcActiveTabWithTabPositionRight_Office2010Blue td.dxtc
{
	white-space: nowrap;
    background-color: transparent!important;
    background-image: url('')!important;
    border-width: 0px!important;
    padding: 0px!important;
}
.dxtcActiveTabHover_Office2010Blue
{
	background-color: white;
}
/* Tab */
.dxtcTab_Office2010Blue,
.dxtcTabWithTabPositionLeft_Office2010Blue,
.dxtcTabWithTabPositionBottom_Office2010Blue,
.dxtcTabWithTabPositionRight_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background-color: #b4cae3;
	border: solid 1px #859ebf;
	padding: 4px 12px 4px 12px;
	text-align: center;
}
.dxtcTab_Office2010Blue table.dxtc,
.dxtcTabWithTabPositionBottom_Office2010Blue table.dxtc,
.dxtcTabWithTabPositionLeft_Office2010Blue table.dxtc,
.dxtcTabWithTabPositionRight_Office2010Blue table.dxtc
{
	font: 8pt Verdana;
	color: #1e395b;
}
.dxtcTab_Office2010Blue td.dxtc,
.dxtcTabWithTabPositionBottom_Office2010Blue td.dxtc,
.dxtcTabWithTabPositionLeft_Office2010Blue td.dxtc,
.dxtcTabWithTabPositionRight_Office2010Blue td.dxtc
{
	white-space: nowrap;
    background-color: transparent!important;
    background-image: url('')!important;
    border-width: 0px!important;
    padding: 0px!important;
}
.dxtcTabWithTabPositionBottom_Office2010Blue
{
}
.dxtcTabWithTabPositionLeft_Office2010Blue
{
}
.dxtcTabWithTabPositionRight_Office2010Blue
{
}
/* Hover */
.dxtcTabHover_Office2010Blue,
.dxtcTabHoverWithTabPositionBottom_Office2010Blue,
.dxtcTabHoverWithTabPositionLeft_Office2010Blue,
.dxtcTabHoverWithTabPositionRight_Office2010Blue
{
	background-color: #d4e3f6;
}
.dxtcPageContent_Office2010Blue,
.dxtcPageContentWithTabPositionBottom_Office2010Blue,
.dxtcPageContentWithTabPositionLeft_Office2010Blue,
.dxtcPageContentWithTabPositionRight_Office2010Blue,
.dxtcPageContentWithoutTabs_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
	background-color: White;
	vertical-align: top;
}
.dxtcContent_Office2010Blue,
.dxtcContentWithTabPositionBottom_Office2010Blue,
.dxtcContentWithTabPositionLeft_Office2010Blue,
.dxtcContentWithTabPositionRight_Office2010Blue
{
	font: 8pt Verdana;
	color: Black;
	border: solid 1px #859ebf;
	background-color: White;
	vertical-align: top;
}
.dxtcControl_Office2010Blue td.dxtcTabsCell,
.dxtcControl_Office2010Blue td.dxtcTabsCellWithTabPositionBottom,
.dxtcControl_Office2010Blue td.dxtcTabsCellWithTabPositionLeft,
.dxtcControl_Office2010Blue td.dxtcTabsCellWithTabPositionRight
{
}
/* Scrolling */
.dxtcScrollButtonCell_Office2010Blue
{
	border: none;
	width: 1px;
}
.dxtcScrollButtonSeparator_Office2010Blue,
.dxtcScrollButtonSeparator_Office2010Blue div
{
	height: 1px;
	width: 1px;
}
.dxtcScrollButtonIndent_Office2010Blue,
.dxtcScrollButtonIndent_Office2010Blue div
{
	height: 1px;
	width: 5px;
}
.dxtcScrollButton_Office2010Blue
{
	cursor: pointer;
}
.dxtcScrollButtonDisabled_Office2010Blue
{
	cursor: default;
}
/* Multi-row */
.dxtcMultiRow .dxtcTab_Office2010Blue
{
	border-bottom-width: 0 !important;
}
.dxtcMultiRow .dxtcActiveRow_Office2010Blue  .dxtcTab_Office2010Blue
{
	border-bottom-width: 1px !important;
}
.dxtcMultiRow .dxtcTabWithTabPositionBottom_Office2010Blue
{
	border-top-width: 0 !important;
}
.dxtcMultiRow .dxtcActiveRow_Office2010Blue  .dxtcTabWithTabPositionBottom_Office2010Blue
{
	border-top-width: 1px !important;
}
/* Misc */
.dxtcLeftAlignCell_Office2010Blue,
.dxtcTabsCellWithTabPositionBottom_Office2010Blue .dxtcLeftAlignCell_Office2010Blue
{
	text-align: left;
}
.dxtcRightAlignCell_Office2010Blue,
.dxtcTabsCellWithTabPositionBottom_Office2010Blue .dxtcRightAlignCell_Office2010Blue
{
	text-align: right;
}
/* Disabled */
.dxtcDisabled_Office2010Blue,
.dxtcDisabled_Office2010Blue table.dxtc
{
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxTabControl Lite -- */
.dxtcLite_Office2010Blue
{
	overflow: hidden;
    float: left;
}
.dxtcLite_Office2010Blue .dxtc-strip,
.dxtcLite_Office2010Blue .dxtc-wrapper
{
    list-style: none outside none;
    float: left;
    padding: 0;
    margin: 0;
    _overflow: hidden;
}
.dxtcLite_Office2010Blue .dxtc-tab,
.dxtcLite_Office2010Blue .dxtc-activeTab,
.dxtcLite_Office2010Blue .dxtc-leftIndent,
.dxtcLite_Office2010Blue .dxtc-spacer,
.dxtcLite_Office2010Blue .dxtc-rightIndent,
.dxtcLite_Office2010Blue .dxtc-sbWrapper,
.dxtcLite_Office2010Blue .dxtc-sbIndent,
.dxtcLite_Office2010Blue .dxtc-sbSpacer
{
	display: block;
    height: 21px;
    margin: 0;
    float: left;
    border-top: solid 1px transparent;
    border-bottom: 1px solid #859ebf;
    overflow: hidden;

    _border-top-color: #000001;
 	_zoom: 1;
	_filter:progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite_Office2010Blue .dxtc-lineBreak
{
	float: none;
	display: block;
	clear: both;
	height: 0;
	width: 0;
	font-size: 0;
	line-height: 0;
	overflow: hidden;
	visibility: hidden;
}
.dxtcLite_Office2010Blue .dxtc-tab,
.dxtcLite_Office2010Blue .dxtc-activeTab
{
	background: #b4cae3;
	border: 1px solid #859ebf;
	border-left: none;
    float: left;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
}
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-tab,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-activeTab
{
	border: 1px solid #859ebf;
	border-right: none;
}
.dxtcLite_Office2010Blue .dxtc-activeTab,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-activeTab
{
	background: White;
    border-bottom: 1px solid White;
}
.dxtcLite_Office2010Blue .dxtc-lead,
.dxtcLite_Office2010Blue .dxtc-n
{
	border-left: 1px solid #859ebf;
}
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-lead,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-n
{
	border-right: 1px solid #859ebf;
}
.dxtcLite_Office2010Blue .dxtc-tab a
{
	text-decoration: none;
	color: #1e395b;
}
.dxtcLite_Office2010Blue .dxtc-tabHover
{
	background: #d4e3f6;
}
.dxtcLite_Office2010Blue .dxtc-spacer
{
    width: 1px;
}
.dxtcLite_Office2010Blue .dxtc-leftIndent,
.dxtcLite_Office2010Blue .dxtc-rightIndent
{
    width: 5px;
}
.dxtcLite_Office2010Blue .dxtc-link
{
	padding: 4px 12px;
	display: block;
	font-size: 0;
    text-decoration: none;
    height: 100%;
    _float: left;
}
.dxtcLite_Office2010Blue .dxtc-activeTab .dxtc-link
{
	padding: 4px 12px;
}
.dxtcLite_Office2010Blue .dxtc-text,
.dxtcLite_Office2010Blue .dxtc-leftIndent,
.dxtcLite_Office2010Blue .dxtc-rightIndent
{
    color: #1e395b;
	font: 8pt Verdana;
    font-weight: normal;
    text-decoration: none;
    white-space: nowrap;
}
.dxtcLite_Office2010Blue .dxtc-img
{
	border: none;
	margin: -2px 3px -4px 0;
	width: 16px;
	height: 16px;
}
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-img
{
	margin: -2px 0 -4px 3px;
}
.dxtcLite_Office2010Blue .dxtc-content
{
	color: #1e395b;
	background: White;
	border: 1px solid #859ebf;
	font: 8pt Verdana;
    float:left;
    clear:left;
    overflow: hidden;
    padding: 11px;
}
.dxtcLite_Office2010Blue.dxtc-top .dxtc-content
{
	border-top: none !important;
}
/* Rtl */
.dxtcLite_Office2010Blue.dxtc-rtl,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-content,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-strip,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-wrapper,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-leftIndent,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-spacer,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-rightIndent,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-sbWrapper,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-sbIndent,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-sbSpacer,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-tab,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-activeTab
{
	float: right;
}
.dxtc-top.dxtc-rtl .dxtc-content,
.dxtc-bottom.dxtc-rtl .dxtc-strip,
.dxtc-bottom.dxtc-rtl .dxtc-wrapper
{
	clear: right !important;
}
.dxtc-left.dxtc-rtl .dxtc-strip
{
	float: left;
}
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-content,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-strip,
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-wrapper
{
	*float: left;
}
.dxtcLite_Office2010Blue.dxtc-rtl .dxtc-content
{
	*clear: left !important;
}
/* Scrolling */
.dxtcLite_Office2010Blue .dxtc-sb
{
	border: none;
    cursor: pointer;
    font-size: 0;
}
.dxtcLite_Office2010Blue .dxtc-sb img
{
	border: none 0;
}
.dxtcLite_Office2010Blue .dxtc-sbIndent
{
	width: 5px;
}
.dxtcLite_Office2010Blue .dxtc-sbSpacer
{
	width: 1px;
}
/* Multi-row */
.dxtcLite_Office2010Blue .dxtc-n
{
	_display: inline;
}
.dxtcLite_Office2010Blue.dxtc-multiRow.dxtc-top .dxtc-tab
{
	border-bottom-width: 0 !important;
	height: 22px;
}
.dxtcLite_Office2010Blue.dxtc-multiRow.dxtc-top .dxtc-tab.dxtc-activeRowItem
{
	border-bottom-width: 1px !important;
	height: 21px;
}
.dxtcLite_Office2010Blue.dxtc-multiRow.dxtc-bottom .dxtc-tab
{
	border-top-width: 0 !important;
	height: 22px;
}
.dxtcLite_Office2010Blue.dxtc-multiRow.dxtc-bottom .dxtc-tab.dxtc-activeRowItem
{
	border-top-width: 1px !important;
	height: 21px;
}
.dxtcLiteDisabled_Office2010Blue,
.dxtcLiteDisabled_Office2010Blue .dxtc-text,
.dxtcLiteDisabled_Office2010Blue .dxtc-activeTab .dxtc-text,
.dxtcLiteDisabled_Office2010Blue .dxtc-content
{
	color: #bbbbbb;
	cursor: default;
}
/* bottom  */
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-strip,
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-wrapper
{
	clear: left;
	*float: none;
}
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-leftIndent,
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-spacer,
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-rightIndent,
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-sbWrapper,
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-sbIndent,
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-sbSpacer,
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-tab
{
    border-top: solid 1px #859ebf;
    border-bottom: none;

    _border-bottom-color: #000001;
	_zoom: 1;
	_filter:progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-tab,
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-activeTab
{
	border-bottom: 1px solid #859ebf;
}
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-activeTab
{
    border-top: solid 1px White;
}
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-content
{
	clear: right;
    border: solid 1px #859ebf;
    border-bottom: none !important;
}
.dxtcLite_Office2010Blue.dxtc-bottom .dxtc-sb
{
	margin: 1px 0 0;
}
/* left */
.dxtcLite_Office2010Blue.dxtc-left .dxtc-tab,
.dxtcLite_Office2010Blue.dxtc-left .dxtc-activeTab,
.dxtcLite_Office2010Blue.dxtc-left .dxtc-leftIndent,
.dxtcLite_Office2010Blue.dxtc-left .dxtc-spacer,
.dxtcLite_Office2010Blue.dxtc-left .dxtc-rightIndent
{
	float: none;
	clear: none;
	width: auto;
	height: auto;

	*float: left;
	*clear: both;
}
.dxtcLite_Office2010Blue.dxtc-left .dxtc-tab
{
	border: 1px solid #859ebf;
	border-top: none;
}
.dxtcLite_Office2010Blue.dxtc-left .dxtc-activeTab
{
    border: solid 1px #859ebf;
    border-right: solid 1px White;
    border-top: none;
}
.dxtcLite_Office2010Blue.dxtc-left .dxtc-lead
{
	border-top: 1px solid #859ebf;
}
.dxtcLite_Office2010Blue.dxtc-left .dxtc-activeTab .dxtc-link
{
	padding: 4px 12px;
}
.dxtcLite_Office2010Blue.dxtc-left .dxtc-leftIndent,
.dxtcLite_Office2010Blue.dxtc-left .dxtc-spacer,
.dxtcLite_Office2010Blue.dxtc-left .dxtc-rightIndent
{
	border: none 0;
    border-right: solid 1px #859ebf;
    border-left: solid 1px transparent;
    width: auto;

    _border-left-color: #000001;
	_zoom: 1;
	_filter:progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite_Office2010Blue.dxtc-left .dxtc-leftIndent,
.dxtcLite_Office2010Blue.dxtc-left .dxtc-rightIndent
{
	height: 3px;
}
.dxtcLite_Office2010Blue.dxtc-left .dxtc-spacer
{
	height: 1px;
}
.dxtcLite_Office2010Blue.dxtc-left .dxtc-content
{
    border-left: none !important;
    float: left;
    clear: none;
}
/* right */
.dxtcLite_Office2010Blue.dxtc-right .dxtc-tab,
.dxtcLite_Office2010Blue.dxtc-right .dxtc-activeTab,
.dxtcLite_Office2010Blue.dxtc-right .dxtc-leftIndent,
.dxtcLite_Office2010Blue.dxtc-right .dxtc-spacer,
.dxtcLite_Office2010Blue.dxtc-right .dxtc-rightIndent
{
	float: none;
	clear: none;
	width: auto;
	height: auto;

	*float: left;
	*clear: both;
}
.dxtcLite_Office2010Blue.dxtc-right .dxtc-tab
{
	border: 1px solid #859ebf;
	border-top: none;
}
.dxtcLite_Office2010Blue.dxtc-right .dxtc-activeTab
{
    border: solid 1px #859ebf;
    border-left: solid 1px White;
    border-top: none;
}
.dxtcLite_Office2010Blue.dxtc-right .dxtc-lead
{
	border-top: 1px solid #859ebf;
}
.dxtcLite_Office2010Blue.dxtc-right .dxtc-activeTab .dxtc-link
{
	padding: 4px 12px;
}
.dxtcLite_Office2010Blue.dxtc-right .dxtc-leftIndent,
.dxtcLite_Office2010Blue.dxtc-right .dxtc-spacer,
.dxtcLite_Office2010Blue.dxtc-right .dxtc-rightIndent
{
	border: none 0;
    border-left: solid 1px #859ebf;
    border-right: solid 1px transparent;

    _border-right-color: #000001;
	_zoom: 1;
	_filter:progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite_Office2010Blue.dxtc-right .dxtc-leftIndent,
.dxtcLite_Office2010Blue.dxtc-right .dxtc-rightIndent
{
	height: 3px;
}
.dxtcLite_Office2010Blue.dxtc-right .dxtc-spacer
{
	height: 1px;
}
.dxtcLite_Office2010Blue.dxtc-right .dxtc-content
{
    border: solid 1px #859ebf;
    border-right: none !important;
    float: left;
    clear: none;
}
/* Services rules */
.dxtcLite_Office2010Blue.dxtc-noTabs .dxtc-content
{
	border: solid 1px #859ebf !important;
}

/* -- ASPxTitleIndex -- */
.dxtiControl_Office2010Blue a:hover
{
    text-decoration: underline!important;
}
.dxtiControl_Office2010Blue a:visited
{
    color: #8467b2 !important;
}
.dxtiControl_Office2010Blue
{
	font: 8pt Verdana;
	color: #498bc2;
	background: White;
	border-style: none;
}
.dxtiLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxtiLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxtiItem_Office2010Blue,
.dxtiItem_Office2010Blue a
{
	font: 8pt Verdana;
	color: #498bc2;
	text-decoration: none;
}
.dxtiItem_Office2010Blue
{
	white-space: nowrap;
}
.dxtiGroupHeader_Office2010Blue,
.dxtiGroupHeaderCategorized_Office2010Blue
{
	font: 13pt Verdana;
	text-decoration: none;
}
.dxtiGroupHeader_Office2010Blue,
.dxtiGroupHeaderCategorized_Office2010Blue
{
	background: White;
	white-space:nowrap;
}
.dxtiGroupHeaderCategorized_Office2010Blue
{
}
/* - GroupHeaderText - */
.dxtiGroupHeaderText_Office2010Blue
{
    color: White;
    background: #498bc2;
    padding: 0 4px 1px ;
}
.dxtiGroupHeaderTextCategorized_Office2010Blue
{
    color: White;
    font-size: 15pt;
}
.dxtiGroupHeaderTextCategorized_Office2010Blue
{
    padding-left: 7px;
    padding-right: 7px;
    padding-top: 2px;
}
/* - FilterBox - */
.dxtiFilterBox_Office2010Blue
{
    font: 8pt Verdana;
    color: #1e395b;
    font-weight: normal;
    background: #d2e4f1 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.tiFBBack.png")%>') repeat-x left top;
    padding: 10px;
}
.dxtiFilterBoxInfoText_Office2010Blue
{
}
.dxtiFilterBoxEdit_Office2010Blue
{
    font-size: 8pt;
    width: 165px;
    border: 1px solid #8ba0bc;
    padding-left: 3px;
}
/* - IndexPanel - */
.dxtiIndexPanel_Office2010Blue
{
    background: White;
    padding: 5px 0;
    text-decoration: none;
}
.dxtiIndexPanelItem_Office2010Blue,
.dxtiIndexPanelItem_Office2010Blue a,
.dxtiCurrentIndexPanelItem_Office2010Blue
{
    color: #498bc2;
    font-family: Verdana;
    text-decoration: none;
}
.dxtiIndexPanelItem_Office2010Blue a:hover
{
    text-decoration: underline;
}

.dxtiIndexPanelItem_Office2010Blue,
.dxtiCurrentIndexPanelItem_Office2010Blue
{
    padding: 2px 4px;
}
.dxtiCurrentIndexPanelItem_Office2010Blue
{
    color: #8467b2;
}
/* - BackToTop - */
.dxtiBackToTop_Office2010Blue,
.dxtiBackToTopRtl_Office2010Blue
{
    padding: 4px 4px 0 ;
	border-top: 1px solid #c5cfdd;
}
.dxtiBackToTop_Office2010Blue,
.dxtiBackToTop_Office2010Blue a
{
    font: 8pt Verdana;
	color: #498bc2;
    text-decoration: none;
}
.dxtiBackToTop_Office2010Blue a:hover
{
    text-decoration: underline;
}
.dxtiBackToTop_Office2010Blue a:visited
{
    color: #8467b2;
	text-decoration: none;
}
/* Disabled */
.dxtiDisabled_Office2010Blue
{
	color: #b2b7bd;
	cursor: default;
}
/* -- ASPxUploadControl -- */
.dxucControl_Office2010Blue,
.dxucEditArea_Office2010Blue
{
    font-size: 8pt;
    font-family: Verdana;
}
.dxucErrorCell_Office2010Blue
{
    font-size: 8pt;
    font-family: Verdana;
    color: Red;
    text-align: left;
}
.dxucButton_Office2010Blue,
.dxucButton_Office2010Blue a
{
    font-size: 8pt;
    font-family: Verdana;
    color: #1e395b;
    cursor: pointer;
    white-space: nowrap;
}
/* ProgressBar */
.dxucProgressBar_Office2010Blue,
.dxucProgressBar_Office2010Blue td.dx
{
    font-family: Verdana;
    font-size: 8pt;
   	color: #1e395b;
}
.dxucProgressBar_Office2010Blue .dxucPBMainCell,
.dxucProgressBar_Office2010Blue td.dx
{
    padding: 0;
}
.dxucProgressBar_Office2010Blue
{
    background: #f9f9fa url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.ucProgressBack.png")%>') repeat-x left top;
    border: 1px solid #a5acb5;
}
.dxucProgressBarIndicator_Office2010Blue
{
    background: #dfe6ed url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.ucProgressIndicatorBack.png")%>') repeat-x left top;
}
/* Disabled */
.dxucDisabled_Office2010Blue,
.dxucDisabled_Office2010Blue a
{
    font-size: 8pt;
	color: #b2b7bd;
	cursor: default;
}

/* -- ASPxSplitter -- */
.dxsplControl_Office2010Blue,
.dxsplVSeparator_Office2010Blue,
.dxsplHSeparator_Office2010Blue
{
	background-color: White;
}
.dxsplControl_Office2010Blue,
.dxsplVSeparator_Office2010Blue,
.dxsplHSeparator_Office2010Blue,
.dxsplPane_Office2010Blue,
.dxsplPaneCollapsed_Office2010Blue,
.dxsplVSeparator_Office2010Blue,
.dxsplHSeparator_Office2010Blue,
.dxsplVSeparatorCollapsed_Office2010Blue,
.dxsplHSeparatorCollapsed_Office2010Blue
{
	border: solid 0px #849dbd;
}
.dxsplPane_Office2010Blue,
.dxsplPaneCollapsed_Office2010Blue
{
	border-width: 1px;
}
.dxsplPaneCollapsed_Office2010Blue
{
	border-right-width: 0px;
	border-bottom-width: 0px;
}
.dxsplVSeparator_Office2010Blue,
.dxsplHSeparator_Office2010Blue
{
    background: #cfddee;
}
.dxsplVSeparator_Office2010Blue
{
    border-top-width: 1px;
    border-bottom-width: 1px;
}
.dxsplHSeparator_Office2010Blue
{
    border-left-width: 1px;
    border-right-width: 1px;
}

.dxsplVSeparatorHover_Office2010Blue
{
	cursor: w-resize;
}
.dxsplHSeparatorHover_Office2010Blue
{
	cursor: n-resize;
}
.dxsplVSeparatorCollapsed_Office2010Blue
{
	border-top-width: 1px;
	border-bottom-width: 1px;
}
.dxsplHSeparatorCollapsed_Office2010Blue
{
	border-left-width: 1px;
	border-right-width: 1px;
}
.dxsplVSeparatorCollapsed_Office2010Blue,
.dxsplHSeparatorCollapsed_Office2010Blue
{
	cursor: default !important;
}
.dxsplVSeparatorButton_Office2010Blue
{
	cursor: pointer;
	padding: 3px 0;
}
.dxsplHSeparatorButton_Office2010Blue
{
	cursor: pointer;
	padding: 0 3px;
}
.dxsplVSeparatorHover_Office2010Blue,
.dxsplVSeparatorButtonHover_Office2010Blue
{
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.splVSepHBack.png")%>');
}
.dxsplHSeparatorHover_Office2010Blue,
.dxsplHSeparatorButtonHover_Office2010Blue
{
	background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.splHSepHBack.png")%>');
}
.dxsplVSeparatorHover_Office2010Blue,
.dxsplHSeparatorHover_Office2010Blue,
.dxsplVSeparatorButtonHover_Office2010Blue,
.dxsplHSeparatorButtonHover_Office2010Blue
{
	background-color: #f8e393;
}
.dxsplResizingPointer_Office2010Blue
{
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.splResizingPointer.gif")%>');
	background-repeat: repeat;
}
.dxsplResizingPointer_Office2010Blue,
.dxsplS
{
	font-size: 0px;
	line-height: 0px;
}
.dxsplLCC,
.dxsplCC,
.dxsplS
{
	overflow: hidden;
}
.dxsplLCC,
.dxsplCC,
.dxsplP
{
	width: 100%;
	height: 100%;
}
.dxsplLCC
{
	padding: 8px 8px 8px 8px;
}

/* -- ASPxTreeView -- */
.dxtvControl_Office2010Blue
{
	float: left;
}
.dxtvControl_Office2010Blue li
{
	font-family: Verdana;
	font-size: 8pt;
	overflow-y: hidden;
}
.dxtvControl_Office2010Blue ul
{
	list-style-type: none;
	margin: 0;
    padding: 0;
	overflow-y: hidden;
}
.dxtvControl_Office2010Blue a
{
	color: #6289b8;
	text-decoration: none;
}
.dxtvControl_Office2010Blue .dxtv-ln
{
	vertical-align: top;
}
.dxtvControl_Office2010Blue .dxtv-nd
{
	color: Black;
	float: left;
	display: block;
	text-decoration: none;
	padding: 1px;
	margin: 1px 1px 1px 0;
	cursor: pointer;
    outline: 0 none;
}
.dxtvControl_Office2010Blue .dxtv-elbNoLn,
.dxtvControl_Office2010Blue .dxtv-elb
{
	width: 26px;
	height: 21px;
	vertical-align: top;
	float: left;
}

.dxtvControl_Office2010Blue .dxtv-btn
{
	margin-left: 8px;
	margin-top: 3px;
	cursor: pointer;
}
.dxtvControl_Office2010Blue .dxtv-subnd
{
	margin-left: 22px;
}
.dxtvControl_Office2010Blue .dxtv-ndImg
{
	padding-left: 5px;
	float: left;
	vertical-align: middle;
	cursor: pointer;
}
.dxtvControl_Office2010Blue .dxtv-ndTxt
{
	padding: 3px 4px;
	float: left;
	white-space: nowrap;
	vertical-align: middle;
	cursor: pointer;
}
.dxtvControl_Office2010Blue .dxtv-ndChk
{
	padding: 0;
	float: left;
	vertical-align: middle;
    cursor: default;
    margin: 3px 3px 3px 6px;
    /*for IE6-7*/
    *margin: 0 0 0 2px;
}
.dxtvControl_Office2010Blue .dxtv-ndTmpl
{
	float: left;
	white-space: nowrap;
}

.dxtvControl_Office2010Blue .dxtv-ndSel
{
	background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemSBack.png")%>') repeat-x left top;
	border: 1px solid #c2762b;
	padding: 0;
	cursor: default;
}

.dxtv-ndSel .dxtv-ndTxt,
.dxtv-ndSel .dxtv-ndImg
{
	cursor: default;
}

.dxtvControl_Office2010Blue .dxtv-ndHov
{
    background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mSubItemHBack.png")%>') repeat-x left top;
	border: 1px solid #f2ca58;
	padding: 0;
    cursor: pointer;
}

.dxtv-ndHov .dxtv-ndTxt,
.dxtv-ndHov .dxtv-ndImg
{
	cursor: pointer;
}

.dxtvControl_Office2010Blue .dxtv-clr,
.dxtvControl_Office2010Blue .dxtv-clrIE7
{
	clear:both;
	font-size:0;
	height:0;
	visibility:hidden;
	width:0;
    display:block;
}

.dxtvControl_Office2010Blue .dxtv-clr
{
	line-height:0;
}

.dxtvControl_Office2010Blue.dxtvRtl,
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-nd,
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-elbNoLn,
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-elb,
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-ndTxt,
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-ndImg,
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-ndChk,
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-ndTmpl
{
    float: right;
}
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-nd
{
    margin: 1px 0 1px 1px;
}
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-elb,
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-ln
{
    background-position: right top;
}
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-btn
{
    margin: 3px 8px 0 0;
}
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-subnd
{
    margin: 0 22px 0 0;
}
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-ndImg
{
    padding: 0 5px 0 0;
}
.dxtvControl_Office2010Blue.dxtvRtl.OperaRtlFix .dxtv-btn
{
    margin: 3px 0 0 8px;
}
.dxtvControl_Office2010Blue.dxtvRtl .dxtv-ndChk
{
    margin: 4px 6px 3px 3px;
    /*for IE6-7*/
    *margin: 0 2px 0 0;
}
.dxtvControl_Office2010Blue.dxtvRtl.OperaRtlFix .dxtv-subnd
{
    overflow-x: hidden;
}

.dxtvDisabled_Office2010Blue,
.dxtvControl_Office2010Blue .dxtvDisabled,
.dxtvDisabled_Office2010Blue a,
.dxtvDisabled_Office2010Blue .dxtv-ndTxt,
.dxtvDisabled_Office2010Blue .dxtv-ndImg,
.dxtvDisabled_Office2010Blue .dxtv-btn,
.dxtvDisabled_Office2010Blue .dxtv-nd
{
	color: #c7cacf;
	cursor: default;
}

.dxtvLoadingPanelWithContent_Office2010Blue
{
	font: 8pt Verdana;
	color: #3c3c3c;
    background: White;
    border: solid 1px #757575;
}
.dxtvLoadingPanelWithContent_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}

.dx-clear
{
	display: block;
	clear: both;
	height: 0;
	width: 0;
	font-size: 0;
	line-height: 0;
	overflow: hidden;
	visibility: hidden;
}
/* menuButtons_Aqua and menuLinks_Aqua uses in XAF */
.menuButtons_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background:none repeat scroll 0 0 transparent !important;
}

.menuButtons_Office2010Blue .dxmMenuSeparator_Office2010Blue .dx,
.menuButtons_Office2010Blue .dxmMenuFullHeightSeparator_Office2010Blue .dx
{
	font-size: 0;
	line-height: 0;
	overflow: hidden;
	width: 1px;
    height: 1px;
}
.menuButtons_Office2010Blue .dxmMenuFullHeightSeparator_Office2010Blue
{
    display: inline;
}

.menuButtons_Office2010Blue .dxmMenuSeparator_Office2010Blue .dx,
.menuButtons_Office2010Blue .dxmMenuFullHeightSeparator_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuVerticalSeparator_Office2010Blue
{
	background: none;
	width: 5px;
}
.menuButtons_Office2010Blue .dxmMenuSeparator_Office2010Blue
{
	display: none;
}
.menuButtons_Office2010Blue .dxmMenuVerticalSeparator_Office2010Blue
{
	width: 100%;
	height: 1px;
}

.menuButtons_Office2010Blue .dxmMenuItem_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuItemWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuItemWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuItemWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItem_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItem_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItem_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemWithImageWithPopOutImage_Office2010Blue
{
	font-size: 8pt;
	font-family: Verdana;
	font-weight:normal;
	vertical-align: middle;
	background: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemBack.png")%>') repeat-x center bottom #d1dfef;
	background-repeat:repeat-x;
	padding-top: 2px;
	padding-right: 10px;
	padding-bottom: 3px;
	padding-left: 11px;
	cursor: pointer;
	color: #1e395b;
	border: solid 1px #abbad0;
}
.menuButtons_Office2010Blue .dxmMenuItemHover_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuItemHoverWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuItemHoverWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuItemHoverWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemHover_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemHoverWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemHoverWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemHoverWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemHover_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemHoverWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemHoverWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemHover_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemHoverWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemHoverWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue
{
	color: #1e395b;
	background: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemHBack.png")%>') repeat-x center bottom #fcf8e5;
	padding-top: 2px;
	padding-right: 10px;
	padding-bottom: 3px;
	padding-left: 11px;
	font-size: 8pt;
	font-family: Verdana;
	font-weight:normal;
	vertical-align: middle;
	border: solid 1px #eecc53;
	cursor: pointer;
}
.menuButtons_Office2010Blue .dxmMenuItemWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuItemWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuItemHoverWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuItemHoverWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemHoverWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuItemHoverWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemHoverWithImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemHover_Office2010Blue,
.menuButtons_Office2010Blue .dxmVerticalMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue
{
    padding-top: 2px !important;
	padding-bottom: 1px !important;
}

.menuLinks_Office2010Blue
{
	font: 8pt Verdana;
	background:none repeat scroll 0 0 transparent !important;
	border: 0px !important;
}

.menuLinks_Office2010Blue .dxmMenuItemHover_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuItemHoverWithImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuItemHoverWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuItemHoverWithImageWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuItemHover_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuItemHoverWithImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuItemHoverWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuItemHoverWithImageWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuLargeItemHover_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuLargeItemHoverWithImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuLargeItemHoverWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemHover_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemHoverWithImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemHoverWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue a
{
	text-decoration: underline;
}

.menuLinks_Office2010Blue .dxmMenuItemHover_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuItemHoverWithImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuItemHoverWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuItemHoverWithImageWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuItemHover_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuItemHoverWithImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuItemHoverWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuItemHoverWithImageWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuLargeItemHover_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuLargeItemHoverWithImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuLargeItemHoverWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemHover_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemHoverWithImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemHoverWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemHoverWithImageWithPopOutImage_Office2010Blue
{
	background: none repeat scroll 0 0 transparent;
	padding-right: 5px;
	padding-left: 11px;
	font-size: 8pt;
	font-family: Verdana;
	font-weight:normal;
	vertical-align: middle;
	cursor: pointer;
    border: 0px;
}

.menuLinks_Office2010Blue .dxmMenuItem_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuItem_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmMenuItemWithImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuItemWithImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmMenuItemWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuItemWithPopOutImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmMenuItemWithImageWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuItemWithImageWithPopOutImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmVerticalMenuItem_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuItem_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmVerticalMenuItemWithImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuItemWithImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmVerticalMenuItemWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuItemWithPopOutImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmVerticalMenuItemWithImageWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuItemWithImageWithPopOutImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmMenuLargeItem_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuLargeItem_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmMenuLargeItemWithImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuLargeItemWithImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmMenuLargeItemWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuLargeItemWithPopOutImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmMenuLargeItemWithImageWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmMenuLargeItemWithImageWithPopOutImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItem_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItem_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemWithImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemWithImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemWithPopOutImage_Office2010Blue a:visited,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemWithImageWithPopOutImage_Office2010Blue a,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemWithImageWithPopOutImage_Office2010Blue a:visited
{
	color: #1e395b;
	text-decoration: underline;
}

.menuLinks_Office2010Blue .dxmMenuItem_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuItemWithImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuItemWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuItemWithImageWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuItem_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuItemWithImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuItemWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuItemWithImageWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuLargeItem_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuLargeItemWithImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuLargeItemWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmMenuLargeItemWithImageWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItem_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemWithImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemWithPopOutImage_Office2010Blue,
.menuLinks_Office2010Blue .dxmVerticalMenuLargeItemWithImageWithPopOutImage_Office2010Blue
{
	font-size: 8pt;
	font-family: Verdana;
	font-weight:normal;
	vertical-align: middle;
	background: none repeat scroll 0 0 transparent;
	padding-right: 5px;
	padding-left: 11px;
	padding-top: 0px;
	padding-bottom: 0px;
	cursor: pointer;
}
/* --- ASPxMenu Lite skins for XAF --- */
.menuLinks_Office2010Blue .dxm-item,
.menuLinks_Office2010Blue .dxm-hovered,
.menuLinks_Office2010Blue .dxm-disabled
{
	border: none;
	background: none !important;
}
.menuLinks_Office2010Blue .dxm-content,
.menuLinks_Office2010Blue .dxm-hovered .dxm-content,
.menuLinks_Office2010Blue .dxm-disabled .dxm-content
{
	padding-top: 3px !important;
	padding-bottom: 0px !important;
}
.menuLinks_Office2010Blue .dxm-popOut,
.menuLinks_Office2010Blue .dxm-hovered .dxm-popOut,
.menuLinks_Office2010Blue .dxm-disabled .dxm-popOut
{
	padding-top: 9px !important;
	padding-bottom: 0px !important;
}
.menuLinks_Office2010Blue .dxm-separator
{
	padding-top: 3px !important;
}
.menuLinks_Office2010Blue .dxm-image,
.menuLinks_Office2010Blue .dxm-hovered .dxm-image,
.menuLinks_Office2010Blue .dxm-disabled .dxm-image
{
	vertical-align: text-top;
	display:block;
	border: none;
	float: left;
}
.menuLinks_Office2010Blue a.dx
{
	text-decoration: underline !important;
	color: #1e395b !important;
}
.menuLinks_Office2010Blue .dxm-hovered a.dx
{
	text-decoration: underline !important;
	color: #1e395b !important;
}
.menuLinks_Office2010Blue .dxm-disabled
{
	text-decoration: underline !important;
}
.menuLinks_Office2010Blue .dxm-popOut,
.menuLinks_Office2010Blue .dxm-hovered .dxm-popOut,
.menuLinks_Office2010Blue .dxm-disabled .dxm-popOut
{
	border-left: none !important;
}
.menuLinks_Office2010Blue .dxm-dropDownMode .dxm-content
{
	padding-right: 3px !important;
}

.menuButtons_Office2010Blue .dxm-item,
.menuButtons_Office2010Blue .dxm-hovered,
.menuButtons_Office2010Blue .dxm-disabled
{
	border: none;
	background: none !important;
}
.menuButtons_Office2010Blue .dxm-content
{
	border-width: 1px !important;
}
.menuButtons_Office2010Blue .dxm-content,
.menuButtons_Office2010Blue .dxm-hovered .dxm-content,
.menuButtons_Office2010Blue .dxm-disabled .dxm-content
{
	padding-top: 2px !important;
	padding-bottom: 1px !important;
}
.menuButtons_Office2010Blue .dxm-noImages .dxm-content,
.menuButtons_Office2010Blue .dxm-noImage .dxm-content,
.menuButtons_Office2010Blue .dxm-noImage .dxm-hovered .dxm-content,
.menuButtons_Office2010Blue .dxm-noImage .dxm-disabled .dxm-content
{
	padding-top: 2px !important;
	padding-bottom: 3px !important;
}
.menuButtons_Office2010Blue .dxm-popOut,
.menuButtons_Office2010Blue .dxm-hovered .dxm-popOut,
.menuButtons_Office2010Blue .dxm-disabled .dxm-popOut
{
	padding: 6px 11px 6px 10px !important;
	border-width: 1px 1px 1px 0px !important;
}
.menuButtons_Office2010Blue .dxm-item .dxm-content,
.menuButtons_Office2010Blue .dxm-item .dxm-popOut
{
	color: #1e395b;
	border: solid #abbad0;
	background: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemBack.png")%>') repeat-x center bottom #d1dfef;
}
.menuButtons_Office2010Blue .dxm-hovered .dxm-content,
.menuButtons_Office2010Blue .dxm-hovered .dxm-popOut
{
	color: #1e395b;
	border: solid #eecc53;
	background: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemHBack.png")%>') repeat-x center bottom #fcf8e5;
}

/* ASPxFileManager */
.dxfmControl_Office2010Blue
{
	font: 8pt Verdana;
	outline: 0px;
}
.dxfmDisabled_Office2010Blue
{
	color:#ACACAC;
}
/* FileManager - Splitter */
.dxfmControl_Office2010Blue .dxsplControl_Office2010Blue
{
	border-width: 1px;
	border-color: #909AA6;
}
.dxfmControl_Office2010Blue .dxsplPane_Office2010Blue
{
	background-color: White;
	border-width: 0px;
}
.dxfmControl_Office2010Blue .dxsplLCC {
	outline-width: 0px;
	padding: 4px;
}
.dxfmControl_Office2010Blue .dxsplVSeparator_Office2010Blue
{
	width:3px;
	background: White url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.fmSplitterSeparator.gif")%>') right;
	background-repeat:repeat-y;
	border: 0px;
}
.dxfmControl_Office2010Blue .dxsplHSeparator_Office2010Blue
{
    border: 0px;
	background-image: none;
	background-color: #909AA6;
}

/* FileManager - TreeView */
.dxfmControl_Office2010Blue .dxtvControl_Office2010Blue
{
	margin-left: -5px;
}
.dxfmControl_Office2010Blue .dxtvControl_Office2010Blue .dxtv-nd .dxtv-ndTxt
{
	padding-left: 1px;
}
.dxfmControl_Office2010Blue .dxtvControl_Office2010Blue .dxtv-nd
{
	padding-left: 3px;
	margin-bottom: 0px;
}
.dxfmControl_Office2010Blue .dxtvControl_Office2010Blue .dxfm-folderSI
{
	border: dotted 1px #888888;
	padding: 0px 0px 0px 2px;
}
.dxfmControl_Office2010Blue .dxtvControl_Office2010Blue .dxtv-ndHov
{
	border: solid 1px #888888;
	padding-left: 2px;
}
.dxfmControl_Office2010Blue .dxtvControl_Office2010Blue .dxtv-ndSel
{
	padding-left: 2px;
}
.dxfmControl_Office2010Blue .dxtvControl_Office2010Blue .dxtv-ndImg
{
	padding: 0px;
	margin-right: 3px;
	margin-top: 2px;
}

/* FileManager - File */
.dxfmControl_Office2010Blue .dxfm-file
{
	float: left;
	text-align: center;
	cursor: pointer;
	white-space: nowrap;

	padding: 5px;
	margin: 5px;
}
.dxfmDisabled_Office2010Blue .dxfm-file
{
	cursor: default;
}
.dxfmControl_Office2010Blue .dxfm-fileSI
{
	border: dotted 1px #888888;
}
.dxfmControl_Office2010Blue .dxfm-fileSA
{
	background: #fddc7f url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemSBack.png")%>') repeat-x left top;
	border: solid 1px #888888;
}
.dxfmControl_Office2010Blue .dxfm-fileH
{
	background: #FCF9DF url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemHBack.png")%>') repeat-x left top;
	border: 1px solid #F2CA58;
}
.dxfmControl_Office2010Blue .dxfm-content
{
	overflow: hidden;
}
.dxfmControl_Office2010Blue .dxfm-content div
{
	overflow: hidden;
	width: 100%;
	white-space: nowrap;
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
}
.dxfmControl_Office2010Blue .dxfm-content div
{
	height: 18px;
}
.dxfmControl_Office2010Blue .dxfm-content .dxfm-highlight
{
	background: none repeat scroll 0 0 #d5e8ff;
    color: Black;
    font-weight:bold;
}

/* FileManager - Toolbar */
.dxfmControl_Office2010Blue .dxfm-toolbar
{
    background-color: #F2F2F7;
	background: #DAE5F2 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemBack.png")%>') repeat-x left top;
	background-repeat: repeat-x;
	background-position: top left;
}
.dxfmControl_Office2010Blue .dxfm-toolbar table.dxfm
{
	width: 100%;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxfm-filter
{
	text-align: right;
	vertical-align: top;
	white-space: nowrap;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxfm-filter input
{
    border: 1px solid #8BA0BC;
	margin: 4px 4px 0px 3px;
	width: 150px;
	height: 16px;
	font: 8pt Verdana;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxfm-path input
{
    border: 1px solid #8BA0BC;
	width: 250px;
	height: 16px;
	font: 8pt Verdana;
}

/* FileManager - Toolbar - Light */
.dxfmControl_Office2010Blue .dxfm-toolbar .dxsplLCC
{
	padding: 5px;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmLite_Office2010Blue .dxm-main
{
	margin-top: 1px;
    border-width: 0px;
    background-color: transparent;
    background-image: none;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmLite_Office2010Blue .dxm-horizontal.dxmtb .dxm-separator
{
	margin: 0px 11px;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmLite_Office2010Blue .dxfm-path
{
	padding-left: 2px;
	padding-top: 1px;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmLite_Office2010Blue .dxfm-path input
{
	margin: 1px 8px 0px 4px;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmLite_Office2010Blue .dxm-item .dxm-content
{
	padding-top: 4px;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmLite_Office2010Blue .dxm-item .dxm-content .dxm-image {
	margin: 0px;
}

/* FileManager - Toolbar - Classic */
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmMenu_Office2010Blue
{
	border-width: 0px;
	background-color: transparent;
	background-image: none;
	padding-top: 3px;
	padding-left: 3px;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmMenu_Office2010Blue .dxmMenuSeparator_Office2010Blue
{
	padding: 0px 11px;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmMenu_Office2010Blue .dxmMenuItemSpacing_Office2010Blue
{
	width: 4px;
	display: block;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmMenu_Office2010Blue .dxmMenuItem_Office2010Blue.dxfm-path
{
	padding-right: 0px;
	padding-left: 0px;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmMenu_Office2010Blue .dxmMenuItem_Office2010Blue
{
	padding-top: 1px;
	padding-left: 3px;
	background-image: none;
	background-color: transparent;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmMenu_Office2010Blue .dxmMenuItemWithImage_Office2010Blue
{
	background-image: none;
	background-color: transparent;
}
.dxfmControl_Office2010Blue .dxfm-toolbar .dxmMenu_Office2010Blue .dxmMenuItemHoverWithImage_Office2010Blue
{
	background: #fcf9df url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.mItemHBack.png")%>') repeat-x left top;
	border: 1px solid #8ba0bc;
	padding: 2px 4px;
}

.dxfmControl_Office2010Blue .dxfm-toolbar .dxmMenu_Office2010Blue .dxfm-path input
{
	margin: 0px 8px 0px 4px;
}

/* FileManager - UploadPanel */
.dxfmControl_Office2010Blue .dxfm-uploadPanel
{
	background: #BBCEE7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.pcFBack.png")%>') repeat-x left top;
	text-align: right;
}
.dxfmControl_Office2010Blue .dxfm-uploadPanel table.dxfm-uploadPanelTable
{
	display: inline-block;
	margin-right: 4px;
	margin-top: 3px;
}
.dxfmControl_Office2010Blue .dxfm-uploadPanel table.dxfm-uploadPanelTable .dxucControl_Office2010Blue
{
	margin-right: 10px;
}
.dxfmControl_Office2010Blue .dxfm-uploadPanel table.dxfm-uploadPanelTable a
{
	color: #1B3F91;
}
.dxfmControl_Office2010Blue .dxfm-uploadPanel table.dxfm-uploadPanelTable a.dxfm-uploadDisable
{
	color: #777777;
	cursor: default;
}

/* FileManager - Create, Rename input */
.dxfmControl_Office2010Blue .dxfm-cInput,
.dxfmControl_Office2010Blue .dxfm-rInput
{
    border: 1px solid #8BA0BC;
	padding: 1px;
	font: 8pt Verdana;
	outline-width: 0px;
	margin:0px;
}

/* FileManager - LoadingPanel */
.dxfmControl_Office2010Blue .dxfmLoadingPanel_Office2010Blue
{
	background-color:white;
	border:1px solid #9F9F9F;
	color:#303030;
	font:9pt Tahoma;
}
.dxfmControl_Office2010Blue .dxfmLoadingPanel_Office2010Blue td.dx {
	padding:12px;
	text-align:center;
	white-space:nowrap;
}

/* FileManager - Move PopupControl */
.dxfmControl_Office2010Blue .dxpcContent_Office2010Blue
{
	padding: 5px 0px 0px 0px;
}
.dxfmControl_Office2010Blue .dxpcContent_Office2010Blue .dxfm-mpFoldersC
{
	overflow:auto;
	padding: 0px 0px 20px 5px;
}
.dxfmControl_Office2010Blue .dxpcContent_Office2010Blue .dxfm-mpButtonC
{
	margin-top: 20px;
	border-top: 1px solid #909AA6;
	padding: 10px;
	text-align: right;
	background: #BBCEE7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.Web.pcFBack.png")%>') repeat-x left top;
	padding: 10px 15px 10px 10px;
}
.dxfmControl_Office2010Blue .dxpcContent_Office2010Blue .dxfm-mpButtonC a
{
	margin-left: 12px;
	color: #1B3F91;
}