import * as React from "react"
import { render } from "react-dom"
import { App<PERSON>ontaine<PERSON> } from "react-hot-loader"
import * as ES6Promise from "es6-promise"
import { WApp } from '@wface/container'
import config from './configs/wface/wface.config'
import LangState, {LangContext} from "./src/internationalization/context/lang"
import i18next from "i18next"
import LanguageDetector from 'i18next-browser-languagedetector'
import HttpApi from 'i18next-http-backend'
import { initReactI18next } from "react-i18next"
import 'bootstrap/dist/css/bootstrap-grid.css'
import 'bootstrap/dist/js/bootstrap'
import 'flag-icon-css/css/flag-icons.min.css'

ES6Promise.polyfill();

i18next
    .use(initReactI18next)
    .use(LanguageDetector)
    .use(HttpApi)
    .init({
        supportedLngs: ['en', 'tr'],
        // lng: document.querySelector('html').lang,
        fallbackLng: "en",
        detection: {
            order: ['path', 'cookie', 'htmlTag', 'localStorage', 'subdomain'],
            caches: ['cookie'],
        },
        backend: {
            loadPath: '/src/internationalization/locales/{{lng}}/translation.json'
        },
        react: {
            useSuspense: false
        },
    }).then()

const rootEl = document.getElementById("root");

render(
  <AppContainer>
      <LangState>
          <WApp configuration={config}/>
      </LangState>
  </AppContainer>,
  rootEl
);

// Hot Module Replacement API
declare let module: { hot: any };

if (module.hot) {
  module.hot.accept();
}
