.dxTreeList_CollapsedButton_Office2010Blue,
.dxTreeList_CollapsedButtonRtl_Office2010Blue,
.dxTreeList_ExpandedButton_Office2010Blue,
.dxTreeList_ExpandedButtonRtl_Office2010Blue,
.dxTreeList_SortAsc_Office2010Blue,
.dxTreeList_SortDesc_Office2010Blue,
.dxTreeList_DragAndDropArrowDown_Office2010Blue,
.dxTreeList_DragAndDropArrowUp_Office2010Blue,
.dxTreeList_DragAndDropHide_Office2010Blue,
.dxTreeList_DragAndDropNode_Office2010Blue {
    background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.TreeList.sprite.png")%>');
    -background-image: url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.TreeList.sprite.gif")%>'); /* for IE6 */
    background-repeat: no-repeat;
    background-color: transparent;
}

.dxTreeList_CollapsedButton_Office2010Blue {
    background-position: -64px 0px;
    width: 13px;
    height: 13px;
}

.dxTreeList_CollapsedButtonRtl_Office2010Blue {
    background-position: -83px 0px;
    width: 13px;
    height: 13px;
}

.dxTreeList_ExpandedButton_Office2010Blue {
    background-position: -64px -17px;
    width: 13px;
    height: 13px;
}

.dxTreeList_ExpandedButtonRtl_Office2010Blue {
    background-position: -83px -17px;
    width: 13px;
    height: 13px;
}

.dxTreeList_SortAsc_Office2010Blue {
    background-position: -49px -17px;
    width: 7px;
    height: 5px;
}

.dxTreeList_SortDesc_Office2010Blue {
    background-position: -49px -0px;
    width: 7px;
    height: 5px;
}

.dxTreeList_DragAndDropArrowDown_Office2010Blue {
    background-position: -30px 0px;
    width: 11px;
    height: 9px;
}

.dxTreeList_DragAndDropArrowUp_Office2010Blue {
    background-position: -30px -17px;
    width: 11px;
    height: 9px;
}

.dxTreeList_DragAndDropHide_Office2010Blue {
    background-position: 0px 0px;
    width: 22px;
    height: 22px;
}

.dxTreeList_DragAndDropNode_Office2010Blue {
    background-position: -100px 0px;
    width: 13px;
    height: 13px;
}