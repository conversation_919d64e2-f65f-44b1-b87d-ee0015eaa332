﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DigiFlowEmailActions.Helpers.WorkflowHelpers
{
    internal class IsMaasAvansHelper
    {
        internal static void SaveForAdvanceFinalRequest(Digiturk.Workflow.Common.WFContext CurrentWFContext, Digiturk.Workflow.Entities.FWfWorkflowInstance CurrentWfIns, string pageName, FLogin assignedUser, FLogin LoginObject)
        {
            if (CurrentWfIns.WfCurrentState != null)
            {
                Digiturk.Workflow.Entities.FWfStateInstance CurrentStateIns = WFRepository<FWfStateInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfStateInstanceId);
                if (Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetIsLastState(pageName, CurrentStateIns.WfStateDef.WfStateDefId))
                {
                    #region Son Aşamada Farklı Kaydetme İşlemini Sağlar
                    AdvanceRequest CurrentAdvanceRequestObject = WFRepository<Digiturk.Workflow.Digiflow.Entities.AdvanceRequest>.GetEntity(CurrentWfIns.EntityRefId);
                    Digiturk.Workflow.Digiflow.Entities.AdvanceFinalRequest AdvanceRequestObject = new Digiturk.Workflow.Digiflow.Entities.AdvanceFinalRequest();
                    AdvanceRequestObject.RelatedRequestID = CurrentWfIns.EntityRefId.Value;
                    AdvanceRequestObject.WfInstanceId = CurrentWfIns.WfWorkflowInstanceId;
                    AdvanceRequestObject.PaymentPrice = CurrentAdvanceRequestObject.AdvancePrice;
                    AdvanceRequestObject.AdvancePriceCurrently = CurrentAdvanceRequestObject.AdvancePriceCurrently;
                    AdvanceRequestObject.PaymentType = CurrentAdvanceRequestObject.PaymentType;
                    AdvanceRequestObject.PaymentDate = CurrentAdvanceRequestObject.PaymentDate;
                    AdvanceRequestObject.Created = DateTime.Now;
                    AdvanceRequestObject.CreatedBy = LoginObject.LoginId;
                    AdvanceRequestObject.ApproveDate = DateTime.Today;
                    Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.EntitySave(AdvanceRequestObject);
                    CurrentWFContext.Parameters.AddOrChangeItem("AdvancePaymentTypeTr", (GetAdvancePaymentTypeTr(AdvanceRequestObject.PaymentType)));
                    CurrentWFContext.Parameters.AddOrChangeItem("PriceDate", AdvanceRequestObject.PaymentDate.ToShortDateString());
                    CurrentWFContext.Parameters.AddOrChangeItem("AdvancePrice", AdvanceRequestObject.PaymentPrice.ToString());
                    CurrentWFContext.Parameters.AddOrChangeItem("AdvancePriceCurrently", AdvanceRequestObject.AdvancePriceCurrently);
                    CurrentWFContext.Save();
                    #endregion Son Aşamada Farklı Kaydetme İşlemini Sağlar
                }
            }
        }

        private static string GetAdvancePaymentTypeTr(long paymentType)
        {
            switch (paymentType)
            {
                case 1:
                    return "Nakit";
                case 2:
                    return "Havale";
                case 3:
                    return "Çek";
                default:
                    return "";
            }
        }
    }
}
