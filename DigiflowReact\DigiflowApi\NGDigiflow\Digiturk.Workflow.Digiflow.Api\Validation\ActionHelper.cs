﻿using Digiturk.Workflow.Digiflow.Validation;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Web;

namespace Digiturk.Workflow.Digiflow.Api
{
    public class ActionHelper
    {

        public static void CreateWorkFlow(object Entity, long WorkFlowDefinitionId, long LoginId)
        {
            string assemblyName = Assembly.GetExecutingAssembly().FullName; // Assmblyname de Configden okunacak.            
            Type type = Type.GetType("Digiturk.Workflow.Digiflow.Validation.DelegationValidation");
            IValidation validation = Activator.CreateInstance(type) as IValidation; // Burda Activator Kulllanıcaz.
            if (validation.CreateValidate((EntityBase)Entity))
            {
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.CreateWorkFlow(Entity, WorkFlowDefinitionId, LoginId);
            }
            else
            {
                throw new ValidationException(((ValidationBase)validation).CreateValidationError);
                // Exception Fırlatıcaz.
            }
        }
    }
}