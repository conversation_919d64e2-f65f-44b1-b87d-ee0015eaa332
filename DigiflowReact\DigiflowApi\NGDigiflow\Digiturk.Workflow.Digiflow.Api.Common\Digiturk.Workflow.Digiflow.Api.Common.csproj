﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2D7F1806-4791-4C74-B0AE-5001DF76052A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Digiturk.Workflow.Digiflow.Api.Common</RootNamespace>
    <AssemblyName>Digiturk.Workflow.Digiflow.Api.Common</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Digiturk.Framework.DAL">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Framework.DAL.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Entities">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Digiflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>..\..\..\..\..\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DataTransferObjects\Contract\DTOContract.cs" />
    <Compile Include="DataTransferObjects\Delegation\DTODelegation.cs" />
    <Compile Include="DataTransferObjects\Inbox\DTOCommendedInbox.cs" />
    <Compile Include="DataTransferObjects\Inbox\DTODataTableType.cs" />
    <Compile Include="DataTransferObjects\Inbox\DTODelegation.cs" />
    <Compile Include="DataTransferObjects\Inbox\DTOHistory.cs" />
    <Compile Include="DataTransferObjects\Inbox\DTOInbox.cs" />
    <Compile Include="DataTransferObjects\Inbox\DTOUserItem.cs" />
    <Compile Include="DataTransferObjects\Workflow\DTOWorkflowDefinition.cs" />
    <Compile Include="DataTransferObjects\Workflow\DTOWorkflowInstance.cs" />
    <Compile Include="DataTransferObjects\Workflow\DTOWorkflowProcesses.cs" />
    <Compile Include="DataTransferObjects\Workflow\DTOWorkflowStateDefinition.cs" />
    <Compile Include="DataTransferObjects\Workflow\DTOWorkflowStateInstance.cs" />
    <Compile Include="Entites\InstaedOf.cs" />
    <Compile Include="Exceptions\BaseException.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Results\WorkflowResult.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Constant\" />
    <Folder Include="Enums\" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Entites\InstaedOf.hbm.xml" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>