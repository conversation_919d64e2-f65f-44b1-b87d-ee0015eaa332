﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Security.AccessControl;
using System.Web;
using System.Web.Services;

namespace DigiflowQlikViewYetki
{
    /// <summary>
    /// Summary description for YetkiVerTest
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [System.ComponentModel.ToolboxItem(false)]

    public class YetkiVerTest : System.Web.Services.WebService
    {

        [WebMethod]
        public void Test()
        {
            //DirectoryInfo directoryInfo = new DirectoryInfo(@"D:\DIGITURK\QV\Documents\FollowMe\");
            //DirectorySecurity dirSecurity = directoryInfo.GetAccessControl();
            //dirSecurity.AddAccessRule(new FileSystemAccessRule(@"DIGITURK\DTCSBCELIKKAYA", FileSystemRights.ReadAndExecute, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
            //dirSecurity.AddAccessRule(new FileSystemAccessRule(@"DIGITURK\DTCSBCELIKKAYA", FileSystemRights.ReadData, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
            //dirSecurity.AddAccessRule(new FileSystemAccessRule(@"DIGITURK\DTCSBCELIKKAYA", FileSystemRights.ListDirectory, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));
            //dirSecurity.AddAccessRule(new FileSystemAccessRule(@"DIGITURK\DTCSBCELIKKAYA", FileSystemRights.Read, InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit, PropagationFlags.InheritOnly, AccessControlType.Allow));



            //directoryInfo.SetAccessControl(dirSecurity);
        }
    }
}
