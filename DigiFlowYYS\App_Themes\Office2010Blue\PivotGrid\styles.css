.dxpgControl_Office2010Blue
{
	color: #1e395b;
	font: 8pt Verdana;
	border: 1px solid #8ba0bc;
	background-color: white;
}
.dxpgContainerCell_Office2010Blue
{
	vertical-align: top;
}
.dxpgMainTable_Office2010Blue
{
	color: #0a0a0a;
	font: 8pt Verdana;
	border-width: 0;
	border-collapse: separate;
	width: 100%;
}
.dxpgMainTable_Office2010Blue caption
{
	font-size: 8pt;
	font-weight: normal;
	padding: 3px 3px 5px;
	text-align: center;
	background: #bdd0e7 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.PivotGrid.TitleBack.png")%>') repeat-x left top;
	color: #1e395b;
	border-bottom: 1px solid #8ba0bc;
}
.dxpgHeader_Office2010Blue
{
	border-width: 0;
	color: #1e395b;
	background: #e4eefa url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.PivotGrid.HeaderBack.png")%>') repeat-x left top;
	cursor: pointer;
	white-space: nowrap;
}
.dxpgHeaderTable_Office2010Blue
{
	border-width: 1px;
	width: 100%;
}
.dxpgHeaderGroupButton_Office2010Blue
{
	padding-left: 4px;
	vertical-align: middle;
}
.dxpgHeaderText_Office2010Blue
{
	padding: 3px 6px;
}
.dxpgHeaderSort_Office2010Blue
{
	padding-left: 0px;
	padding-right: 4px;
	padding-top: 0px;
	padding-bottom: 0px;
	padding: 0 4px 0 0;
	vertical-align: middle;
}
.dxpgHeaderFilter_Office2010Blue
{
	padding: 2px 2px 2px 0;
	vertical-align: middle;
}
.dxpgHeaderHover_Office2010Blue
{
	background: #eef5fc url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.PivotGrid.HeaderHoverBack.png")%>') repeat-x left top;
}
.dxpgArea_Office2010Blue, .dxpgArea_Office2010Blue table
{
	color: #1e395b;
	font: 8pt Verdana;
}
.dxpgArea_Office2010Blue
{
	border-width: 1px;
	border-color: #abbacf;
	border-style: none;
	background-color: White;
}
.dxpgColumnArea_Office2010Blue
{
	border-bottom-style: solid;
}
.dxpgRowArea_Office2010Blue
{
}
.dxpgDataArea_Office2010Blue
{
}
.dxpgFilterArea_Office2010Blue
{
	background-color: #cfddee;
	color: #1e395b;
	border-bottom-style: solid;
}
.dxpgEmptyArea_Office2010Blue
{
	cursor: default;
	padding: 6px;
}
.dxpgColumnFieldValue_Office2010Blue
{
    color: #1e395b;
	background: #e4eefa url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.PivotGrid.HeaderBack.png")%>') repeat-x left top;
	border: 1px solid #abbacf;
	border-left-style: solid;
	border-bottom-style: solid;
	border-right-style: none;
	border-top-style: none;
	padding: 4px 6px;
	font-weight: normal;
	text-align: left;
}
.dxpgColumnTotalFieldValue_Office2010Blue
{
	background: #cfdbeb url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.PivotGrid.TotalBack.png")%>') repeat-x left top;
	border-left-style: solid;
	border-bottom-style: solid;
	border-right-style: none;
	border-top-style: none;
}
.dxpgColumnGrandTotalFieldValue_Office2010Blue
{
	background: #c0d1e2 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.PivotGrid.GrandTotalBack.png")%>') repeat-x left top;
	border-left-style: solid;
	border-bottom-style: solid;
	border-right-style: none;
	border-top-style: none;
}
.dxpgRowFieldValue_Office2010Blue
{
    color: #1e395b;
	background: #e4eefa url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.PivotGrid.HeaderBack.png")%>') repeat-x left top;
	border: 1px solid #abbacf;
	border-left-style: none;
	border-bottom-style: none;
	border-right-style: solid;
	border-top-style: solid;
	padding: 3px 6px;
	font-weight: normal;
	text-align: left;
}
.dxpgRowTotalFieldValue_Office2010Blue
{
	background: #cfdbeb url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.PivotGrid.TotalBack.png")%>') repeat-x left top;
	border-left-style: none;
	border-bottom-style: none;
	border-right-style: solid;
	border-top-style: solid;
}
.dxpgRowTreeFieldValue_Office2010Blue
{
	padding: 0px;
	font: 0pt;
}
.dxpgRowGrandTotalFieldValue_Office2010Blue
{
	background: #c0d1e2 url('<%=WebResource("DevExpress.Web.ASPxThemes.App_Themes.Office2010Blue.PivotGrid.GrandTotalBack.png")%>') repeat-x left top;
	border-left-style: none;
	border-bottom-style: none;
	border-right-style: solid;
	border-top-style: solid;
	padding-top: 5px;
	padding-bottom: 5px;
}
.dxpgCollapsedButton_Office2010Blue
{
	vertical-align: -2px;
	border: 0;
	margin-right: 5px;
}
.dxpgSortByColumnImage_Office2010Blue
{
	vertical-align: -1px;
	border: 0;
	margin-left: 5px;
}
.dxpgCell_Office2010Blue
{
	text-align: right;
	background-color: White;
	border-color: #c4d9f6;
	border-width: 1px;
	border-top-style: solid;
	border-left-style: solid;
	border-bottom-style: none;
	border-right-style: none;
	padding: 3px 4px;
	white-space: nowrap;
}
.dxpgKPICell_Office2010Blue
{
	text-align: center;
	vertical-align: middle;
}
.dxpgTotalCell_Office2010Blue
{
	background-color: #f4f6f9;
}
.dxpgGrandTotalCell_Office2010Blue
{
	background-color: #e4eaf1;
}
.dxpgFilterWindow_Office2010Blue
{
	color: Black;
	font: 8pt Verdana;
	border: 1px solid #8ba0bc;
}
.dxpgFilterItemsArea_Office2010Blue
{
	color: Black;
	background-color: White;
}
.dxpgFilterItem_Office2010Blue
{
	font: 8pt Verdana;
    white-space: nowrap;
}
.dxpgFilterButton_Office2010Blue
{
	font: 8pt Verdana;
	padding: 2px 6px;
}
.dxpgFilterButtonPanel_Office2010Blue
{
	font: 8pt Verdana;
	color: #1e395b;
	background-color: #cfddee;
	border-top: 1px solid #8ba0bc;
}
.dxpgLoadingDiv_Office2010Blue
{
	background-color: White;
	opacity: 0.01;
	filter: alpha(opacity=1);
}
.dxpgTopPager_Office2010Blue,
.dxpgBottomPager_Office2010Blue
{
    background: #e4effa;
    border-color: #8ba0bc;
    border-style: solid;
}
.dxpgTopPager_Office2010Blue
{
    border-width: 0 0 1px 0;
}
.dxpgBottomPager_Office2010Blue
{
	border-width: 1px 0 0 0;
}
.dxpgCustomizationFieldsHeader_Office2010Blue
{
	color: #1e395b;
	font: 8pt Verdana;
}
.dxpgCustomizationFieldsContent_Office2010Blue
{
	padding: 0px !important;
}
.dxpgLoadingPanel_Office2010Blue
{
	font: 8pt Verdana;
    color: #1e395b;
    background: White;
	border: 1px solid #859ebf;
}
.dxpgLoadingPanel_Office2010Blue td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 10px 14px;
}
.dxpgMenuItem_Office2010Blue
{
	font: 8pt Verdana;
}
.dxpgDataHeadersImage_Office2010Blue
{
	margin-right: 5px;
	vertical-align: -2px;
}
.dxpgPrefilterPanelContainer_Office2010Blue
{
	border-bottom-style: none;
	border-left-style: none;
	border-right-style: none;
}
.dxpgPrefilterPanel_Office2010Blue
{
	border: none;
	background: #cfddee;
}
.dxpgPrefilterPanelLink_Office2010Blue
{
	color: #1e395b;
	text-decoration: underline;
}
.dxpgPrefilterPanelCheckBoxCell_Office2010Blue
{
	padding: 0 3px;
	padding-right: 7px;
}
.dxpgPrefilterPanelImageCell_Office2010Blue
{
	padding: 0 3px;
	padding-right: 1px;
	cursor: pointer;
}
.dxpgPrefilterPanelExpressionCell_Office2010Blue
{
	font-size: 8pt;
	padding: 5px 5px 8px 0;
	white-space: nowrap;
}
.dxpgPrefilterPanelClearButtonCell_Office2010Blue
{
	font-size: 8pt;
	padding: 5px 6px 8px;
}
.dxpgFilterBuilderMainArea_Office2010Blue
{
	background: white;
	padding: 6px 2px;
}
.dxpgFilterBuilderButtonArea_Office2010Blue
{
	background: #cfddee;
	border-top: 1px solid #aec0d5;
	padding: 6px;
}
.dxpgGroupSeparator_Office2010Blue
{
	vertical-align: middle;
}
.dxpgCustFieldsFilterAreaHeaders_Office2010Blue,
.dxpgCustFieldsRowAreaHeaders_Office2010Blue,
.dxpgCustFieldsColumnAreaHeaders_Office2010Blue,
.dxpgCustFieldsDataAreaHeaders_Office2010Blue,
.BottomPanelOnly1by4 .dxpgFLFRDiv_Office2010Blue,
.BottomPanelOnly1by4 .dxpgFLCDDiv_Office2010Blue
{
    width: 100%;
    height: 50%;
}
.StackedDefault .dxpgCustFieldsFieldList_Office2010Blue
{
    height: 33%;
}
.StackedDefault .dxpgFLFRDiv_Office2010Blue
{
    width: 50%;
    height: 66%;
    float: left;
}
.StackedDefault .dxpgFLCDDiv_Office2010Blue
{
    width: 50%;
    height: 66%;
    float: right;
}
.StackedDefault .dxpgCustFieldsFilterAreaHeaders_Office2010Blue .dxpgFLTextDiv_Office2010Blue,
.StackedDefault .dxpgCustFieldsColumnAreaHeaders_Office2010Blue .dxpgFLTextDiv_Office2010Blue
{
    height: 47px;
}
.TopPanelOnly .dxpgCustFieldsFieldList_Office2010Blue
{
    width: 100%;
    height: 100%;
}
.TopPanelOnly .dxpgFLDefereDiv_Office2010Blue .dxeBase_Office2010Blue,
.TopPanelOnly .dxpgFLDefereDiv_Office2010Blue .dxpgFLDefereDB_Office2010Blue,
.BottomPanelOnly1by4 .dxpgCustFieldsFieldList_Office2010Blue,
.TopPanelOnly .dxpgFLFRDiv_Office2010Blue,
.TopPanelOnly .dxpgFLCDDiv_Office2010Blue,
.BottomPanelOnly2by2 .dxpgCustFieldsFieldList_Office2010Blue,
.TopPanelOnly .dxpgFLTextDiv_Office2010Blue div
{
    display: none;
}
.TopPanelOnly .dxpgFLTextDiv_Office2010Blue
{
    height: 10px;
}
.TopPanelOnly .dxpgFLDefereDiv_Office2010Blue
{
    height: 16px;
}
.BottomPanelOnly2by2 .dxpgFLFRDiv_Office2010Blue,
.StackedSideBySide .dxpgCustFieldsFieldList_Office2010Blue
{
    width: 50%;
    height: 100%;
    float: left;
}
.BottomPanelOnly2by2 .dxpgFLCDDiv_Office2010Blue
{
    width: 50%;
    height: 100%;
    float: right;
}
.StackedSideBySide .dxpgFLFRDiv_Office2010Blue,
.StackedSideBySide .dxpgFLCDDiv_Office2010Blue
{
    width: 50%;
    height: 50%;
    float: right;
}
.dxpgCustFields_Office2010Blue
{
    display: block;
    position: relative;
}
.dxpgFLListDiv_Office2010Blue div
{
    border: 1px solid #8ba0bc;
    position: relative;
    display: block;
    height: 100%;
    padding: 1px;
    background: White;
    overflow: hidden;
}
.dxpgFLListDiv_Office2010Blue div div
{
    height: 100%;
    padding: 0px;
    border-color: #8ba0bc;
    border-style: solid;
    border-width: 0;
    overflow-y: auto;
}
.DragOver .dxpgFLListDiv_Office2010Blue div
{
    background: #ffd324;
}
.DragOver .dxpgFLListDiv_Office2010Blue div div
{
    background: White;
}
.dxpgFLListDiv_Office2010Blue
{
    padding: 0px 3px;
}
.dxpgFLButtonDiv_Office2010Blue .dxbButton_Office2010Blue div.dxb
{
    padding: 2px 8px 1px;
}
.dxpgFLButtonDiv_Office2010Blue .dxbButton_Office2010Blue div.dxbf
{
	border: 1px dotted Black;
	padding: 1px 7px 0px;
}
.dxpgFLTextDiv_Office2010Blue
{
    height: 28px;
}
.dxpgFLTextDiv_Office2010Blue div
{
   display: block;
   float: left;
   margin: -17px 0px 3px;
   left: 6px;
   top: 100%;
   position: relative;
}
.dxpgFLButtonDiv_Office2010Blue
{
    float: right;
    height: 28px;
    position:relative;
    z-index:1;
}
.dxpgFLDefereDiv_Office2010Blue
{
    height: 46px;
}
.dxpgCustFieldsDiv_Office2010Blue
{
     clear: both;
     padding: 0px 9px;
}
.dxpgFLButton_Office2010Blue
{
     margin: 7px 12px 0px 0px;
     width: 40px;
     height: 23px;
}
.dxpgFLDefereDiv_Office2010Blue .dxeBase_Office2010Blue
{
     float: left;
     display: block;
     border-collapse: separate;
     padding: 14px 0px 0px 0px;
     margin-left: 9px;
}
.dxpgFLDefereDB_Office2010Blue
{
    float: right;
    display: block;
    padding: 0px 12px 0px 0px;
    margin-top: 12px;
}
.dxpgFLDefereDiv_Office2010Blue .dxbButton_Office2010Blue div.dxb
{
    padding: 2px 14px;
}
.dxpgFLDefereDiv_Office2010Blue .dxbButton_Office2010Blue div.dxbf
{
    padding: 1px 13px;
    border: 1px dotted Black;
}
.dxpgFLListDiv_Office2010Blue table
{
 width:100%;
 table-layout:fixed;
 overflow:visible;
}
.dxpgFLListDiv_Office2010Blue table table td
{
     overflow:hidden;
}
div.dxpgFLTextImgDiv_Office2010Blue
{
    display:block;
    height:16px;
    width:16px;
    margin:-18px 0px 3px 0px;
    left:3px;
}