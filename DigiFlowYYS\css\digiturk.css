﻿body
{
    font-family: Arial,sans-serif;
    font-size:12px;
	color: #000;
    margin:0;
    padding:0;
    text-align:left;
    unicode-bidi:embed;
}

.modalBackground {
	background-color:Gray;
	filter:alpha(opacity=70);
	opacity:0.7;
}

.modalPopup {
	background-color:#ffffdd;
	border-width:3px;
	border-style:solid;
	border-color:Gray;
	padding:3px;
	width:250px;
}

.updateProgress
{
    border-width: 1px;
    border-style: solid;
    background-color: #FFFFFF;
    position: absolute;
    width: 180px;
    height: 65px;
}

.PageTitle
{
    font-family: Arial,sans-serif;
    font-size:15px;
    font-weight: bold;
	color: #BC022B;
}
  /*
#progressBackgroundFilter {
    position:fixed;
    top:0px;
    bottom:0px;
    left:0px;
    right:0px;
    overflow:hidden;
    padding:0;
    margin:0;
    background-color:#000;
    filter:alpha(opacity=50);
    opacity:0.5;
    z-index:1000;
}
*/
#processMessage {
    position:fixed;
    top:30%;
    left:43%;
    padding:10px;
    width:14%;
    z-index:1001;
    background-color:#fff;
    border:solid 1px #000;
}