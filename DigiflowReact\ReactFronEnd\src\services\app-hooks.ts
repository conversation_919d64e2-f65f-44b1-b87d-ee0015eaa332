import IAppHooks from '@wface/ioc/src/interfaces/i-app-hooks';
import { injectable } from "inversify";
import {setUser} from "./wface-helper";
import Api from "./http-service"

@injectable()
export default class AppHooks implements IAppHooks {
  onAppMount() {
    console.log('App mounted');
  }
  onAppWillMount() {
    Api.GetData("GetUserInformation?LoginId=1763&InstanceId=0&wfDefId=0").then(result=>{
      setUser(result)
    })
  }
  onAppDidMount() {
  }

  onLogin() {
    console.log("================================Login completed================================");
  }

  onLogout() {
    console.log("================================Logout completed================================");
  }
}
