using DigiflowAPI.Domain.Entities.Framework;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Interfaces.Services.Workflow;
using DigiflowAPI.Application.Interfaces.Services;
using Microsoft.AspNetCore.Http;
using DigiflowAPI.Domain.Enums;
using DigiflowAPI.Application.DTOs.Workflow.XML.Definition;
using DigiflowAPI.Application.DTOs.Workflow.Operation;
using System.Text.Json;

namespace DigiflowAPI.Infrastructure.Services
{
    public class WorkflowHelperService(IWorkflowRepository workflowRepository, IUserService userService, IGlobalHelpers globalHelpers, IHttpContextAccessor httpContextAccessor) : IWorkflowHelperService
    {
        public async Task<long> GetActiveDelegateWithRecursiveDateValidation(long ownerLoginID, long delegationLoginId, long workflowDefID, DateTime delegationStartDate, DateTime delegationEndDate)
        {
            return await workflowRepository.GetActiveDelegateWithRecursiveDateValidation(ownerLoginID, delegationLoginId, workflowDefID, delegationStartDate, delegationEndDate);
        }

        public async Task<long> GetActiveDelegateWithRecursive(long loginID, long workflowDefID)
        {
            return await workflowRepository.GetActiveDelegateWithRecursive(loginID, workflowDefID);
        }

        public async Task<IEnumerable<WorkflowXmlDefinitionSelectDto>> GetAllWorkflowsAsync()
        {
            var workflows = await workflowRepository.GetAllWorkflowsAsync();
            return workflows.Select(w => new WorkflowXmlDefinitionSelectDto
            {
                Value = w.WfWorkflowDefId.ToString(),
                Label = w.Name,
                LabelEn = w.Name
            });
        }

        public async Task<bool> CheckForDelegationOwn(long LoginId, long WfDefinitionId, DateTime StartDate, DateTime EndDate)
        {
            return await workflowRepository.CheckForDelegationOwn(LoginId, WfDefinitionId, StartDate, EndDate);
        }

        public async Task<bool?> IsWorkflowInstanceExists(int wfInstanceId)
        {
            return await workflowRepository.IsWorkflowInstanceExists(wfInstanceId);
        }

        public async Task<string> GetWorkflowAdmins(string defPageName)
        {
            return await workflowRepository.GetWorkflowAdmins(defPageName);
        }

        public async Task<string> GetWorkflowAdmins(long defId)
        {
            return await workflowRepository.GetWorkflowAdmins(defId);
        }

        public async Task<int> GetStateDefId(string stateDefAdi)
        {
            return await workflowRepository.GetStateDefId(stateDefAdi);
        }


        public async Task<bool> FlowIsSuspended(long InstanceId)
        {
            if (InstanceId > 0)
            {
                var currentActionTaskInstance = await CurrentActionTaskInstance(InstanceId);
                if (currentActionTaskInstance != null)
                    return currentActionTaskInstance.StartTime > DateTime.Now;
            }
            return false;
        }

        public async Task<bool> FormDelegation(long InstanceId)
        {
            if (InstanceId == 0)
                return false;
            var activeUser = await userService.GetUserInfo();
            IList<long> assignToIdList = await AssignToIdListLong(InstanceId);
            return assignToIdList.Any(item => workflowRepository.DelegationCheck(InstanceId, item, activeUser, DateTime.Now).Result);
        }

        public async Task<bool> IsActionFlow(long WorkFlowInstanceId, long LoginId)
        {
            return await workflowRepository.IsActionFlow(WorkFlowInstanceId, LoginId);
        }

        public async Task<bool> OwnDelegation(long InstanceId)
        {
            return await workflowRepository.DelegateCheck(InstanceId, await userService.GetUserInfo());
        }
        public async Task<long> CurrentWFDefId(long InstanceId)
        {
            var result = await workflowRepository.GetCurrentWfIns(InstanceId);
            try
            {
                return result.WfWorkflowDef.WfWorkflowDefId;
            }
            catch { }
            return 0;
        }
        public async Task<string?> CurrentWFDefName(long InstanceId)
        {
            var result = await workflowRepository.GetCurrentWfIns(InstanceId);
            try
            {
                var wfDefId = result.WfWorkflowDef.Name;
                if (wfDefId != null)
                    return result.WfWorkflowDef.Name;
            }
            catch { }
            return null;
        }

        public async Task<FWfWorkflowInstance?> CurrentWfIns()
        {
            long instanceId = 0;
            long copyInstanceId = 0;
            if (httpContextAccessor.HttpContext.Request.Headers.TryGetValue("X-Workflow-Instance-Id", out var instanceIdValue))
            {
                long.TryParse(instanceIdValue, out instanceId);
            }
            if (httpContextAccessor.HttpContext.Request.Headers.TryGetValue("X-Workflow-Copy-Instance-Id", out var copyInstanceIdValue))
            {
                long.TryParse(copyInstanceIdValue, out copyInstanceId);
            }
            if (instanceId > 0)
            {
                return await workflowRepository.GetWorkflowInstance(instanceId);

            }
            else if (copyInstanceId > 0)
            {
                return await workflowRepository.GetWorkflowInstance((long)copyInstanceId);
            }
            return null;
        }

        public async Task<FWfStateInstance?> CurrentStateIns()
        {
            var currentWfIns = await CurrentWfIns();
            return currentWfIns != null ? currentWfIns.WfCurrentState ?? null : null;
        }

        public async Task<FWfStateDef?> CurrentStateDef()
        {
            var currentStateDef = await CurrentStateIns();
            return currentStateDef != null ? currentStateDef.WfStateDef ?? null : null;
        }

        public async Task<long> AssignToId(long instanceId, long loginId) => await workflowRepository.AssignToId(instanceId, loginId);
        public async Task<IList<FLogin>> AssignToIdList(long InstanceId) => await workflowRepository.AssignToIdList(InstanceId);
        public async Task<IList<long>> AssignToIdListLong(long InstanceId) => await workflowRepository.AssignToIdListLong(InstanceId);


        public async Task<FLogin?> AssignedUser(long instanceId, long loginId)
        {
            try
            {
                var currentAction = await CurrentActionTaskInstance(instanceId);
                if (currentAction == null)
                {
                    throw new InvalidOperationException("Current action task instance not found.");
                }

                var assignToIdList = await AssignToIdListLong(instanceId);
                var isFormDelegated = await FormDelegation(instanceId);

                if (isFormDelegated)
                {
                    if (assignToIdList.Count > 1)
                    {
                        foreach (long item in assignToIdList)
                        {
                            if (await DelegationCheck(instanceId, item, loginId, DateTime.Now))
                            {
                                return await userService.GetFLoginInfo(item);
                            }
                        }

                        if (assignToIdList.Contains(loginId))
                        {
                            return await userService.GetFLoginInfo(loginId);
                        }
                    }
                }
                else
                {
                    if (assignToIdList.Contains(loginId))
                    {
                        return await userService.GetFLoginInfo(loginId);
                    }
                }

                // If we haven't returned yet, get the first assigned user
                var assignedList = await workflowRepository.GetAssignedList(currentAction.WfActionTaskInstanceId);
                return assignedList.FirstOrDefault();
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"An error occurred in GetAssignedUser: {ex.Message}");
                // You might want to log this exception or handle it according to your application's error handling strategy
                return null;
            }

        }

        public async Task<FLogin?> FlowAdmin(long instanceId, long loginId) => await IsFlowAdmin(instanceId, loginId) ? await userService.GetFLoginInfo(loginId) : null;
        public async Task<bool> IsFlowAdmin(long instanceId, long loginId) => await workflowRepository.IsFlowAdmin(instanceId, loginId);
        public async Task<long> CommendToId(long instanceId) => await workflowRepository.CommendToId(instanceId);
        public async Task<IList<long>> CommendToIdLongList(long instanceId) => await workflowRepository.CommendToIdLongList(instanceId);
        public async Task<IEnumerable<long>> GetCommendToLoginDelegeIDList(long instanceId) => await workflowRepository.GetCommendToLoginDelegeIDList(instanceId);
        public async Task<FWfActionTaskInstance> CurrentActionTaskInstance(long InstanceId) => await workflowRepository.GetCurrentActionTaskInstance(InstanceId);
        public async Task<bool> DelegationCheck(long InstanceId, long AssignedLoginId, long ActionLoginId, DateTime DelegationCheckDate)
        {
            return await workflowRepository.DelegationCheck(InstanceId, AssignedLoginId, ActionLoginId, DelegationCheckDate);
        }


        public bool IsInFlowManagerList(string val, string AccessLoginIdList)
        {
            return workflowRepository.IsInFlowManagerList(val, AccessLoginIdList);
        }


        public async Task<YYSAdminType> GetFlowAdminType(long LoginId)
        {
            return await workflowRepository.GetFlowAdminType(LoginId);
        }

        public async Task<bool> UpdateEntity(UpdateEntityRequestDto request, string workflowName)
        {
            // Convert JsonElement properties to Dictionary<string, object>
            var properties = new Dictionary<string, object>();

            if (request.Properties.ValueKind == JsonValueKind.Object)
            {
                foreach (var property in request.Properties.EnumerateObject())
                {
                    properties[property.Name] = property.Value.GetRawText();
                }
            }

            return await workflowRepository.UpdateEntity(request.Id, workflowName, properties);
        }

        public async Task<IEnumerable<FWfWorkflowDef>> GetLiveWorkFlows()
        {
            return await workflowRepository.GetLiveWorkFlows();
        }

        public async Task<IEnumerable<FWfWorkflowDef>> GetWorkflowsOfAdmin(long adminId, bool isAdmin)
        {
            return await workflowRepository.GetWorkflowsOfAdmin(adminId, isAdmin);
        }

        public async Task<long> DelegeAssignUser(long instanceId, long loginId)
        {
            foreach (var item in await AssignToIdListLong(instanceId))
            {
                if (await DelegationCheck(instanceId, item, loginId, DateTime.Now))
                {
                    return item;
                }
            }
            return 0;
        }

        public async Task<bool> IsStateStatik(long WorkFlowInstanceId, long WorkFlowStateId) => await workflowRepository.IsStateStatik(WorkFlowInstanceId, WorkFlowStateId);
        public async Task<long> GetMaxDinamicAssignLoginId(long WorkFlowInstanceId) => await workflowRepository.GetMaxDinamicAssignLoginId(WorkFlowInstanceId);
        public async Task<bool> IsActionFlow(long InstanceId) => await workflowRepository.IsActionFlow(InstanceId, await userService.GetUserInfo());
        public async Task<bool> IsViewUser(long UserId, long WfInstanceId, long wfDefinitionId) => await workflowRepository.IsViewUser(UserId, WfInstanceId, wfDefinitionId);
        public async Task<bool> IsReportAdmin(long WfDefId, long loginId) => await workflowRepository.IsReportAdmin(WfDefId, loginId);
        public async Task<bool> IsRolledBack(long InstanceId) => await workflowRepository.IsRolledBack(InstanceId);
        public async Task<bool> IsFlowAdmin(long InstanceId) => await workflowRepository.IsFlowAdmin(InstanceId, await userService.GetUserInfo());
        public async Task<bool> IsWfAdmin(long wfDefId) => await workflowRepository.IsWfAdmin(wfDefId);
        public async Task<long> LastSendTaskLoginId(long InstanceId) => await workflowRepository.GetLastActionToLoginId(InstanceId);
        public async Task<IList<string>?> LastSendTaskLoginIdList(long InstanceId) => await workflowRepository.GetLastActionToLoginIdList(InstanceId);
        public async Task<bool> IsLastSendTaskLoginUser(long InstanceId) => await LastSendTaskLoginId(InstanceId) == await userService.GetUserInfo();
    }
}