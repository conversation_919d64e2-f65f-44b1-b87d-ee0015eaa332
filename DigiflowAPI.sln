Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Application", "src\DigiflowAPI.Application\DigiflowAPI.Application.csproj", "{F8B9AD16-F83F-9070-81BD-D3AE707CC425}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.CompatibilityApi", "src\DigiflowAPI.CompatibilityApi\DigiflowAPI.CompatibilityApi.csproj", "{BE904AD5-43B7-458D-B923-4A293C874742}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Domain", "src\DigiflowAPI.Domain\DigiflowAPI.Domain.csproj", "{11D246D1-ECA3-DFB7-7D3C-FE4FA8CF45E5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Infrastructure", "src\DigiflowAPI.Infrastructure\DigiflowAPI.Infrastructure.csproj", "{D0A811FD-E3EA-7DE4-B12C-B3CB66867CCB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.MobileAPI", "src\DigiflowAPI.MobileApi\DigiflowAPI.MobileAPI.csproj", "{776B4B05-7E6A-9601-AD14-F9BCAB589085}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Resources", "src\DigiflowAPI.Resources\DigiflowAPI.Resources.csproj", "{B52FC1F3-9FD6-44A8-98BC-B2380346EA13}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Security", "src\DigiflowAPI.Security\DigiflowAPI.Security.csproj", "{863BCA9E-3BEC-D69E-3203-F5E29BA8EFAB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.WebAPI", "src\DigiflowAPI.WebApi\DigiflowAPI.WebAPI.csproj", "{E6AD7FDA-F947-DAD9-EE73-E37810B964FE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Application.Tests", "tests\DigiflowAPI.Application.Tests\DigiflowAPI.Application.Tests.csproj", "{7F871ABC-5654-5CE8-976F-8F25637F24E0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Domain.Tests", "tests\DigiflowAPI.Domain.Tests\DigiflowAPI.Domain.Tests.csproj", "{538F1A89-59C5-A18F-5B80-4CFAE7994C0B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Infrastructure.Tests", "tests\DigiflowAPI.Infrastructure.Tests\DigiflowAPI.Infrastructure.Tests.csproj", "{5E61B431-9A13-D3F0-E9BC-D3B1988BC21F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Logging.Tests", "tests\DigiflowAPI.Logging.Tests\DigiflowAPI.Logging.Tests.csproj", "{0BE6EFC7-7A5D-B21F-1365-A80C60E99EBA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.Security.Tests", "tests\DigiflowAPI.Security.Tests\DigiflowAPI.Security.Tests.csproj", "{E7EF7768-A858-1C62-3F58-B1988EB5FB8B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DigiflowAPI.WebApi.Tests", "tests\DigiflowAPI.WebApi.Tests\DigiflowAPI.WebApi.Tests.csproj", "{5974194D-1198-49D0-DB4E-7458E9764698}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F8B9AD16-F83F-9070-81BD-D3AE707CC425}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F8B9AD16-F83F-9070-81BD-D3AE707CC425}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F8B9AD16-F83F-9070-81BD-D3AE707CC425}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F8B9AD16-F83F-9070-81BD-D3AE707CC425}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE904AD5-43B7-458D-B923-4A293C874742}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE904AD5-43B7-458D-B923-4A293C874742}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE904AD5-43B7-458D-B923-4A293C874742}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE904AD5-43B7-458D-B923-4A293C874742}.Release|Any CPU.Build.0 = Release|Any CPU
		{11D246D1-ECA3-DFB7-7D3C-FE4FA8CF45E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11D246D1-ECA3-DFB7-7D3C-FE4FA8CF45E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11D246D1-ECA3-DFB7-7D3C-FE4FA8CF45E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11D246D1-ECA3-DFB7-7D3C-FE4FA8CF45E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0A811FD-E3EA-7DE4-B12C-B3CB66867CCB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0A811FD-E3EA-7DE4-B12C-B3CB66867CCB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0A811FD-E3EA-7DE4-B12C-B3CB66867CCB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0A811FD-E3EA-7DE4-B12C-B3CB66867CCB}.Release|Any CPU.Build.0 = Release|Any CPU
		{776B4B05-7E6A-9601-AD14-F9BCAB589085}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{776B4B05-7E6A-9601-AD14-F9BCAB589085}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{776B4B05-7E6A-9601-AD14-F9BCAB589085}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{776B4B05-7E6A-9601-AD14-F9BCAB589085}.Release|Any CPU.Build.0 = Release|Any CPU
		{B52FC1F3-9FD6-44A8-98BC-B2380346EA13}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B52FC1F3-9FD6-44A8-98BC-B2380346EA13}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B52FC1F3-9FD6-44A8-98BC-B2380346EA13}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B52FC1F3-9FD6-44A8-98BC-B2380346EA13}.Release|Any CPU.Build.0 = Release|Any CPU
		{863BCA9E-3BEC-D69E-3203-F5E29BA8EFAB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{863BCA9E-3BEC-D69E-3203-F5E29BA8EFAB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{863BCA9E-3BEC-D69E-3203-F5E29BA8EFAB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{863BCA9E-3BEC-D69E-3203-F5E29BA8EFAB}.Release|Any CPU.Build.0 = Release|Any CPU
		{E6AD7FDA-F947-DAD9-EE73-E37810B964FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E6AD7FDA-F947-DAD9-EE73-E37810B964FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E6AD7FDA-F947-DAD9-EE73-E37810B964FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E6AD7FDA-F947-DAD9-EE73-E37810B964FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F871ABC-5654-5CE8-976F-8F25637F24E0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F871ABC-5654-5CE8-976F-8F25637F24E0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F871ABC-5654-5CE8-976F-8F25637F24E0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F871ABC-5654-5CE8-976F-8F25637F24E0}.Release|Any CPU.Build.0 = Release|Any CPU
		{538F1A89-59C5-A18F-5B80-4CFAE7994C0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{538F1A89-59C5-A18F-5B80-4CFAE7994C0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{538F1A89-59C5-A18F-5B80-4CFAE7994C0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{538F1A89-59C5-A18F-5B80-4CFAE7994C0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{5E61B431-9A13-D3F0-E9BC-D3B1988BC21F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5E61B431-9A13-D3F0-E9BC-D3B1988BC21F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5E61B431-9A13-D3F0-E9BC-D3B1988BC21F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5E61B431-9A13-D3F0-E9BC-D3B1988BC21F}.Release|Any CPU.Build.0 = Release|Any CPU
		{0BE6EFC7-7A5D-B21F-1365-A80C60E99EBA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0BE6EFC7-7A5D-B21F-1365-A80C60E99EBA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0BE6EFC7-7A5D-B21F-1365-A80C60E99EBA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0BE6EFC7-7A5D-B21F-1365-A80C60E99EBA}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7EF7768-A858-1C62-3F58-B1988EB5FB8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7EF7768-A858-1C62-3F58-B1988EB5FB8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7EF7768-A858-1C62-3F58-B1988EB5FB8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7EF7768-A858-1C62-3F58-B1988EB5FB8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{5974194D-1198-49D0-DB4E-7458E9764698}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5974194D-1198-49D0-DB4E-7458E9764698}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5974194D-1198-49D0-DB4E-7458E9764698}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5974194D-1198-49D0-DB4E-7458E9764698}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F8B9AD16-F83F-9070-81BD-D3AE707CC425} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{BE904AD5-43B7-458D-B923-4A293C874742} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{11D246D1-ECA3-DFB7-7D3C-FE4FA8CF45E5} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{D0A811FD-E3EA-7DE4-B12C-B3CB66867CCB} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{776B4B05-7E6A-9601-AD14-F9BCAB589085} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{B52FC1F3-9FD6-44A8-98BC-B2380346EA13} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{863BCA9E-3BEC-D69E-3203-F5E29BA8EFAB} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{E6AD7FDA-F947-DAD9-EE73-E37810B964FE} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{7F871ABC-5654-5CE8-976F-8F25637F24E0} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{538F1A89-59C5-A18F-5B80-4CFAE7994C0B} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{5E61B431-9A13-D3F0-E9BC-D3B1988BC21F} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{0BE6EFC7-7A5D-B21F-1365-A80C60E99EBA} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{E7EF7768-A858-1C62-3F58-B1988EB5FB8B} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{5974194D-1198-49D0-DB4E-7458E9764698} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8CA4F1DD-BC80-4400-9CB6-AB32B87375B8}
	EndGlobalSection
EndGlobal
